export interface DatePickerProps {
  initialDate?: Date
  onDateChange?: (date: Date) => void
  className?: string
}

export interface CalendarDate {
  date: number
  isSelected: boolean
  isCurrentMonth: boolean
}

export type MonthNavigationDirection = 'prev' | 'next'

export interface DateDisplayState {
  currentDate: Date
  isCalendarVisible: boolean
  isAnimating: boolean
}

// Deutsche Lokalisierungskonstanten
export const GERMAN_MONTHS_LONG = [
  "JANUAR", "FEBRUAR", "MÄRZ", "APRIL", "MAI", "JUNI",
  "JUL<PERSON>", "AUGUST", "SEPTEMBER", "OKTOBER", "NOVEMBER", "DEZEMBER"
] as const

export const GERMAN_MONTHS_SHORT = [
  "Jan", "Feb", "Mär", "Apr", "Mai", "Jun",
  "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"
] as const

export const GERMAN_DAYS = ["So", "<PERSON>", "Di", "Mi", "Do", "Fr", "Sa"] as const

export type GermanMonthLong = typeof GERMAN_MONTHS_LONG[number]
export type GermanMonthShort = typeof GERMAN_MONTHS_SHORT[number]
export type GermanDay = typeof GERMAN_DAYS[number]