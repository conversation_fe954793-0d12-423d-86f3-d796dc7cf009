const sqlite3 = require('sqlite3').verbose(); // TODO: Auf PostgreSQL (pg) umstellen
const path = require('path');

// Pfad zur SQLite-Datenbank
const dbPath = path.resolve(__dirname, '../database/sfm_dashboard.db'); // TODO: PostgreSQL-Verbindung verwenden

// Maschinen-Identifikatoren aus der SystemStatusHeatmap-Komponente
const machineIdentifiers = [
  'M1-T-H3', 'M2-T-H3', 'M3-R-H3', 'M4-T-H3', 'M5-R-H3',
  'M6-T-H1', 'M7-R-H1', 'M8-T-H1', 'M9-R-H1', 'M10-T-H1',
  'M11-R-H1', 'M12-T-H1', 'M13-R-H1', 'M14-T-H1', 'M15-R-H1',
  'M16-T-H1', 'M17-R-H1', 'M18-T-H1', 'M19-T-H1', 'M20-T-H1',
  'M21-R-H1', 'M22-T-H3', 'M23-T-H1', 'M24-T-H3', 'M25-RR-H1',
  'M26-T-H1', 'M27-R-H3', 'M28-T-H1'
];

// Alle System-Definitionen
const systemDefinitions = [
  // Maschinen
  ...machineIdentifiers.map(id => {
    // Setze einige Maschinen auf OFF-Status für Demonstrationszwecke
    let status = 'OK';
    let metadata = {
      type: 'machine',
      location: id.includes('H3') ? 'Halle 3' : 'Halle 1',
      machine_type: id.includes('-T-') ? 'Terminal' : id.includes('-R-') ? 'Ring' : id.includes('-RR-') ? 'Ring-Ring' : 'Unknown',
      category: 'Maschinen'
    };
    
    // Bestimmte Maschinen auf OFF setzen
    if (['M5-R-H3', 'M15-R-H1', 'M25-RR-H1'].includes(id)) {
      status = 'OFF';
      metadata.reason = 'Geplante Wartung';
      metadata.next_maintenance = 'Morgen 08:00';
    }
    
    return {
      system_name: id,
      status: status,
      metadata: JSON.stringify(metadata)
    };
  }),
  
  // Datenbanken
  {
    system_name: 'Datenbank Primary Server',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'database',
      location: 'Serverraum',
      category: 'Datenbanken',
      role: 'primary'
    })
  },
  {
    system_name: 'Datenbank Backup Server',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'database',
      location: 'Serverraum',
      category: 'Datenbanken',
      role: 'backup'
    })
  },
  
  // Terminals
  {
    system_name: 'Terminal Station 1',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'terminal',
      location: 'Halle 1',
      category: 'Terminals',
      station_id: 1
    })
  },
  {
    system_name: 'Terminal Station 2',
    status: 'OFF',
    metadata: JSON.stringify({
      type: 'terminal',
      location: 'Halle 2',
      category: 'Terminals',
      station_id: 2,
      reason: 'Planmäßige Wartung'
    })
  },
  {
    system_name: 'Terminal Station 3',
    status: 'WARNING',
    metadata: JSON.stringify({
      type: 'terminal',
      location: 'Halle 3',
      category: 'Terminals',
      station_id: 3,
      issue: 'Wartung erforderlich'
    })
  },
  
  // Fördertechnik
  {
    system_name: 'Fördertechnik Zone A',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'conveyor',
      location: 'Zone A',
      category: 'Fördertechnik',
      capacity: '500 Pakete/h'
    })
  },
  {
    system_name: 'Fördertechnik Zone B',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'conveyor',
      location: 'Zone B',
      category: 'Fördertechnik',
      capacity: '500 Pakete/h'
    })
  },
  {
    system_name: 'Fördertechnik Zone C',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'conveyor',
      location: 'Zone C',
      category: 'Fördertechnik',
      capacity: '300 Pakete/h'
    })
  },
  
  // Schrumpfanlagen
  {
    system_name: 'Schrumpfanlage A01',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'shrink_wrap',
      location: 'Halle 1',
      category: 'Anlagen',
      model: 'A01',
      capacity: '150 Pakete/h'
    })
  },
  {
    system_name: 'Schrumpfanlage B02',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'shrink_wrap',
      location: 'Halle 2',
      category: 'Anlagen',
      model: 'B02',
      capacity: '200 Pakete/h'
    })
  },
  
  // Netzwerk
  {
    system_name: 'Wlan Access Point 1',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'network',
      location: 'Halle 1',
      category: 'Netzwerk',
      device_type: 'access_point'
    })
  },
  {
    system_name: 'Wlan Access Point 2',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'network',
      location: 'Halle 2',
      category: 'Netzwerk',
      device_type: 'access_point'
    })
  },
  {
    system_name: 'Wlan Core Switch',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'network',
      location: 'Serverraum',
      category: 'Netzwerk',
      device_type: 'core_switch'
    })
  },
  
  // Lager
  {
    system_name: 'Automatisches Trommellager',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'storage',
      location: 'Halle 1',
      category: 'Läger',
      storage_type: 'drum',
      capacity: '10000 Plätze'
    })
  },
  {
    system_name: 'Automatisches Ringlager',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'storage',
      location: 'Halle 2',
      category: 'Läger',
      storage_type: 'ring',
      capacity: '8000 Plätze'
    })
  },
  
  // Flurförderzeuge
  {
    system_name: 'Stapler',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'forklift',
      location: 'Zentralsteuerung',
      category: 'Flurförderzeuge',
      fleet_size: 12
    })
  },
  {
    system_name: 'FTS',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'agv',
      location: 'Produktionshalle',
      category: 'Flurförderzeuge',
      vehicle_count: 8
    })
  },
  
  // SAP
  {
    system_name: 'SAP',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'erp',
      location: 'Datacenter',
      category: 'SAP',
      modules: ['WM', 'PP', 'MM', 'FI']
    })
  },
  
  // ITM
  {
    system_name: 'ITM',
    status: 'OK',
    metadata: JSON.stringify({
      type: 'wcs',
      location: 'Serverraum',
      category: 'ITM',
      version: '2.4.1'
    })
  }
];

function seedSystemStatus() {
  console.log('🌱 Starting SystemStatus seeding...');
  
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('❌ Error connecting to database:', err.message);
        reject(err);
        return;
      }
      console.log('✅ Connected to SQLite database');
    });

    // Lösche zuerst alle bestehenden Einträge
    console.log('🗑️  Clearing existing SystemStatus entries...');
    db.run('DELETE FROM SystemStatus', (err) => {
      if (err) {
        console.error('❌ Error clearing SystemStatus:', err.message);
        reject(err);
        return;
      }
      console.log('✅ Cleared existing entries');
      
      // Füge alle System-Definitionen hinzu
      console.log('📝 Creating new SystemStatus entries...');
      
      const stmt = db.prepare(`
        INSERT INTO SystemStatus (system_name, status, metadata, created_at, updated_at, last_check)
        VALUES (?, ?, ?, datetime('now'), datetime('now'), datetime('now'))
      `);

      let count = 0;
      const categories = {};
      const statusCounts = {};

      systemDefinitions.forEach((systemDef) => {
        stmt.run([systemDef.system_name, systemDef.status, systemDef.metadata], (err) => {
          if (err) {
            console.error(`❌ Error inserting system ${systemDef.system_name}:`, err.message);
            return;
          }
          
          count++;
          
          // Kategorie-Zählung
          const metadata = JSON.parse(systemDef.metadata);
          const category = metadata.category || 'Unknown';
          categories[category] = (categories[category] || 0) + 1;
          
          // Status-Zählung
          statusCounts[systemDef.status] = (statusCounts[systemDef.status] || 0) + 1;
          
          // Wenn alle Einträge eingefügt wurden
          if (count === systemDefinitions.length) {
            stmt.finalize();
            
            console.log(`✅ Created ${count} SystemStatus entries`);
            
            console.log('\n📊 Created systems by category:');
            Object.entries(categories).forEach(([category, count]) => {
              console.log(`  - ${category}: ${count} systems`);
            });
            
            console.log('\n🚦 Status distribution:');
            Object.entries(statusCounts).forEach(([status, count]) => {
              console.log(`  - ${status}: ${count} systems`);
            });
            
            console.log('\n🎉 SystemStatus seeding completed successfully!');
            
            db.close((err) => {
              if (err) {
                console.error('❌ Error closing database:', err.message);
                reject(err);
              } else {
                console.log('✅ Database connection closed');
                resolve();
              }
            });
          }
        });
      });
    });
  });
}

// Führe das Script aus
seedSystemStatus().catch((error) => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
