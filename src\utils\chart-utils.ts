/**
 * Chart-Utilities für robuste Datenverarbeitung
 * 
 * Diese Utilities lösen das Problem mit null-Datumswerten in Charts
 * und implementieren defensive Programmierung für Chart-Komponenten.
 */

import { format, isValid, parseISO } from 'date-fns';
import { de } from 'date-fns/locale';

export interface ChartDataPoint {
  date?: string | null;
  datum?: string | null;
  [key: string]: any;
}

/**
 * Normalisiert Datumswerte zu einem konsistenten Format
 * @param raw - Roher <PERSON>swert (Date, string, null, undefined)
 * @returns Normalisiertes Datum als ISO-String (YYYY-MM-DD) oder null
 */
export function normalizeDate(raw: unknown): string | null {
  // Date-Objekte
  if (raw instanceof Date && isValid(raw)) {
    return raw.toISOString().slice(0, 10);
  }
  
  // ISO-String-Format
  if (typeof raw === 'string' && /^\d{4}-\d{2}-\d{2}/.test(raw)) {
    const parsed = parseISO(raw);
    if (isValid(parsed)) {
      return parsed.toISOString().slice(0, 10);
    }
  }
  
  // Andere String-Formate versuchen zu parsen
  if (typeof raw === 'string' && raw.trim()) {
    try {
      const parsed = new Date(raw);
      if (isValid(parsed)) {
        return parsed.toISOString().slice(0, 10);
      }
    } catch {
      // Parsing fehlgeschlagen, return null
    }
  }
  
  return null;
}

/**
 * Erstellt ein Fallback-Datum für Daten ohne gültiges Datum
 * @param index - Index des Datenpunkts für eindeutige Fallback-Daten
 * @returns Fallback-Datum als ISO-String
 */
export function createFallbackDate(index: number = 0): string {
  // Verwende heute's Datum minus Index Tage für eindeutige Fallback-Daten
  const fallbackDate = new Date();
  fallbackDate.setDate(fallbackDate.getDate() - index);
  return fallbackDate.toISOString().slice(0, 10);
}

/**
 * Verarbeitet Chart-Daten und normalisiert Datumswerte
 * @param rawData - Rohe Chart-Daten
 * @param options - Verarbeitungsoptionen
 * @returns Verarbeitete Chart-Daten mit normalisierten Datumsangaben
 */
export function processChartData<T extends ChartDataPoint>(
  rawData: T[],
  options: {
    ignoreInvalidDates?: boolean;
    useFallbackDates?: boolean;
    dateFieldName?: keyof T;
  } = {}
): (T & { normalizedDate: string | null; hasValidDate: boolean })[] {
  const {
    ignoreInvalidDates = false,
    useFallbackDates = true,
    dateFieldName = 'date'
  } = options;

  return rawData.map((item, index) => {
    // Versuche Datum aus verschiedenen Feldern zu extrahieren
    const dateValue = item[dateFieldName] || item.datum || item.date;
    const normalizedDate = normalizeDate(dateValue);
    const hasValidDate = normalizedDate !== null;
    
    // Fallback-Datum erstellen wenn gewünscht und kein gültiges Datum vorhanden
    let finalDate = normalizedDate;
    if (!hasValidDate && useFallbackDates) {
      finalDate = createFallbackDate(index);
    }
    
    return {
      ...item,
      normalizedDate: finalDate,
      hasValidDate,
      // Überschreibe das ursprüngliche Datumsfeld mit normalisiertem Wert
      [dateFieldName]: finalDate
    } as T & { normalizedDate: string | null; hasValidDate: boolean };
  }).filter(item => {
    // Filtere ungültige Daten heraus wenn gewünscht
    if (ignoreInvalidDates) {
      return item.hasValidDate || item.normalizedDate !== null;
    }
    return true;
  });
}

/**
 * Analysiert die Datenqualität in Bezug auf Datumswerte
 * @param data - Chart-Daten zur Analyse
 * @param dateFieldName - Name des Datumsfelds
 * @returns Datenqualitäts-Statistiken
 */
export function analyzeDataQuality<T extends ChartDataPoint>(
  data: T[],
  dateFieldName: keyof T = 'date'
): {
  totalRecords: number;
  validDates: number;
  invalidDates: number;
  validDatePercentage: number;
  hasDataQualityIssues: boolean;
} {
  if (data.length === 0) {
    return {
      totalRecords: 0,
      validDates: 0,
      invalidDates: 0,
      validDatePercentage: 0,
      hasDataQualityIssues: false
    };
  }

  const validDates = data.filter(item => {
    const dateValue = item[dateFieldName];
    return normalizeDate(dateValue) !== null;
  }).length;

  const invalidDates = data.length - validDates;
  const validDatePercentage = (validDates / data.length) * 100;
  const hasDataQualityIssues = validDatePercentage < 90; // Weniger als 90% gültige Daten

  return {
    totalRecords: data.length,
    validDates,
    invalidDates,
    validDatePercentage: Math.round(validDatePercentage * 10) / 10,
    hasDataQualityIssues
  };
}

/**
 * Formatiert ein Datum für die Anzeige in Charts
 * @param dateString - ISO-Datum-String
 * @param formatString - Format-String für date-fns
 * @returns Formatiertes Datum oder Fallback
 */
export function formatChartDate(
  dateString: string | null | undefined,
  formatString: string = 'dd.MM.yyyy'
): string {
  if (!dateString) return 'Unbekannt';
  
  try {
    const date = parseISO(dateString);
    if (isValid(date)) {
      return format(date, formatString, { locale: de });
    }
  } catch {
    // Format-Fehler
  }
  
  return dateString || 'Unbekannt';
}

/**
 * Erstellt eine Warnung für Datenqualitätsprobleme
 * @param qualityAnalysis - Ergebnis der Datenqualitäts-Analyse
 * @returns Warnungs-Objekt oder null
 */
export function createDataQualityWarning(qualityAnalysis: ReturnType<typeof analyzeDataQuality>): {
  level: 'info' | 'warning' | 'error';
  message: string;
  details: string;
} | null {
  if (!qualityAnalysis.hasDataQualityIssues) return null;

  const { validDatePercentage, invalidDates, totalRecords } = qualityAnalysis;

  if (validDatePercentage === 0) {
    return {
      level: 'error',
      message: 'Keine gültigen Datumswerte verfügbar',
      details: `Alle ${totalRecords} Datensätze haben ungültige oder fehlende Datumsangaben. Fallback-Daten werden verwendet.`
    };
  }

  if (validDatePercentage < 50) {
    return {
      level: 'error',
      message: 'Schwere Datenqualitätsprobleme',
      details: `Nur ${validDatePercentage}% (${totalRecords - invalidDates}/${totalRecords}) der Datensätze haben gültige Datumsangaben.`
    };
  }

  return {
    level: 'warning',
    message: 'Datenqualitätsprobleme erkannt',
    details: `${invalidDates} von ${totalRecords} Datensätzen (${(100 - validDatePercentage).toFixed(1)}%) haben ungültige Datumsangaben.`
  };
}

/**
 * Hilfsfunktion für TypeScript-sichere Datumsfeld-Extraktion
 */
export function extractDateValue<T extends Record<string, any>>(
  item: T,
  possibleFields: (keyof T)[] = ['date', 'datum', 'createdAt', 'timestamp']
): string | null {
  for (const field of possibleFields) {
    const value = item[field];
    const normalized = normalizeDate(value);
    if (normalized !== null) {
      return normalized;
    }
  }
  return null;
}
