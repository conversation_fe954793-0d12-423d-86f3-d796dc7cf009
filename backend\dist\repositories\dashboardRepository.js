"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dashboardRepository = exports.DashboardRepository = void 0;
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
class DashboardRepository {
    constructor() {
        this.db = db_1.db;
    }
    /**
     * Holt ServiceGrad-Daten aus der Datenbank
     */
    async getServiceLevelData(startDate, endDate) {
        let whereConditions = [(0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.servicegrad)]; // Nur Datensätze mit servicegrad
        if (startDate && endDate) {
            whereConditions.push((0, drizzle_orm_1.gte)(schema_1.dispatchData.datum, startDate), (0, drizzle_orm_1.lte)(schema_1.dispatchData.datum, endDate));
        }
        const result = await this.db.select({
            datum: schema_1.dispatchData.datum,
            servicegrad: schema_1.dispatchData.servicegrad
        })
            .from(schema_1.dispatchData)
            .where((0, drizzle_orm_1.and)(...whereConditions))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum));
        return result.map((row) => ({
            datum: row.datum,
            servicegrad: Number(row.servicegrad) || 0
        }));
    }
    /**
     * Holt Lieferpositionen-Daten aus der Datenbank
     */
    async getDeliveryPositionsData(startDate, endDate) {
        let whereConditions = [
            (0, drizzle_orm_1.sql) `${schema_1.dispatchData.ausgeliefert_lup} IS NOT NULL OR ${schema_1.dispatchData.rueckstaendig} IS NOT NULL`
        ];
        if (startDate && endDate) {
            whereConditions.push((0, drizzle_orm_1.gte)(schema_1.dispatchData.datum, startDate), (0, drizzle_orm_1.lte)(schema_1.dispatchData.datum, endDate));
        }
        const result = await this.db.select({
            datum: schema_1.dispatchData.datum,
            ausgeliefert_lup: schema_1.dispatchData.ausgeliefert_lup,
            rueckstaendig: schema_1.dispatchData.rueckstaendig
        })
            .from(schema_1.dispatchData)
            .where((0, drizzle_orm_1.and)(...whereConditions))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum));
        return result.map((row) => ({
            datum: row.datum,
            ausgeliefert_lup: Number(row.ausgeliefert_lup) || 0,
            rueckstaendig: Number(row.rueckstaendig) || 0
        }));
    }
    /**
     * Holt Tagesleistung-Daten aus der Datenbank
     */
    async getTagesleistungData(startDate, endDate) {
        let whereConditions = [(0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.produzierte_tonnagen)]; // Nur Datensätze mit produzierte_tonnagen
        if (startDate && endDate) {
            whereConditions.push((0, drizzle_orm_1.gte)(schema_1.dispatchData.datum, startDate), (0, drizzle_orm_1.lte)(schema_1.dispatchData.datum, endDate));
        }
        const result = await this.db.select({
            datum: schema_1.dispatchData.datum,
            produzierte_tonnagen: schema_1.dispatchData.produzierte_tonnagen,
            direktverladung_kiaa: schema_1.dispatchData.direktverladung_kiaa,
            umschlag: schema_1.dispatchData.umschlag,
            kg_pro_colli: schema_1.dispatchData.kg_pro_colli,
            elefanten: schema_1.dispatchData.elefanten
        })
            .from(schema_1.dispatchData)
            .where((0, drizzle_orm_1.and)(...whereConditions))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum));
        return result.map((row) => ({
            datum: row.datum,
            produzierte_tonnagen: Number(row.produzierte_tonnagen) || 0,
            direktverladung_kiaa: Number(row.direktverladung_kiaa) || 0,
            umschlag: Number(row.umschlag) || 0,
            kg_pro_colli: Number(row.kg_pro_colli) || 0,
            elefanten: Number(row.elefanten) || 0
        }));
    }
    /**
     * Holt Picking-Daten aus der Datenbank
     */
    async getPickingData(startDate, endDate) {
        let whereConditions = [
            (0, drizzle_orm_1.sql) `${schema_1.dispatchData.atrl} IS NOT NULL OR ${schema_1.dispatchData.aril} IS NOT NULL`
        ];
        if (startDate && endDate) {
            whereConditions.push((0, drizzle_orm_1.gte)(schema_1.dispatchData.datum, startDate), (0, drizzle_orm_1.lte)(schema_1.dispatchData.datum, endDate));
        }
        const result = await this.db.select({
            datum: schema_1.dispatchData.datum,
            atrl: schema_1.dispatchData.atrl,
            aril: schema_1.dispatchData.aril,
            fuellgrad_aril: schema_1.dispatchData.fuellgrad_aril
        })
            .from(schema_1.dispatchData)
            .where((0, drizzle_orm_1.and)(...whereConditions))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum));
        return result.map((row) => ({
            datum: row.datum,
            atrl: Number(row.atrl) || 0,
            aril: Number(row.aril) || 0,
            fuellgrad_aril: Number(row.fuellgrad_aril) || 0
        }));
    }
    /**
     * Holt alle Dashboard-Daten in einer Abfrage für bessere Performance
     */
    async getAllDashboardData(startDate, endDate) {
        let whereConditions = [];
        if (startDate && endDate) {
            whereConditions.push((0, drizzle_orm_1.gte)(schema_1.dispatchData.datum, startDate), (0, drizzle_orm_1.lte)(schema_1.dispatchData.datum, endDate));
        }
        const allData = await this.db.select({
            datum: schema_1.dispatchData.datum,
            servicegrad: schema_1.dispatchData.servicegrad,
            ausgeliefert_lup: schema_1.dispatchData.ausgeliefert_lup,
            rueckstaendig: schema_1.dispatchData.rueckstaendig,
            produzierte_tonnagen: schema_1.dispatchData.produzierte_tonnagen,
            direktverladung_kiaa: schema_1.dispatchData.direktverladung_kiaa,
            umschlag: schema_1.dispatchData.umschlag,
            kg_pro_colli: schema_1.dispatchData.kg_pro_colli,
            elefanten: schema_1.dispatchData.elefanten,
            atrl: schema_1.dispatchData.atrl,
            aril: schema_1.dispatchData.aril,
            fuellgrad_aril: schema_1.dispatchData.fuellgrad_aril
        })
            .from(schema_1.dispatchData)
            .where(whereConditions.length > 0 ? (0, drizzle_orm_1.and)(...whereConditions) : undefined)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum));
        const serviceLevel = [];
        const deliveryPositions = [];
        const tagesleistung = [];
        const picking = [];
        allData.forEach((row) => {
            // Service Level Data
            if (row.servicegrad !== null && row.servicegrad !== undefined) {
                serviceLevel.push({
                    datum: row.datum,
                    servicegrad: Number(row.servicegrad) || 0
                });
            }
            // Delivery Positions Data
            if ((row.ausgeliefert_lup !== null && row.ausgeliefert_lup !== undefined) ||
                (row.rueckstaendig !== null && row.rueckstaendig !== undefined)) {
                deliveryPositions.push({
                    datum: row.datum,
                    ausgeliefert_lup: Number(row.ausgeliefert_lup) || 0,
                    rueckstaendig: Number(row.rueckstaendig) || 0
                });
            }
            // Tagesleistung Data
            if (row.produzierte_tonnagen !== null && row.produzierte_tonnagen !== undefined) {
                tagesleistung.push({
                    datum: row.datum,
                    produzierte_tonnagen: Number(row.produzierte_tonnagen) || 0,
                    direktverladung_kiaa: Number(row.direktverladung_kiaa) || 0,
                    umschlag: Number(row.umschlag) || 0,
                    kg_pro_colli: Number(row.kg_pro_colli) || 0,
                    elefanten: Number(row.elefanten) || 0
                });
            }
            // Picking Data
            if ((row.atrl !== null && row.atrl !== undefined) ||
                (row.aril !== null && row.aril !== undefined)) {
                picking.push({
                    datum: row.datum,
                    atrl: Number(row.atrl) || 0,
                    aril: Number(row.aril) || 0,
                    fuellgrad_aril: Number(row.fuellgrad_aril) || 0
                });
            }
        });
        return {
            serviceLevel,
            deliveryPositions,
            tagesleistung,
            picking
        };
    }
}
exports.DashboardRepository = DashboardRepository;
exports.dashboardRepository = new DashboardRepository();
