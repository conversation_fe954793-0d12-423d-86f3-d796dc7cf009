import { useState, useEffect, useCallback } from 'react';
import { CardPosition } from '../components/DragDrop/DragDropProvider';

// Schlüssel für localStorage
const STORAGE_KEY = 'dashboard-kpi-card-positions';

// Hilfsfunktion zur Überprüfung der localStorage-Verfügbarkeit
const isLocalStorageAvailable = (): boolean => {
  try {
    const testKey = '__test__';
    localStorage.setItem(testKey, testKey);
    localStorage.removeItem(testKey);
    return true;
  } catch (e) {
    console.error('localStorage ist nicht verfügbar:', e);
    return false;
  }
};

// Interface für den Hook-Return-Wert
interface UsePersistentCardPositionsReturn {
  positions: CardPosition[];
  updatePositions: (newPositions: CardPosition[]) => void;
  resetPositions: () => void;
  isLoading: boolean;
}

// Standard-Kartenpositionen
const DEFAULT_POSITIONS: CardPosition[] = [
  { id: 'wareneingang', position: 0, type: 'kpi-card' },
  { id: 'tonnage', position: 1, type: 'kpi-card' },
  { id: 'performance', position: 2, type: 'kpi-card' },
  { id: 'efficiency', position: 3, type: 'kpi-card' },
  { id: 'quality', position: 4, type: 'kpi-card' },
  { id: 'delivery', position: 5, type: 'kpi-card' },
  { id: 'picking', position: 6, type: 'kpi-card' },
  { id: 'automatisierung', position: 7, type: 'kpi-card' },
  { id: 'produktion', position: 8, type: 'kpi-card' },
  { id: 'lager', position: 9, type: 'kpi-card' },
  { id: 'versand', position: 10, type: 'kpi-card' },
  { id: 'service', position: 11, type: 'kpi-card' },
];


/**
 * Hook für persistente Speicherung der KPI-Kartenpositionen
 * 
 * Dieser Hook verwaltet die Positionen der KPI-Karten und speichert sie
 * automatisch im localStorage. Bei jedem Laden der Anwendung werden die
 * gespeicherten Positionen wiederhergestellt.
 * 
 * Features:
 * - Automatische Speicherung im localStorage
 * - Wiederherstellung beim App-Start
 * - Fallback auf Standardpositionen
 * - Reset-Funktionalität
 * - Loading-State für bessere UX
 * - Fehlerbehandlung bei localStorage-Problemen
 * 
 * @returns Objekt mit aktuellen Positionen und Verwaltungsfunktionen
 */
export const usePersistentCardPositions = (): UsePersistentCardPositionsReturn => {
  const [positions, setPositions] = useState<CardPosition[]>(DEFAULT_POSITIONS);
  const [isLoading, setIsLoading] = useState(true);

  // Positionen aus localStorage laden
  useEffect(() => {
    const loadPositions = () => {
      // Prüfe ob localStorage verfügbar ist
      if (!isLocalStorageAvailable()) {
        console.warn('localStorage nicht verfügbar. Verwende Standardwerte.');
        setPositions(DEFAULT_POSITIONS);
        setIsLoading(false);
        return;
      }
      
      try {
        const savedPositions = localStorage.getItem(STORAGE_KEY);
        
        if (savedPositions) {
          const parsedPositions: CardPosition[] = JSON.parse(savedPositions);
          
          // Validierung der geladenen Daten
          if (Array.isArray(parsedPositions) && parsedPositions.length > 0) {
            // Prüfen ob alle erforderlichen Felder vorhanden sind
            const isValid = parsedPositions.every(pos =>
              typeof pos.id === 'string' &&
              typeof pos.position === 'number' &&
              typeof pos.type === 'string'
            );
            
            if (isValid) {
              // Positionen sortieren um sicherzustellen, dass sie korrekt sind
              const sortedPositions = parsedPositions.sort((a, b) => a.position - b.position);
              setPositions(sortedPositions);
            } else {
              console.warn('Ungültige Kartenpositionen im localStorage gefunden. Verwende Standardpositionen.');
              setPositions(DEFAULT_POSITIONS);
            }
          } else {
            console.warn('Keine gültigen Kartenpositionen im localStorage gefunden. Verwende Standardpositionen.');
            setPositions(DEFAULT_POSITIONS);
          }
        } else {
          // Keine gespeicherten Positionen gefunden - verwende Standardpositionen
          setPositions(DEFAULT_POSITIONS);
        }
      } catch (error) {
        console.error('Fehler beim Laden der Kartenpositionen aus localStorage:', error);
        setPositions(DEFAULT_POSITIONS);
      } finally {
        setIsLoading(false);
      }
    };

    loadPositions();
  }, []);

  // Positionen im localStorage speichern
  const savePositions = useCallback((newPositions: CardPosition[]) => {
    if (!isLocalStorageAvailable()) {
      console.warn('localStorage nicht verfügbar. Positionen werden nicht gespeichert.');
      return;
    }
    
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newPositions));
    } catch (error) {
      console.error('Fehler beim Speichern der Kartenpositionen im localStorage:', error);
      // Hier könnte eine Benutzerbenachrichtigung angezeigt werden
    }
  }, []);

  // Positionen aktualisieren
  const updatePositions = useCallback((newPositions: CardPosition[]) => {
    // Validierung der neuen Positionen
    if (!Array.isArray(newPositions) || newPositions.length === 0) {
      console.warn('Ungültige Positionen übergeben. Aktualisierung wird ignoriert.');
      return;
    }

    // Positionen normalisieren (sicherstellen, dass sie von 0 bis n-1 gehen)
    const normalizedPositions = newPositions
      .sort((a, b) => a.position - b.position)
      .map((pos, index) => ({ ...pos, position: index }));

    setPositions(normalizedPositions);
    savePositions(normalizedPositions);
  }, [savePositions]);

  // Positionen auf Standard zurücksetzen
  const resetPositions = useCallback(() => {
    setPositions(DEFAULT_POSITIONS);
    savePositions(DEFAULT_POSITIONS);
  }, [savePositions]);

  return {
    positions,
    updatePositions,
    resetPositions,
    isLoading,
  };
};

export default usePersistentCardPositions;