/**
 * Error Monitoring API Routes
 * 
 * Stellt Error-Statistiken und -Überwachung über REST-API zur Verfügung.
 */

import express from 'express';
import { ErrorHandlerService } from '../services/error-handler.service';
import { rateLimitConfig } from '../middleware/rate-limiting.middleware';
import { ApiResponse } from '../types/database.types';

const router = express.Router();

/**
 * Hilfsfunktion für Error-Handling
 */
async function handleRequest<T>(
  req: express.Request,
  res: express.Response,
  operation: () => Promise<T> | T
): Promise<void> {
  try {
    const result = await operation();
    const response: ApiResponse<T> = {
      success: true,
      data: result
    };
    res.json(response);
  } catch (error) {
    console.error(`[ERROR-MONITORING-API] Fehler für ${req.originalUrl}:`, error);
    const response: ApiResponse<T> = {
      success: false,
      error: error instanceof Error ? error.message : 'Unbekann<PERSON> Fehler'
    };
    res.status(500).json(response);
  }
}

// Rate-Limiting für Error-Monitoring-Endpunkte
router.use(rateLimitConfig.metricsEndpoint);

/**
 * Error-Statistiken abrufen
 */
router.get('/stats', async (req, res) => {
  await handleRequest(req, res, async () => {
    // Versuche zuerst Daten aus der Datenbank zu holen
    try {
      const { db } = await import('../db');
      const { sql } = await import('drizzle-orm');

      // Direkte SQL-Abfrage für die system_errors Tabelle
      const result = await db.execute(sql`
        SELECT
          COUNT(*) as total,
          COUNT(*) FILTER (WHERE severity = 'critical') as critical,
          COUNT(*) FILTER (WHERE severity = 'high') as high,
          COUNT(*) FILTER (WHERE severity = 'medium') as medium,
          COUNT(*) FILTER (WHERE severity = 'low') as low,
          COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as last24h
        FROM system_errors
      `);

      if (result.rows && result.rows.length > 0) {
        const stats = result.rows[0];
        return {
          totalErrors: Number(stats.total),
          errorsBySeverity: {
            critical: Number(stats.critical),
            high: Number(stats.high),
            medium: Number(stats.medium),
            low: Number(stats.low)
          },
          recentErrors: [],
          errorRate: Number(stats.last24h) / 24, // Errors per hour
          lastErrorTime: Date.now()
        };
      }
    } catch (dbError) {
      console.warn('Database query failed, falling back to in-memory stats:', dbError);
    }

    // Fallback auf In-Memory ErrorHandler
    const errorHandler = ErrorHandlerService.getInstance();
    return errorHandler.getErrorStats();
  });
});

/**
 * Error-History abrufen
 */
router.get('/history', async (req, res) => {
  const { limit, severity, type } = req.query as {
    limit?: string;
    severity?: string;
    type?: string;
  };

  await handleRequest(req, res, async () => {
    // Versuche zuerst Daten aus der Datenbank zu holen
    try {
      const { db } = await import('../db');
      const { sql } = await import('drizzle-orm');

      let query = sql`
        SELECT * FROM system_errors
        ORDER BY created_at DESC
      `;

      // Füge Filter hinzu
      const conditions = [];
      if (severity) {
        conditions.push(sql`severity = ${severity}`);
      }
      if (type) {
        conditions.push(sql`type = ${type}`);
      }

      if (conditions.length > 0) {
        query = sql`
          SELECT * FROM system_errors
          WHERE ${sql.join(conditions, sql` AND `)}
          ORDER BY created_at DESC
        `;
      }

      // Füge LIMIT hinzu
      if (limit) {
        query = sql`${query} LIMIT ${parseInt(limit, 10)}`;
      }

      const result = await db.execute(query);

      if (result.rows && result.rows.length > 0) {
        const errors = result.rows.map((row: any) => ({
          id: row.id,
          type: row.type,
          severity: row.severity,
          message: row.message,
          userMessage: row.user_message,
          code: row.code,
          context: {
            timestamp: new Date(row.created_at as string).getTime()
          },
          details: row.details,
          suggestions: row.suggestions
        }));

        return {
          errors,
          total: errors.length,
          filters: { limit, severity, type }
        };
      }
    } catch (dbError) {
      console.warn('Database query failed, falling back to in-memory history:', dbError);
    }

    // Fallback auf In-Memory ErrorHandler
    const errorHandler = ErrorHandlerService.getInstance();
    let history = errorHandler.getErrorHistory(limit ? parseInt(limit, 10) : undefined);

    // Filter nach Severity
    if (severity) {
      history = history.filter(error => error.severity === severity);
    }

    // Filter nach Type
    if (type) {
      history = history.filter(error => error.type === type);
    }

    return {
      errors: history,
      total: history.length,
      filters: { limit, severity, type }
    };
  });
});

/**
 * Recent Critical Errors abrufen
 */
router.get('/critical', async (req, res) => {
  const { hours } = req.query as { hours?: string };
  const hoursBack = hours ? parseInt(hours, 10) : 24;
  
  await handleRequest(req, res, () => {
    const errorHandler = ErrorHandlerService.getInstance();
    const allErrors = errorHandler.getErrorHistory();
    
    const cutoffTime = Date.now() - (hoursBack * 60 * 60 * 1000);
    const criticalErrors = allErrors.filter(error => 
      error.context!.timestamp >= cutoffTime && 
      (error.severity === 'critical' || error.severity === 'high')
    );
    
    return {
      criticalErrors,
      count: criticalErrors.length,
      timeRange: {
        hours: hoursBack,
        from: new Date(cutoffTime).toISOString(),
        to: new Date().toISOString()
      }
    };
  });
});

/**
 * Error-Trends (stündliche Gruppierung)
 */
router.get('/trends', async (req, res) => {
  const { hours } = req.query as { hours?: string };
  const hoursBack = hours ? parseInt(hours, 10) : 24;
  
  await handleRequest(req, res, () => {
    const errorHandler = ErrorHandlerService.getInstance();
    const allErrors = errorHandler.getErrorHistory();
    
    const cutoffTime = Date.now() - (hoursBack * 60 * 60 * 1000);
    const recentErrors = allErrors.filter(error => 
      error.context!.timestamp >= cutoffTime
    );
    
    // Gruppiere Errors nach Stunden
    const hourlyData: Record<string, any> = {};
    
    recentErrors.forEach(error => {
      const hour = new Date(error.context!.timestamp).toISOString().slice(0, 13); // YYYY-MM-DDTHH
      
      if (!hourlyData[hour]) {
        hourlyData[hour] = {
          total: 0,
          byType: {},
          bySeverity: {},
          hour
        };
      }
      
      hourlyData[hour].total++;
      hourlyData[hour].byType[error.type] = (hourlyData[hour].byType[error.type] || 0) + 1;
      hourlyData[hour].bySeverity[error.severity] = (hourlyData[hour].bySeverity[error.severity] || 0) + 1;
    });
    
    // Sortiere chronologisch
    const trends = Object.values(hourlyData).sort((a: any, b: any) => 
      a.hour.localeCompare(b.hour)
    );
    
    return {
      trends,
      summary: {
        totalErrors: recentErrors.length,
        timeRange: hoursBack,
        mostCommonType: getMostCommon(recentErrors.map(e => e.type)),
        mostCommonSeverity: getMostCommon(recentErrors.map(e => e.severity))
      }
    };
  });
});

/**
 * Error-Details für spezifische Error-ID
 */
router.get('/details/:errorId', async (req, res) => {
  const { errorId } = req.params;
  
  await handleRequest(req, res, () => {
    const errorHandler = ErrorHandlerService.getInstance();
    const allErrors = errorHandler.getErrorHistory();
    
    const error = allErrors.find(e => e.id === errorId);
    
    if (!error) {
      throw new Error(`Error mit ID ${errorId} nicht gefunden`);
    }
    
    return {
      error,
      relatedErrors: allErrors
        .filter(e => 
          e.type === error.type && 
          e.id !== error.id &&
          Math.abs(e.context!.timestamp - error.context!.timestamp) < (60 * 60 * 1000) // Innerhalb 1 Stunde
        )
        .slice(0, 5) // Maximal 5 verwandte Errors
    };
  });
});

/**
 * Top Error Types (häufigste Fehlertypen)
 */
router.get('/top-errors', async (req, res) => {
  const { limit, hours } = req.query as { limit?: string; hours?: string };
  const maxResults = limit ? parseInt(limit, 10) : 10;
  const hoursBack = hours ? parseInt(hours, 10) : 24;
  
  await handleRequest(req, res, () => {
    const errorHandler = ErrorHandlerService.getInstance();
    const allErrors = errorHandler.getErrorHistory();
    
    const cutoffTime = Date.now() - (hoursBack * 60 * 60 * 1000);
    const recentErrors = allErrors.filter(error => 
      error.context!.timestamp >= cutoffTime
    );
    
    // Zähle Error-Types
    const errorTypeCounts: Record<string, {
      count: number;
      type: string;
      lastOccurrence: number;
      severity: string;
      sampleMessage: string;
    }> = {};
    
    recentErrors.forEach(error => {
      if (!errorTypeCounts[error.type]) {
        errorTypeCounts[error.type] = {
          count: 0,
          type: error.type,
          lastOccurrence: 0,
          severity: error.severity,
          sampleMessage: error.message
        };
      }
      
      errorTypeCounts[error.type].count++;
      if (error.context!.timestamp > errorTypeCounts[error.type].lastOccurrence) {
        errorTypeCounts[error.type].lastOccurrence = error.context!.timestamp;
        errorTypeCounts[error.type].sampleMessage = error.message;
      }
    });
    
    // Sortiere nach Häufigkeit
    const topErrors = Object.values(errorTypeCounts)
      .sort((a, b) => b.count - a.count)
      .slice(0, maxResults)
      .map(error => ({
        ...error,
        lastOccurrence: new Date(error.lastOccurrence).toISOString(),
        percentage: recentErrors.length > 0 ? (error.count / recentErrors.length) * 100 : 0
      }));
    
    return {
      topErrors,
      summary: {
        totalUniqueErrorTypes: Object.keys(errorTypeCounts).length,
        totalErrors: recentErrors.length,
        timeRange: hoursBack
      }
    };
  });
});

/**
 * Error-Rate-Analyse
 */
router.get('/rate-analysis', async (req, res) => {
  await handleRequest(req, res, () => {
    const errorHandler = ErrorHandlerService.getInstance();
    const stats = errorHandler.getErrorStats();
    const allErrors = errorHandler.getErrorHistory();
    
    // Berechne verschiedene Zeitperioden
    const now = Date.now();
    const periods = {
      last5Minutes: now - (5 * 60 * 1000),
      lastHour: now - (60 * 60 * 1000),
      last24Hours: now - (24 * 60 * 60 * 1000)
    };
    
    const rateAnalysis = {
      current: stats.errorRate, // Errors per minute (letzte 5 Minuten)
      last5Minutes: allErrors.filter(e => e.context!.timestamp >= periods.last5Minutes).length / 5,
      lastHour: allErrors.filter(e => e.context!.timestamp >= periods.lastHour).length / 60,
      last24Hours: allErrors.filter(e => e.context!.timestamp >= periods.last24Hours).length / (24 * 60),
      trend: calculateTrend(allErrors),
      status: getErrorRateStatus(stats.errorRate)
    };
    
    return {
      rateAnalysis,
      recommendations: generateErrorRateRecommendations(rateAnalysis),
      timestamp: new Date().toISOString()
    };
  });
});

/**
 * Error-Diagnose für häufige Probleme
 */
router.get('/diagnostics', async (req, res) => {
  await handleRequest(req, res, () => {
    const errorHandler = ErrorHandlerService.getInstance();
    const allErrors = errorHandler.getErrorHistory();
    const stats = errorHandler.getErrorStats();
    
    const lastHour = Date.now() - (60 * 60 * 1000);
    const recentErrors = allErrors.filter(e => e.context!.timestamp >= lastHour);
    
    // Diagnose-Checks
    const diagnostics = {
      databaseConnectivity: analyzeDatabaseErrors(recentErrors),
      cachePerformance: analyzeCacheErrors(recentErrors),
      validationIssues: analyzeValidationErrors(recentErrors),
      timeoutProblems: analyzeTimeoutErrors(recentErrors),
      overallHealth: {
        status: stats.errorRate < 1 ? 'healthy' : stats.errorRate < 5 ? 'warning' : 'critical',
        errorRate: stats.errorRate,
        totalErrors: stats.totalErrors,
        criticalErrors: recentErrors.filter(e => e.severity === 'critical').length
      }
    };
    
    return {
      diagnostics,
      recommendations: generateDiagnosticRecommendations(diagnostics),
      timestamp: new Date().toISOString()
    };
  });
});

/**
 * Helper Functions
 */

function getMostCommon<T>(items: T[]): T | null {
  if (items.length === 0) return null;
  
  const counts: Record<string, number> = {};
  items.forEach(item => {
    const key = String(item);
    counts[key] = (counts[key] || 0) + 1;
  });
  
  const mostCommon = Object.entries(counts)
    .sort(([,a], [,b]) => b - a)[0];
  
  return mostCommon ? mostCommon[0] as T : null;
}

function calculateTrend(errors: any[]): 'increasing' | 'decreasing' | 'stable' {
  if (errors.length < 10) return 'stable';
  
  const now = Date.now();
  const halfHourAgo = now - (30 * 60 * 1000);
  const oneHourAgo = now - (60 * 60 * 1000);
  
  const recent = errors.filter(e => e.context.timestamp >= halfHourAgo).length;
  const previous = errors.filter(e => 
    e.context.timestamp >= oneHourAgo && e.context.timestamp < halfHourAgo
  ).length;
  
  if (recent > previous * 1.2) return 'increasing';
  if (recent < previous * 0.8) return 'decreasing';
  return 'stable';
}

function getErrorRateStatus(rate: number): 'good' | 'warning' | 'critical' {
  if (rate < 1) return 'good';
  if (rate < 5) return 'warning';
  return 'critical';
}

function generateErrorRateRecommendations(analysis: any): string[] {
  const recommendations: string[] = [];
  
  if (analysis.status === 'critical') {
    recommendations.push('Kritische Error-Rate erkannt. Sofortige Untersuchung erforderlich.');
    recommendations.push('Überprüfen Sie System-Resources und Database-Performance.');
  }
  
  if (analysis.trend === 'increasing') {
    recommendations.push('Error-Rate steigt an. Monitoring intensivieren.');
  }
  
  if (analysis.last5Minutes > 10) {
    recommendations.push('Hohe Error-Anzahl in den letzten 5 Minuten. System überprüfen.');
  }
  
  return recommendations;
}

function analyzeDatabaseErrors(errors: any[]): any {
  const dbErrors = errors.filter(e => e.type === 'DATABASE_ERROR');
  return {
    count: dbErrors.length,
    percentage: errors.length > 0 ? (dbErrors.length / errors.length) * 100 : 0,
    status: dbErrors.length === 0 ? 'good' : dbErrors.length < 5 ? 'warning' : 'critical'
  };
}

function analyzeCacheErrors(errors: any[]): any {
  const cacheErrors = errors.filter(e => e.type === 'CACHE_ERROR');
  return {
    count: cacheErrors.length,
    percentage: errors.length > 0 ? (cacheErrors.length / errors.length) * 100 : 0,
    status: cacheErrors.length === 0 ? 'good' : cacheErrors.length < 3 ? 'warning' : 'critical'
  };
}

function analyzeValidationErrors(errors: any[]): any {
  const validationErrors = errors.filter(e => e.type === 'VALIDATION_ERROR');
  return {
    count: validationErrors.length,
    percentage: errors.length > 0 ? (validationErrors.length / errors.length) * 100 : 0,
    status: validationErrors.length < 10 ? 'good' : validationErrors.length < 20 ? 'warning' : 'critical'
  };
}

function analyzeTimeoutErrors(errors: any[]): any {
  const timeoutErrors = errors.filter(e => e.type === 'TIMEOUT_ERROR');
  return {
    count: timeoutErrors.length,
    percentage: errors.length > 0 ? (timeoutErrors.length / errors.length) * 100 : 0,
    status: timeoutErrors.length === 0 ? 'good' : timeoutErrors.length < 3 ? 'warning' : 'critical'
  };
}

function generateDiagnosticRecommendations(diagnostics: any): string[] {
  const recommendations: string[] = [];
  
  if (diagnostics.databaseConnectivity.status === 'critical') {
    recommendations.push('Database-Verbindungsprobleme erkannt. Connection-Pool überprüfen.');
  }
  
  if (diagnostics.cachePerformance.status === 'critical') {
    recommendations.push('Cache-Performance-Probleme. Memory-Limits und TTL-Konfiguration prüfen.');
  }
  
  if (diagnostics.validationIssues.status === 'critical') {
    recommendations.push('Viele Validierungsfehler. Input-Validation und API-Dokumentation überprüfen.');
  }
  
  if (diagnostics.timeoutProblems.status === 'critical') {
    recommendations.push('Timeout-Probleme erkannt. Query-Performance und Network-Latenz prüfen.');
  }
  
  return recommendations;
}

export default router;