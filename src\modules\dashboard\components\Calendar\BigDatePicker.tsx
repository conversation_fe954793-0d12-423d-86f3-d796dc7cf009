"use client"

import React, { useState, useRef } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface BigDatePickerProps {
  initialDate?: Date
  onDateChange?: (date: Date) => void
  className?: string
}

const BigDatePicker: React.FC<BigDatePickerProps> = ({
  initialDate = new Date(2025, 8, 3), // September 3, 2025
  onDateChange,
  className
}) => {
  // Separierte States für sofortige Kalendermarkierung und animierte Anzeige
  const [selectedDate, setSelectedDate] = useState<Date>(initialDate) // Steuert Kalendermarkierung
  const [displayedDate, setDisplayedDate] = useState<Date>(initialDate) // Steuert Flip-Animation
  const [currentMonth, setCurrentMonth] = useState<Date>(initialDate) // Steuert Kalendernavigation
  const [isCalendarVisible, setIsCalendarVisible] = useState<boolean>(false)
  const [isAnimating, setIsAnimating] = useState<boolean>(false)
  
  const currentPageRef = useRef<HTMLDivElement>(null)
  const nextPageRef = useRef<HTMLDivElement>(null)

  // Deutsche Lokalisierung entsprechend der Memory-Anforderung
  const monthNamesLong = [
    "JANUAR", "FEBRUAR", "MÄRZ", "APRIL", "MAI", "JUNI",
    "JULI", "AUGUST", "SEPTEMBER", "OKTOBER", "NOVEMBER", "DEZEMBER"
  ]
  
  const monthNamesShort = [
    "Jan", "Feb", "Mär", "Apr", "Mai", "Jun",
    "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"
  ]

  const dayNames = ["So", "Mo", "Di", "Mi", "Do", "Fr", "Sa"]

  // Hilfsfunktion für Datum-Suffix
  const getDateSuffix = (day: number): string => {
    if (day > 3 && day < 21) return 'th'
    switch (day % 10) {
      case 1: return "st"
      case 2: return "nd"
      case 3: return "rd"
      default: return "th"
    }
  }

  // Flip-Animation Funktion mit Callback für entkoppelte State-Updates
  const animateFlip = (newDate: Date, onComplete?: () => void) => {
    if (isAnimating) return
    
    const day = newDate.getDate()
    const newDateString = `${day}`
    
    // Prüfen ob sich das Datum tatsächlich geändert hat
    if (currentPageRef.current?.textContent?.trim() === newDateString) {
      onComplete?.()
      return
    }
    
    setIsAnimating(true)
    
    // Nächste Seite vorbereiten
    if (nextPageRef.current) {
      nextPageRef.current.textContent = newDateString
      nextPageRef.current.className = 'date-page next'
    }
    
    // Aktuelle Seite animieren
    if (currentPageRef.current) {
      currentPageRef.current.className = 'date-page flipping-out'
    }
    
    // Nach Animation: Seiten tauschen und Callback ausführen
    setTimeout(() => {
      if (currentPageRef.current && nextPageRef.current) {
        // Seiten tauschen
        const temp = currentPageRef.current
        currentPageRef.current = nextPageRef.current
        nextPageRef.current = temp
        
        // Klassen zurücksetzen
        currentPageRef.current.className = 'date-page current'
        nextPageRef.current.className = 'date-page next'
      }
      
      // Callback für State-Update ausführen
      onComplete?.()
      onDateChange?.(newDate)
      setIsAnimating(false)
    }, 800) // Dauer der Animation
  }
  
  // Datum aktualisieren - sofortige Kalendermarkierung, verzögerte Anzeige
  const updateDate = (newDate: Date) => {
    // Sofortige Aktualisierung der Kalendermarkierung
    setSelectedDate(newDate)
    
    // Verzögerte Aktualisierung der Anzeige mit Animation
    animateFlip(newDate, () => {
      setDisplayedDate(newDate)
    })
  }

  // Kalender rendern - nutzt selectedDate für sofortige Markierung
  const renderCalendar = () => {
    const year = currentMonth.getFullYear()
    const month = currentMonth.getMonth()
    const firstDayOfMonth = new Date(year, month, 1).getDay()
    const daysInMonth = new Date(year, month + 1, 0).getDate()
    
    const calendarDays: React.ReactElement[] = []
    
    // Leere Felder für den Monatsanfang
    for (let i = 0; i < firstDayOfMonth; i++) {
      calendarDays.push(
        <div key={`empty-${i}`} className="h-9 w-9"></div>
      )
    }
    
    // Tage des Monats - Markierung basiert auf selectedDate für sofortige Rückmeldung
    for (let i = 1; i <= daysInMonth; i++) {
      const isSelected = i === selectedDate.getDate() && 
                        year === selectedDate.getFullYear() && 
                        month === selectedDate.getMonth()
      calendarDays.push(
        <button
          key={i}
          onClick={() => {
            const newDate = new Date(year, month, i)
            updateDate(newDate)
          }}
          className={cn(
            "h-9 w-9 rounded-full text-sm font-medium transition-colors hover:bg-slate-100",
            isSelected 
              ? "bg-[#ff7a05] text-white font-semibold hover:bg-[#ff7a05]/30" 
              : "text-slate-700 hover:text-slate-900"
          )}
        >
          {i}
        </button>
      )
    }
    
    return calendarDays
  }

  // Navigation - nutzt separaten currentMonth State
  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth)
    if (direction === 'prev') {
      newMonth.setMonth(newMonth.getMonth() - 1)
    } else {
      newMonth.setMonth(newMonth.getMonth() + 1)
    }
    setCurrentMonth(newMonth)
  }

  // Toggle Kalender
  const toggleCalendar = () => {
    setIsCalendarVisible(!isCalendarVisible)
  }

  return (
    <div className={cn("flex gap-2 flex-wrap justify-center flex-row-reverse", className)}>
      {/* Custom CSS für die Flip-Animation */}
      <style>{`
        .flipper-container {
          perspective: 1000px;
          overflow: hidden;
        }
        
        .date-page {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f8fafc;
          transform-origin: center bottom;
          font-size: 5rem;
          font-weight: 600;
          color: #343D4F;
        }
        
        .date-page.current {
          z-index: 2;
          transform: rotateX(0deg);
        }
        
        .date-page.next {
          z-index: 1;
          transform: rotateX(0deg);
        }
        
        .date-page.flipping-out {
          z-index: 3;
          animation: flipOut 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
        
        @keyframes flipOut {
          0% {
            transform: rotateX(0deg);
          }
          100% {
            transform: rotateX(90deg);
          }
        }
      `}</style>

      {/* Rechte Anzeige für das ausgewählte Datum */}
      <div className="w-35 h-35 bg-white rounded-xl shadow-lg border-2 border-[#ff7a05] flex flex-col overflow-hidden">
        
        {/* Header */}
        <div className="w-full h-7 bg-[#ff7a05] text-white flex items-center py-3 font-semibold text-lg tracking-wider relative">
          <div className="absolute left-1 top-1/2 transform -translate-y-1/2">
            <button
              onClick={toggleCalendar}
              className="text-white hover:text-slate-600 rounded p-0.5 w-6 h-6 flex items-center justify-start transition-colors"
              aria-label={isCalendarVisible ? "Kalender schließen" : "Kalender öffnen"}
            >
              {isCalendarVisible ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
            </button>
          </div>
          <div className="w-full text-center h-8">
            <span className="text-sm">{monthNamesLong[displayedDate.getMonth()]}</span>
          </div>
        </div>
        
        {/* Hauptanzeige - Mit Flip-Animation */}
         <div className="flex-grow flex items-center justify-center relative pt-1 flipper-container">
           <div ref={currentPageRef} className="date-page current">
             {displayedDate.getDate()}
           </div>
           <div ref={nextPageRef} className="date-page">
             {displayedDate.getDate()}
           </div>
         </div>
        
        {/* Footer */}
        <div className="w-full h-9 p-2 text-center text-lg font-medium text-slate-400 bg-slate-50">
          <span>{displayedDate.getFullYear()}</span>
        </div>
      </div>

      {/* Linker Kalender zur Auswahl */}
      <div className={cn(
        "w-80 bg-slate-50 rounded-xl border-2 border-[#ff7a05] p-5 shadow-lg transition-all duration-300",
        isCalendarVisible 
          ? "opacity-100 scale-100 pointer-events-auto" 
          : "opacity-0 scale-95 pointer-events-none"
      )}>
        {/* Kalender Header */}
        <div className="flex justify-between items-center mb-5 font-semibold">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('prev')}
            className="text-slate-700 hover:bg-slate-100 p-1 h-auto"
            aria-label="Vorheriger Monat"
          >
            <ChevronLeft size={20} />
          </Button>
          <span className="text-base">
            {monthNamesShort[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('next')}
            className="text-slate-700 hover:bg-slate-100 p-1 h-auto"
            aria-label="Nächster Monat"
          >
            <ChevronRight size={20} />
          </Button>
        </div>
        
        {/* Wochentage */}
        <div className="grid grid-cols-7 gap-1 mb-3">
          {dayNames.map((day) => (
            <div key={day} className="text-center font-semibold text-slate-400 text-xs pb-2">
              {day}
            </div>
          ))}
        </div>
        
        {/* Kalendertage */}
        <div className="grid grid-cols-7 gap-1">
          {renderCalendar()}
        </div>
      </div>
    </div>
  )
}

export default BigDatePicker