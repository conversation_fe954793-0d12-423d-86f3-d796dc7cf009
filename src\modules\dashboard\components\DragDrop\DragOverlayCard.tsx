"use client";

import React from 'react';
import { UniqueIdentifier } from '@dnd-kit/core';
import { useDragDropContext } from './DragDropProvider';
import { cn } from '@/lib/utils';

/**
 * DragOverlayCard - Komponente für die visuelle Vorschau während des Drag-Vorgangs
 * 
 * Diese Komponente zeigt eine realistische Vorschau der gezogenen KPI-Karte an,
 * anstatt nur einen generischen Platzhalter zu verwenden.
 * 
 * Features:
 * - Zeigt eine vereinfachte Version der echten Karte
 * - Visuelle Effekte für bessere UX (Rotation, Skalierung, Transparenz)
 * - Responsive Design für verschiedene Kartengrößen
 * - Accessibility-freundlich mit Screen Reader Support
 */

interface DragOverlayCardProps {
  activeId: UniqueIdentifier;
}

export const DragOverlayCard: React.FC<DragOverlayCardProps> = ({ activeId }) => {
  const { availableCards } = useDragDropContext();
  
  // Finde die entsprechende Karte basierend auf der activeId
  const activeCard = availableCards.find(card => card.id === activeId);
  const cardSize = 'medium'; // Standardgröße, da die Kartengrößen-Funktion entfernt wurde
  
  // Fallback falls die Karte nicht gefunden wird
  if (!activeCard) {
    return (
      <div className="opacity-60 transform rotate-2 scale-105 transition-transform">
        <div className="bg-white border-2 border-dashed border-gray-400 rounded-lg p-4 shadow-lg min-w-[200px] min-h-[120px]">
          <div className="text-sm text-gray-600">Karte wird verschoben...</div>
        </div>
      </div>
    );
  }
  
  // Bestimme die Kartengröße basierend auf dem gespeicherten Wert
  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'small':
      case 't1':
        return 'w-48 h-32';
      case 'medium':
      case 't2':
        return 'w-64 h-40';
      case 'large':
      case 't3':
        return 'w-80 h-48';
      default:
        return 'w-64 h-40';
    }
  };
  
  // Bestimme das Icon basierend auf der Karten-ID
  const getCardIcon = (cardId: string) => {
    switch (cardId) {
      case 'wareneingang':
        return '📦';
      case 'tonnage':
        return '⚖️';
      case 'performance':
        return '📊';
      case 'efficiency':
        return '⚡';
      case 'quality':
        return '✅';
      case 'delivery':
        return '🚚';
      case 'picking':
        return '📦';
      case 'automatisierung':
        return '🤖';
      case 'produktion':
        return '🔧';
      case 'lager':
        return '🏭';
      case 'versand':
        return '🚛';
      case 'service':
        return '🔧';
      default:
        return '📈';
    }
  };
  
  return (
    <div 
      className="opacity-70 transform rotate-3 scale-105 transition-all duration-200 pointer-events-none"
      style={{
        filter: 'drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2))'
      }}
    >
      <div className={cn(
        "bg-white border-2 border-blue-400 rounded-lg shadow-xl",
        "flex flex-col justify-between p-4",
        getSizeClasses(cardSize)
      )}>
        {/* Header mit Icon und Titel */}
        <div className="flex items-center gap-2 mb-2">
          <span className="text-lg" role="img" aria-hidden="true">
            {getCardIcon(activeCard.id)}
          </span>
          <span className="text-sm font-medium text-gray-700 truncate">
            {activeCard.title}
          </span>
        </div>
        
        {/* Vereinfachter Inhalt */}
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-800 mb-1">
              ---
            </div>
            <div className="text-xs text-gray-500">
              {activeCard.description || 'KPI-Daten'}
            </div>
          </div>
        </div>
        
        {/* Footer mit Drag-Indikator */}
        <div className="flex items-center justify-center mt-2">
          <div className="flex gap-1">
            <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse"></div>
            <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
          </div>
        </div>
        
        {/* Screen Reader Information */}
        <div className="sr-only">
          {activeCard.title} wird verschoben
        </div>
      </div>
    </div>
  );
};

export default DragOverlayCard;