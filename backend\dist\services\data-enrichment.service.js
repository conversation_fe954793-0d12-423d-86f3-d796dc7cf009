"use strict";
/**
 * Data Enrichment Service
 *
 * Analyzes user queries and enriches them with relevant database context
 * for AI chatbot responses.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataEnrichmentService = void 0;
const db_1 = require("../db");
const stoerungen_repository_1 = require("../repositories/stoerungen.repository");
const dispatch_repository_1 = require("../repositories/dispatch.repository");
const cutting_repository_1 = require("../repositories/cutting.repository");
const repository_query_coordinator_service_1 = require("./repository-query-coordinator.service");
const data_formatting_service_1 = require("./data-formatting.service");
const performance_monitoring_service_1 = __importDefault(require("./performance-monitoring.service"));
const data_cache_service_1 = __importDefault(require("./data-cache.service"));
/**
 * Intent recognition patterns for different data domains
 */
const INTENT_PATTERNS = {
    stoerungen: [
        'störungen', 'störung', 'incidents', 'incident', 'problems', 'problem',
        'issues', 'issue', 'fehler', 'system status', 'systemstatus',
        'mttr', 'resolution', 'auflösung', 'severity', 'schweregrad',
        'critical', 'kritisch', 'resolved', 'gelöst', 'behoben'
    ],
    dispatch: [
        'versand', 'dispatch', 'service level', 'servicelevel', 'servicegrad',
        'tonnage', 'tonnagen', 'delivery', 'lieferung', 'picking', 'kommissionierung',
        'atrl', 'aril', 'returns', 'retouren', 'qm', 'performance', 'leistung',
        'logistics', 'logistik', 'auslieferung', 'rückstände', 'rückstand'
    ],
    cutting: [
        'ablängerei', 'cutting', 'schnitt', 'schnitte', 'machine', 'maschine',
        'efficiency', 'effizienz', 'cuts', 'lager', 'warehouse',
        'production', 'produktion', 'maschinen', 'cutting performance'
    ],
    timeRange: [
        'heute', 'today', 'gestern', 'yesterday', 'woche', 'week',
        'monat', 'month', 'letzte', 'last', 'diese', 'this',
        'aktuell', 'current', 'jetzt', 'now', 'kürzlich', 'recent',
        'vom', 'von', 'am', 'bis', 'zwischen', 'from', 'to', 'on',
        'datum', 'date', 'tag', 'day', 'zeitraum', 'period',
        'januar', 'februar', 'märz', 'april', 'mai', 'juni',
        'juli', 'august', 'september', 'oktober', 'november', 'dezember',
        'january', 'february', 'march', 'may', 'june', 'july',
        'august', 'september', 'october', 'november', 'december'
    ],
    metrics: [
        'kpi', 'statistics', 'statistiken', 'metrics', 'metriken',
        'zahlen', 'numbers', 'daten', 'data', 'übersicht', 'overview',
        'zusammenfassung', 'summary', 'bericht', 'report'
    ]
};
/**
 * Default configuration for data enrichment
 */
const DEFAULT_CONFIG = {
    intentThreshold: 0.3,
    maxQueryTime: 5000, // 5 seconds
    cacheEnabled: true,
    cacheTTL: 300000 // 5 minutes
};
/**
 * Data Enrichment Service
 *
 * Core service responsible for analyzing user queries and enriching them with relevant
 * database context for AI chatbot responses. This service coordinates intent recognition,
 * data retrieval from multiple repositories, and context building for LLM consumption.
 *
 * @class DataEnrichmentService
 * @implements {IDataEnrichmentService}
 *
 * @example
 * ```typescript
 * const enrichmentService = new DataEnrichmentService(db, {
 *   intentThreshold: 0.4,
 *   maxQueryTime: 3000
 * });
 *
 * const enrichedContext = await enrichmentService.enrichChatContext(
 *   "Wie viele Störungen hatten wir diese Woche?"
 * );
 * ```
 */
class DataEnrichmentService {
    /**
     * Creates a new DataEnrichmentService instance
     *
     * @param database - Drizzle database instance for database access
     * @param config - Optional configuration overrides for the service
     *
     * @throws {Error} When database instance is not provided or invalid
     */
    constructor(database = db_1.db, config = {}) {
        this.stoerungenRepo = stoerungen_repository_1.StoerungenRepository.getInstance(database);
        this.dispatchRepo = new dispatch_repository_1.DispatchRepositoryImpl(database);
        this.cuttingRepo = new cutting_repository_1.CuttingRepositoryImpl(database);
        this.queryCoordinator = new repository_query_coordinator_service_1.RepositoryQueryCoordinator(database, {
            maxConcurrentQueries: 3,
            queryTimeout: config.maxQueryTime || DEFAULT_CONFIG.maxQueryTime,
            enableMetrics: true,
            enableCaching: config.cacheEnabled !== undefined ? config.cacheEnabled : DEFAULT_CONFIG.cacheEnabled
        });
        this.dataFormatter = new data_formatting_service_1.DataFormattingService();
        this.config = { ...DEFAULT_CONFIG, ...config };
    }
    /**
     * Enriches a chat message with relevant database context
     *
     * This is the main entry point for the data enrichment process. It analyzes the user's
     * message for database-related intents, queries relevant repositories, and builds a
     * comprehensive context for AI consumption.
     *
     * @param message - The user's chat message to analyze and enrich
     * @returns Promise resolving to enriched context with database information
     *
     * @throws {Error} When database queries fail or timeout
     * @throws {Error} When intent recognition encounters critical errors
     *
     * @example
     * ```typescript
     * const context = await service.enrichChatContext("Show me today's incidents");
     * console.log(context.hasData); // true if data was found
     * console.log(context.dataTypes); // ['stoerungen']
     * ```
     *
     * @performance Typical response time: 500-2000ms depending on query complexity
     * @caching Results are cached for 5 minutes by default to improve performance
     */
    async enrichChatContext(message) {
        var _a;
        const startTime = Date.now();
        const requestId = this.generateRequestId();
        try {
            console.log(`🔍 [DATA-ENRICHMENT] [${requestId}] Analyzing message: "${message.substring(0, 100)}..."`);
            // Parse user intent from the message with performance tracking
            const intentStartTime = Date.now();
            const detectedIntents = this.parseIntent(message);
            const intentTime = Date.now() - intentStartTime;
            // Record intent recognition performance
            performance_monitoring_service_1.default.recordIntentRecognition(message, detectedIntents.map(i => i.type), detectedIntents.length > 0 ? Math.max(...detectedIntents.map(i => i.confidence)) : 0, intentTime, detectedIntents.flatMap(i => i.keywords));
            if (detectedIntents.length === 0) {
                console.log(`📝 [DATA-ENRICHMENT] [${requestId}] No database-related intents detected`);
                const fallbackContext = this.createFallbackContext(message, [], 'NO_INTENTS_DETECTED');
                // Record enrichment performance
                performance_monitoring_service_1.default.recordEnrichmentPerformance(requestId, Date.now() - startTime, false, {
                    intentCount: 0,
                    queryCount: 0,
                    successfulQueries: 0,
                    fallbackUsed: true,
                    dataTypes: []
                });
                return fallbackContext;
            }
            console.log(`🎯 [DATA-ENRICHMENT] [${requestId}] Detected ${detectedIntents.length} intents:`, detectedIntents.map(i => `${i.type} (${i.confidence.toFixed(2)})`));
            // Check cache first for enriched context
            const cachedContext = await data_cache_service_1.default.getCachedEnrichedContext(message, detectedIntents);
            if (cachedContext && this.config.cacheEnabled) {
                console.log(`🎯 [DATA-ENRICHMENT] [${requestId}] Using cached enriched context`);
                // Record cache hit performance
                performance_monitoring_service_1.default.recordEnrichmentPerformance(requestId, Date.now() - startTime, true, {
                    intentCount: detectedIntents.length,
                    queryCount: 0,
                    successfulQueries: ((_a = cachedContext.dataTypes) === null || _a === void 0 ? void 0 : _a.length) || 0,
                    fallbackUsed: false,
                    dataTypes: cachedContext.dataTypes || []
                });
                return {
                    ...cachedContext,
                    requestId,
                    processingTime: Date.now() - startTime
                };
            }
            // Execute database queries with timeout and error handling
            const queryResults = await this.executeQueriesWithFallback(detectedIntents, requestId);
            // Check if we have any successful results
            const successfulResults = queryResults.filter(r => r.success);
            if (successfulResults.length === 0) {
                console.warn(`⚠️ [DATA-ENRICHMENT] [${requestId}] No successful queries, using fallback`);
                const fallbackContext = this.createFallbackContext(message, detectedIntents, 'ALL_QUERIES_FAILED', queryResults);
                // Record enrichment performance
                performance_monitoring_service_1.default.recordEnrichmentPerformance(requestId, Date.now() - startTime, false, {
                    intentCount: detectedIntents.length,
                    queryCount: queryResults.length,
                    successfulQueries: 0,
                    fallbackUsed: true,
                    dataTypes: []
                });
                return fallbackContext;
            }
            // Format data for LLM consumption using the new formatting service
            let databaseContext = '';
            try {
                databaseContext = this.dataFormatter.buildMultiSourceContext(queryResults, {
                    format: 'summary',
                    includeTimestamp: true,
                    includeFreshness: true,
                    includeTrends: true,
                    language: 'de'
                }, detectedIntents);
            }
            catch (formattingError) {
                console.error(`❌ [DATA-ENRICHMENT] [${requestId}] Data formatting failed:`, formattingError);
                databaseContext = this.createFallbackDataContext(successfulResults);
            }
            const enrichedContext = {
                originalMessage: message,
                detectedIntents,
                databaseContext,
                hasData: successfulResults.length > 0,
                dataTypes: successfulResults.map(r => r.dataType),
                timestamp: new Date(),
                requestId,
                processingTime: Date.now() - startTime,
                partialFailure: queryResults.some(r => !r.success)
            };
            // Cache the enriched context for future use
            if (this.config.cacheEnabled && enrichedContext.hasData) {
                await data_cache_service_1.default.cacheEnrichedContext(message, detectedIntents, enrichedContext);
            }
            // Record enrichment performance
            performance_monitoring_service_1.default.recordEnrichmentPerformance(requestId, Date.now() - startTime, true, {
                intentCount: detectedIntents.length,
                queryCount: queryResults.length,
                successfulQueries: successfulResults.length,
                fallbackUsed: false,
                dataTypes: enrichedContext.dataTypes
            });
            console.log(`✅ [DATA-ENRICHMENT] [${requestId}] Context enriched with ${enrichedContext.dataTypes.length} data types in ${enrichedContext.processingTime}ms`);
            return enrichedContext;
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            console.error(`❌ [DATA-ENRICHMENT] [${requestId}] Critical error enriching context (${processingTime}ms):`, error);
            // Log detailed error information for debugging
            this.logEnrichmentError(error, message, requestId, processingTime);
            // Record failed enrichment performance
            performance_monitoring_service_1.default.recordEnrichmentPerformance(requestId, processingTime, false, {
                intentCount: 0,
                queryCount: 0,
                successfulQueries: 0,
                fallbackUsed: true,
                dataTypes: []
            });
            // Return graceful fallback context
            return this.createFallbackContext(message, [], 'CRITICAL_ERROR', undefined, error);
        }
    }
    /**
     * Parses user intent from a message using keyword pattern matching
     *
     * Analyzes the user's message to identify database-related intents such as
     * queries about Störungen, Dispatch, or Cutting operations. Uses predefined
     * keyword patterns and confidence scoring to determine the most likely intents.
     *
     * @param message - The user message to analyze for intent
     * @returns Array of detected query intents with confidence scores
     *
     * @example
     * ```typescript
     * const intents = service.parseIntent("Show me dispatch performance this week");
     * // Returns: [{
     * //   type: 'dispatch',
     * //   keywords: ['dispatch', 'performance'],
     * //   confidence: 0.8,
     * //   timeRange: { startDate: '2024-01-15', endDate: '2024-01-21' }
     * // }]
     * ```
     *
     * @algorithm
     * 1. Normalizes message to lowercase
     * 2. Matches against predefined keyword patterns
     * 3. Calculates confidence based on keyword density
     * 4. Extracts time range information if present
     * 5. Filters results by minimum confidence threshold
     *
     * @performance O(n*m) where n = message length, m = pattern count
     */
    parseIntent(message) {
        const normalizedMessage = message.toLowerCase();
        const intents = [];
        // Check for each intent type
        for (const [intentType, keywords] of Object.entries(INTENT_PATTERNS)) {
            if (intentType === 'timeRange' || intentType === 'metrics')
                continue;
            const matchedKeywords = keywords.filter((keyword) => normalizedMessage.includes(keyword.toLowerCase()));
            if (matchedKeywords.length > 0) {
                const confidence = Math.min(matchedKeywords.length * 0.5, 1.0);
                if (confidence > 0) { // Lower threshold for testing
                    const intent = {
                        type: intentType,
                        keywords: matchedKeywords,
                        confidence,
                        timeRange: this.parseTimeRange(normalizedMessage),
                        specificMetrics: this.parseSpecificMetrics(normalizedMessage)
                    };
                    intents.push(intent);
                }
            }
        }
        // If no specific intents found but metrics keywords present, add general intent
        if (intents.length === 0) {
            const metricsKeywords = INTENT_PATTERNS.metrics.filter((keyword) => normalizedMessage.includes(keyword.toLowerCase()));
            if (metricsKeywords.length > 0) {
                intents.push({
                    type: 'general',
                    keywords: metricsKeywords,
                    confidence: 0.5,
                    timeRange: this.parseTimeRange(normalizedMessage)
                });
            }
        }
        return intents.sort((a, b) => b.confidence - a.confidence);
    }
    /**
     * Parse time range from natural language including specific dates
     *
     * Supports both relative time expressions (heute, gestern, letzte Woche) and
     * specific date formats (DD.MM.YYYY, YYYY-MM-DD, DD/MM/YYYY, etc.)
     *
     * @param message - The message to parse for time information
     * @returns DateRange object with startDate and endDate, or undefined if no time info found
     *
     * @example
     * ```typescript
     * parseTimeRange("Servicegrad vom 17.04.2025") // { startDate: "2025-04-17", endDate: "2025-04-17" }
     * parseTimeRange("Daten von heute") // { startDate: "2024-01-20", endDate: "2024-01-20" }
     * parseTimeRange("letzte Woche") // { startDate: "2024-01-13", endDate: "2024-01-20" }
     * ```
     */
    parseTimeRange(message) {
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const lastWeekStart = new Date(today);
        lastWeekStart.setDate(lastWeekStart.getDate() - 7);
        const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
        // Helper function to format date as YYYY-MM-DD
        const formatDate = (date) => {
            return date.toISOString().split('T')[0];
        };
        // Helper function to validate and parse date
        const parseSpecificDate = (dateStr) => {
            try {
                const date = new Date(dateStr);
                if (isNaN(date.getTime()))
                    return null;
                return date;
            }
            catch (_a) {
                return null;
            }
        };
        // 1. Check for specific date patterns first (highest priority)
        // German date format: DD.MM.YYYY or DD.MM.YY
        const germanDatePattern = /(\d{1,2})\.(\d{1,2})\.(\d{2,4})/g;
        const germanMatches = [...message.matchAll(germanDatePattern)];
        if (germanMatches.length > 0) {
            const match = germanMatches[0];
            const day = parseInt(match[1]);
            const month = parseInt(match[2]);
            let year = parseInt(match[3]);
            // Handle 2-digit years (assume 20xx for years < 50, 19xx for years >= 50)
            if (year < 100) {
                year += year < 50 ? 2000 : 1900;
            }
            // Validate date components
            if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900 && year <= 2100) {
                // Create date using UTC to avoid timezone issues
                const specificDate = new Date(Date.UTC(year, month - 1, day));
                if (!isNaN(specificDate.getTime())) {
                    const formattedDate = specificDate.toISOString().split('T')[0];
                    console.log(`🗓️ [DATA-ENRICHMENT] Detected German date: ${day}.${month}.${year} -> ${formattedDate}`);
                    return {
                        startDate: formattedDate,
                        endDate: formattedDate
                    };
                }
            }
        }
        // ISO date format: YYYY-MM-DD
        const isoDatePattern = /(\d{4})-(\d{1,2})-(\d{1,2})/g;
        const isoMatches = [...message.matchAll(isoDatePattern)];
        if (isoMatches.length > 0) {
            const match = isoMatches[0];
            const year = parseInt(match[1]);
            const month = parseInt(match[2]);
            const day = parseInt(match[3]);
            if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900 && year <= 2100) {
                // Create date using UTC to avoid timezone issues
                const specificDate = new Date(Date.UTC(year, month - 1, day));
                if (!isNaN(specificDate.getTime())) {
                    const formattedDate = specificDate.toISOString().split('T')[0];
                    console.log(`🗓️ [DATA-ENRICHMENT] Detected ISO date: ${year}-${month}-${day} -> ${formattedDate}`);
                    return {
                        startDate: formattedDate,
                        endDate: formattedDate
                    };
                }
            }
        }
        // Slash date formats: DD/MM/YYYY, MM/DD/YYYY, YYYY/MM/DD
        const slashDatePattern = /(\d{1,4})\/(\d{1,2})\/(\d{1,4})/g;
        const slashMatches = [...message.matchAll(slashDatePattern)];
        if (slashMatches.length > 0) {
            const match = slashMatches[0];
            const part1 = parseInt(match[1]);
            const part2 = parseInt(match[2]);
            const part3 = parseInt(match[3]);
            let year, month, day;
            // Determine format based on which part could be a year
            if (part1 > 31 || part1 > 1900) {
                // YYYY/MM/DD format
                year = part1;
                month = part2;
                day = part3;
            }
            else if (part3 > 31 || part3 > 1900) {
                // DD/MM/YYYY or MM/DD/YYYY format
                // Assume DD/MM/YYYY for European context (can be configured)
                day = part1;
                month = part2;
                year = part3;
            }
            else {
                // Ambiguous format, skip this match
                console.log(`⚠️ [DATA-ENRICHMENT] Ambiguous date format: ${match[0]}`);
                // Don't process this match, but continue with other date patterns
            }
            if (year !== undefined && month !== undefined && day !== undefined &&
                year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                // Create date using UTC to avoid timezone issues
                const specificDate = new Date(Date.UTC(year, month - 1, day));
                if (!isNaN(specificDate.getTime())) {
                    const formattedDate = specificDate.toISOString().split('T')[0];
                    console.log(`🗓️ [DATA-ENRICHMENT] Detected slash date: ${day}/${month}/${year} -> ${formattedDate}`);
                    return {
                        startDate: formattedDate,
                        endDate: formattedDate
                    };
                }
            }
        }
        // 2. Check for date ranges (von X bis Y, from X to Y)
        const rangePatterns = [
            /von\s+(\d{1,2}\.\d{1,2}\.\d{2,4})\s+bis\s+(\d{1,2}\.\d{1,2}\.\d{2,4})/i,
            /from\s+(\d{1,2}\/\d{1,2}\/\d{2,4})\s+to\s+(\d{1,2}\/\d{1,2}\/\d{2,4})/i,
            /zwischen\s+(\d{1,2}\.\d{1,2}\.\d{2,4})\s+und\s+(\d{1,2}\.\d{1,2}\.\d{2,4})/i
        ];
        for (const pattern of rangePatterns) {
            const rangeMatch = message.match(pattern);
            if (rangeMatch) {
                const startDateStr = rangeMatch[1];
                const endDateStr = rangeMatch[2];
                // Parse both dates using the same logic as above
                const startDateRange = this.parseTimeRange(`datum ${startDateStr}`);
                const endDateRange = this.parseTimeRange(`datum ${endDateStr}`);
                if (startDateRange && endDateRange) {
                    console.log(`🗓️ [DATA-ENRICHMENT] Detected date range: ${startDateStr} to ${endDateStr}`);
                    return {
                        startDate: startDateRange.startDate,
                        endDate: endDateRange.endDate
                    };
                }
            }
        }
        // 3. Check for relative time patterns (existing logic)
        if (message.includes('heute') || message.includes('today')) {
            return {
                startDate: formatDate(today),
                endDate: formatDate(today)
            };
        }
        if (message.includes('gestern') || message.includes('yesterday')) {
            return {
                startDate: formatDate(yesterday),
                endDate: formatDate(yesterday)
            };
        }
        if (message.includes('letzte woche') || message.includes('last week') || message.includes('letzten woche')) {
            return {
                startDate: formatDate(lastWeekStart),
                endDate: formatDate(today)
            };
        }
        if (message.includes('diese woche') || message.includes('this week')) {
            const thisWeekStart = new Date(today);
            thisWeekStart.setDate(today.getDate() - today.getDay());
            return {
                startDate: formatDate(thisWeekStart),
                endDate: formatDate(today)
            };
        }
        if (message.includes('letzter monat') || message.includes('last month')) {
            return {
                startDate: formatDate(lastMonthStart),
                endDate: formatDate(lastMonthEnd)
            };
        }
        if (message.includes('dieser monat') || message.includes('this month')) {
            return {
                startDate: formatDate(thisMonthStart),
                endDate: formatDate(today)
            };
        }
        // 4. Check for "last X days" patterns
        const lastDaysPattern = /(?:letzte|letzten|last)\s+(\d+)\s+(?:tage|days)/i;
        const lastDaysMatch = message.match(lastDaysPattern);
        if (lastDaysMatch) {
            const days = parseInt(lastDaysMatch[1]);
            const startDate = new Date(today);
            startDate.setDate(startDate.getDate() - days);
            return {
                startDate: formatDate(startDate),
                endDate: formatDate(today)
            };
        }
        // 5. Check for month names (Januar, February, etc.)
        const monthNames = {
            'januar': 0, 'january': 0, 'jan': 0,
            'februar': 1, 'february': 1, 'feb': 1,
            'märz': 2, 'march': 2, 'mar': 2, 'mär': 2,
            'april': 3, 'apr': 3,
            'mai': 4, 'may': 4,
            'juni': 5, 'june': 5, 'jun': 5,
            'juli': 6, 'july': 6, 'jul': 6,
            'august': 7, 'aug': 7,
            'september': 8, 'sep': 8, 'sept': 8,
            'oktober': 9, 'october': 9, 'oct': 9, 'okt': 9,
            'november': 10, 'nov': 10,
            'dezember': 11, 'december': 11, 'dec': 11, 'dez': 11
        };
        for (const [monthName, monthIndex] of Object.entries(monthNames)) {
            const monthPattern = new RegExp(`\\b${monthName}\\s+(\\d{4})\\b`, 'i');
            const monthMatch = message.match(monthPattern);
            if (monthMatch) {
                const year = parseInt(monthMatch[1]);
                // Create dates using UTC to avoid timezone issues
                const monthStart = new Date(Date.UTC(year, monthIndex, 1));
                const monthEnd = new Date(Date.UTC(year, monthIndex + 1, 0));
                console.log(`🗓️ [DATA-ENRICHMENT] Detected month: ${monthName} ${year}`);
                return {
                    startDate: monthStart.toISOString().split('T')[0],
                    endDate: monthEnd.toISOString().split('T')[0]
                };
            }
        }
        return undefined;
    }
    /**
     * Parse specific metrics from message
     */
    parseSpecificMetrics(message) {
        const metrics = [];
        // Störungen metrics
        if (message.toLowerCase().includes('mttr'))
            metrics.push('mttr');
        if (message.toLowerCase().includes('severity') || message.toLowerCase().includes('schweregrad'))
            metrics.push('severity');
        if (message.toLowerCase().includes('status'))
            metrics.push('status');
        // Dispatch metrics
        if (message.toLowerCase().includes('service level') || message.toLowerCase().includes('servicegrad'))
            metrics.push('serviceLevel');
        if (message.toLowerCase().includes('tonnage') || message.toLowerCase().includes('tonnagen'))
            metrics.push('tonnage');
        if (message.toLowerCase().includes('atrl'))
            metrics.push('atrl');
        if (message.toLowerCase().includes('aril'))
            metrics.push('aril');
        // Cutting metrics
        if (message.toLowerCase().includes('efficiency') || message.toLowerCase().includes('effizienz'))
            metrics.push('efficiency');
        if (message.toLowerCase().includes('cuts') || message.toLowerCase().includes('schnitte'))
            metrics.push('cuts');
        return metrics;
    }
    /**
     * Get query coordinator metrics for monitoring
     */
    getQueryCoordinatorMetrics() {
        return this.queryCoordinator.getMetrics();
    }
    /**
     * Reset query coordinator metrics
     */
    resetQueryCoordinatorMetrics() {
        this.queryCoordinator.resetMetrics();
    }
    /**
     * Execute queries with comprehensive error handling and fallback mechanisms
     */
    async executeQueriesWithFallback(intents, requestId) {
        try {
            // Set timeout for the entire query execution
            const queryPromise = this.queryCoordinator.executeQueries(intents);
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Query execution timeout')), this.config.maxQueryTime);
            });
            const queryResults = await Promise.race([queryPromise, timeoutPromise]);
            // Check for partial failures and log them
            const failedQueries = queryResults.filter(r => !r.success);
            if (failedQueries.length > 0) {
                console.warn(`⚠️ [DATA-ENRICHMENT] [${requestId}] ${failedQueries.length} queries failed:`, failedQueries.map(q => `${q.dataType}: ${q.error}`));
            }
            return queryResults;
        }
        catch (error) {
            console.error(`❌ [DATA-ENRICHMENT] [${requestId}] Query coordinator failed:`, error);
            // Return fallback results for each intent
            return intents.map(intent => this.createFallbackQueryResult(intent, error));
        }
    }
    /**
     * Create fallback query result when repository is unavailable
     */
    createFallbackQueryResult(intent, error) {
        const fallbackData = this.getFallbackDataForIntent(intent);
        return {
            dataType: intent.type,
            data: fallbackData,
            summary: this.getFallbackSummaryForIntent(intent),
            timestamp: new Date(),
            success: false,
            error: (error === null || error === void 0 ? void 0 : error.message) || 'Repository unavailable',
            queryTime: 0,
            intent: intent.type,
            fallback: true
        };
    }
    /**
     * Get fallback data when repositories are unavailable
     */
    getFallbackDataForIntent(intent) {
        switch (intent.type) {
            case 'stoerungen':
                return {
                    statistics: { total: 0, active: 0, resolved: 0, critical: 0, high: 0, medium: 0, low: 0 },
                    systemStatus: [],
                    recentIncidents: [],
                    message: 'Störungsdaten sind momentan nicht verfügbar'
                };
            case 'dispatch':
                return {
                    serviceLevel: [],
                    performance: [],
                    picking: [],
                    returns: [],
                    metrics: { totalDeliveries: 0, averagePickingRate: 0, averageServiceLevel: 0 },
                    message: 'Versanddaten sind momentan nicht verfügbar'
                };
            case 'cutting':
                return {
                    cuttingData: [],
                    efficiency: [],
                    overview: { totalCuts: { cutTT: 0, cutTR: 0, cutRR: 0, pickCut: 0 }, averageEfficiency: 0 },
                    topMachines: [],
                    message: 'Schnittdaten sind momentan nicht verfügbar'
                };
            default:
                return {
                    message: 'Daten sind momentan nicht verfügbar'
                };
        }
    }
    /**
     * Get fallback summary when repositories are unavailable
     */
    getFallbackSummaryForIntent(intent) {
        switch (intent.type) {
            case 'stoerungen':
                return 'Störungsdaten sind momentan nicht verfügbar. Das System arbeitet daran, die Verbindung wiederherzustellen.';
            case 'dispatch':
                return 'Versanddaten sind momentan nicht verfügbar. Bitte versuchen Sie es in wenigen Minuten erneut.';
            case 'cutting':
                return 'Schnittdaten sind momentan nicht verfügbar. Die Datenbank wird gerade aktualisiert.';
            default:
                return 'Die angeforderten Daten sind momentan nicht verfügbar. Bitte versuchen Sie es später erneut.';
        }
    }
    /**
     * Create fallback enriched context when data enrichment fails
     */
    createFallbackContext(message, intents, reason, queryResults, error) {
        let fallbackContext = '';
        switch (reason) {
            case 'NO_INTENTS_DETECTED':
                fallbackContext = 'Keine spezifischen Systemdaten für diese Anfrage erkannt. ' +
                    'Ich kann allgemeine Informationen über das JOZI1 Lapp Dashboard System bereitstellen.';
                break;
            case 'ALL_QUERIES_FAILED':
                fallbackContext = 'Die Systemdatenbanken sind momentan nicht erreichbar. ' +
                    'Ich kann Ihnen allgemeine Informationen und Hilfestellungen anbieten, ' +
                    'aber keine aktuellen Betriebsdaten abrufen.';
                break;
            case 'CRITICAL_ERROR':
                fallbackContext = 'Ein technischer Fehler ist bei der Datenabfrage aufgetreten. ' +
                    'Das System arbeitet normal, aber aktuelle Daten können momentan nicht abgerufen werden. ' +
                    'Bitte versuchen Sie es in wenigen Minuten erneut.';
                break;
            default:
                fallbackContext = 'Systemdaten sind momentan eingeschränkt verfügbar. ' +
                    'Ich helfe Ihnen gerne mit allgemeinen Informationen weiter.';
        }
        // Add available data if some queries succeeded
        if (queryResults && queryResults.some(r => r.success)) {
            const successfulTypes = queryResults.filter(r => r.success).map(r => r.dataType);
            fallbackContext += ` Verfügbare Daten: ${successfulTypes.join(', ')}.`;
        }
        return {
            originalMessage: message,
            detectedIntents: intents,
            databaseContext: fallbackContext,
            hasData: false,
            dataTypes: [],
            timestamp: new Date(),
            requestId: this.generateRequestId(),
            processingTime: 0,
            fallbackReason: reason,
            partialFailure: queryResults ? queryResults.some(r => !r.success) : false
        };
    }
    /**
     * Create fallback data context when formatting fails
     */
    createFallbackDataContext(successfulResults) {
        let context = 'SYSTEMDATEN (Vereinfachte Darstellung):\n\n';
        successfulResults.forEach(result => {
            context += `${result.dataType.toUpperCase()}:\n`;
            context += `${result.summary || 'Daten verfügbar, aber Formatierung fehlgeschlagen'}\n\n`;
        });
        context += 'Hinweis: Die Datenformatierung war eingeschränkt. ' +
            'Grundlegende Informationen sind verfügbar.';
        return context;
    }
    /**
     * Log detailed error information for debugging
     */
    logEnrichmentError(error, message, requestId, processingTime) {
        var _a, _b;
        const errorDetails = {
            requestId,
            message: message.substring(0, 200),
            processingTime,
            errorType: ((_a = error === null || error === void 0 ? void 0 : error.constructor) === null || _a === void 0 ? void 0 : _a.name) || 'Unknown',
            errorMessage: (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error',
            stack: (_b = error === null || error === void 0 ? void 0 : error.stack) === null || _b === void 0 ? void 0 : _b.split('\n').slice(0, 5).join('\n'), // First 5 lines of stack
            timestamp: new Date().toISOString(),
            config: {
                maxQueryTime: this.config.maxQueryTime,
                cacheEnabled: this.config.cacheEnabled,
                intentThreshold: this.config.intentThreshold
            }
        };
        console.error('🔍 [DATA-ENRICHMENT] [ERROR-DETAILS]', JSON.stringify(errorDetails, null, 2));
    }
    /**
     * Generate unique request ID for tracking
     */
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
    }
    /**
     * Format data for LLM consumption
     */
    formatDataForLLM(queryResults, options = {}) {
        const { format = 'summary', includeTimestamp = true } = options;
        if (queryResults.length === 0 || !queryResults.some(r => r.success)) {
            return 'Keine relevanten Daten gefunden.';
        }
        const successfulResults = queryResults.filter(r => r.success);
        let context = '';
        if (includeTimestamp) {
            context += `Datenstand: ${new Date().toLocaleString('de-DE')}\n\n`;
        }
        context += 'AKTUELLE SYSTEMDATEN:\n\n';
        successfulResults.forEach(result => {
            context += `${result.dataType.toUpperCase()}:\n`;
            context += `${result.summary}\n\n`;
            if (format === 'detailed' && result.data) {
                context += this.formatDetailedData(result);
            }
        });
        context += '\nDiese Daten können zur Beantwortung von Fragen über den aktuellen Systemstatus, ' +
            'Performance-Metriken und operative Kennzahlen verwendet werden.';
        return context;
    }
    /**
     * Format detailed data for specific result types
     */
    formatDetailedData(result) {
        var _a, _b, _c, _d;
        let details = '';
        switch (result.dataType) {
            case 'stoerungen':
                if ((_a = result.data) === null || _a === void 0 ? void 0 : _a.systemStatus) {
                    details += 'Systemstatus:\n';
                    result.data.systemStatus.forEach((status) => {
                        details += `- ${status.system_name}: ${status.status}\n`;
                    });
                    details += '\n';
                }
                break;
            case 'dispatch':
                if ((_b = result.data) === null || _b === void 0 ? void 0 : _b.returns) {
                    details += 'Retouren:\n';
                    result.data.returns.forEach((item) => {
                        details += `- ${item.name}: ${item.value}\n`;
                    });
                    details += '\n';
                }
                break;
            case 'cutting':
                if ((_d = (_c = result.data) === null || _c === void 0 ? void 0 : _c.overview) === null || _d === void 0 ? void 0 : _d.topMachine) {
                    details += `Beste Maschine: ${result.data.overview.topMachine.name} ` +
                        `(${result.data.overview.topMachine.efficiency}% Effizienz)\n\n`;
                }
                break;
        }
        return details;
    }
}
exports.DataEnrichmentService = DataEnrichmentService;
exports.default = DataEnrichmentService;
