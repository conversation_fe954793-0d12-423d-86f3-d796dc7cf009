"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const dashboardController_1 = require("../controllers/dashboardController");
const router = (0, express_1.Router)();
/**
 * Dashboard API Routes
 * Alle Endpunkte für Dashboard-Daten
 */
// Service Level Daten
router.get('/service-level', dashboardController_1.dashboardController.getServiceLevelData);
// Lieferpositionen Daten
router.get('/delivery-positions', dashboardController_1.dashboardController.getDeliveryPositionsData);
// Tagesleistung Daten
router.get('/tagesleistung', dashboardController_1.dashboardController.getTagesleistungData);
// Picking Daten
router.get('/picking', dashboardController_1.dashboardController.getPickingData);
// Alle Dashboard-Daten (optimiert)
router.get('/all', dashboardController_1.dashboardController.getAllDashboardData);
// Dashboard-Statistiken
router.get('/statistics', dashboardController_1.dashboardController.getDashboardStatistics);
// Gesundheitsprüfung
router.get('/health', dashboardController_1.dashboardController.getHealth);
// Alternative Routen für verschiedene Chart-Typen
router.get('/charts/service-level', dashboardController_1.dashboardController.getServiceLevelData);
router.get('/charts/delivery-positions', dashboardController_1.dashboardController.getDeliveryPositionsData);
router.get('/charts/tagesleistung', dashboardController_1.dashboardController.getTagesleistungData);
router.get('/charts/picking', dashboardController_1.dashboardController.getPickingData);
// Batch-Operationen
router.get('/batch/all', dashboardController_1.dashboardController.getAllDashboardData);
router.get('/batch/statistics', dashboardController_1.dashboardController.getDashboardStatistics);
exports.default = router;
