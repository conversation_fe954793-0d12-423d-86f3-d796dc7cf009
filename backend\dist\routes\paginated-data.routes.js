"use strict";
/**
 * Paginated Data API Routes
 *
 * TODO: Nach Migration vollständig auf Drizzle ORM umstellen
 * Temporär deaktiviert während der Migration.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const router = express_1.default.Router();
/**
 * Temporäre Platzhalter-Route während der Drizzle-Migration
 */
router.use('*', (req, res) => {
    const response = {
        success: false,
        error: 'Paginated Data Service ist temporär nicht verfügbar während der Migration von Prisma zu Drizzle ORM.',
        data: null
    };
    res.status(501).json(response);
});
// TODO: Implementiere alle ursprünglichen Routen mit Drizzle ORM:
// - /alle-daten/cursor
// - /alle-daten/offset  
// - /alle-daten
// - /ablaengerei/cursor
// - /ablaengerei/offset
// - /performance-comparison
// - /schema-info
exports.default = router;
