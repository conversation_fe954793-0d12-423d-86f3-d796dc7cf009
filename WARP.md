# JOZI1 Leitstand Dashboard - Projekt-Dokumentation

## 🎯 Überblick & Scope

**JOZI1 Leitstand Dashboard** ist eine Desktop-Anwendung für die Visualisierung und Verwaltung von Logistik- und Produktionsdaten bei Lapp Kabel. Die Applikation bietet KI-gestützte Optimierung, Echtzeit-Monitoring und umfassende Analytik-Funktionen.

### Kern-Features
- **Dashboard-Visualisierung:** Echtzeit-KPIs für Wareneingang, Versand, Schnittleistung und Kommissionierung
- **KI-Modul:** RAG-basierter Chatbot, Schnittoptimierung, Bestandsanalyse und Prozessoptimierung
- **Störungsmanagement:** Vollständiges Incident-Management mit Bereitschaftszuweisung
- **Dual-Database-Architektur:** Produktionsdaten + KI-Wissensdatenbank
- **Cross-Platform Desktop:** Electron-basierte Applikation für Windows, macOS und Linux

---

## 🏗️ Technologie-Stack & Architektur

### Core-Stack

| Layer | Technologie | Version | Beschreibung |
|-------|------------|---------|------------|
| **Package Manager** | pnpm | workspace | ⚠️ **NUR pnpm verwenden** - NIEMALS npm/yarn! |
| **ORM** | Drizzle ORM | 0.44.5 | ⚠️ **NUR Drizzle** - KEINE Prisma-Imports! |
| **Desktop Framework** | Electron | 35.7.2 | Cross-Platform Desktop App |
| **Build Tool** | Vite | 6.3.5 | Fast HMR, optimized bundling |
| **Frontend** | React | 18.2.0 | TypeScript 5.8.3 |
| **UI Components** | shadcn/ui + Radix UI | latest | Tailwind CSS 4.1.11 |
| **Routing** | TanStack Router | 1.121.2 | Hash-based für Electron |
| **Backend** | Express.js | 5.1.0 | REST API Server |
| **Database** | SQLite3 | via libsql | Dual-DB Setup |
| **AI Integration** | OpenRouter/OpenAI | latest | RAG, Embeddings, Chat |

### Architektur-Diagramm

```mermaid
graph TB
    subgraph "Desktop Application"
        E[Electron Main Process]
        R[React Renderer Process]
    end
    
    subgraph "Frontend Layer"
        UI[shadcn/ui Components]
        M[Feature Modules]
        TR[TanStack Router]
    end
    
    subgraph "AI Service Layer"
        RAG[RAG Service]
        CO[Cutting Optimizer]
        IA[Inventory Analytics]
        PO[Process Optimizer]
        PA[Predictive Analytics]
    end
    
    subgraph "Backend API"
        EX[Express Server :3001]
        MW[Middleware Layer]
        SVC[Business Services]
        REPO[Repositories]
    end
    
    subgraph "Data Layer"
        DB1[(SFM Dashboard DB)]
        DB2[(RAG Knowledge DB)]
        CACHE[Cache Layer]
    end
    
    E --> R
    R --> UI
    UI --> M
    M --> TR
    TR --> EX
    
    EX --> MW
    MW --> SVC
    SVC --> REPO
    
    M --> RAG
    M --> CO
    M --> IA
    RAG --> DB2
    
    REPO --> DB1
    REPO --> CACHE
    
    classDef critical fill:#ff6b6b,stroke:#c92a2a,stroke-width:3px
    class E,DB1,DB2 critical
```

### Dual-Database-Setup

#### 1. **SFM Dashboard Database** (Hauptdatenbank)
- **Verbindung:** PostgreSQL Server (konfigurierbar über Umgebungsvariablen)
- **Schema:** `src/db/schema.ts` (41 Tabellen, 573 Spalten, 127 Indizes)
- **Import:** `import { db } from '@/db'`
- **Inhalt:** Störungen, Workflows, Materialdaten, Trommeldaten, KPIs, etc. ...

#### 2. **RAG Knowledge Database** (KI-Wissensdatenbank)
- **Verbindung:** PostgreSQL Server (separate Datenbank oder Schema)
- **Schema:** `src/db/rag-schema.ts` (12 Tabellen, Vector Storage)
- **Import:** `import { ragDb } from '@/db/rag-db'`
- **Inhalt:** Embeddings, Dokumente, Knowledge Bases, Vector-Suche, etc. ...

---

## 📁 Projektstruktur

```
Leitstand_App/
├── 📂 src/                           # Frontend React Application
│   ├── 📂 components/                # Wiederverwendbare UI-Komponenten
│   │   ├── ui/                       # shadcn/ui Basis-Komponenten
│   │   └── shared/                   # App-spezifische Komponenten
│   ├── 📂 modules/                   # Feature-Module (Kern-Architektur)
│   │   ├── 📂 ai/                    # KI-Modul
│   │   │   ├── components/           # RAG Chat, Optimizer UI
│   │   │   ├── services/             # RAG, Embedding, Vector Services
│   │   │   ├── pages/                # AI Dashboard, Settings
│   │   │   └── types/                # TypeScript Definitionen
│   │   ├── 📂 dashboard/             # KPI & Analytics
│   │   │   ├── components/           # Charts, KPI-Cards
│   │   │   └── pages/                # Dashboard Views
│   │   ├── 📂 stoerungen/            # Störungsmanagement
│   │   │   ├── components/           # Störungs-Formulare
│   │   │   └── services/             # Störungs-API
│   │   ├── 📂 backend/               # Backend-Monitoring
│   │   │   └── workflows/            # Python-Workflow Integration
│   │   └── 📂 settings/              # App-Einstellungen
│   ├── 📂 db/                        # Drizzle Database Layer
│   │   ├── index.ts                  # Main DB Connection
│   │   ├── schema.ts                 # SFM Schema (41 Tables)
│   │   ├── relations.ts              # Table Relations
│   │   ├── rag-db.ts                 # RAG DB Connection
│   │   └── rag-schema.ts             # RAG Schema (12 Tables)
│   ├── 📂 routes/                    # TanStack Router Config
│   ├── 📂 services/                  # Frontend API Services
│   └── main.ts                       # Electron Renderer Entry
│
├── 📂 backend/                       # Express.js API Server
│   ├── 📂 src/
│   │   ├── 📂 controllers/           # Request Handler
│   │   ├── 📂 services/              # Business Logic
│   │   │   ├── rag/                  # RAG Implementation
│   │   │   └── database.service.ts   # Core DB Service
│   │   ├── 📂 repositories/          # Data Access (Drizzle)
│   │   ├── 📂 middleware/            # Auth, Security, Logging
│   │   └── server.ts                 # Express Entry Point
│   └── 📂 database/                  # Database Configuration
│       ├── migrations/               # PostgreSQL Migrations
│       └── seeds/                    # Initial Data Seeds
│
├── 📂 electron/                      # Electron Main Process
│   ├── main.ts                       # Main Process Entry
│   └── preload.ts                    # Context Bridge
│
├── 📂 scripts/                       # Utility Scripts
│   ├── test-*.ts                     # Test Scripts
│   └── migrate-*.ts                  # Migration Scripts
│
├── 📂 drizzle/                       # Generated Migrations
├── drizzle.config.ts                 # Main DB Config
├── drizzle.config.rag.ts             # RAG DB Config
├── forge.config.ts                   # Electron Forge Config
├── vite.config.ts                    # Vite Build Config
├── pnpm-workspace.yaml               # Workspace Config
└── package.json                      # Root Dependencies
```

---

## 🚀 Build & Development Workflows

### Entwicklungs-Kommandos

| Kategorie | Befehl | Beschreibung |
|-----------|--------|------------|
| **Development** | | |
| | `pnpm run dev` | Startet Frontend + Backend (empfohlen) |
| | `pnpm run dev:app` | Nur Electron + Vite Frontend |
| | `pnpm run dev:backend` | Nur Express Backend |
| | `pnpm run dev:frontend` | Nur Vite Dev Server |
| **Database (Drizzle)** | | |
| | `pnpm run db:generate` | Schema → SQL Migrations |
| | `pnpm run db:generate:rag` | RAG Schema Migrations |
| | `pnpm run db:push` | Schema direkt anwenden |
| | `pnpm run db:push:rag` | RAG Schema anwenden |
| | `pnpm run db:introspect` | DB → TypeScript Schema |
| | `pnpm run db:introspect:rag` | RAG DB introspection |
| | `pnpm run db:test` | Verbindungstest |
| | `pnpm run db:test:repository` | Repository Tests |
| | `pnpm run db:test:service` | Service Tests |
| | `pnpm run db:rebuild-native` | better-sqlite3 rebuild |
| **AI Module** | | |
| | `pnpm run test:ai` | AI Module Tests |
| | `pnpm run test:ai:comprehensive` | Vollständige Test-Suite |
| | `pnpm run test:ai:performance` | Performance Tests |
| | `pnpm run health:ai` | Health Check |
| | `pnpm run deploy:ai` | AI Module Deployment |
| | `pnpm run validate:ai:deployment` | Deployment Validierung |
| **Testing** | | |
| | `pnpm run test:unit` | Vitest Unit Tests |
| | `pnpm run test:watch` | Tests im Watch Mode |
| | `pnpm run test:e2e` | Playwright E2E Tests |
| | `pnpm run test:all` | Alle Tests |
| **Building** | | |
| | `pnpm run package` | Electron Package erstellen |
| | `pnpm run make` | Distributables erstellen |
| | `pnpm run build:portable` | Portable .exe (Windows) |

### Native Module Management

⚠️ **Wichtig für Electron + better-sqlite3:**

```bash
# Bei Platform-Wechsel (Windows ↔ WSL)
pnpm run db:rebuild-native

# Alternative bei Problemen
cd backend && pnpm rebuild better-sqlite3
```

---

## 🤖 AI-Modul

### Architektur & Services

Das AI-Modul bietet umfassende KI-Funktionalitäten mit folgenden Kern-Services:

#### 1. **RAG Service** (Retrieval-Augmented Generation)
- **Pfad:** `src/modules/ai/services/rag/RAGService.ts`
- **Features:**
  - Vector-basierte Dokumentensuche
  - Kontext-angereicherte Antworten
  - Knowledge Base Management
  - SQLite-VSS Extension für Vector-Operationen

#### 2. **Cutting Optimizer** (Schnittoptimierung)
- **Pfad:** `src/modules/ai/services/cutting/CuttingOptimizerService.ts`
- **Features:**
  - Bin-Packing Algorithmen
  - Trommelauslastungs-Optimierung
  - Verschnitt-Minimierung
  - Multi-Objective Optimization

#### 3. **Inventory Intelligence** (Lager-KI)
- **Pfad:** `src/modules/ai/services/inventory/InventoryIntelligenceService.ts`
- **Features:**
  - ABC-Analyse 2.0
  - Demand Forecasting
  - Reorder-Point Optimization
  - Seasonal Pattern Detection

#### 4. **Process Optimizer**
- **Features:**
  - Bottleneck-Identifikation
  - Discrete Event Simulation
  - Process Change Impact Analysis

#### 5. **Predictive Analytics**
- **Features:**
  - KPI-Vorhersagen
  - Anomalie-Erkennung
  - Capacity Planning
  - Time Series Analysis

### AI Security & Permissions

```typescript
// Definierte AI-Permissions (src/modules/ai/types/security.ts)
export const AI_PERMISSIONS = {
  RAG_QUERY: ['Besucher', 'Benutzer', 'Administrator'],
  CUTTING_OPTIMIZATION: ['Benutzer', 'Administrator'],
  INVENTORY_INTELLIGENCE: ['Benutzer', 'Administrator'],
  PROCESS_OPTIMIZATION: ['Benutzer', 'Administrator'],
  PREDICTIVE_ANALYTICS: ['Benutzer', 'Administrator'],
  WAREHOUSE_OPTIMIZATION: ['Benutzer', 'Administrator'],
  SUPPLY_CHAIN_OPTIMIZATION: ['Administrator'],
  AUTOMATED_REPORTING: ['Benutzer', 'Administrator'],
  AI_CONFIGURATION: ['Administrator']
};
```

### AI-Routen & Pages

```typescript
// Verfügbare AI-Module-Routen
/modules/ai                        // AI Dashboard
/modules/ai/rag-management         // RAG Wissensdatenbank
/modules/ai/cutting-optimization   // Schnittoptimierung
/modules/ai/inventory-intelligence // Lager-Intelligenz
/modules/ai/process-optimization   // Prozessoptimierung
/modules/ai/predictive-analytics   // Vorhersageanalyse
/modules/ai/warehouse-optimization // Lageroptimierung
/modules/ai/supply-chain-optimization // Lieferkette
/modules/ai/automated-reporting    // Automatisierte Berichte
/modules/ai/security              // Sicherheitsdashboard
```

---

## 🔒 Security, Testing & Performance

### Security Stack

| Feature | Implementierung | Beschreibung |
|---------|----------------|------------|
| **Authentication** | JWT + bcryptjs | Token-basierte Authentifizierung |
| **API Protection** | API Keys + Rate Limiting | Route-Schutz |
| **Input Validation** | Zod Schemas | Type-safe Validation |
| **CORS** | Configured Middleware | Cross-Origin Protection |
| **Headers** | Helmet | Security Headers |
| **File Upload** | Multer + Type Validation | Sichere Datei-Uploads |
| **SQL Injection** | Drizzle ORM | Prepared Statements |
| **OWASP Compliance** | Top 10 berücksichtigt | Security Best Practices |

### Testing-Strategie

```bash
# Unit Tests (Vitest + happy-dom)
pnpm run test:unit         # Coverage-Schwelle: ≥85%

# Component Tests (Playwright)
pnpm run test:e2e          # E2E mit packaged App

# AI-spezifische Tests
pnpm run test:ai:comprehensive  # Vollständige AI-Test-Suite

# Database Tests
pnpm run db:test:repository    # Repository-Pattern Tests
```

### Performance-Optimierungen

#### Frontend
- **Code Splitting:** Lazy Loading per Module
- **Bundle Optimization:** Vite Tree-Shaking
- **State Management:** TanStack Query mit intelligentem Caching
- **Route-based Splitting:** TanStack Router
- **Asset Optimization:** Optimierte Icons/Images

#### Backend
- **Database:** SQLite WAL Mode, NORMAL Sync
- **Connection Pooling:** Singleton Patterns
- **Caching:** In-Memory Cache für häufige Queries
- **Query Optimization:** 127 Indizes (Main), 45 Indizes (RAG)
- **Middleware:** Performance Tracking

#### Observability (geplant)
- **Logging:** electron-log (main), console → IPC (renderer)
- **Metrics:** OpenTelemetry-Integration vorbereitet
- **Tracing:** Distributed Tracing für AI-Operations
- **Monitoring:** Health-Checks, SLO/SLA-Metriken

---

## 🔄 Migration Prisma → Drizzle

### Status: ✅ **100% Core-Migration abgeschlossen**

#### Erfolgreich migriert
- ✅ Drizzle ORM vollständig installiert und konfiguriert
- ✅ Schema-Introspection beider Datenbanken
- ✅ Core Repositories (Stoerungen, User, Database Service)
- ✅ Frontend nutzt ausschließlich Drizzle
- ✅ 291 Störungen + 2 User validiert
- ✅ Performance: Sub-Millisekunden Queries

#### Hybrid-Architektur
- **Frontend:** 100% Drizzle-basiert
- **Backend:** Legacy Prisma parallel zu Drizzle (schrittweise Migration)
- **Zero Downtime:** Alle Features bleiben funktional

### Migration-Kommandos

```bash
# Schema aus bestehender DB generieren
pnpm run db:introspect

# Migration erstellen
pnpm run db:generate

# Schema direkt anwenden (Development)
pnpm run db:push

# Native Module Issues (WSL/Windows)
pnpm run db:rebuild-native
```

### ⚠️ Wichtige Hinweise

```typescript
// ✅ RICHTIG - Drizzle Imports
import { db } from '@/db';
import { stoerungen } from '@/db/schema';
const result = await db.select().from(stoerungen).where(eq(stoerungen.id, 1));

// ❌ FALSCH - Prisma Imports (NIEMALS verwenden!)
import { PrismaClient } from '@prisma/client';  // VERBOTEN!
```

---

## 📋 Best Practices & Entwicklungsrichtlinien

### 1. Code-Qualität & Stil
- **Klarheit vor Cleverness:** Verständlicher, lesbarer Code
- **Lint/Format:** ESLint + Prettier (printWidth: 100)
- **Naming:** Sprechende Bezeichner im Domain-Vokabular
- **TypeScript:** strict, noImplicitAny, exactOptionalPropertyTypes

### 2. Modularität & Architektur
- **Clean Architecture:** Trennung Domain/Application/Infrastructure
- **Single Responsibility:** Jedes exportierte Symbol = eine Aufgabe
- **Dependency Direction:** Nur zu höheren Abstraktionen
- **Module Structure:** Features in `src/modules/`

### 3. Test-First Development
- **Unit Tests:** ARRANGE-ACT-ASSERT Pattern
- **Coverage:** Minimum 85%
- **Integration Tests:** Kritische Happy Paths
- **Mocks:** Plausible Test-Doubles

### 4. Security by Design
- **Keine Hardcoded Secrets:** Environment Variables nutzen
- **Sichere Algorithmen:** bcrypt statt MD5
- **OWASP Top 10:** Berücksichtigen
- **GDPR/DSGVO:** Compliance beachten

### 5. Git Workflow
```bash
# Atomic Commits mit Conventional Commits
git commit -m "feat: add RAG service integration"
git commit -m "fix: resolve database connection issue"
git commit -m "refactor: optimize cutting algorithm"

# Branch-Naming
feature/<username>/<ticket-id>
bugfix/<username>/<issue-description>

# PR Requirements
- ✅ Grüne CI-Pipeline
- ✅ Senior Review
- ✅ Tests passed
- ✅ Lint clean
```

### 6. Performance & Skalierung
- **Algorithmic Efficiency > Premature Optimization**
- **Async/Streaming für I/O-intensive Operationen**
- **Big-O Komplexität dokumentieren**
- **Bottlenecks mit Messstrategie identifizieren**

### 7. Fehlerbehandlung
- **Explizite Fehlertypen:** Keine stillen Fallbacks
- **Differenzierte Logs:** Sicher, ohne Datenlecks
- **Fail-Fast oder Recover-Graceful:** Niemals undefiniert

### 8. Dokumentation
- **Public APIs:** Docstrings + Beispiele
- **Architektur-Entscheidungen:** ADRs begründen
- **README:** Lauffähige Copy-Paste Beispiele

---

## 🚀 Quick-Start Guide

### 1. Projekt Setup

```bash
# Repository klonen
git clone [repository-url]
cd Leitstand_App

# Dependencies installieren (NUR pnpm!)
pnpm install
pnpm run backend:prepare

# Native Module für Platform kompilieren
pnpm run db:rebuild-native
```

### 2. Development starten

```bash
# Vollständige Entwicklungsumgebung (empfohlen)
pnpm run dev

# Alternativ einzeln starten
pnpm run dev:app         # Nur Frontend
pnpm run dev:backend     # Nur Backend
```

### 3. Datenbank-Verbindung testen

```bash
# Verbindungstest
pnpm run db:test

# Repository-Tests
pnpm run db:test:repository
```

### 4. Applikation nutzen

- **Electron App:** Startet automatisch
- **Backend API:** http://localhost:3001
- **Frontend Dev:** http://localhost:3000

---

## 🔧 Troubleshooting

### Problem: better-sqlite3 Platform-Fehler

```bash
# Lösung: Native Module rebuild
pnpm run db:rebuild-native

# Alternative
cd backend && pnpm rebuild better-sqlite3
```

### Problem: Electron Dev Server startet nicht

```bash
# Cache löschen
rm -rf node_modules/.vite
pnpm run dev:app
```

### Problem: TanStack Router Navigation

- Nutzt Hash-basiertes Routing (`#/path`) für Electron
- Check: `src/routes/router.tsx`

### Problem: API-Verbindung fehlgeschlagen

1. Backend läuft auf Port 3001?
2. Proxy in `vite.config.ts` prüfen
3. CORS in `backend/src/server.ts` checken

### Problem: Database Migration Fehler

```bash
# Reset und neu generieren
pnpm run db:introspect
pnpm run db:generate
pnpm run db:push
```

---

## 📊 Projekt-Metriken

### Code-Statistiken
- **Hauptdatenbank:** 41 Tabellen, 573 Spalten, 127 Indizes
- **RAG-Datenbank:** 12 Tabellen, 92 Spalten, 45 Indizes
- **Module:** 5 Haupt-Feature-Module
- **AI-Services:** 10+ spezialisierte Services
- **Test-Coverage:** >85% für Core-Components

### Performance
- **Query-Zeit:** Sub-Millisekunden mit Caching
- **Bundle-Size:** Optimiert durch Code-Splitting
- **Memory-Usage:** Singleton-Patterns für Services
- **Startup-Zeit:** <3 Sekunden

### Migration-Status
- **Package Manager:** npm → pnpm ✅
- **ORM:** Prisma → Drizzle ✅ (Core 100%)
- **Database:** Dual-DB Setup aktiv ✅
- **AI-Module:** Vollständig implementiert ✅

---

## 📝 Wichtige Dateien & Konfiguration

| Datei | Zweck |
|-------|-------|
| `drizzle.config.ts` | Hauptdatenbank-Konfiguration |
| `drizzle.config.rag.ts` | RAG-Datenbank-Konfiguration |
| `src/db/schema.ts` | SFM Dashboard Schema (41 Tabellen) |
| `src/db/rag-schema.ts` | RAG Knowledge Schema (12 Tabellen) |
| `src/db/index.ts` | Drizzle DB Connection + Pragmas |
| `backend/src/server.ts` | Express Server mit Middleware |
| `forge.config.ts` | Electron Forge Packaging |
| `vite.config.ts` | Vite Build Configuration |
| `pnpm-workspace.yaml` | Workspace Configuration |
| `.env` (Backend) | Environment Variables |

---

## 🎯 Entwickler-Checkliste

### ✅ IMMER verwenden
- [ ] `pnpm` für ALLE Package-Operationen
- [ ] `import { db } from '@/db'` für Datenbank
- [ ] Drizzle Query Syntax
- [ ] TanStack Router für Navigation
- [ ] Module-basierte Architektur
- [ ] TypeScript strict mode

### ❌ NIEMALS verwenden
- [ ] `npm` oder `yarn` Befehle
- [ ] `@prisma/*` Imports
- [ ] Relative DB-Imports
- [ ] File-basiertes Routing
- [ ] Direkte SQL ohne Drizzle
- [ ] Hardcoded Secrets

---

## 📞 Support & Kontakt

Bei Fragen oder Problemen:
1. Check diese Dokumentation
2. Konsultiere `CLAUDE.md` für Details
3. Review `project_rules.md` für Richtlinien
4. Erstelle ein Issue im Repository

---

*Letzte Aktualisierung: September 2025 - Post-Migration zu pnpm + Drizzle ORM*
*Dokumentation erstellt gemäß den Entwicklungsrichtlinien und Best Practices*
