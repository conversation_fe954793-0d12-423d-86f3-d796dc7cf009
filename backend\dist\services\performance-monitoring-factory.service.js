"use strict";
/**
 * Performance Monitoring Factory Service
 *
 * Factory-Service zur Erstellung der richtigen Performance Monitoring Instanz
 * basierend auf der Konfiguration (In-Memory oder Database-Persistent).
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isUsingDatabasePersistence = exports.getPerformanceMonitor = exports.initializePerformanceMonitoring = exports.PerformanceMonitoringFactory = void 0;
const db_1 = require("../db");
const performance_monitoring_service_1 = require("./performance-monitoring.service");
const performance_monitoring_db_service_1 = __importDefault(require("./performance-monitoring-db.service"));
const DEFAULT_CONFIG = {
    useDatabasePersistence: true, // Default to database persistence for production
    inMemoryConfig: {
        maxMetrics: 10000,
        cleanupInterval: 60 * 60 * 1000 // 1 hour
    },
    databaseConfig: {
        enableInMemoryCache: true,
        inMemoryCacheSize: 1000,
        batchSize: 50,
        flushInterval: 30 * 1000, // 30 seconds
        retentionDays: 90, // 3 months
        enableAggregation: true
    }
};
class PerformanceMonitoringFactory {
    /**
     * Initialize the performance monitoring service
     */
    static initialize(database, config = {}) {
        this.config = { ...DEFAULT_CONFIG, ...config };
        if (this.instance) {
            console.warn('⚠️ [PERF-FACTORY] Performance monitoring service already initialized');
            return this.instance;
        }
        if (this.config.useDatabasePersistence) {
            const dbInstance = database || db_1.db;
            console.log('📊 [PERF-FACTORY] Initializing database-persistent performance monitoring');
            this.instance = new performance_monitoring_db_service_1.default(dbInstance, this.config.databaseConfig);
        }
        else {
            console.log('📊 [PERF-FACTORY] Initializing in-memory performance monitoring');
            this.instance = new performance_monitoring_service_1.PerformanceMonitoringService();
        }
        return this.instance;
    }
    /**
     * Get the current performance monitoring instance
     */
    static getInstance() {
        if (!this.instance) {
            throw new Error('Performance monitoring service not initialized. Call initialize() first.');
        }
        return this.instance;
    }
    /**
     * Check if database persistence is enabled
     */
    static isDatabasePersistenceEnabled() {
        var _a;
        return ((_a = this.config) === null || _a === void 0 ? void 0 : _a.useDatabasePersistence) || false;
    }
    /**
     * Destroy the current instance
     */
    static destroy() {
        if (this.instance) {
            this.instance.destroy();
            this.instance = null;
        }
    }
    /**
     * Reinitialize with new configuration
     */
    static reinitialize(database, config = {}) {
        this.destroy();
        return this.initialize(database, config);
    }
}
exports.PerformanceMonitoringFactory = PerformanceMonitoringFactory;
PerformanceMonitoringFactory.instance = null;
// Convenience functions for common operations
const initializePerformanceMonitoring = (database, config) => PerformanceMonitoringFactory.initialize(database, config);
exports.initializePerformanceMonitoring = initializePerformanceMonitoring;
const getPerformanceMonitor = () => PerformanceMonitoringFactory.getInstance();
exports.getPerformanceMonitor = getPerformanceMonitor;
const isUsingDatabasePersistence = () => PerformanceMonitoringFactory.isDatabasePersistenceEnabled();
exports.isUsingDatabasePersistence = isUsingDatabasePersistence;
exports.default = PerformanceMonitoringFactory;
