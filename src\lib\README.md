# 📚 Library (lib) - Utility Functions & Database Management

## 🎯 Übersicht & Zweck

Der `src/lib` Ordner bildet das **Service Layer** der Lapp Leitstand Anwendung und enthält zentrale Utility-Funktionen sowie Datenbankmanagement-Module. Diese Bibliothek stellt wiederverwendbare Funktionen für die gesamte Anwendung bereit und abstrahiert komplexe Operationen in einfach zu verwendende APIs.

**Hauptfunktionen:**
- 🗄️ Datenbankverbindung und -management (SQLite)
- 📅 Datumsformatierung und -utilities
- 🎨 CSS-Klassen-Utilities für Tailwind CSS
- 🔧 Allgemeine Hilfsfunktionen

**Architekturrolle:** Service Layer / Utility Layer - Stellt grundlegende Services und Hilfsfunktionen für die UI-Komponenten und Business-Logic bereit.

---

## 📁 Dateistruktur-Analyse

```
src/lib/
├── 🗃️ DatabaseManager.ts    # Singleton-basiertes Datenbankmanagement (better-sqlite3)
├── 🗄️ db.ts                 # Legacy SQLite-Verbindung mit sqlite3
├── 📅 date-utils.ts          # Datumsformatierungs-Utilities
└── 🎨 utils.ts               # CSS-Klassen-Utilities (clsx + tailwind-merge)
```

### Logische Gruppierung:
- **Datenbankschicht:** `DatabaseManager.ts`, `db.ts`
- **Utility-Funktionen:** `date-utils.ts`, `utils.ts`

---

## 📋 Detaillierte Dateibeschreibungen

### 🗃️ DatabaseManager.ts
**Zweck:** Modernes Singleton-Pattern für SQLite-Datenbankmanagement mit better-sqlite3

**Hauptfunktionalitäten:**
- Singleton-Pattern für einheitliche Datenbankverbindung
- Synchrone SQLite-Operationen mit better-sqlite3
- Tabellen-Introspection und Datenabfrage
- Automatische Fremdschlüssel-Aktivierung

**Exportierte Klassen/Methoden:**
```typescript
class DatabaseManager {
  static getInstance(): DatabaseManager
  static async connect(dbPath: string): Promise<void>
  static async getTables(): Promise<{name: string}[]>
  static async getTableData(tableName: string, limit?: number): Promise<{columns: string[], rows: any[]}>
  static async query(sql: string, params?: any[]): Promise<any>
  static close(): void
}
```

**Design Pattern:** Singleton Pattern für globale Datenbankinstanz

### 🗄️ db.ts
**Zweck:** Legacy-Datenbankverbindung mit asynchronem sqlite3-Driver

**Hauptfunktionalitäten:**
- Asynchrone SQLite-Datenbankverbindung
- Automatische Datenbankinitialisierung
- Pfadmanagement für Entwicklung vs. Produktion
- Schema-Definition für `dispatch_data` Tabelle

**Exportierte Funktionen/Interfaces:**
```typescript
interface DispatchData { /* 21 Felder für Logistikdaten */ }
async function getDb(): Promise<Database>
```

**Parameter:**
- Entwicklung: `./database/sfm_dashboard.db`
- Produktion: `userData/database/sfm_dashboard.db`

### 📅 date-utils.ts
**Zweck:** Spezialisierte Datumsformatierung für deutsche Lokalisierung

**Hauptfunktionalitäten:**
- Deutsche Datumsformatierung mit Wochentagen
- Mehrzeilige Datumsdarstellung für UI-Komponenten
- Fehlerbehandlung bei ungültigen Datumswerten

**Exportierte Funktionen:**
```typescript
function formatDateWithWeekday(dateValue: Date | string): string
```

**Ausgabeformat:**
```
"01.01."
"Mo"
```

### 🎨 utils.ts
**Zweck:** CSS-Klassen-Utilities für Tailwind CSS Integration

**Hauptfunktionalitäten:**
- Kombiniert clsx und tailwind-merge für optimale CSS-Klassen
- Löst Tailwind-Konflikte automatisch auf
- Conditional CSS-Klassen-Anwendung

**Exportierte Funktionen:**
```typescript
function cn(...inputs: ClassValue[]): string
```

---

## 🔗 Abhängigkeiten & Verbindungen

### Externe NPM-Pakete:
| Paket | Version | Zweck |
|-------|---------|-------|
| `better-sqlite3` | ^7.6.13 | Synchrone SQLite-Operationen |
| `sqlite3` | - | Asynchrone SQLite-Operationen (Legacy) |
| `sqlite` | - | Promise-Wrapper für sqlite3 |
| `clsx` | ^2.1.1 | Conditional CSS-Klassen |
| `tailwind-merge` | - | Tailwind CSS-Konfliktlösung |
| `electron` | ^35.7.2 | Desktop-App-Framework |

### Interne Abhängigkeiten:
- **Von UI-Komponenten verwendet:** `utils.ts` für CSS-Klassen
- **Von Charts/Dashboard verwendet:** `date-utils.ts` für Datumsformatierung
- **Von Services verwendet:** `DatabaseManager.ts` und `db.ts` für Datenpersistierung

### Datenfluss:
```
UI Components → lib/utils.ts → CSS-Klassen
Charts → lib/date-utils.ts → Formatierte Daten
Services → lib/DatabaseManager.ts → SQLite-Datenbank
```

---

## ⚙️ Technische Details

### Verwendete Technologien:
- **TypeScript** - Typsichere Entwicklung
- **SQLite** - Lokale Datenbankpersistierung
- **Electron** - Desktop-App-Integration
- **Tailwind CSS** - Utility-First CSS-Framework

### Performance-kritische Bereiche:
- **DatabaseManager:** Singleton-Pattern verhindert mehrfache Verbindungen
- **better-sqlite3:** Synchrone Operationen für bessere Performance
- **tailwind-merge:** Optimiert CSS-Bundle-Größe

### Umgebungsvariablen:
Keine direkten Umgebungsvariablen erforderlich. Pfade werden automatisch basierend auf `app.isPackaged` bestimmt.

---

## 💡 Verwendungsbeispiele

### DatabaseManager Usage:
```typescript
import DatabaseManager from '@/lib/DatabaseManager';

// Verbindung herstellen
await DatabaseManager.connect('./database/app.db');

// Tabellen abrufen
const tables = await DatabaseManager.getTables();
console.log('Verfügbare Tabellen:', tables);

// Daten abfragen
const data = await DatabaseManager.getTableData('dispatch_data', 50);
console.log('Spalten:', data.columns);
console.log('Zeilen:', data.rows);

// Custom Query
const results = await DatabaseManager.query(
  'SELECT * FROM dispatch_data WHERE jahr = ?', 
  [2024]
);
```

### Date Utils Usage:
```typescript
import { formatDateWithWeekday } from '@/lib/date-utils';

const formattedDate = formatDateWithWeekday(new Date());
console.log(formattedDate);
// Output:
// "15.01."
// "Mo"

// In React Component
const DateDisplay = ({ date }: { date: Date }) => (
  <div className="text-center">
    {formatDateWithWeekday(date).split('\n').map((line, i) => (
      <div key={i}>{line}</div>
    ))}
  </div>
);
```

### CSS Utils Usage:
```typescript
import { cn } from '@/lib/utils';

// In React Components
const Button = ({ className, variant, ...props }) => (
  <button 
    className={cn(
      'px-4 py-2 rounded-md font-medium',
      variant === 'primary' && 'bg-blue-500 text-white',
      variant === 'secondary' && 'bg-gray-200 text-gray-900',
      className
    )}
    {...props}
  />
);
```

### Legacy DB Usage:
```typescript
import { getDb, DispatchData } from '@/lib/db';

const db = await getDb();
const data = await db.all<DispatchData[]>(
  'SELECT * FROM dispatch_data WHERE monat = ?', 
  [1]
);
```

---

## 📊 Datenmodelle & Schnittstellen

### DispatchData Interface:
```typescript
interface DispatchData {
  id: number;                    // Primärschlüssel
  datum: string;                 // Datum im ISO-Format
  tag: number;                   // Tag des Monats
  monat: number;                 // Monat (1-12)
  kw: number;                    // Kalenderwoche
  jahr: number;                  // Jahr
  servicegrad: number;           // Servicegrad in %
  ausgeliefert_lup: number;      // Ausgelieferte LUP
  rueckstaendig: number;         // Rückständige Aufträge
  produzierte_tonnagen: number;  // Produzierte Tonnagen
  direktverladung_kiaa: number;  // Direktverladung KIAA
  umschlag: number;              // Umschlag
  kg_pro_colli: number;          // Kilogramm pro Colli
  elefanten: number;             // Elefanten-Kennzahl
  atrl: number;                  // ATRL-Wert
  aril: number;                  // ARIL-Wert
  fuellgrad_aril: number;        // Füllgrad ARIL in %
  qm_angenommen: number;         // QM angenommen
  qm_abgelehnt: number;          // QM abgelehnt
  qm_offen: number;              // QM offen
  mitarbeiter_std: number;       // Mitarbeiterstunden
}
```

### Database Schema:
```sql
CREATE TABLE dispatch_data (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  datum TEXT NOT NULL,
  tag INTEGER,
  monat INTEGER,
  kw INTEGER,
  jahr INTEGER,
  servicegrad REAL,
  ausgeliefert_lup INTEGER,
  rueckstaendig INTEGER,
  produzierte_tonnagen REAL,
  direktverladung_kiaa REAL,
  umschlag REAL,
  kg_pro_colli REAL,
  elefanten INTEGER,
  atrl INTEGER,
  aril INTEGER,
  fuellgrad_aril REAL,
  qm_angenommen REAL,
  qm_abgelehnt REAL,
  qm_offen REAL,
  mitarbeiter_std REAL
);
```

---

## 🧪 Testing

### Vorhandene Tests:
- **Unit Tests:** Vitest-basierte Tests für Utility-Funktionen
- **Integration Tests:** Playwright für Electron-Integration
- **Database Tests:** In-Memory SQLite für Datenbankoperationen

### Test-Ausführung:
```bash
# Unit Tests
pnpm test:unit

# E2E Tests
pnpm test:e2e

# Alle Tests
pnpm test:all

# Watch Mode
pnpm test:watch
```

### Testabdeckung:
- **Ziel:** ≥ 85% Coverage
- **Aktuell:** Tests für kritische Pfade implementiert

---

## 🛠️ Entwicklungshinweise

### Best Practices für Erweiterungen:

1. **Neue Utility-Funktionen:**
   ```typescript
   // ✅ Gut: Typsicher und dokumentiert
   /**
    * Konvertiert Bytes in menschenlesbare Größe
    * @param bytes - Anzahl der Bytes
    * @returns Formatierte Größe (z.B. "1.5 MB")
    */
   export function formatBytes(bytes: number): string {
     // Implementation...
   }
   ```

2. **Datenbankoperationen:**
   ```typescript
   // ✅ Gut: Verwende DatabaseManager für neue Operationen
   const result = await DatabaseManager.query(
     'SELECT * FROM table WHERE condition = ?',
     [parameter]
   );
   
   // ❌ Schlecht: Direkte sqlite3-Verwendung
   ```

3. **CSS-Utilities:**
   ```typescript
   // ✅ Gut: Verwende cn() für bedingte Klassen
   className={cn(
     'base-classes',
     condition && 'conditional-classes',
     props.className
   )}
   ```

### Häufige Fehlerquellen:

1. **Datenbankverbindung nicht geschlossen:**
   ```typescript
   // ✅ Immer Verbindung schließen
   try {
     await DatabaseManager.connect(dbPath);
     // Operationen...
   } finally {
     DatabaseManager.close();
   }
   ```

2. **Datumsformatierung ohne Fehlerbehandlung:**
   ```typescript
   // ✅ Immer try-catch verwenden
   try {
     return formatDateWithWeekday(dateValue);
   } catch (error) {
     return 'Ungültiges Datum';
   }
   ```

### TODOs & Verbesserungspotentiale:

- [ ] **Migration:** `db.ts` zu `DatabaseManager.ts` migrieren
- [ ] **Testing:** Umfassende Unit-Tests für alle Utility-Funktionen
- [ ] **Performance:** Connection Pooling für DatabaseManager
- [ ] **Typisierung:** Strikte Typen für SQL-Query-Results
- [ ] **Logging:** Strukturiertes Logging für Datenbankoperationen
- [ ] **Validation:** Zod-Schemas für Datenbankmodelle

### Coding-Standards:

- **Funktionen:** Immer JSDoc-Kommentare
- **Fehlerbehandlung:** Try-catch für alle externen Operationen
- **Typisierung:** Strikte TypeScript-Typen
- **Naming:** Beschreibende Funktions- und Variablennamen
- **Exports:** Named Exports bevorzugen (außer bei Klassen)

---

## 🔄 Migration & Wartung

### Geplante Änderungen:
1. **Drizzle ORM Integration:** Ersetzung der direkten SQLite-Calls
2. **Zod Validation:** Schema-Validierung für alle Datenmodelle
3. **React Query Integration:** Caching für Datenbankabfragen

### Wartungshinweise:
- **Datenbankschema-Änderungen:** Immer Migrations-Skripte erstellen
- **Breaking Changes:** Semantic Versioning beachten
- **Performance Monitoring:** Datenbankabfragen regelmäßig optimieren

---

*Diese README wurde automatisch generiert und sollte bei Änderungen am Code aktualisiert werden.*