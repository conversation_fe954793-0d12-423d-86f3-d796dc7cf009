"use strict";
/**
 * Chat-Routen für die direkte Kommunikation mit dem KI-Assistenten
 *
 * Diese Routen sind unter /api/chat verfügbar und leiten direkt an die KI-Dienste weiter.
 * Implementiert mit Drizzle ORM - Migration von Prisma abgeschlossen.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const zod_1 = require("zod");
const validation_middleware_1 = require("../middleware/validation.middleware");
const rate_limiting_middleware_1 = require("../middleware/rate-limiting.middleware");
const openrouter_service_1 = __importDefault(require("../services/openrouter.service"));
const router = express_1.default.Router();
// Validierungsschema für Chat-Anfragen
const chatRequestSchema = zod_1.z.object({
    message: zod_1.z.string().min(1, "Nachricht darf nicht leer sein").max(1000, "Nachricht zu lang"),
    includeInsights: zod_1.z.boolean().optional().default(false),
    includeAnomalies: zod_1.z.boolean().optional().default(false)
});
/**
 * POST /api/chat
 * Einfacher Chat-Endpunkt für direkte KI-Kommunikation
 * Identisch mit /api/ai/chat für Kompatibilität
 */
router.post('/', rate_limiting_middleware_1.rateLimitConfig.general, (0, validation_middleware_1.createValidationMiddleware)({ body: chatRequestSchema }), async (req, res) => {
    try {
        const { message } = req.body;
        console.log(`📝 [CHAT] Anfrage erhalten: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
        // Anfrage an OpenRouter-Service weiterleiten
        const response = await openrouter_service_1.default.generateResponse({
            message,
            includeInsights: false,
            includeAnomalies: false
        });
        console.log(`✅ [CHAT] Antwort gesendet für: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [CHAT] Fehler:', error);
        res.status(500).json({
            success: false,
            error: 'Interner Serverfehler',
            message: 'Bei der Verarbeitung der Chat-Anfrage ist ein Fehler aufgetreten.'
        });
    }
});
/**
 * POST /api/chat/enhanced
 * Erweiterter Chat-Endpunkt mit Insights und Anomalien
 * Identisch mit /api/ai/chat/enhanced für Kompatibilität
 */
router.post('/enhanced', rate_limiting_middleware_1.rateLimitConfig.general, (0, validation_middleware_1.createValidationMiddleware)({ body: chatRequestSchema }), async (req, res) => {
    try {
        const { message, includeInsights, includeAnomalies } = req.body;
        console.log(`📝 [CHAT-ENHANCED] Anfrage erhalten: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}" (Insights: ${includeInsights}, Anomalien: ${includeAnomalies})`);
        // Anfrage an OpenRouter-Service weiterleiten
        const response = await openrouter_service_1.default.generateResponse({
            message,
            includeInsights,
            includeAnomalies
        });
        console.log(`✅ [CHAT-ENHANCED] Antwort gesendet für: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
        res.json(response);
    }
    catch (error) {
        console.error('❌ [CHAT-ENHANCED] Fehler:', error);
        res.status(500).json({
            success: false,
            error: 'Interner Serverfehler',
            message: 'Bei der Verarbeitung der erweiterten Chat-Anfrage ist ein Fehler aufgetreten.'
        });
    }
});
/**
 * GET /api/chat/status
 * Status-Endpunkt zur Überprüfung der Chat-Dienstverfügbarkeit
 */
router.get('/status', (req, res) => {
    res.json({
        success: true,
        message: 'Chat-Dienst ist verfügbar',
        timestamp: new Date().toISOString(),
        endpoints: [
            'POST /api/chat - Einfacher Chat',
            'POST /api/chat/enhanced - Erweiterter Chat mit Insights'
        ]
    });
});
exports.default = router;
