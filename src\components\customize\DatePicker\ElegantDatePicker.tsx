// ElegantDatePicker.tsx
import React, { useState, useEffect, useRef } from 'react';
import { CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { RippleButton } from './ripple-button';
import './ElegantDatePicker.css';

interface DateRange {
  start: Date | null;
  end: Date | null;
}

interface ElegantDatePickerProps {
  // Single date picker props
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  
  // Range picker props (if provided, component works as range picker)
  rangeValue?: { from?: Date; to?: Date };
  onRangeChange?: (range: { from?: Date; to?: Date } | undefined) => void;
  
  // Common props
  placeholder?: string;
  label?: string;
  description?: string; // Neue Beschreibung
  className?: string;
  showHeader?: boolean;
  title?: string; // Neue Überschrift für den Kalender
  headerText?: string; // Text über der gesamten Card
  headerDescription?: string; // Beschreibung über der Card
}

const ElegantDatePicker: React.FC<ElegantDatePickerProps> = ({
  value,
  onChange,
  rangeValue,
  onRangeChange,
  placeholder = "Datum auswählen",
  label = "Datumsauswahl",
  description,
  title,
  className,
  showHeader = true,
  headerText,
  headerDescription
}) => {
  const isRangePicker = !!onRangeChange;
  const [calendarPosition, setCalendarPosition] = useState<string>('');
  
  // Convert props to internal state
  const getInitialRange = (): DateRange => {
    if (isRangePicker && rangeValue) {
      return { start: rangeValue.from || null, end: rangeValue.to || null };
    } else if (!isRangePicker && value) {
      return { start: value, end: null };
    }
    return { start: null, end: null };
  };
  
  // Initialize currentDate based on selected date or today
  const getInitialCurrentDate = (): Date => {
    const initialRange = getInitialRange();
    if (initialRange.start) {
      return new Date(initialRange.start);
    }
    return new Date();
  };
  
  const [currentDate, setCurrentDate] = useState<Date>(getInitialCurrentDate());
  const [selectedRange, setSelectedRange] = useState<DateRange>(getInitialRange());
  const [isCalendarOpen, setIsCalendarOpen] = useState<boolean>(false);
  const calendarRef = useRef<HTMLDivElement>(null);

  // Update internal state when props change
  useEffect(() => {
    const newRange = getInitialRange();
    setSelectedRange(newRange);
    
    // Update currentDate to show the month of the selected date
    if (newRange.start) {
      setCurrentDate(new Date(newRange.start));
    }
  }, [value, rangeValue, isRangePicker]);

  // Calculate calendar position when opening
  const calculateCalendarPosition = () => {
    if (calendarRef.current) {
      const rect = calendarRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const calendarWidth = 700; // Default calendar width
      
      // Check if calendar would overflow on the right
      if (rect.right + calendarWidth > viewportWidth) {
        // Check if we have space on the left
        if (rect.left - calendarWidth >= 0) {
          setCalendarPosition('adjust-left');
        } else {
          // Center it if no space on either side
          setCalendarPosition('adjust-center');
        }
      } else {
        // Default: align to the right of the input
        setCalendarPosition('');
      }
    }
  };

  // Open calendar with position calculation
  const toggleCalendar = () => {
    if (!isCalendarOpen) {
      calculateCalendarPosition();
    }
    setIsCalendarOpen(!isCalendarOpen);
  };

  // Schließe Kalender beim Klick außerhalb
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
        setIsCalendarOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Formatierung des Datums
  const formatDate = (date: Date | null): string => {
    if (!date) return '';
    return date.toLocaleDateString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Navigiere zum vorherigen Monat
  const goToPreviousMonth = () => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(newDate.getMonth() - 1);
      return newDate;
    });
  };

  // Navigiere zum nächsten Monat
  const goToNextMonth = () => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(newDate.getMonth() + 1);
      return newDate;
    });
  };

  // Setze das heutige Datum
  const selectToday = () => {
    const today = new Date();
    setSelectedRange({ start: today, end: null });
    // Navigiere zum aktuellen Monat
    setCurrentDate(today);
  };

  // Setze die Auswahl zurück
  const resetSelection = () => {
    setSelectedRange({ start: null, end: null });
  };

  // Akzeptiere die Auswahl
  const acceptSelection = () => {
    if (isRangePicker && onRangeChange) {
      onRangeChange(selectedRange.start && selectedRange.end ? {
        from: selectedRange.start,
        to: new Date(
          selectedRange.end.getFullYear(),
          selectedRange.end.getMonth(),
          selectedRange.end.getDate(),
          23, 59, 59, 999 // Ende des Tages
        )
      } : undefined);
    } else if (!isRangePicker && onChange) {
      onChange(selectedRange.start || undefined);
    }
    setIsCalendarOpen(false);
  };

  // Wähle ein Datum aus
  const selectDate = (date: Date) => {
    if (isRangePicker) {
      setSelectedRange(prev => {
        if (!prev.start || (prev.start && prev.end)) {
          // Neuen Bereich beginnen
          return { start: date, end: null };
        } else if (date >= prev.start) {
          // Bereich beenden
          return { start: prev.start, end: date };
        } else {
          // Bereich neu beginnen (wenn Enddatum vor Startdatum gewählt wird)
          return { start: date, end: null };
        }
      });
    } else {
      // Single date selection
      setSelectedRange({ start: date, end: null });
    }
  };

  // Generiere die Tage für einen Monat
  const generateMonthDays = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    
    // Ersten Tag des Monats und den Wochentag ermitteln
    const firstDay = new Date(year, month, 1);
    const firstDayIndex = firstDay.getDay() === 0 ? 6 : firstDay.getDay() - 1;
    
    // Letzten Tag des Monats ermitteln
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    
    // Tage des Vormonats
    const prevMonthLastDay = new Date(year, month, 0).getDate();
    const days = [];
    
    // Tage des Vormonats
    for (let i = 0; i < firstDayIndex; i++) {
      const day = prevMonthLastDay - firstDayIndex + i + 1;
      days.push(
        <div key={`prev-${i}`} className="calendar-day other-month">
          {day}
        </div>
      );
    }
    
    // Tage des aktuellen Monats
    const today = new Date();
    for (let i = 1; i <= daysInMonth; i++) {
      const dayDate = new Date(year, month, i);
      const isToday = 
        today.getDate() === i && 
        today.getMonth() === month && 
        today.getFullYear() === year;
      
      const isSelectedStart = selectedRange.start && 
        dayDate.getTime() === selectedRange.start.getTime();
      
      const isSelectedEnd = selectedRange.end && 
        dayDate.getTime() === selectedRange.end.getTime();
      
      const isInRange = selectedRange.start && selectedRange.end && 
        dayDate >= selectedRange.start && dayDate <= selectedRange.end;
      
      const dayClass = `calendar-day current-month ${isToday ? 'today' : ''} ${
        isSelectedStart || isSelectedEnd ? 'selected' : ''
      } ${isInRange ? 'in-range' : ''}`;
      
      days.push(
        <div 
          key={`current-${i}`} 
          className={dayClass}
          onClick={() => selectDate(dayDate)}
        >
          {i}
        </div>
      );
    }
    
    // Tage des nächsten Monats
    const daysNeeded = 42 - (firstDayIndex + daysInMonth);
    for (let i = 1; i <= daysNeeded; i++) {
      days.push(
        <div key={`next-${i}`} className="calendar-day other-month">
          {i}
        </div>
      );
    }
    
    return days;
  };

  // Bereite den nächsten Monat vor
  const nextMonth = new Date(currentDate);
  nextMonth.setMonth(nextMonth.getMonth() + 1);

  const formatDisplayValue = () => {
    if (isRangePicker) {
      if (selectedRange.start && selectedRange.end) {
        return `${formatDate(selectedRange.start)} – ${formatDate(selectedRange.end)}`;
      } else if (selectedRange.start) {
        return formatDate(selectedRange.start);
      }
      return placeholder || "Zeitraum auswählen";
    } else {
      return selectedRange.start ? formatDate(selectedRange.start) : (placeholder || "Datum auswählen");
    }
  };

  return (
    <div className={cn("elegant-datepicker-container", className)}>
      {/* Nur der einfache Text wie gewünscht */}
      <div className="text-sm -mb-3 ml-2" style={{color: '#1e293b'}}>
        Datum auswählen
      </div>
        <div className="date-input-container">
          {description && (
            <p className="text-sm text-gray-600 mb-2">{description}</p>
          )}
          <div className="input-wrapper">
            <input
              type="text"
              className="date-input"
              placeholder={isRangePicker ? "tt.mm.jjjj – tt.mm.jjjj" : "tt.mm.jjjj"}
              readOnly
              value={formatDisplayValue()}
              onClick={toggleCalendar}
            />
            <div className="input-icon">
              <CalendarIcon className="h-5 w-5" />
            </div>
          </div>
          
          {isCalendarOpen && (
            <div className={`calendar-container ${calendarPosition}`} ref={calendarRef}>
              <div className="calendar">
                {title && (
                  <div className="calendar-custom-header">
                    <h3 className="calendar-custom-title">{title}</h3>
                  </div>
                )}
                <div className="calendar-header">
                  <button className="calendar-nav prev" onClick={goToPreviousMonth}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="15,18 9,12 15,6"></polyline>
                    </svg>
                  </button>
                  <h3 className="calendar-title">
                    {currentDate.toLocaleDateString('de-DE', { month: 'long' })} –{' '}
                    {nextMonth.toLocaleDateString('de-DE', { month: 'long' })} {currentDate.getFullYear()}
                  </h3>
                  <button className="calendar-nav next" onClick={goToNextMonth}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                  </button>
                </div>
                
                <div className="calendar-months">
                  <div className="calendar-month">
                    <div className="calendar-month-title">
                      {currentDate.toLocaleDateString('de-DE', { month: 'long', year: 'numeric' })}
                    </div>
                    <div className="calendar-grid">
                      {['Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa', 'So'].map(day => (
                        <div key={day} className="calendar-day-header">
                          {day}
                        </div>
                      ))}
                      {generateMonthDays(currentDate)}
                    </div>
                  </div>
                  
                  <div className="calendar-month">
                    <div className="calendar-month-title">
                      {nextMonth.toLocaleDateString('de-DE', { month: 'long', year: 'numeric' })}
                    </div>
                    <div className="calendar-grid">
                      {['Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa', 'So'].map(day => (
                        <div key={day} className="calendar-day-header">
                          {day}
                        </div>
                      ))}
                      {generateMonthDays(nextMonth)}
                    </div>
                  </div>
                </div>
                
                <div className="selected-range">
                  {isRangePicker ? (
                    selectedRange.start && selectedRange.end
                      ? `Ausgewählter Bereich: ${formatDate(selectedRange.start)} - ${formatDate(selectedRange.end)}`
                      : selectedRange.start
                      ? `Ausgewählter Bereich: ${formatDate(selectedRange.start)} - ...`
                      : 'Ausgewählter Bereich: Bitte wählen Sie ein Start- und Enddatum'
                  ) : (
                    selectedRange.start
                      ? `Ausgewähltes Datum: ${formatDate(selectedRange.start)}`
                      : 'Ausgewähltes Datum: Bitte wählen Sie ein Datum'
                  )}
                </div>
                
                <div className="calendar-actions">
                  <RippleButton 
                    className="calendar-btn reset" 
                    onClick={resetSelection}
                    rippleColor="#ffffff"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="1,4 1,10 7,10"></polyline>
                      <path d="M3.51,15a9,9,0,0,0,2.13,3.09A9.06,9.06,0,0,0,21,12a9,9,0,0,0-18,0l4,0"></path>
                    </svg>
                    Zurücksetzen
                  </RippleButton>
                  <RippleButton 
                    className="calendar-btn today" 
                    onClick={selectToday}
                    rippleColor="#ffffff"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                    Heute
                  </RippleButton>
                  <RippleButton 
                    className="calendar-btn accept" 
                    onClick={acceptSelection}
                    rippleColor="#10b981"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20,6 9,17 4,12"></polyline>
                    </svg>
                    Akzeptieren
                  </RippleButton>
                </div>
              </div>
            </div>
          )}
        </div>
    </div>
  );
};

export default ElegantDatePicker;