const { Client } = require('pg');
const dotenv = require('dotenv');

// Lade Umgebungsvariablen
dotenv.config();

const client = new Client({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'leitstand_dashboard',
  user: process.env.DB_USER || 'leitstand_dashboard',
  password: process.env.DB_PASSWORD || 'leitstand_dashboard'
});

async function debugWETable() {
  try {
    await client.connect();
    console.log('✅ Datenbankverbindung erfolgreich');
    
    // Prüfe, ob WE-Tabelle existiert
    const tableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'WE'
      );
    `);
    
    console.log('WE-Tabelle existiert:', tableExists.rows[0].exists);
    
    if (tableExists.rows[0].exists) {
      // Zeige Spaltenstruktur
      const columns = await client.query(`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'WE' 
        AND table_schema = 'public'
        ORDER BY ordinal_position;
      `);
      
      console.log('\nSpalten der WE-Tabelle:');
      columns.rows.forEach(col => {
        console.log(`- ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
      });
      
      // Versuche eine einfache Abfrage
      const sampleData = await client.query('SELECT * FROM "WE" LIMIT 1');
      console.log('\nBeispieldaten:', sampleData.rows);
    }
    
  } catch (error) {
    console.error('❌ Fehler:', error.message);
  } finally {
    await client.end();
  }
}

debugWETable();