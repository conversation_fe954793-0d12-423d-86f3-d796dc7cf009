/**
 * Dispatch Data Repository Interface
 * 
 * Spezifische Repository-Schnittstelle für Dispatch-/Versanddaten
 * mit optimierten Methoden für häufige Abfragen.
 */

import { BaseRepository, FindAllOptions } from './base.repository.interface';
import {
  ServiceLevelDataPoint,
  DailyPerformanceDataPoint,
  PickingDataPoint,
  DeliveryPositionsDataPoint,
  TagesleistungDataPoint
} from '../../types/database.types';

export interface DispatchDataEntity {
  id: number;
  datum: string | null; // Datum als String im Format YYYY-MM-DD
  servicegrad: number | null;
  produzierte_tonnagen: number | null;
  atrl: number | null;
  aril: number | null;
  fuellgrad_aril: number | null;
  qm_angenommen: number | null;
  qm_abgelehnt: number | null;
  qm_offen: number | null;
  ausgeliefert_lup: number | null;
  rueckstaendig: number | null;
  direktverladung_kiaa: number | null;
  umschlag: number | null;
  kg_pro_colli: number | null;
  elefanten: number | null;
}

export interface DispatchRepository extends BaseRepository<DispatchDataEntity> {
  /**
   * Service Level Daten für Dashboard-Charts
   */
  getServiceLevelData(dateRange?: DateRange): Promise<ServiceLevelDataPoint[]>;

  /**
   * Tägliche Performance-Daten
   */
  getDailyPerformanceData(dateRange?: DateRange): Promise<DailyPerformanceDataPoint[]>;

  /**
   * Kommissionierungsdaten
   */
  getPickingData(dateRange?: DateRange): Promise<PickingDataPoint[]>;

  /**
   * Lieferpositionsdaten
   */
  getDeliveryPositionsData(dateRange?: DateRange): Promise<DeliveryPositionsDataPoint[]>;

  /**
   * Tagesleistungsdaten
   */
  getTagesleistungData(dateRange?: DateRange): Promise<TagesleistungDataPoint[]>;

  /**
   * Retourendaten aggregiert
   */
  getReturnsData(): Promise<Array<{ name: string; value: number }>>;

  /**
   * Performance-Metriken für einen bestimmten Zeitraum
   */
  getPerformanceMetrics(dateRange: DateRange): Promise<PerformanceMetrics>;

  /**
   * Top-Performance-Tage ermitteln
   */
  getTopPerformanceDays(limit?: number): Promise<Array<{
    datum: string; // Datum als string für Konsistenz
    produzierte_tonnagen: number;
    servicegrad: number;
  }>>;
}

/**
 * Datum-Bereich für Abfragen
 */
export interface DateRange {
  startDate?: string;
  endDate?: string;
}

/**
 * Performance-Metriken
 */
export interface PerformanceMetrics {
  totalTonnage: number;
  averageServiceLevel: number;
  totalDeliveries: number;
  averagePickingRate: number;
  period: {
    startDate: string;
    endDate: string;
    days: number;
  };
}