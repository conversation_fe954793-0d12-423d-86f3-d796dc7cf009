import React, { useState } from "react";
import { DateRange } from "react-day-picker";
import { ArilDataChart } from "@/modules/dashboard/components/charts/ArilDataChart";
import { Lagerauslastung240Chart } from "@/modules/dashboard/components/charts/Lagerauslastung240Chart";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Shell, FileSpreadsheet, FileText } from "lucide-react";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import { AskJaszButton } from "@/modules/ai/components";
import { createPageContext } from "@/contexts/AskJaszContext";
import { ExportService } from "@/services/export.service";
import { collectArilChartData } from "@/services/chart-data-collector.service";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

/**
 * ARiL-Bereich Dashboard
 * 
 * Zeigt verschiedene Kennzahlen für den ARiL-Bereich an:
 * - ARiL-Daten als kombiniertes Diagramm mit gestapelten Balken
 * - Cutting Lager (Kunde/Rest), WaTa Positionen, Umlagerungen
 * - Lager Cutting-Daten
 */
export default function ArilPage() {
  // Zustand für den ausgewählten Datumsbereich - Standard: Zeitraum mit verfügbaren Daten
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 5, 1), // 1. Juni 2025 (Daten verfügbar ab Juni)
    to: new Date(2025, 6, 15), // 15. Juli 2025 (Daten verfügbar bis August)
  });

  // Zustand für Export-Loading
  const [isExporting, setIsExporting] = useState<{ excel: boolean; powerpoint: boolean }>({
    excel: false,
    powerpoint: false
  });

  // Export-Funktionen
  const handleExcelExport = async () => {
    if (!dateRange?.from || !dateRange?.to) {
      toast.error('Bitte wählen Sie einen Datumsbereich aus');
      return;
    }

    setIsExporting(prev => ({ ...prev, excel: true }));

    try {
      const chartData = await collectArilChartData(dateRange);

      if (chartData.length === 0) {
        toast.error('Keine Daten für den Export verfügbar');
        return;
      }

      await ExportService.exportToExcel(chartData, {
        dateRange,
        includeSummary: true,
        dashboardType: 'aril'
      });

      toast.success('Excel-Datei wurde erfolgreich erstellt');
    } catch (error) {
      console.error('Excel-Export Fehler:', error);
      toast.error('Fehler beim Erstellen der Excel-Datei');
    } finally {
      setIsExporting(prev => ({ ...prev, excel: false }));
    }
  };

  const handlePowerPointExport = async () => {
    if (!dateRange?.from || !dateRange?.to) {
      toast.error('Bitte wählen Sie einen Datumsbereich aus');
      return;
    }

    setIsExporting(prev => ({ ...prev, powerpoint: true }));

    try {
      const chartData = await collectArilChartData(dateRange);

      if (chartData.length === 0) {
        toast.error('Keine Daten für den Export verfügbar');
        return;
      }

      await ExportService.exportToPowerPoint(chartData, {
        dateRange,
        includeSummary: true,
        dashboardType: 'aril'
      });

      toast.success('PowerPoint-Datei wurde erfolgreich erstellt');
    } catch (error) {
      console.error('PowerPoint-Export Fehler:', error);
      toast.error('Fehler beim Erstellen der PowerPoint-Datei');
    } finally {
      setIsExporting(prev => ({ ...prev, powerpoint: false }));
    }
  };

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Shell className="h-10 w-10 mr-2" />
          <h1 className="text-4xl font-bold text-black">AUTOMATISCHES RINGLAGER</h1>
          <div className="-mt-7">
            <AskJaszButton
              context={createPageContext(
                "ARiL Dashboard",
                [
                  "Automatisches Ringlager Übersicht",
                  "Lagerauslastung und -bewegungen",
                  "Cutting Lager (Kunde/Rest) Daten",
                  "WaTa Positionen und Umlagerungen"
                ],
                "ARiL Bereich Performance Dashboard"
              )}
              position="inline"
              size="md"
              variant="default"
              tooltip="Hilfe zum ARiL Dashboard erhalten"
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex gap-2">
            <Button
              onClick={handleExcelExport}
              disabled={isExporting.excel}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <img src="/src/assets/excel-pn.png" alt="Excel Export" className="h-8 w-8" />
            </Button>
            <Button
              onClick={handlePowerPointExport}
              disabled={isExporting.powerpoint}
              variant="ghost"
              className="flex items-center gap-2"
            >
              <img src="/src/assets/pp.png" alt="PowerPoint Export" className="h-8 w-8" />
            </Button>
          </div>
          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
            label="Datumsauswahl"
          />
        </div>
      </div>
      
      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 gap-6">
          {/* ATrL-Daten Chart */}
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <ArilDataChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>

          {/* Lagerauslastung Chart */}
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <Lagerauslastung240Chart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
} 