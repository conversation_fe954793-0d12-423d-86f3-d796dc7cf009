"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dashboardController = exports.DashboardController = void 0;
const dashboardService_1 = require("../services/dashboardService");
class DashboardController {
    /**
     * GET /api/dashboard/service-level
     * Holt ServiceGrad-Daten für Dashboard-Charts
     */
    async getServiceLevelData(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const data = await dashboardService_1.dashboardService.getServiceLevelData(startDate, endDate);
            res.json({
                success: true,
                data,
                count: data.length
            });
        }
        catch (error) {
            console.error('DashboardController: <PERSON><PERSON> beim <PERSON>den der ServiceGrad-Daten:', error);
            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * GET /api/dashboard/delivery-positions
     * Holt Lieferpositionen-Daten für Dashboard-Charts
     */
    async getDeliveryPositionsData(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const data = await dashboardService_1.dashboardService.getDeliveryPositionsData(startDate, endDate);
            res.json({
                success: true,
                data,
                count: data.length
            });
        }
        catch (error) {
            console.error('DashboardController: Fehler beim Laden der Lieferpositionen-Daten:', error);
            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * GET /api/dashboard/tagesleistung
     * Holt Tagesleistung-Daten für Dashboard-Charts
     */
    async getTagesleistungData(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const data = await dashboardService_1.dashboardService.getTagesleistungData(startDate, endDate);
            res.json({
                success: true,
                data,
                count: data.length
            });
        }
        catch (error) {
            console.error('DashboardController: Fehler beim Laden der Tagesleistung-Daten:', error);
            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * GET /api/dashboard/picking
     * Holt Picking-Daten für Dashboard-Charts
     */
    async getPickingData(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const data = await dashboardService_1.dashboardService.getPickingData(startDate, endDate);
            res.json({
                success: true,
                data,
                count: data.length
            });
        }
        catch (error) {
            console.error('DashboardController: Fehler beim Laden der Picking-Daten:', error);
            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * GET /api/dashboard/all
     * Holt alle Dashboard-Daten in einer optimierten Abfrage
     */
    async getAllDashboardData(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const data = await dashboardService_1.dashboardService.getAllDashboardData(startDate, endDate);
            res.json({
                success: true,
                data,
                counts: {
                    serviceLevel: data.serviceLevel.length,
                    deliveryPositions: data.deliveryPositions.length,
                    tagesleistung: data.tagesleistung.length,
                    picking: data.picking.length
                }
            });
        }
        catch (error) {
            console.error('DashboardController: Fehler beim Laden aller Dashboard-Daten:', error);
            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * GET /api/dashboard/statistics
     * Holt aggregierte Statistiken für Dashboard
     */
    async getDashboardStatistics(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const statistics = await dashboardService_1.dashboardService.getDashboardStatistics(startDate, endDate);
            res.json({
                success: true,
                data: statistics
            });
        }
        catch (error) {
            console.error('DashboardController: Fehler beim Berechnen der Statistiken:', error);
            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * GET /api/dashboard/health
     * Prüft die Verfügbarkeit des Dashboard-Services
     */
    async getHealth(req, res) {
        try {
            // Einfache Gesundheitsprüfung - versuche Daten zu laden
            const data = await dashboardService_1.dashboardService.getServiceLevelData();
            res.json({
                success: true,
                status: 'healthy',
                message: 'Dashboard-Service ist verfügbar',
                dataAvailable: data.length > 0
            });
        }
        catch (error) {
            console.error('DashboardController: Gesundheitsprüfung fehlgeschlagen:', error);
            res.status(503).json({
                success: false,
                status: 'unhealthy',
                error: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
}
exports.DashboardController = DashboardController;
exports.dashboardController = new DashboardController();
