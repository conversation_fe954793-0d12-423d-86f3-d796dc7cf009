import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { X, Save, BookOpen, Plus, Trash2, ChevronUp, ChevronDown } from 'lucide-react';
import { Runbook, RunbookCreateData, RunbookUpdateData, RunbookStep } from '@/types/runbooks.types';
import { runbookService } from '@/services/runbookService';
import { useToast } from "@/components/ui/use-toast";
import stoerungKategorienService from '@/services/stoerung-kategorien.service';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface RunbookEditorProps {
  runbook: Runbook | null;
  onClose: () => void;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: () => void;
}

export const RunbookEditor: React.FC<RunbookEditorProps> = ({ runbook, onClose, open, onOpenChange, onSubmit }) => {
  const [formData, setFormData] = useState<Partial<RunbookCreateData & { category?: string[] }>>({});
  const [steps, setSteps] = useState<RunbookStep[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Kategorien-Management (wie in StoerungsEditForm)
  const [kategorien, setKategorien] = useState<any[]>([]);
  const [kategorienLoading, setKategorienLoading] = useState(true);
  const [kategorienError, setKategorienError] = useState<string | null>(null);
  const [affectedSystemOptions, setAffectedSystemOptions] = useState<string[]>([]);

  // Toast für Erfolgsmeldungen
  const { toast } = useToast();

  const handleClose = () => {
    onOpenChange(false);
    // Reset error state when closing
    setError(null);
  };

  // Lade Kategorien beim ersten Rendern
  useEffect(() => {
    const loadKategorien = async () => {
      try {
        setKategorienLoading(true);
        console.log('[DEBUG] Lade Kategorien...');
        const data = await stoerungKategorienService.getKategorien();
        console.log('[DEBUG] Kategorien geladen:', data);
        console.log('[DEBUG] Kategorien-Details:', data?.map((k: any) => ({
          name: k.name,
          systemeType: Array.isArray(k.systeme) ? 'array' : typeof k.systeme,
          systemeLength: Array.isArray(k.systeme) ? k.systeme.length : 'N/A',
          systemeContent: Array.isArray(k.systeme) ? k.systeme : 'N/A'
        })));
        setKategorien(data);
        setKategorienError(null);
      } catch (err) {
        console.error("Fehler beim Laden der Kategorien:", err);
        setKategorienError("Fehler beim Laden der Kategorien");

        // Fallback auf hardcoded Daten falls Service nicht verfügbar
        console.log('[DEBUG] Verwende Fallback-Kategorien');
        const fallbackData = [
          {
            name: "Systeme",
            systeme: ["SAP", "ITM", "Wamas", "Mail", "Citrix", "MFR"],
          },
          { name: "Infrastruktur", systeme: ["Netzwerk", "WLAN"] },
          {
            name: "Technische Komponenten",
            systeme: [
              "Förderband",
              "FTS",
              "RBG",
              "Greifer",
              "Schrumpfanlage",
              "Ablängmaschinen",
            ],
          },
          {
            name: "Kommissionierung",
            systeme: [
              "ATrL",
              "ARiL",
              "SCS",
            ],
          },
          {
            name: "Hardware",
            systeme: [
              "Mobile Drucker",
              "Label Drucker",
              "Lieferschein Drucker",
              "Terminal",
              "PC",
            ],
          },
        ];
        setKategorien(fallbackData);
      } finally {
        setKategorienLoading(false);
      }
    };

    loadKategorien();
  }, []);

  // Aktualisiere betroffene Systeme basierend auf gewählter Kategorie
  useEffect(() => {
    console.log('[DEBUG] Kategorien-Update:', {
      kategorienLength: kategorien?.length,
      formDataCategory: formData.category,
      formDataCategoryFirst: formData.category?.[0],
      affectedSystemOptionsLength: affectedSystemOptions.length
    });

    // Verwende formData.category?.[0] da category ein Array ist
    const selectedCategoryName = formData.category?.[0];

    if (selectedCategoryName && kategorien?.length > 0) {
      const selectedCategory = kategorien.find(
        (k) => k?.name === selectedCategoryName,
      );

      console.log('[DEBUG] Gewählte Kategorie:', selectedCategory);

      if (selectedCategory?.systeme) {
        // Debug: Zeige alle Systeme vor der Filterung
        console.log('[DEBUG] Alle Systeme in Kategorie:', selectedCategory.systeme);
        console.log('[DEBUG] Systeme-Typen:', selectedCategory.systeme.map((s: any) => ({ value: s, type: typeof s, isString: typeof s === "string", length: typeof s === "string" ? s.length : "N/A" })));

        // Verbesserte Filterung für betroffene Systeme
        const validSystems = selectedCategory.systeme.filter(
          (system: any) => {
            const isValid = system && (typeof system === "string" ? system.trim().length > 0 : true);
            console.log(`[DEBUG] System "${system}" valid: ${isValid} (type: ${typeof system}, trimmed: "${typeof system === "string" ? system.trim() : "N/A"}")`);
            return isValid;
          }
        ).map((system: any) => {
          // Stelle sicher, dass wir immer einen String haben
          if (typeof system === "string") {
            return system.trim();
          } else if (system && typeof system === "object" && system.name) {
            // Falls es ein Objekt mit name-Property ist, verwende den Namen
            return String(system.name).trim();
          } else {
            // Fallback: konvertiere zu String
            return String(system).trim();
          }
        }).filter((system: string) => system.length > 0);

        console.log('[DEBUG] Valide Systeme nach Filterung:', validSystems);

        // Fallback: Wenn keine validen Systeme gefunden wurden, verwende alle Systeme
        if (validSystems.length === 0 && selectedCategory.systeme.length > 0) {
          console.log('[DEBUG] Keine validen Systeme gefunden, verwende alle Systeme als Fallback');
          const fallbackSystems = selectedCategory.systeme.map((s: any) =>
            typeof s === "string" ? s : String(s)
          ).filter((s: string) => s.trim().length > 0);
          console.log('[DEBUG] Fallback-Systeme:', fallbackSystems);
          setAffectedSystemOptions(fallbackSystems);
        } else {
          setAffectedSystemOptions(validSystems);
        }
      } else {
        console.log('[DEBUG] Keine Systeme in Kategorie gefunden');
        setAffectedSystemOptions([]);
      }
    } else {
      // Wenn keine Kategorie gewählt ist, aber ein affected_system aus der DB existiert,
      // füge es zu den Optionen hinzu, damit es angezeigt werden kann
      if (
        formData.affected_systems &&
        formData.affected_systems.length > 0 &&
        formData.affected_systems[0]?.trim().length > 0
      ) {
        console.log('[DEBUG] Verwende bestehende Systeme aus DB:', formData.affected_systems);
        setAffectedSystemOptions(formData.affected_systems);
      } else {
        console.log('[DEBUG] Keine Systeme verfügbar - Kategorie:', selectedCategoryName, 'affected_systems:', formData.affected_systems);
        setAffectedSystemOptions([]);
      }
    }
  }, [formData.category, kategorien, formData.affected_systems]);

  useEffect(() => {
    if (open) {
      if (runbook) {
        setFormData({
          title: runbook.title,
          content: runbook.content,
          overallDescription: runbook.overallDescription,
          affected_systems: runbook.affected_systems,
          category: runbook.category || [],
        });
        // Always use step mode - load steps from database or parse from content
        if (runbook.steps && runbook.steps.length > 0) {
          setSteps(runbook.steps);
        } else {
          // Fallback: parse steps from content if no steps in database
          const parsedSteps = parseStepsFromContent(runbook.content || '');
          setSteps(parsedSteps.length > 0 ? parsedSteps : []);
        }
      } else {
        setFormData({
          title: '',
          affected_systems: [],
          category: [],
        });
        setSteps([]);
      }
    }
  }, [runbook, open]);

  // Helper function to parse steps from markdown content
  const parseStepsFromContent = (content: string): RunbookStep[] => {
    const steps: RunbookStep[] = [];
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const stepMatch = line.match(/^(\*\*)?Schritt (\d+):(\*\*)?(.*)/i);

      if (stepMatch) {
        const stepNumber = parseInt(stepMatch[2], 10);
        const description = stepMatch[4].trim();

        if (description) {
          steps.push({
            id: `step-${Date.now()}-${i}`,
            description: description,
            order: stepNumber
          });
        }
      }
    }

    return steps.sort((a, b) => a.order - b.order);
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    if (!formData.title?.trim()) {
      setError('Titel ist ein Pflichtfeld.');
      setLoading(false);
      return;
    }

    // Validate that at least one step is provided
    if (steps.length === 0) {
      setError('Mindestens ein Schritt muss definiert werden.');
      setLoading(false);
      return;
    }

    // Ensure arrays are properly initialized
    const finalFormData = {
      ...formData,
      affected_systems: formData.affected_systems || [],
      category: formData.category || [],
    };

    // Prepare data for saving - remove category as it's not part of RunbookCreateData
    const { category, ...saveData } = finalFormData;

    // Convert steps to markdown content for backwards compatibility
    let content = '';
    if (finalFormData.overallDescription && finalFormData.overallDescription.trim()) {
      content += `${finalFormData.overallDescription.trim()}\n\n`;
    }

    const stepsContent = steps
      .sort((a, b) => a.order - b.order)
      .map(step => `**Schritt ${step.order}:**\n${step.description || ''}`)
      .join('\n\n');

    if (stepsContent.trim()) {
      content += stepsContent;
    }

    // Only set content if it's not empty
    if (content.trim()) {
      saveData.content = content.trim();
    }

    // Always save steps as JSON array
    saveData.steps = steps;

    try {
      if (runbook) {
        await runbookService.updateRunbook(runbook.id, saveData as RunbookUpdateData);
        toast({
          title: 'Runbook aktualisiert',
          description: 'Das Runbook wurde erfolgreich aktualisiert.',
        });
      } else {
        await runbookService.createRunbook(saveData as RunbookCreateData);
        toast({
          title: 'Runbook erstellt',
          description: 'Das neue Runbook wurde erfolgreich erstellt.',
        });
      }

      // Modal schließen und Callback ausführen
      setTimeout(() => {
        onOpenChange(false);
        onSubmit();
      }, 1000);
    } catch (error) {
      console.error('Fehler beim Speichern des Runbooks:', error);
      setError('Fehler beim Speichern des Runbooks.');
      toast({
        title: 'Fehler beim Speichern',
        description: 'Das Runbook konnte nicht gespeichert werden. Bitte versuchen Sie es erneut.',
      });
    } finally {
      setLoading(false);
    }
  };


  const addStep = () => {
    const newStep: RunbookStep = {
      id: `step-${Date.now()}`,
      description: '',
      order: steps.length + 1,
    };
    setSteps([...steps, newStep]);
  };

  const removeStep = (stepId: string) => {
    setSteps(steps.filter(step => step.id !== stepId));
  };

  const updateStep = (stepId: string, value: string) => {
    setSteps(steps.map(step =>
      step.id === stepId ? { ...step, description: value } : step
    ));
  };

  const moveStep = (stepId: string, direction: 'up' | 'down') => {
    const currentIndex = steps.findIndex(step => step.id === stepId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= steps.length) return;

    const newSteps = [...steps];
    [newSteps[currentIndex], newSteps[newIndex]] = [newSteps[newIndex], newSteps[currentIndex]];

    // Update order numbers
    newSteps.forEach((step, index) => {
      step.order = index + 1;
    });

    setSteps(newSteps);
  };

  // No mode change needed - always use step mode

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-start justify-center pt-4 pb-4">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50"
        onClick={handleClose}
      />

      {/* Dialog Content */}
      <div className="relative bg-white rounded-lg shadow-lg max-w-2xl w-full mx-4 overflow-hidden flex flex-col max-h-[90vh]">
        {/* Header */}
        <div className="flex items-center justify-between py-2 px-6 border-b bg-gray-100 flex-shrink-0">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-blue-600" />
            {runbook ? 'Eintrag bearbeiten' : 'Neuen Eintrag erstellen'}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-6 overflow-y-auto flex-1 min-h-0">
          <form onSubmit={handleSave} className="space-y-4">
            {/* Error Message */}
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            )}

            {/* Titel */}
            <div className="space-y-2">
              <Label htmlFor="title">Titel *</Label>
              <Input
                id="title"
                value={formData.title || ''}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
                placeholder="Titel des Runbooks"
                required
              />
            </div>

            {/* Content - always step mode */}
            <div className="space-y-4">
              {/* Overall Description */}
              <div className="space-y-2">
                <Label htmlFor="overallDescription">Gesamtbeschreibung (Optional)</Label>
                <Textarea
                  id="overallDescription"
                  value={formData.overallDescription || ''}
                  onChange={(e) => setFormData({...formData, overallDescription: e.target.value})}
                  placeholder="Allgemeine Beschreibung oder Einleitung"
                  rows={4}
                />
              </div>

              {/* Steps Section */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label>Schritte</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addStep}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Schritt hinzufügen
                  </Button>
                </div>

                {steps.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                    <p>Noch keine Schritte definiert.</p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addStep}
                      className="mt-2"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Ersten Schritt hinzufügen
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {steps
                      .sort((a, b) => a.order - b.order)
                      .map((step, index) => (
                        <div key={step.id} className="border rounded-lg p-4 space-y-3 bg-gray-50">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-gray-900">Schritt {step.order}</h4>
                            <div className="flex gap-1">
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => moveStep(step.id, 'up')}
                                disabled={index === 0}
                                className="h-8 w-8 p-0"
                              >
                                <ChevronUp className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => moveStep(step.id, 'down')}
                                disabled={index === steps.length - 1}
                                className="h-8 w-8 p-0"
                              >
                                <ChevronDown className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeStep(step.id)}
                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor={`step-description-${step.id}`} className="text-sm font-medium">
                              Beschreibung
                            </Label>
                            <Textarea
                              id={`step-description-${step.id}`}
                              value={step.description}
                              onChange={(e) => updateStep(step.id, e.target.value)}
                              placeholder="Detaillierte Beschreibung des Schritts"
                              rows={3}
                              className="resize-none"
                            />
                          </div>
                        </div>
                      ))}
                  </div>
                )}
              </div>
            </div>

            {/* Kategorie und Betroffenes System */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Kategorie</Label>
                <Select
                  value={formData.category?.[0] || ''}
                  onValueChange={(value) => {
                    console.log('[DEBUG] Kategorie ausgewählt:', value);
                    setFormData({...formData, category: [value], affected_systems: []});
                  }}
                  disabled={kategorienLoading}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        kategorienLoading
                          ? "Lade Kategorien..."
                          : "Kategorie wählen"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {kategorien
                      ?.map((k) => k?.name)
                      .filter(
                        (name): name is string =>
                          name != null && typeof name === "string" && name.trim().length > 0,
                      )
                      .map((category, index) => {
                        // Stelle sicher, dass category ein String ist
                        const categoryString = typeof category === "string" ? category.trim() : String(category).trim();
                        return (
                          <SelectItem
                            key={`category-${index}-${categoryString}`}
                            value={categoryString}
                          >
                            {categoryString}
                          </SelectItem>
                        );
                      })}
                  </SelectContent>
                </Select>
                {kategorienError && (
                  <p className="text-sm text-yellow-600">
                    ⚠️ {kategorienError} - Verwende Fallback-Daten
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="affected_systems">Betroffenes System</Label>
                <Select
                  value={formData.affected_systems?.[0] || ''}
                  onValueChange={(value) => {
                    console.log('[DEBUG] System ausgewählt:', value);
                    setFormData({...formData, affected_systems: [value]});
                  }}
                  disabled={!formData.category?.[0] && affectedSystemOptions.length === 0 && !formData.affected_systems?.[0]}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        !formData.category?.[0] && affectedSystemOptions.length === 0 && !formData.affected_systems?.[0]
                          ? "Erst Kategorie wählen"
                          : "System wählen"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {/* Wenn ein affected_system aus der DB existiert, aber nicht in den aktuellen Optionen ist, füge es hinzu */}
                    {formData.affected_systems?.[0] &&
                      !affectedSystemOptions.includes(formData.affected_systems[0]) && (
                        <SelectItem
                          key={`current-system-${formData.affected_systems[0]}`}
                          value={formData.affected_systems[0]}
                        >
                          {typeof formData.affected_systems[0] === "string"
                            ? formData.affected_systems[0]
                            : String(formData.affected_systems[0])} (Aktuell)
                        </SelectItem>
                      )}
                    {affectedSystemOptions.map((system, index) => {
                      // Stelle sicher, dass system ein String ist
                      const systemString = typeof system === "string" ? system.trim() : String(system).trim();
                      return (
                        <SelectItem
                          key={`system-${index}-${systemString}`}
                          value={systemString}
                        >
                          {systemString}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Buttons */}
            <div className="flex gap-2 pt-4">
              <Button type="submit" variant="accept" disabled={loading} className="">
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Speichere...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Speichern
                  </>
                )}
              </Button>

              <Button type="button" variant="ghost" onClick={handleClose}>
                Abbrechen
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};