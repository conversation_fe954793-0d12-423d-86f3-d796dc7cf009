"use strict";
/**
 * Dispatch Data Integration Service
 *
 * Specialized service for querying and formatting Dispatch (Versand) data
 * for AI chatbot integration. Provides service level metrics, picking performance,
 * delivery statistics, tonnage data, and quality metrics with time-based filtering.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DispatchDataIntegrationService = void 0;
const db_1 = require("../db");
const dispatch_repository_1 = require("../repositories/dispatch.repository");
class DispatchDataIntegrationService {
    constructor(database = db_1.db) {
        this.dispatchRepo = new dispatch_repository_1.DispatchRepositoryImpl(database);
    }
    /**
     * Get comprehensive Dispatch data for AI chatbot context
     */
    async getDispatchDataForAI(options = {}) {
        const startTime = Date.now();
        try {
            console.log('🚚 [DISPATCH-INTEGRATION] Fetching comprehensive Dispatch data');
            // Execute parallel queries for better performance
            const [serviceLevel, performanceMetrics, pickingData, deliveryData, tonnageData, returnsData, topPerformanceDays, trends] = await Promise.all([
                options.includeServiceLevel !== false ? this.getServiceLevelData(options.timeRange) : Promise.resolve([]),
                this.getPerformanceMetrics(options.timeRange),
                options.includePickingData !== false ? this.getPickingData(options.timeRange) : Promise.resolve([]),
                options.includeDeliveryData !== false ? this.getDeliveryData(options.timeRange) : Promise.resolve([]),
                options.includeTonnageData !== false ? this.getTonnageData(options.timeRange) : Promise.resolve([]),
                options.includeReturnsData !== false ? this.getReturnsData() : Promise.resolve([]),
                options.includeTopPerformance !== false ? this.getTopPerformanceDays(options.maxDataPoints || 5) : Promise.resolve([]),
                options.includeTrends !== false ? this.getDispatchTrends(options.timeRange) : Promise.resolve(this.getEmptyTrends())
            ]);
            // Generate comprehensive summary
            const summary = this.formatDispatchSummary(serviceLevel, performanceMetrics, pickingData, deliveryData, tonnageData, returnsData, trends);
            const result = {
                serviceLevel,
                performanceMetrics,
                pickingData,
                deliveryData,
                tonnageData,
                returnsData,
                topPerformanceDays,
                trends,
                summary,
                timestamp: new Date()
            };
            console.log(`✅ [DISPATCH-INTEGRATION] Data fetched in ${Date.now() - startTime}ms`);
            return result;
        }
        catch (error) {
            console.error('❌ [DISPATCH-INTEGRATION] Error fetching Dispatch data:', error);
            throw new Error(`Failed to fetch Dispatch data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get service level data with optional time filtering
     */
    async getServiceLevelData(timeRange) {
        try {
            const data = await this.dispatchRepo.getServiceLevelData(timeRange);
            // Convert servicegrad to percentage and sort by date
            return data
                .map(item => ({
                ...item,
                servicegrad: item.servicegrad * 100 // Convert to percentage
            }))
                .sort((a, b) => new Date(a.datum).getTime() - new Date(b.datum).getTime());
        }
        catch (error) {
            console.error('❌ [DISPATCH-INTEGRATION] Error fetching service level data:', error);
            throw error;
        }
    }
    /**
     * Get performance metrics for the specified time range
     */
    async getPerformanceMetrics(timeRange) {
        try {
            // If no time range specified, use last 30 days
            const defaultTimeRange = timeRange || this.getDefaultTimeRange();
            return await this.dispatchRepo.getPerformanceMetrics(defaultTimeRange);
        }
        catch (error) {
            console.error('❌ [DISPATCH-INTEGRATION] Error fetching performance metrics:', error);
            throw error;
        }
    }
    /**
     * Get picking data with performance indicators
     */
    async getPickingData(timeRange) {
        try {
            const data = await this.dispatchRepo.getPickingData(timeRange);
            // Sort by date and ensure data quality
            return data
                .filter(item => item.atrl > 0 || item.aril > 0) // Filter out empty records
                .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        }
        catch (error) {
            console.error('❌ [DISPATCH-INTEGRATION] Error fetching picking data:', error);
            throw error;
        }
    }
    /**
     * Get delivery positions data
     */
    async getDeliveryData(timeRange) {
        try {
            const data = await this.dispatchRepo.getDeliveryPositionsData(timeRange);
            return data.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        }
        catch (error) {
            console.error('❌ [DISPATCH-INTEGRATION] Error fetching delivery data:', error);
            throw error;
        }
    }
    /**
     * Get tonnage and production data
     */
    async getTonnageData(timeRange) {
        try {
            const data = await this.dispatchRepo.getTagesleistungData(timeRange);
            return data
                .filter(item => item.produzierte_tonnagen > 0) // Filter out zero production days
                .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        }
        catch (error) {
            console.error('❌ [DISPATCH-INTEGRATION] Error fetching tonnage data:', error);
            throw error;
        }
    }
    /**
     * Get returns and quality metrics data
     */
    async getReturnsData() {
        try {
            return await this.dispatchRepo.getReturnsData();
        }
        catch (error) {
            console.error('❌ [DISPATCH-INTEGRATION] Error fetching returns data:', error);
            throw error;
        }
    }
    /**
     * Get top performing days
     */
    async getTopPerformanceDays(limit = 5) {
        try {
            return await this.dispatchRepo.getTopPerformanceDays(limit);
        }
        catch (error) {
            console.error('❌ [DISPATCH-INTEGRATION] Error fetching top performance days:', error);
            throw error;
        }
    }
    /**
     * Calculate dispatch trends and performance indicators
     */
    async getDispatchTrends(timeRange) {
        try {
            const [serviceLevel, pickingData, deliveryData, returnsData] = await Promise.all([
                this.getServiceLevelData(timeRange),
                this.getPickingData(timeRange),
                this.getDeliveryData(timeRange),
                this.getReturnsData()
            ]);
            // Calculate service level trends
            const serviceLevelTrend = this.calculateServiceLevelTrend(serviceLevel);
            // Calculate tonnage trends (using performance metrics)
            const performanceMetrics = await this.getPerformanceMetrics(timeRange);
            const tonnageTrend = this.calculateTonnageTrend(performanceMetrics);
            // Calculate picking efficiency
            const pickingEfficiency = this.calculatePickingEfficiency(pickingData);
            // Calculate delivery performance
            const deliveryPerformance = this.calculateDeliveryPerformance(deliveryData);
            // Calculate quality metrics
            const qualityMetrics = this.calculateQualityMetrics(returnsData);
            return {
                serviceLevelTrend,
                tonnageTrend,
                pickingEfficiency,
                deliveryPerformance,
                qualityMetrics
            };
        }
        catch (error) {
            console.error('❌ [DISPATCH-INTEGRATION] Error calculating dispatch trends:', error);
            return this.getEmptyTrends();
        }
    }
    /**
     * Format comprehensive Dispatch summary for AI consumption
     */
    formatDispatchSummary(serviceLevel, performanceMetrics, pickingData, deliveryData, tonnageData, returnsData, trends) {
        let summary = `VERSAND-ÜBERSICHT:\n`;
        // Performance metrics summary
        summary += `Gesamttonnage: ${performanceMetrics.totalTonnage.toFixed(1)} t\n`;
        summary += `Durchschnittlicher Servicegrad: ${performanceMetrics.averageServiceLevel.toFixed(1)}%\n`;
        summary += `Gesamtlieferungen: ${performanceMetrics.totalDeliveries}\n`;
        summary += `Durchschnittliche Kommissionierrate: ${performanceMetrics.averagePickingRate.toFixed(1)}\n\n`;
        // Service level trends
        summary += `SERVICEGRAD-TREND:\n`;
        summary += `Aktuell: ${trends.serviceLevelTrend.current.toFixed(1)}% (${trends.serviceLevelTrend.trend === 'up' ? '↗️' : trends.serviceLevelTrend.trend === 'down' ? '↘️' : '→'} ${Math.abs(trends.serviceLevelTrend.changePercent).toFixed(1)}%)\n`;
        summary += `Durchschnitt: ${trends.serviceLevelTrend.average.toFixed(1)}%\n\n`;
        // Tonnage trends
        summary += `TONNAGE-TREND:\n`;
        summary += `Aktuell: ${trends.tonnageTrend.current.toFixed(1)} t (${trends.tonnageTrend.trend === 'up' ? '↗️' : trends.tonnageTrend.trend === 'down' ? '↘️' : '→'} ${Math.abs(trends.tonnageTrend.changePercent).toFixed(1)}%)\n`;
        summary += `Durchschnitt: ${trends.tonnageTrend.average.toFixed(1)} t\n\n`;
        // Picking efficiency
        summary += `KOMMISSIONIERUNG:\n`;
        summary += `ATRL Durchschnitt: ${trends.pickingEfficiency.atrlAverage.toFixed(1)}\n`;
        summary += `ARIL Durchschnitt: ${trends.pickingEfficiency.arilAverage.toFixed(1)}\n`;
        summary += `Füllgrad ARIL: ${trends.pickingEfficiency.fuellgradAverage.toFixed(1)}%\n`;
        summary += `Effizienz: ${this.getEfficiencyDisplayName(trends.pickingEfficiency.efficiency)}\n\n`;
        // Delivery performance
        summary += `LIEFERLEISTUNG:\n`;
        summary += `Ausgeliefert: ${trends.deliveryPerformance.deliveredTotal} LUP\n`;
        summary += `Rückständig: ${trends.deliveryPerformance.backlogTotal} LUP\n`;
        summary += `Lieferrate: ${trends.deliveryPerformance.deliveryRate.toFixed(1)}%\n`;
        summary += `Leistung: ${this.getPerformanceDisplayName(trends.deliveryPerformance.performance)}\n\n`;
        // Quality metrics
        if (returnsData.length > 0) {
            summary += `QUALITÄTSMETRIKEN:\n`;
            summary += `Annahmerate: ${trends.qualityMetrics.acceptanceRate.toFixed(1)}%\n`;
            summary += `Ablehnungsrate: ${trends.qualityMetrics.rejectionRate.toFixed(1)}%\n`;
            summary += `Offene Rate: ${trends.qualityMetrics.openRate.toFixed(1)}%\n`;
            summary += `Qualitätsscore: ${trends.qualityMetrics.qualityScore}/100\n\n`;
        }
        // Recent performance highlights
        if (serviceLevel.length > 0) {
            const recentServiceLevel = serviceLevel.slice(-5);
            const avgRecentServiceLevel = recentServiceLevel.reduce((sum, item) => sum + item.servicegrad, 0) / recentServiceLevel.length;
            summary += `AKTUELLE LEISTUNG:\n`;
            summary += `Servicegrad letzte 5 Tage: ${(avgRecentServiceLevel * 100).toFixed(1)}%\n`;
        }
        if (tonnageData.length > 0) {
            const recentTonnage = tonnageData.slice(-5);
            const avgRecentTonnage = recentTonnage.reduce((sum, item) => sum + item.produzierte_tonnagen, 0) / recentTonnage.length;
            summary += `Tonnage letzte 5 Tage: ${avgRecentTonnage.toFixed(1)} t/Tag\n`;
        }
        return summary;
    }
    /**
     * Format dispatch data for specific AI queries
     */
    formatDispatchDataForQuery(data, queryType = 'overview') {
        let formatted = '';
        switch (queryType) {
            case 'service-level':
                formatted = 'SERVICEGRAD-DATEN:\n';
                if (data.serviceLevel.length > 0) {
                    const recent = data.serviceLevel.slice(-7); // Last 7 days
                    formatted += `Aktuelle Werte (letzte 7 Tage):\n`;
                    recent.forEach(item => {
                        formatted += `${this.formatDate(item.datum)}: ${(item.servicegrad * 100).toFixed(1)}%\n`;
                    });
                    formatted += `Durchschnitt: ${(data.trends.serviceLevelTrend.average * 100).toFixed(1)}%\n`;
                }
                else {
                    formatted += 'Keine Servicegrad-Daten verfügbar.\n';
                }
                break;
            case 'picking':
                formatted = 'KOMMISSIONIERUNGS-DATEN:\n';
                if (data.pickingData.length > 0) {
                    const recent = data.pickingData.slice(-5); // Last 5 days
                    formatted += `Aktuelle Leistung:\n`;
                    recent.forEach(item => {
                        formatted += `${this.formatDate(item.date)}: ATRL ${item.atrl}, ARIL ${item.aril}, Füllgrad ${item.fuellgrad_aril.toFixed(1)}%\n`;
                    });
                    formatted += `Durchschnittliche Effizienz: ${this.getEfficiencyDisplayName(data.trends.pickingEfficiency.efficiency)}\n`;
                }
                else {
                    formatted += 'Keine Kommissionierungsdaten verfügbar.\n';
                }
                break;
            case 'delivery':
                formatted = 'LIEFERLEISTUNGS-DATEN:\n';
                if (data.deliveryData.length > 0) {
                    const recent = data.deliveryData.slice(-5); // Last 5 days
                    formatted += `Aktuelle Lieferungen:\n`;
                    recent.forEach(item => {
                        formatted += `${this.formatDate(item.date)}: ${item.ausgeliefert_lup} LUP ausgeliefert, ${item.rueckstaendig} LUP rückständig\n`;
                    });
                    formatted += `Gesamtlieferrate: ${data.trends.deliveryPerformance.deliveryRate.toFixed(1)}%\n`;
                }
                else {
                    formatted += 'Keine Lieferleistungsdaten verfügbar.\n';
                }
                break;
            case 'tonnage':
                formatted = 'TONNAGE-DATEN:\n';
                if (data.tonnageData.length > 0) {
                    const recent = data.tonnageData.slice(-5); // Last 5 days
                    formatted += `Aktuelle Produktion:\n`;
                    recent.forEach(item => {
                        formatted += `${this.formatDate(item.date)}: ${item.produzierte_tonnagen.toFixed(1)} t produziert\n`;
                    });
                    formatted += `Gesamttonnage: ${data.performanceMetrics.totalTonnage.toFixed(1)} t\n`;
                    formatted += `Trend: ${data.trends.tonnageTrend.trend === 'up' ? 'Steigend' : data.trends.tonnageTrend.trend === 'down' ? 'Fallend' : 'Stabil'}\n`;
                }
                else {
                    formatted += 'Keine Tonnage-Daten verfügbar.\n';
                }
                break;
            case 'quality':
                formatted = 'QUALITÄTS-DATEN:\n';
                if (data.returnsData.length > 0) {
                    formatted += `Retouren-Übersicht:\n`;
                    data.returnsData.forEach(item => {
                        formatted += `${item.name}: ${item.value}\n`;
                    });
                    formatted += `Qualitätsscore: ${data.trends.qualityMetrics.qualityScore}/100\n`;
                    formatted += `Annahmerate: ${data.trends.qualityMetrics.acceptanceRate.toFixed(1)}%\n`;
                }
                else {
                    formatted += 'Keine Qualitätsdaten verfügbar.\n';
                }
                break;
            case 'overview':
            default:
                formatted = data.summary;
                break;
        }
        return formatted;
    }
    // Private helper methods
    getDefaultTimeRange() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30); // Last 30 days
        return {
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0]
        };
    }
    calculateServiceLevelTrend(serviceLevel) {
        var _a;
        if (serviceLevel.length === 0) {
            return { current: 0, average: 0, trend: 'stable', changePercent: 0 };
        }
        const current = ((_a = serviceLevel[serviceLevel.length - 1]) === null || _a === void 0 ? void 0 : _a.servicegrad) || 0;
        const average = serviceLevel.reduce((sum, item) => sum + item.servicegrad, 0) / serviceLevel.length;
        // Calculate trend based on last 5 vs previous 5 data points
        let trend = 'stable';
        let changePercent = 0;
        if (serviceLevel.length >= 10) {
            const recent5 = serviceLevel.slice(-5);
            const previous5 = serviceLevel.slice(-10, -5);
            const recentAvg = recent5.reduce((sum, item) => sum + item.servicegrad, 0) / recent5.length;
            const previousAvg = previous5.reduce((sum, item) => sum + item.servicegrad, 0) / previous5.length;
            changePercent = ((recentAvg - previousAvg) / previousAvg) * 100;
            if (Math.abs(changePercent) > 2) { // 2% threshold for trend detection
                trend = changePercent > 0 ? 'up' : 'down';
            }
        }
        return { current, average, trend, changePercent };
    }
    calculateTonnageTrend(performanceMetrics) {
        // For now, we'll use the total tonnage as current and create a mock trend
        // In a real implementation, this would compare current vs previous periods
        const current = performanceMetrics.totalTonnage / (performanceMetrics.period.days || 1); // Daily average
        const average = current; // Mock average - would be calculated from historical data
        return {
            current,
            average,
            trend: 'stable', // Mock trend - would be calculated from historical comparison
            changePercent: 0
        };
    }
    calculatePickingEfficiency(pickingData) {
        if (pickingData.length === 0) {
            return { atrlAverage: 0, arilAverage: 0, fuellgradAverage: 0, efficiency: 'poor' };
        }
        const atrlAverage = pickingData.reduce((sum, item) => sum + item.atrl, 0) / pickingData.length;
        const arilAverage = pickingData.reduce((sum, item) => sum + item.aril, 0) / pickingData.length;
        const fuellgradAverage = pickingData.reduce((sum, item) => sum + item.fuellgrad_aril, 0) / pickingData.length;
        // Determine efficiency based on performance thresholds
        let efficiency = 'poor';
        if (fuellgradAverage >= 90 && atrlAverage >= 80) {
            efficiency = 'excellent';
        }
        else if (fuellgradAverage >= 80 && atrlAverage >= 70) {
            efficiency = 'good';
        }
        else if (fuellgradAverage >= 70 && atrlAverage >= 60) {
            efficiency = 'average';
        }
        return { atrlAverage, arilAverage, fuellgradAverage, efficiency };
    }
    calculateDeliveryPerformance(deliveryData) {
        if (deliveryData.length === 0) {
            return { deliveredTotal: 0, backlogTotal: 0, deliveryRate: 0, performance: 'poor' };
        }
        const deliveredTotal = deliveryData.reduce((sum, item) => sum + item.ausgeliefert_lup, 0);
        const backlogTotal = deliveryData.reduce((sum, item) => sum + item.rueckstaendig, 0);
        const totalPositions = deliveredTotal + backlogTotal;
        const deliveryRate = totalPositions > 0 ? (deliveredTotal / totalPositions) * 100 : 0;
        // Determine performance based on delivery rate
        let performance = 'poor';
        if (deliveryRate >= 95) {
            performance = 'excellent';
        }
        else if (deliveryRate >= 90) {
            performance = 'good';
        }
        else if (deliveryRate >= 80) {
            performance = 'average';
        }
        return { deliveredTotal, backlogTotal, deliveryRate, performance };
    }
    calculateQualityMetrics(returnsData) {
        var _a, _b, _c;
        if (returnsData.length === 0) {
            return { acceptanceRate: 0, rejectionRate: 0, openRate: 0, qualityScore: 0 };
        }
        const angenommen = ((_a = returnsData.find(item => item.name === 'Angenommen')) === null || _a === void 0 ? void 0 : _a.value) || 0;
        const abgelehnt = ((_b = returnsData.find(item => item.name === 'Abgelehnt')) === null || _b === void 0 ? void 0 : _b.value) || 0;
        const offen = ((_c = returnsData.find(item => item.name === 'Offen')) === null || _c === void 0 ? void 0 : _c.value) || 0;
        const total = angenommen + abgelehnt + offen;
        if (total === 0) {
            return { acceptanceRate: 0, rejectionRate: 0, openRate: 0, qualityScore: 0 };
        }
        const acceptanceRate = (angenommen / total) * 100;
        const rejectionRate = (abgelehnt / total) * 100;
        const openRate = (offen / total) * 100;
        // Calculate quality score (higher acceptance rate = higher score)
        const qualityScore = Math.round(acceptanceRate);
        return { acceptanceRate, rejectionRate, openRate, qualityScore };
    }
    getEmptyTrends() {
        return {
            serviceLevelTrend: { current: 0, average: 0, trend: 'stable', changePercent: 0 },
            tonnageTrend: { current: 0, average: 0, trend: 'stable', changePercent: 0 },
            pickingEfficiency: { atrlAverage: 0, arilAverage: 0, fuellgradAverage: 0, efficiency: 'poor' },
            deliveryPerformance: { deliveredTotal: 0, backlogTotal: 0, deliveryRate: 0, performance: 'poor' },
            qualityMetrics: { acceptanceRate: 0, rejectionRate: 0, openRate: 0, qualityScore: 0 }
        };
    }
    getEfficiencyDisplayName(efficiency) {
        const efficiencyMap = {
            'excellent': 'Ausgezeichnet',
            'good': 'Gut',
            'average': 'Durchschnittlich',
            'poor': 'Verbesserungsbedürftig'
        };
        return efficiencyMap[efficiency];
    }
    getPerformanceDisplayName(performance) {
        const performanceMap = {
            'excellent': 'Ausgezeichnet',
            'good': 'Gut',
            'average': 'Durchschnittlich',
            'poor': 'Verbesserungsbedürftig'
        };
        return performanceMap[performance];
    }
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('de-DE', {
            day: '2-digit',
            month: '2-digit',
            year: '2-digit'
        });
    }
}
exports.DispatchDataIntegrationService = DispatchDataIntegrationService;
exports.default = DispatchDataIntegrationService;
