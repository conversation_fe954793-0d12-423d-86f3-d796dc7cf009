#!/usr/bin/env ts-node

/**
 * Debugging-Script für Servicelevel-Daten
 * 
 * Dieses Script überprüft:
 * 1. Rohe Drizzle-Query-Ergebnisse
 * 2. Repository-Layer-Verarbeitung
 * 3. API-Response-Struktur
 * 
 * Problem: Charts zeigen keine Daten, obwohl API 200 zurückgibt
 * Grund: Alle `datum` <PERSON><PERSON> sind `null` in der DB
 */

import { resolve } from 'path';
import { config } from 'dotenv';

// Lade .env files
config({ path: resolve(process.cwd(), '.env') });
config({ path: resolve(process.cwd(), '.env.local') });

import { db } from '../backend/src/db/index';
import { dispatchData } from '../backend/src/db/schema';
import { isNotNull, and, gte, lte, isNull } from 'drizzle-orm';
import { DispatchRepositoryImpl } from '../backend/src/repositories/dispatch.repository';

async function main() {
  console.log('🔍 Verifizierung der Servicelevel-Daten\n');
  
  try {
    // 1. Direkte Drizzle-Query - OHNE isNotNull-Filter
    console.log('📊 1. Rohe Datenbank-Query (OHNE NULL-Filter):');
    const rawData = await db.select({
      datum: dispatchData.datum,
      servicegrad: dispatchData.servicegrad,
    })
    .from(dispatchData)
    .limit(10);
    
    console.log('Raw Data Sample (10 Einträge):');
    rawData.forEach((item, idx) => {
      console.log(`  ${idx + 1}. datum: ${item.datum}, servicegrad: ${item.servicegrad}`);
    });
    
    // 2. Query MIT isNotNull-Filter (wie im Repository)
    console.log('\n📊 2. Repository-Query (MIT NULL-Filter):');
    const filteredData = await db.select({
      datum: dispatchData.datum,
      servicegrad: dispatchData.servicegrad,
    })
    .from(dispatchData)
    .where(and(
      isNotNull(dispatchData.datum),
      isNotNull(dispatchData.servicegrad)
    ))
    .limit(10);
    
    console.log(`Gefilterte Daten (${filteredData.length} Einträge):`);
    filteredData.forEach((item, idx) => {
      console.log(`  ${idx + 1}. datum: ${item.datum}, servicegrad: ${item.servicegrad}`);
    });
    
    // 3. Repository-Layer Test
    console.log('\n📊 3. Repository-Layer Test:');
    const repository = new DispatchRepositoryImpl();
    const repoData = await repository.getServiceLevelData();
    console.log(`Repository Daten: ${repoData.length} Einträge`);
    
    if (repoData.length > 0) {
      console.log('Sample Repository Data:');
      repoData.slice(0, 5).forEach((item, idx) => {
        console.log(`  ${idx + 1}. datum: ${item.datum}, servicegrad: ${item.servicegrad}`);
      });
    }
    
    // 4. Datumsbereich-Test
    console.log('\n📊 4. Datumsbereich-Test (2025-09-05):');
    const dateRangeData = await repository.getServiceLevelData({
      startDate: '2025-09-05',
      endDate: '2025-09-05'
    });
    console.log(`Datumsbereich Daten: ${dateRangeData.length} Einträge`);
    
    // 5. Statistiken
    console.log('\n📊 5. Daten-Statistiken:');
    const totalRows = await db.select().from(dispatchData);
    const nullDatumRows = await db.select()
      .from(dispatchData)
      .where(isNull(dispatchData.datum));
    const nonNullDatumRows = await db.select()
      .from(dispatchData)
      .where(isNotNull(dispatchData.datum));
    
    console.log(`Gesamt-Einträge: ${totalRows.length || 0}`);
    console.log(`NULL-Datum Einträge: ${nullDatumRows.length || 0}`);
    console.log(`Non-NULL-Datum Einträge: ${nonNullDatumRows.length || 0}`);
    
    // 6. Analyse der Datum-Feldwerte
    console.log('\n📊 6. Datum-Feldwerte-Analyse:');
    const uniqueDatumValues = await db.select({ datum: dispatchData.datum })
      .from(dispatchData)
      .groupBy(dispatchData.datum)
      .limit(20);
    
    console.log('Unique Datum Values (erste 20):');
    uniqueDatumValues.forEach((item, idx) => {
      const datumValue = item.datum;
      const displayValue = datumValue === null 
        ? 'NULL' 
        : datumValue instanceof Date 
          ? datumValue.toISOString() 
          : String(datumValue);
      console.log(`  ${idx + 1}. ${displayValue}`);
    });

  } catch (error) {
    console.error('❌ Fehler bei der Datenverifikation:', error);
  } finally {
    process.exit(0);
  }
}

// Führe das Script aus
main().catch(console.error);
