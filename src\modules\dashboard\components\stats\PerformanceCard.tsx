import React from 'react';
import { BaseKPICardProps } from './types';

/**
 * KPI-Karte für Performance (Servicegrad)
 * 
 * <PERSON>eigt den aktuellen Servicegrad und das Ziel an.
 * Verwendet violettes Farbschema mit Balkendiagramm-Icon.
 */
export const PerformanceCard: React.FC<BaseKPICardProps> = ({ 
  departmentKPIs, 
  className = '' 
}) => {
  return (
    <>
      <div className="flex items-center mb-2">
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className="h-6 w-6 text-purple-600 mr-3" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" 
          />
        </svg>
        <h3 className="text-xl text-gray-800">SERVICEGRAD</h3>
      </div>
      
      <div className="text-2xl font-bold text-gray-900">
        {departmentKPIs?.dispatch.serviceLevel.toFixed(1) || '0.0'}
        <span className="text-sm font-normal text-gray-500 ml-1">%</span>
      </div>
      
      <div className="text-sm text-gray-600 mt-1">
        Ziel: {departmentKPIs?.dispatch.targetServiceLevel.toFixed(1) || '98.5'}%
      </div>
    </>
  );
};

export default PerformanceCard;