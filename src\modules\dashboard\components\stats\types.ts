/**
 * Typen für KPI-Karten-Komponenten
 * 
 * Diese Datei definiert die gemeinsamen Interfaces und Typen,
 * die von allen KPI-Karten-Komponenten verwendet werden.
 */

// Interface für echte KPI-Daten aus der Datenbank
export interface DepartmentKPIs {
  dispatch: {
    serviceLevel: number;
    targetServiceLevel: number;
    pickingPerformance: number; // Summe von ATRL + ARIL (keine Prozent)
    pickingArilPerformance: number; // ATRL
    pickingAtrlPerformance: number; // ARIL
    deliveryPerformance: number;
    qualityRate: number;
    producedTonnage: number; // Aus TagesleistungData
    directLoading: number; // Aus TagesleistungData
    umschlag: number; // Aus TagesleistungData
    kgPerColli: number; // Aus TagesleistungData
    elefanten: number; // Aus TagesleistungData
    lastUpdated: string;
  };
  ablaengerei: {
    schnitte: number; // Summe von pickCut + cutRR + cutTR + cutTT
    schnitteTT: number; // cutTT
    schnitteTR: number; // cutTR
    schnitteRR: number; // cutRR
    schnitteCut2Pick: number; // pickCut
    qualityRate: number; // Gleiche Berechnung wie bei Dispatch
    lastUpdated: string;
  };
  wareneingang: {
    weAtrlPos: number; // weAtrl - Automatische WE-Positionen
    weManuellePos: number; // weManl - Manuelle WE-Positionen
    gesamtWE: number; // Summe von weAtrl + weManl
    automatisierungsgrad: number; // Prozentsatz automatischer WE
    qualityRate: number; // Gleiche Berechnung wie bei Dispatch
    lastUpdated: string;
  };
}

// Basis-Props für alle KPI-Karten
export interface BaseKPICardProps {
  departmentKPIs: DepartmentKPIs | null;
  className?: string;
}

// Spezifische Props für verschiedene Karten-Typen
export interface KPICardWithTargetProps extends BaseKPICardProps {
  target?: number;
  unit?: string;
}

export interface KPICardWithCustomContentProps extends BaseKPICardProps {
  title: string;
  icon?: React.ReactNode;
  colorScheme?: 'blue' | 'green' | 'purple' | 'yellow' | 'red' | 'indigo' | 'teal' | 'cyan' | 'orange' | 'lime' | 'rose' | 'violet';
}