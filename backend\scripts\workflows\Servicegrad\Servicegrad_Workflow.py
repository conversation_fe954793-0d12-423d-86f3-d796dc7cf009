#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SAP Servicegrad Automatisierung
Führt nur den ersten SAP-Prozess aus und schließt SAP-Excel
Berechnet Servicegrad, versendet E-Mail und speichert ergebnis in der Datenbank
"""

import os
import sys

# Setze UTF-8 Encoding für Windows-Konsole
if sys.platform.startswith('win'):
    try:
        # Versuche UTF-8 Encoding für stdout zu setzen
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')
    except (AttributeError, OSError):
        # Fallback für ältere Python-Versionen oder wenn reconfigure nicht verfügbar ist
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

import os
import shutil
import pandas as pd
import subprocess
import time
import json
from datetime import datetime, timedelta
import base64  # Für Bild-Einbettung in E-Mails
import sys
import io
# Imports für E-Mail-Funktionalität
import win32com.client as win32
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.image import MIMEImage
from email.mime.application import MIMEApplication
from pathlib import Path
# Pfad zum gemeinsamen Logger-Modul hinzufügen
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from common.workflow_logger import WorkflowLogger

# Globalen Logger für das gesamte Skript erstellen
logger = WorkflowLogger("servicegrad")


# === KONFIGURATION LADEN ===
def load_config():
    """Lädt die Konfiguration aus der JSON-Datei und Umgebungsvariablen"""
    # Pfad zur Konfigurationsdatei
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    
    # Standardkonfiguration
    default_config = {
        "sap": {
            "executable_path": r"C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe",
            "system_id": "PS4",
            "client": "009",
            "language": "DE",
            "tcode": "/n/LSGIT/VS_DLV_CHECK"
        },
        "paths": {
            "export_dir": r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\SG",
            "export_basename": "SG",
            "sap_excel_path": r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\SG",
            "main_excel_path": r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\Workflows\Servicegrad_BluePrint.xlsx",
            "target_excel_path": r"\\adsgroup\Group\UIL-CL-Zentral\04 Statistiken & Auswertungen\01 Statistiken\Servicegrad LO\Geschaeftsjahr 2425\Servicegrad LSS Bot\Servicegrad Kennzahlen.xlsx"
        },
        "email": {
            "recipient": "<EMAIL>",
            "subject": "Servicegrad",
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "smtp_username": "<EMAIL>",
            "use_smtp_fallback": True
        }
    }
    
    # Versuche, die Konfiguration aus der Datei zu laden
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            file_config = json.load(f)
            # Zusammenführen der Konfigurationen (Datei hat Vorrang vor Standard)
            for key in default_config:
                if key in file_config:
                    if isinstance(default_config[key], dict):
                        default_config[key].update(file_config[key])
                    else:
                        default_config[key] = file_config[key]
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logger.warning(f"Konfigurationsdatei konnte nicht geladen werden: {e}. Verwende Standardkonfiguration.")
    
    # Umgebungsvariablen haben höchste Priorität
    config = default_config
    
    # Pfade aus Umgebungsvariablen, falls gesetzt
    env_paths = {
        "export_dir": os.getenv("SERVICEGRAD_EXPORT_DIR"),
        "sap_excel_path": os.getenv("SERVICEGRAD_SAP_EXCEL_PATH"),
        "main_excel_path": os.getenv("SERVICEGRAD_MAIN_EXCEL_PATH"),
        "target_excel_path": os.getenv("SERVICEGRAD_TARGET_EXCEL_PATH")
    }
    
    for path_key, env_value in env_paths.items():
        if env_value:
            config["paths"][path_key] = env_value
            logger.info(f"Verwende Umgebungsvariable für {path_key}: {env_value}")
    
    # SAP-Konfiguration aus Umgebungsvariablen, falls gesetzt
    env_sap = {
        "executable_path": os.getenv("SAP_EXECUTABLE_PATH"),
        "system_id": os.getenv("SAP_SYSTEM_ID"),
        "client": os.getenv("SAP_CLIENT"),
        "language": os.getenv("SAP_LANGUAGE")
    }
    
    for sap_key, env_value in env_sap.items():
        if env_value:
            config["sap"][sap_key] = env_value
            logger.info(f"Verwende Umgebungsvariable für SAP {sap_key}: {env_value}")
    
    # E-Mail-Konfiguration aus Umgebungsvariablen, falls gesetzt
    env_email = {
        "recipient": os.getenv("SERVICEGRAD_EMAIL_RECIPIENT"),
        "smtp_server": os.getenv("SERVICEGRAD_SMTP_SERVER"),
        "smtp_port": os.getenv("SERVICEGRAD_SMTP_PORT"),
        "smtp_username": os.getenv("SERVICEGRAD_SMTP_USERNAME"),
        "smtp_password": os.getenv("SERVICEGRAD_SMTP_PASSWORD")
    }
    
    for email_key, env_value in env_email.items():
        if env_value:
            if email_key == "smtp_port" and env_value.isdigit():
                config["email"][email_key] = int(env_value)
            else:
                config["email"][email_key] = env_value
            logger.info(f"Verwende Umgebungsvariable für E-Mail {email_key}")
    
    return config

# Konfiguration laden
config = load_config()

# Globale Variablen aus der Konfiguration
SAP_EXECUTABLE_PATH = config["sap"]["executable_path"]
SAP_SYSTEM_ID = config["sap"]["system_id"]
SAP_CLIENT = config["sap"]["client"]
SAP_LANGUAGE = config["sap"]["language"]
TCODE = config["sap"]["tcode"]
SCRIPT_DATE = datetime.now()
EXPORT_DIR = config["paths"]["export_dir"]
EXPORT_BASENAME = config["paths"]["export_basename"]
SAP_EXCEL_PATH = config["paths"]["sap_excel_path"]
MAIN_EXCEL_PATH = config["paths"]["main_excel_path"]
TARGET_EXCEL_PATH = config["paths"]["target_excel_path"]
EMAIL_RECIPIENT = config["email"]["recipient"]
EMAIL_SUBJECT = config["email"]["subject"]
SMTP_SERVER = config["email"]["smtp_server"]
SMTP_PORT = config["email"]["smtp_port"]
SMTP_USERNAME = config["email"]["smtp_username"]
SMTP_PASSWORD = os.getenv("SERVICEGRAD_SMTP_PASSWORD", "")  # Nur aus Umgebungsvariable laden
USE_SMTP_IF_OUTLOOK_FAILS = config["email"]["use_smtp_fallback"]


# === SAP-Excel close ===
def close_excel_sap(logger: WorkflowLogger):
    """Schließt alle laufenden Excel-Prozesse und SAP GUI robust mit taskkill."""
    # Schließe Excel-Prozesse
    logger.info("Versuche, alle Excel-Prozesse zu schließen...")
    try:
        result = subprocess.run(['taskkill', '/F', '/IM', 'excel.exe'], 
                              check=True, capture_output=True, text=True)
        logger.info("Excel wurde erfolgreich geschlossen.")
    except subprocess.CalledProcessError as e:
        if "not found" in e.stderr or "Der Prozess" in e.stderr:
            logger.info("Excel war nicht geöffnet, nichts zu tun.")
        else:
            logger.warning(f"Ein Fehler ist beim Schließen von Excel aufgetreten: {e.stderr}")
    except FileNotFoundError:
        logger.error("Fehler: Der Befehl 'taskkill' wurde nicht gefunden.")
    
    # Schließe SAP GUI-Prozesse
    logger.info("Versuche, alle SAP GUI-Prozesse zu schließen...")
    sap_processes = ['saplogon.exe', 'sapgui.exe']
    
    for process in sap_processes:
        try:
            result = subprocess.run(['taskkill', '/F', '/IM', process], 
                                  check=True, capture_output=True, text=True)
            logger.info(f"{process} wurde erfolgreich geschlossen.")
        except subprocess.CalledProcessError as e:
            if "not found" in e.stderr or "Der Prozess" in e.stderr:
                logger.info(f"{process} war nicht geöffnet, nichts zu tun.")
            else:
                logger.warning(f"Ein Fehler ist beim Schließen von {process} aufgetreten: {e.stderr}")
        except FileNotFoundError:
            logger.error(f"Fehler: Der Befehl 'taskkill' wurde nicht gefunden.")


def kill_excel_processes():
    """Schließt alle Excel-Prozesse mit taskkill"""
    try:
        subprocess.run(['taskkill', '/F', '/IM', 'excel.exe'], 
                      capture_output=True, text=True, check=False)
        time.sleep(2)
    except FileNotFoundError:
        pass  # Ignoriere Fehler, wenn taskkill nicht gefunden wird


def calculate_script_date():
    """Berechnet das Script-Datum basierend auf dem aktuellen Wochentag.
    
    Returns:
        datetime: Das berechnete Datum
    """
    today = datetime.now()
    
    # Wenn heute Montag (1) ist, dann Freitag (5) der letzten Woche
    if today.weekday() == 1:  # Montag
        script_date = today - timedelta(days=3)  # Freitag
    else:
        # Ansonsten den Vortag verwenden
        script_date = today - timedelta(days=1)
    
    return script_date


def run_sap_export(session, logger):
    """Führt den SAP-Export durch"""
    logger.info("🔄 Führe SAP-Export durch...")
    
    try:
        # Warte auf das SAP-Fenster
        session.findById("wnd[0]").maximize()
        logger.info("✅ SAP-Fenster maximiert")
        
        # Gehe zum Transaktionscode
        session.findById("wnd[0]/tbar[0]/okcd").text = TCODE
        session.findById("wnd[0]").sendVKey(0)
        logger.info(f"✅ Transaktion {TCODE} gestartet")
        
        # Warte auf das Laden des Bildschirms
        time.sleep(3)
        
        # Setze das Ladedatum
        formatted_date = SCRIPT_DATE.strftime("%d.%m.%Y")
        session.findById("wnd[0]/usr/ctxtS_LFDAT-LOW").text = formatted_date
        logger.info(f"✅ Ladedatum gesetzt: {formatted_date}")
        
        # Führe die Ausführung durch (F8)
        session.findById("wnd[0]").sendVKey(8)
        logger.info("✅ Ausführung gestartet (F8)")
        
        # Warte auf die Ergebnisse
        time.sleep(10)
        
        # Speichere die Tabelle als Excel-Datei
        session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").setCurrentCell(0, "LIFNR")
        session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").selectedRows = "0"
        session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").pressToolbarButton("&LOCAL_EXPORT")
        session.findById("wnd[1]/usr/cmbG_LISTBOX").key = "01"
        session.findById("wnd[1]/tbar[0]/btn[0]").press()
        
        # Warte auf den Speichern-Dialog
        time.sleep(3)
        
        # Setze den Dateipfad und speichere
        export_filename = f"{EXPORT_BASENAME}_{SCRIPT_DATE.strftime('%Y%m%d')}.xlsx"
        export_path = os.path.join(EXPORT_DIR, export_filename)
        
        # Konvertiere den Pfad für SAP
        sap_export_path = export_path.replace("\\", "/")
        
        # Setze den Dateinamen im Dialog
        session.findById("wnd[1]/usr/ctxtDY_PATH").text = os.path.dirname(export_path)
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = os.path.basename(export_path)
        session.findById("wnd[1]/tbar[0]/btn[0]").press()
        
        # Warte auf das Speichern
        time.sleep(5)
        
        # Schließe eventuelle Bestätigungsdialoge
        try:
            if session.findById("wnd[1]").exists():
                session.findById("wnd[1]").sendVKey(0)  # Enter
                time.sleep(2)
        except:
            pass
        
        logger.info(f"✅ SAP-Export gespeichert unter: {export_path}")
        return export_path
        
    except Exception as e:
        logger.error(f"❌ Fehler beim SAP-Export: {e}")
        raise


def safe_copy_sap_data():
    """Kopiert die SAP-Daten nach Tabelle1."""
    try:
        import xlwings as xw
        
        logger.info("=== DATENKOPIE (ALLE SPALTEN A-Q) ===")
        kill_excel_processes()
        
        # Bestimme den korrekten SAP-Dateipfad
        sap_file_path = SAP_EXCEL_PATH
        
        # Falls SAP_EXCEL_PATH ein Verzeichnis ist, suche nach der neuesten Excel-Datei
        if os.path.isdir(SAP_EXCEL_PATH):
            logger.info(f"📁 SAP_EXCEL_PATH ist Verzeichnis: {SAP_EXCEL_PATH}")
            excel_files = [f for f in os.listdir(SAP_EXCEL_PATH) if f.endswith('.xlsx') and f.startswith('SG-')]
            if excel_files:
                # Sortiere nach Änderungsdatum (neueste zuerst)
                excel_files.sort(key=lambda x: os.path.getmtime(os.path.join(SAP_EXCEL_PATH, x)), reverse=True)
                sap_file_path = os.path.join(SAP_EXCEL_PATH, excel_files[0])
                logger.info(f"📄 Verwende neueste SAP-Datei: {excel_files[0]}")
            else:
                logger.error("❌ Keine SAP-Excel-Dateien im Verzeichnis gefunden!")
                return False
        
        logger.info(f"📂 Öffne SAP-Datei: {sap_file_path}")
        
        app = xw.App(visible=False, add_book=False)
        app.display_alerts = False
        app.screen_updating = False
        
        try:
            sap_wb = app.books.open(sap_file_path)
            sap_ws = sap_wb.sheets['Data'] if 'Data' in [s.name for s in sap_wb.sheets] else sap_wb.sheets[0]
            last_row_a = sap_ws.range('A' + str(sap_ws.cells.last_cell.row)).end('up').row
            logger.info(f"📊 SAP-Daten bis Zeile: {last_row_a}")
            
            logger.info("📂 Öffne Main-Excel...")
            main_wb = app.books.open(MAIN_EXCEL_PATH)
            main_ws = main_wb.sheets['Tabelle1'] if 'Tabelle1' in [s.name for s in main_wb.sheets] else main_wb.sheets[0]
            
            logger.info("🗑️ Lösche alte Daten (Formeln bleiben)...")
            main_last_row = main_ws.range('A' + str(main_ws.cells.last_cell.row)).end('up').row
            if main_last_row >= 5:
                main_ws.range(f"A5:Q{main_last_row}").clear_contents()
            
            logger.info("📊 Kopiere SAP-Daten (A-Q)...")
            source_data = sap_ws.range(f'A2:Q{last_row_a}').value
            if source_data:
                main_ws.range('A5').value = source_data
                logger.info(f"✅ {len(source_data) if isinstance(source_data, list) else 1} Datenzeilen kopiert!")

            main_wb.save()
            logger.info("✅ Datenkopie erfolgreich!")
            return True
        finally:
            if app.books:
                for wb in app.books:
                    try:
                        wb.close()
                    except:
                        pass
            try:
                app.quit()
            except:
                pass
            kill_excel_processes()
    except Exception as e:
        logger.error(f"❌ Fehler bei Datenkopie: {e}", details={"traceback": traceback.format_exc()})
        return False


def create_summary_correctly():
    """
    Erstellt die Zusammenfassung in Tabelle2.
    WICHTIG: Schreibt NUR die reinen Zahlen, KEINE Formatierung.
    """
    try:
        import xlwings as xw
        
        logger.info("📊 Erstelle Zusammenfassung (nur reine Daten)...")
        kill_excel_processes()
        app = xw.App(visible=False, add_book=False)
        try:
            wb = app.books.open(MAIN_EXCEL_PATH)
            data_ws = wb.sheets['Tabelle1']
            
            logger.info("   - Lese Daten aus Tabelle1...")
            last_row = data_ws.range('A' + str(data_ws.cells.last_cell.row)).end('up').row
            if last_row < 5:
                logger.warning("   - Keine Daten in Tabelle1 gefunden.")
                return False
            
            data_range = data_ws.range(f'A5:R{last_row}').value
            df = pd.DataFrame(data_range, columns=['Werk', 'Lager', 'Lieferung', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'Status'])

            df.dropna(subset=['Werk', 'Lager', 'Status'], inplace=True)
            df['Werk'] = df['Werk'].apply(lambda x: str(int(x)) if pd.notnull(x) else '')
            df['Lager'] = df['Lager'].apply(lambda x: str(int(x)) if pd.notnull(x) else '')

            lager_configs = [
                {'lagernr': '510', 'name': 'WM Stuttgart LC1', 'werk': '5100', 'lager': ['1000']},
                {'lagernr': '512', 'name': 'WM Ludwigsburg LC6', 'werk': '5100', 'lager': ['1010']},
                {'lagernr': '511', 'name': 'WM Hannover LC3', 'werk': '5110', 'lager': ['1000']},
                {'lagernr': '590', 'name': 'WM Polen LC9', 'werk': '5900', 'lager': ['1000']},
                {'lagernr': '51B', 'name': 'WM Illingen LC8', 'werk': '5100', 'lager': ['1090']}
            ]
            
            # Berechne das Script-Datum (das gleiche für beide Felder)
            script_date = calculate_script_date()
            
            # Berechne das Datum für die Spalte "Kalendertag"
            kalendertag = script_date.strftime("%d.%m.%Y")
            
            # Erstelle das Format "Jul 2025" für KalJahr/Monat - verwende deutsche Monatsabkürzungen
            month_names = {
                1: 'Jan', 2: 'Feb', 3: 'Mär', 4: 'Apr', 5: 'Mai', 6: 'Jun',
                7: 'Jul', 8: 'Aug', 9: 'Sep', 10: 'Okt', 11: 'Nov', 12: 'Dez'
            }
            monat_jahr = f"{month_names[script_date.month]} {script_date.year}"
            
            results = []
            logger.info("   - Analysiere Daten pro Lager...")
            for config in lager_configs:
                mask = (df['Werk'] == config['werk']) & (df['Lager'].isin(config['lager']))
                sub_df = df.loc[mask]
                
                erreicht = (sub_df['Status'] == 'Erreicht').sum()
                nicht_erreicht = (sub_df['Status'] == 'Nicht Erreicht').sum()
                total = erreicht + nicht_erreicht
                servicegrad = erreicht / total if total > 0 else 0
                
                results.append({
                    'lagernr': config['lagernr'], 
                    'name': config['name'], 
                    'monat_jahr': monat_jahr,  # NEU: Monat und Jahr
                    'kalendertag': kalendertag,  # NEU: berechnetes Datum
                    'servicegrad': servicegrad, 
                    'total': total, 
                    'erreicht': erreicht, 
                    'nicht_erreicht': nicht_erreicht, 
                    'lieferungen': sub_df['Lieferung'].nunique()
                })

            summary_ws = wb.sheets['Tabelle2'] if 'Tabelle2' in [s.name for s in wb.sheets] else wb.sheets.add('Tabelle2')
            summary_ws.clear()

            headers = ['KalJahr/Monat', 'Lagernummer', 'Lager', 'Kalendertag', 'Servicegrad', 'Anz.\nLieferungsunterpositionen', 'Anz.\nLieferunterpositionen erreicht', 'Anz.\nLieferunterpositionen nicht erreicht', 'Anz.\nLieferungen']
            summary_ws.range('B12').value = headers
            
            # WICHTIG: Setze das Format der KalJahr/Monat Spalte auf Text, bevor Daten geschrieben werden
            summary_ws.range('B13:B17').number_format = '@'  # @ = Text-Format in Excel
            
            logger.info("   - Schreibe Ergebnisse nach Tabelle2 (ohne Formatierung)...")
            for i, res in enumerate(results):
                row_num = 13 + i
                summary_ws.range(f'B{row_num}').value = [
                    res['monat_jahr'],  # NEU: Monat und Jahr - wird jetzt als Text behandelt
                    res['lagernr'], 
                    res['name'], 
                    res['kalendertag'],  # NEU: berechnetes Datum
                    res['servicegrad'], 
                    res['total'], 
                    res['erreicht'], 
                    res['nicht_erreicht'], 
                    res['lieferungen']
                ]

            total_all = sum(r['total'] for r in results)
            erreicht_all = sum(r['erreicht'] for r in results)
            summary_ws.range('B18').value = 'Gesamtergebnis'
            summary_ws.range('F18').value = erreicht_all / total_all if total_all > 0 else 0
            summary_ws.range('G18').value = total_all
            summary_ws.range('H18').value = erreicht_all
            summary_ws.range('I18').value = total_all - erreicht_all
            summary_ws.range('J18').value = sum(r['lieferungen'] for r in results)
            
            wb.save()
            logger.info("✅ Zusammenfassung (nur Daten) erfolgreich erstellt.")
            return True
        finally:
            if app.books:
                for wb in app.books:
                    try:
                        wb.close()
                    except:
                        pass
            try:
                app.quit()
            except:
                pass
            kill_excel_processes()
    except Exception as e:
        logger.error(f"❌ Fehler bei Zusammenfassung: {e}", details={"traceback": traceback.format_exc()})
        return False


def safe_transfer_to_kennzahlen():
    """Überträgt die Daten in die Kennzahlen-Datei (ohne Diagramm)."""
    logger.info("📊 Übertrage Daten zu Kennzahlen-Datei...")
    try:
        import xlwings as xw
        
        kill_excel_processes()
        app = xw.App(visible=False, add_book=False)
        try:
            main_wb = app.books.open(MAIN_EXCEL_PATH)
            main_ws = main_wb.sheets['Tabelle2']
            
            target_wb = app.books.open(TARGET_EXCEL_PATH)
            current_month_sheet_name = datetime.now().strftime("%b %y").replace("Mrz", "Mär").replace("Mai", "Mai")
            target_ws = target_wb.sheets[current_month_sheet_name]

            script_date = calculate_script_date()
            logger.info(f"   - Suche nach Datum {script_date.strftime('%d.%m.%Y')} in Ziel-Sheet...")
            b_values = target_ws.range('B4:B34').value 
            target_row = -1
            for i, cell_val in enumerate(b_values):
                if isinstance(cell_val, datetime) and cell_val.date() == script_date.date():
                    target_row = i + 4
                    break
            
            if target_row == -1:
                logger.warning(f"⚠️ Datum {script_date.strftime('%d.%m.%Y')} nicht in Kennzahlen-Datei gefunden. Überspringe Datenübertragung.")
                return False

            logger.info(f"   - Gefundene Zeile: {target_row}")
            
            # Daten aus Tabelle2 lesen
            data_range = main_ws.range('B13:J17').value
            
            # Daten in die Ziel-Datei schreiben (ohne Formatierung)
            for i, row_data in enumerate(data_range):
                if row_data and row_data[0]:  # Prüfen, ob die Zeile Daten enthält
                    target_row_num = target_row + i
                    # Nur die relevanten Spalten übertragen
                    target_ws.range(f'B{target_row_num}').value = row_data[0]  # KalJahr/Monat
                    target_ws.range(f'C{target_row_num}').value = row_data[1]  # Lagernummer
                    target_ws.range(f'D{target_row_num}').value = row_data[2]  # Lager
                    target_ws.range(f'E{target_row_num}').value = row_data[3]  # Kalendertag
                    target_ws.range(f'F{target_row_num}').value = row_data[4]  # Servicegrad
                    target_ws.range(f'G{target_row_num}').value = row_data[5]  # Anz. Lieferungsunterpositionen
                    target_ws.range(f'H{target_row_num}').value = row_data[6]  # Anz. erreicht
                    target_ws.range(f'I{target_row_num}').value = row_data[7]  # Anz. nicht erreicht
                    target_ws.range(f'J{target_row_num}').value = row_data[8]  # Anz. Lieferungen
            
            target_wb.save()
            logger.info("✅ Datenübertragung erfolgreich abgeschlossen!")
            return True
            
        finally:
            if app.books:
                for wb in app.books:
                    try:
                        wb.close()
                    except:
                        pass
            try:
                app.quit()
            except:
                pass
            kill_excel_processes()
    except Exception as e:
        logger.error(f"❌ Fehler bei der Datenübertragung: {e}", details={"traceback": traceback.format_exc()})
        return False


def save_servicegrad_to_database(file_path):
    """
    Liest den Servicegrad und das Datum aus der Excel-Datei und schreibt sie
    in die PostgreSQL-Datenbank.
    """
    logger.info("💾 Speichere Servicegrad-Daten in PostgreSQL-Datenbank...")

    # Lade DB-Credentials aus Umgebungsvariablen
    db_host = os.getenv("DB_HOST", "localhost")
    db_name = os.getenv("DB_NAME", "lapp_dashboard")
    db_user = os.getenv("DB_USER", "lapp_user")
    db_password = os.getenv("DB_PASSWORD")

    if not db_password:
        logger.error("❌ DB_PASSWORD-Umgebungsvariable nicht gesetzt. Abbruch.")
        return False

    kill_excel_processes()
    app_excel = None
    conn = None

    try:
        import xlwings as xw
        import psycopg2
        from datetime import datetime
        import pandas as pd
        from io import StringIO

        # --- 1. Daten aus Excel lesen ---
        logger.info("   - Öffne Excel-Datei, um Daten zu lesen...")
        app_excel = xw.App(visible=False)
        wb = app_excel.books.open(file_path)
        ws = wb.sheets['Tabelle2']

        # Lese alle relevanten Daten aus dem Bereich B13:J17
        data_range = ws.range('B13:J17').value
        
        # Schließe Excel so schnell wie möglich
        try:
            wb.close()
            app_excel.quit()
        except Exception as e:
            logger.warning(f"   - Fehler beim Schließen von Excel: {e}")
        finally:
            app_excel = None
            kill_excel_processes()

        if not data_range:
            logger.error("   - Keine Daten im Bereich B13:J17 gefunden.")
            return False

        # --- 2. Daten in ein Pandas DataFrame umwandeln ---
        # Header basierend auf der Struktur in 'Tabelle2'
        columns = [
            'monat_jahr', 'lagernummer', 'lager', 'kalendertag', 'servicegrad',
            'anz_lieferpositionen', 'anz_erreicht', 'anz_nicht_erreicht', 'anz_lieferungen'
        ]
        df = pd.DataFrame(data_range, columns=columns)

        # Datenbereinigung und -transformation
        df.dropna(subset=['lagernummer', 'kalendertag', 'servicegrad'], inplace=True)
        
        # Konvertiere Datum und Servicegrad in die richtigen Formate
        df['kalendertag'] = pd.to_datetime(df['kalendertag'], errors='coerce').dt.strftime('%Y-%m-%d')
        df['servicegrad'] = pd.to_numeric(df['servicegrad'], errors='coerce').round(4)
        
        # Behalte nur die für die DB relevanten Spalten
        df_to_upload = df[['kalendertag', 'servicegrad', 'lagernummer']].copy()
        df_to_upload.rename(columns={'kalendertag': 'datum'}, inplace=True)

        # Entferne Zeilen, bei denen das Datum nicht korrekt konvertiert werden konnte
        df_to_upload.dropna(subset=['datum'], inplace=True)

        if df_to_upload.empty:
            logger.error("   - Keine validen Daten zum Hochladen nach der Bereinigung.")
            return False
            
        logger.info(f"   - {len(df_to_upload)} valide Datensätze für den Upload vorbereitet.")

        # --- 3. Daten in PostgreSQL schreiben ---
        conn_string = f"host='{db_host}' dbname='{db_name}' user='{db_user}' password='{db_password}'"
        
        try:
            conn = psycopg2.connect(conn_string)
            cursor = conn.cursor()
            logger.info("   - Erfolgreich mit PostgreSQL-Datenbank verbunden.")

            # Tabelle erstellen, falls sie nicht existiert (mit neuer Spalte 'lagernummer')
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS dispatch_data (
                    id SERIAL PRIMARY KEY,
                    datum DATE NOT NULL,
                    servicegrad REAL,
                    lagernummer VARCHAR(10),
                    UNIQUE (datum, lagernummer)
                );
            """)
            
            # Temporäre Tabelle für das Upsert-Verfahren
            cursor.execute("CREATE TEMP TABLE temp_dispatch_data (datum DATE, servicegrad REAL, lagernummer VARCHAR(10));")

            # Daten effizient mit copy_from in die temporäre Tabelle laden
            buffer = StringIO()
            df_to_upload.to_csv(buffer, index=False, header=False, sep='\t')
            buffer.seek(0)
            
            cursor.copy_expert("COPY temp_dispatch_data FROM STDIN WITH (FORMAT csv, DELIMITER E'\t')", buffer)
            logger.info(f"   - {len(df_to_upload)} Datensätze in temporäre Tabelle geladen.")

            # Upsert-Logik: Aktualisiere bestehende Einträge, füge neue hinzu
            upsert_query = """
                INSERT INTO dispatch_data (datum, servicegrad, lagernummer)
                SELECT datum, servicegrad, lagernummer FROM temp_dispatch_data
                ON CONFLICT (datum, lagernummer) DO UPDATE SET
                    servicegrad = EXCLUDED.servicegrad;
            """
            cursor.execute(upsert_query)
            
            # Gib die Anzahl der betroffenen Zeilen aus
            updated_rows = cursor.rowcount
            logger.info(f"   - {updated_rows} Datensätze in 'dispatch_data' eingefügt oder aktualisiert.")

            conn.commit()
            logger.info("✅ Servicegrad-Daten erfolgreich in PostgreSQL-Datenbank gespeichert.")
            return True

        except psycopg2.Error as e:
            logger.error(f"   - PostgreSQL-Datenbankfehler: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
                logger.info("   - PostgreSQL-Verbindung geschlossen.")

    except Exception as e:
        logger.error(f"   - Kritischer Fehler in 'save_servicegrad_to_database': {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    finally:
        if app_excel:
            try:
                app_excel.quit()
            except:
                pass
        kill_excel_processes()


def send_report_by_email(file_path):
    """
    Erstellt eine E-Mail mit dem Bericht. Versucht zuerst Outlook zu verwenden,
    und falls das fehlschlägt, wird SMTP als Alternative verwendet.
    Wenn beides fehlschlägt, wird der Bericht lokal gespeichert.
    """
    print("\n📧 Erstelle und versende E-Mail-Bericht...")
    
    kill_excel_processes()
    app_excel = None
    temp_files = []
    report_saved_locally = False
    table_data = None  # Speichert die Tabellendaten
    
    try:
        import xlwings as xw

        app_excel = xw.App(visible=False)
        wb = app_excel.books.open(file_path)
        ws = wb.sheets['Tabelle2']
        
        # Lese die Daten aus dem Bereich B13:J17
        data_range = ws.range('B13:J17').value
        
        # Schließe Excel so schnell wie möglich
        try:
            wb.close()
            app_excel.quit()
        except Exception as e:
            logger.warning(f"   - Fehler beim Schließen von Excel: {e}")
        finally:
            app_excel = None
            kill_excel_processes()
        
        if not data_range:
            logger.error("❌ Keine Daten im Bereich B13:J17 gefunden.")
            return False
        
        # Konvertiere die Daten in ein lesbares Format
        columns = [
            'KalJahr/Monat', 'Lagernummer', 'Lager', 'Kalendertag', 'Servicegrad',
            'Anz. Lieferungsunterpositionen', 'Anz. erreicht', 'Anz. nicht erreicht', 'Anz. Lieferungen'
        ]
        
        # Bereite die Daten für die E-Mail vor
        table_data = []
        for row in data_range:
            if row and row[0]:  # Prüfen, ob die Zeile Daten enthält
                formatted_row = []
                for i, cell in enumerate(row):
                    if i == 4:  # Servicegrad-Spalte
                        try:
                            formatted_row.append(f"{float(cell) * 100:.2f}%")
                        except (ValueError, TypeError):
                            formatted_row.append(str(cell) if cell is not None else "")
                    else:
                        formatted_row.append(str(cell) if cell is not None else "")
                table_data.append(formatted_row)
        
        # Erstelle die HTML-Tabelle
        html_table = "<table border='1' style='border-collapse: collapse; width: 100%;'>"
        html_table += "<tr style='background-color: #f2f2f2;'>"
        for col in columns:
            html_table += f"<th style='padding: 8px; text-align: left; border: 1px solid #ddd;'>{col}</th>"
        html_table += "</tr>"
        
        for row in table_data:
            html_table += "<tr>"
            for cell in row:
                html_table += f"<td style='padding: 8px; text-align: left; border: 1px solid #ddd;'>{cell}</td>"
            html_table += "</tr>"
        html_table += "</table>"
        
        # Erstelle den E-Mail-Inhalt
        script_date = calculate_script_date()
        html_content = f"""
        <html>
        <body>
        <h2>Servicegrad-Bericht</h2>
        <p><strong>Datum:</strong> {script_date.strftime('%d.%m.%Y')}</p>
        
        <h3>Servicegrad-Daten</h3>
        {html_table}
        
        <p><em>Dieser Bericht wurde automatisch vom Servicegrad-Workflow generiert.</em></p>
        </body>
        </html>
        """
        
        # Erstelle eine Textversion für E-Mail-Clients, die kein HTML unterstützen
        text_content = f"Servicegrad-Bericht\n\nDatum: {script_date.strftime('%d.%m.%Y')}\n\n"
        text_content += "Servicegrad-Daten:\n"
        text_content += "\t".join(columns) + "\n"
        for row in table_data:
            text_content += "\t".join(row) + "\n"
        text_content += "\nDieser Bericht wurde automatisch vom Servicegrad-Workflow generiert."
        
        # Erstelle die E-Mail
        msg = MIMEMultipart('alternative')
        msg['Subject'] = f"{EMAIL_SUBJECT} - {script_date.strftime('%d.%m.%Y')}"
        msg['From'] = SMTP_USERNAME
        msg['To'] = EMAIL_RECIPIENT
        
        # Füge die Text- und HTML-Versionen hinzu
        part1 = MIMEText(text_content, 'plain')
        part2 = MIMEText(html_content, 'html')
        msg.attach(part1)
        msg.attach(part2)
        
        # Versuche zuerst, Outlook zu verwenden
        outlook_success = False
        try:
            import win32com.client
            
            logger.info("   - Versuche, Outlook zu verwenden...")
            outlook = win32com.client.Dispatch("Outlook.Application")
            mail = outlook.CreateItem(0)  # 0 = olMailItem
            
            mail.Subject = msg['Subject']
            mail.To = msg['To']
            mail.HTMLBody = html_content
            
            # Zeige die E-Mail an, damit der Benutzer sie überprüfen kann
            mail.Display(False)  # False = nicht modal
            
            # Warte 2 Sekunden, dann sende die E-Mail
            time.sleep(2)
            mail.Send()
            
            logger.info("✅ E-Mail erfolgreich über Outlook versendet.")
            outlook_success = True
            
        except Exception as e:
            logger.warning(f"   - Outlook-Versand fehlgeschlagen: {e}")
            
            # Fallback zu SMTP, wenn aktiviert
            if USE_SMTP_IF_OUTLOOK_FAILS and SMTP_PASSWORD:
                try:
                    logger.info("   - Versuche, SMTP als Alternative zu verwenden...")
                    
                    # Erstelle eine neue Verbindung zum SMTP-Server
                    server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
                    server.starttls()  # Sichere Verbindung herstellen
                    server.login(SMTP_USERNAME, SMTP_PASSWORD)
                    
                    # Sende die E-Mail
                    server.send_message(msg)
                    server.quit()
                    
                    logger.info("✅ E-Mail erfolgreich über SMTP versendet.")
                    outlook_success = True
                    
                except Exception as smtp_error:
                    logger.error(f"   - SMTP-Versand fehlgeschlagen: {smtp_error}")
        
        # Wenn sowohl Outlook als auch SMTP fehlgeschlagen sind, speichere den Bericht lokal
        if not outlook_success:
            logger.warning("⚠️ Sowohl Outlook als auch SMTP fehlgeschlagen. Speichere Bericht lokal...")
            
            # Erstelle einen lokalen Speicherort
            local_reports_dir = os.path.join(os.path.dirname(__file__), "reports")
            os.makedirs(local_reports_dir, exist_ok=True)
            
            # Speichere den Bericht als HTML-Datei
            report_filename = f"Servicegrad_Bericht_{script_date.strftime('%Y%m%d')}.html"
            report_path = os.path.join(local_reports_dir, report_filename)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"✅ Bericht lokal gespeichert unter: {report_path}")
            report_saved_locally = True
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Fehler beim Erstellen des E-Mail-Berichts: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    finally:
        if app_excel:
            try:
                app_excel.quit()
            except:
                pass
        kill_excel_processes()


def run_servicegrad_process(session, script_date, logger):
    """Führt den vollständigen Servicegrad-Prozess aus"""
    try:
        logger.info("🎯 VOLLSTÄNDIGER WORKFLOW: SAP-EXPORT + SERVICEGRAD-BERECHNUNG + E-MAIL + DATENBANK")
        
        # 1. SAP-Export durchführen
        logger.info("\n1. SAP-Export durchführen...")
        export_path = run_sap_export(session, logger)
        if not export_path:
            logger.error("❌ SAP-Export fehlgeschlagen")
            return False
        
        # 2. SAP-Daten kopieren
        logger.info("\n2. SAP-Daten kopieren...")
        if not safe_copy_sap_data():
            logger.error("❌ Datenkopie fehlgeschlagen")
            return False
        
        # 3. Zusammenfassung erstellen
        logger.info("\n3. Zusammenfassung erstellen...")
        if not create_summary_correctly():
            logger.error("❌ Zusammenfassung fehlgeschlagen")
            return False
        
        # 4. Daten an Kennzahlen übertragen
        logger.info("\n4. Daten an Kennzahlen übertragen...")
        if not safe_transfer_to_kennzahlen():
            logger.warning("⚠️ Datenübertragung an Kennzahlen fehlgeschlagen, setze fort...")
        
        # 5. E-Mail-Bericht senden
        logger.info("\n5. E-Mail-Bericht senden...")
        if not send_report_by_email(MAIN_EXCEL_PATH):
            logger.warning("⚠️ E-Mail-Versand fehlgeschlagen, setze fort...")
        
        # 6. Servicegrad-Daten in Datenbank speichern
        logger.info("\n6. Servicegrad-Daten in Datenbank speichern...")
        if save_servicegrad_to_database(MAIN_EXCEL_PATH):
            logger.info("Datenbank-Speicherung erfolgreich")
            print("✅ Datenbank-Speicherung erfolgreich")
        else:
            logger.warning("Datenbank-Speicherung fehlgeschlagen")
            print("⚠️ Datenbank-Speicherung fehlgeschlagen")
        
        logger.info("✅ VOLLSTÄNDIGER WORKFLOW ERFOLGREICH ABGESCHLOSSEN")
        print("\n✅ VOLLSTÄNDIGER WORKFLOW ERFOLGREICH ABGESCHLOSSEN")
        return True
        
    except Exception as e:
        logger.error(f"❌ Fehler im Servicegrad-Prozess: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def main():
    """Hauptfunktion des Servicegrad-Workflows"""
    try:
        logger.log_process_start("Servicegrad Automatisierung")
        
        # SAP-Verbindung herstellen
        import win32com.client
        
        logger.info("Starte SAP...")
        sap_gui = win32com.client.Dispatch("SAPGUI")
        sap = sap_gui.GetScriptingEngine
        
        # SAP-System anmelden
        logger.info("Melde am SAP-System an...")
        session = None
        
        try:
            # Versuche, eine bestehende Sitzung zu verwenden
            if sap.Children.Count > 0:
                session = sap.Children(0)
                logger.info("Verwende bestehende SAP-Sitzung")
            else:
                # Erstelle eine neue Sitzung
                connection = sap.OpenConnection("PS4", True)
                session = connection.Children(0)
                logger.info("Neue SAP-Sitzung erstellt")
            
            # Warte auf die Anmeldung
            time.sleep(5)
            
            # Führe den Servicegrad-Prozess aus
            success = run_servicegrad_process(session, SCRIPT_DATE, logger)
            
            if success:
                logger.log_process_complete("Servicegrad Automatisierung", MAIN_EXCEL_PATH)
            else:
                logger.log_process_error("Servicegrad Automatisierung", "Prozess fehlgeschlagen")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Fehler bei der SAP-Verbindung: {e}")
            logger.log_process_error("Servicegrad Automatisierung", f"SAP-Verbindungsfehler: {e}")
            return False
        finally:
            # Schließe SAP am Ende
            try:
                if session:
                    session.findById("wnd[0]").close()
                    time.sleep(2)
                    # Bestätige eventuelle Dialoge
                    try:
                        session.findById("wnd[1]/usr/btnSPOP-OPTION_1").press()  # Ja
                    except:
                        pass
            except:
                pass
            
            # Schließe Excel-Prozesse
            kill_excel_processes()
    
    except Exception as e:
        logger.error(f"❌ Kritischer Fehler im Servicegrad-Workflow: {e}")
        import traceback
        logger.error(traceback.format_exc())
        logger.log_process_error("Servicegrad Automatisierung", f"Kritischer Fehler: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)