import React from 'react';
import { PackageCheck } from 'lucide-react';
import { BaseKPICardProps } from './types';

/**
 * KPI-Karte für Versand
 * 
 * Zeigt die Direktverladung aus den Dispatch-Daten an.
 * Verwendet rosafarbenes Farbschema mit PackageCheck-Icon aus Lucide.
 */
export const VersandCard: React.FC<BaseKPICardProps> = ({ 
  departmentKPIs, 
  className = '' 
}) => {
  return (
    <>
      <div className="flex items-center mb-2">
        <div className="p-2 bg-rose-100 rounded-lg mr-3">
          <PackageCheck className="h-6 w-6 text-rose-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Versand</h3>
      </div>
      
      <div className="text-2xl font-bold text-gray-900">
        {departmentKPIs?.dispatch.directLoading.toLocaleString() || '0'}
        <span className="text-sm font-normal text-gray-500 ml-1">Pos</span>
      </div>
      
      <div className="text-sm text-gray-600 mt-1">
        Ziel: 1.500 Pos
      </div>
    </>
  );
};

export default VersandCard;