import React from 'react';
import { Clock } from 'lucide-react';
import { BaseKPICardProps } from './types';

/**
 * KPI-Karte für Service
 * 
 * Zeigt die Elefanten-Anzahl aus den Dispatch-Daten an.
 * Verwendet violettes Farbschema mit Clock-Icon aus Lucide.
 */
export const ServiceCard: React.FC<BaseKPICardProps> = ({ 
  departmentKPIs, 
  className = '' 
}) => {
  return (
    <>
      <div className="flex items-center mb-2">
        <Clock className="h-6 w-6 text-violet-600" />
        <h3 className="text-lg font-semibold text-gray-800">Service</h3>
      </div>
      
      <div className="text-2xl font-bold text-gray-900">
        {departmentKPIs?.dispatch.elefanten.toLocaleString() || '0'}
        <span className="text-sm font-normal text-gray-500 ml-1">Elefanten</span>
      </div>
      
      <div className="text-sm text-gray-600 mt-1">
        Ziel: 50 Elefanten
      </div>
    </>
  );
};

export default ServiceCard;