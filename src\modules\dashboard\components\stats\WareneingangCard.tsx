import React from 'react';
import { BaseKPICardProps } from './types';

/**
 * KPI-Karte für Wareneingang
 * 
 * Zeigt die Gesamtzahl der Wareneingangspositionen an.
 * Verwendet blaues Farbschema mit Paket-Icon.
 */
export const WareneingangCard: React.FC<BaseKPICardProps> = ({ 
  departmentKPIs, 
  className = '' 
}) => {
  return (
    <>
      <div className="flex items-center mb-2">
        <div className="p-2 bg-blue-100 rounded-lg mr-3">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6 text-blue-600" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" 
            />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Wareneingang</h3>
      </div>
      
      <div className="text-2xl font-bold text-gray-900">
        {departmentKPIs?.wareneingang.gesamtWE.toLocaleString() || '0'}
        <span className="text-sm font-normal text-gray-500 ml-1">Pos</span>
      </div>
      
      <div className="text-sm text-gray-600 mt-1">
        Ziel: 1.200 Pos
      </div>
    </>
  );
};

export default WareneingangCard;