"use client";

import React, { useEffect, useRef, useState, useMemo } from "react";
import styles from "./BentoGrid.module.css";

// Interface für Department KPIs - erweitert für bessere Typisierung
interface DepartmentKPIs {
  incomingGoods: {
    current: number;
    target: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
  };
  tonnage: {
    current: number;
    target: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
  };
  performance: {
    current: number;
    target: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
  };
  efficiency: {
    current: number;
    target: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
  };
}

export interface BentoGridProps {
  className?: string;
  departmentKPIs?: DepartmentKPIs;
}

interface CardTiltProps {
  children: React.ReactNode;
  className?: string;
  onMouseMove?: (e: React.MouseEvent) => void;
  onTouchMove?: (e: React.TouchEvent) => void;
  onMouseLeave?: () => void;
  onTouchEnd?: () => void;
}

const BentoCard: React.FC<CardTiltProps> = ({
  children,
  className = "",
  onMouseMove,
  onTouchMove,
  onMouseLeave,
  onTouchEnd,
}) => {
  const cardRef = useRef<HTMLElement>(null);

  const clamp = (v: number, min: number, max: number) => Math.min(Math.max(v, min), max);

  const handleMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    let x, y;
    
    if ('clientX' in e) {
      x = e.clientX;
      y = e.clientY;
    } else if (e.touches && e.touches[0]) {
      x = e.touches[0].clientX;
      y = e.touches[0].clientY;
    } else {
      return;
    }
    
    const px = (x - rect.left) / rect.width;
    const py = (y - rect.top) / rect.height;
    const r = 12; // degrees
    const rx = clamp((0.5 - py) * (r * 2), -r, r);
    const ry = clamp((px - 0.5) * (r * 2), -r, r);
    
    cardRef.current.style.setProperty('--rx', `${rx.toFixed(2)}deg`);
    cardRef.current.style.setProperty('--ry', `${ry.toFixed(2)}deg`);
    cardRef.current.style.setProperty('--mx', `${px * 100}%`);
  };

  const handleReset = () => {
    if (!cardRef.current) return;
    cardRef.current.style.setProperty('--rx', '0deg');
    cardRef.current.style.setProperty('--ry', '0deg');
    cardRef.current.style.removeProperty('--mx');
  };

  return (
    <section
      ref={cardRef}
      className={`${styles.card} ${className}`}
      onMouseMove={handleMove}
      onTouchMove={handleMove}
      onMouseLeave={handleReset}
      onTouchEnd={handleReset}
    >
      {children}
    </section>
  );
};

export const BentoGrid: React.FC<BentoGridProps> = ({ className = "", departmentKPIs }) => {
  const [sliderValue, setSliderValue] = useState(55);
  const [performanceValue, setPerformanceValue] = useState(0);

  useEffect(() => {
    // Progressive number animation for performance
    let n = 0;
    const target = departmentKPIs?.performance?.current || 20;
    const step = () => {
      n += 1;
      if (n > target) n = target;
      setPerformanceValue(n);
      if (n < target) requestAnimationFrame(step);
    };
    requestAnimationFrame(step);
  }, [departmentKPIs]);

  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    setSliderValue(value);
    e.target.style.setProperty('--val', `${value}%`);
  };

  // Berechne KPI-Werte mit Fallback-Daten für Demo-Zwecke
  const kpiData = useMemo(() => {
    if (!departmentKPIs) {
      // Fallback Demo-Daten wenn keine KPIs übergeben werden
      return {
        incomingGoods: { current: 1247, target: 1200, unit: 'Stk', trend: 'up' as const },
        tonnage: { current: 89.2, target: 85.0, unit: 't', trend: 'up' as const },
        performance: { current: 94.7, target: 90.0, unit: '%', trend: 'up' as const },
        efficiency: { current: 87.3, target: 85.0, unit: '%', trend: 'up' as const }
      };
    }
    return departmentKPIs;
  }, [departmentKPIs]);

  // Berechne Prozentsätze für visuelle Darstellung
  const getPercentage = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  // Bestimme Farbe basierend auf Performance
  const getPerformanceColor = (current: number, target: number, trend: string) => {
    const percentage = (current / target) * 100;
    if (percentage >= 100) return 'var(--accent-green)';
    if (percentage >= 80) return 'var(--accent-cyan)';
    if (trend === 'up') return 'var(--accent-lime)';
    return 'var(--accent-orange)';
  };

  return (
    <div className={`${styles.wrap} ${className}`}>
      <h2 className={styles.title}>Bento Grid – Tech Showcase</h2>

      <div className={styles.bento}>
        {/* T1 – Wareneingang KPI */}
        <BentoCard className={`${styles.t1}`}>
          <div className={styles.splash} aria-hidden="true"></div>
          <div className={styles.dots} aria-hidden="true"></div>
          <div className={styles.content}>
            <div className={`${styles.labelTop} ${styles.eyebrow}`}>Wareneingang</div>
            <div style={{ marginTop: 'auto' }}>
              <div className={styles.headline} style={{ fontSize: 'clamp(18px,2.6vw,22px)', fontWeight: 600, opacity: 0.95 }}>
                {kpiData.incomingGoods.current.toLocaleString()}
                <span style={{ fontSize: '0.4em', opacity: 0.7, marginLeft: '8px' }}>
                  {kpiData.incomingGoods.unit}
                </span>
              </div>
              <div style={{ 
                fontSize: '12px', 
                opacity: 0.8, 
                color: getPerformanceColor(kpiData.incomingGoods.current, kpiData.incomingGoods.target, kpiData.incomingGoods.trend)
              }}>
                Ziel: {kpiData.incomingGoods.target.toLocaleString()} {kpiData.incomingGoods.unit}
              </div>
            </div>
          </div>
        </BentoCard>

        {/* T2 – Tonnage KPI */}
        <BentoCard className={styles.t2}>
          <div className={styles.content}>
            <div className={`${styles.labelTop} ${styles.eyebrow}`}>Tonnage</div>
            <div className={styles.headline} style={{ fontSize: 'clamp(18px,2.6vw,22px)', fontWeight: 600, opacity: 0.95 }}>
              {kpiData.tonnage.current.toFixed(1)}
              <span style={{ fontSize: '0.5em', opacity: 0.7, marginLeft: '4px' }}>
                {kpiData.tonnage.unit}
              </span>
            </div>
          </div>
          <svg className={styles.chain} viewBox="0 0 64 64" fill="none" aria-hidden="true">
            <defs>
              <linearGradient id="gradChain" x1="0" y1="0" x2="64" y2="64" gradientUnits="userSpaceOnUse">
                <stop stopColor={getPerformanceColor(kpiData.tonnage.current, kpiData.tonnage.target, kpiData.tonnage.trend)} />
                <stop offset="1" stopColor="#22d3ee" />
              </linearGradient>
            </defs>
            <path d="M36 14h10a8 8 0 1 1 0 16h-8M28 50H18a8 8 0 1 1 0-16h8M24 34h16M24 30h16" />
          </svg>
        </BentoCard>

        {/* T3 – Performance Trend */}
        <BentoCard className={styles.t3}>
          <div className={styles.content}>
            <div className={`${styles.labelTop} ${styles.eyebrow}`}>Performance</div>
            <div className={styles.headline} style={{ fontSize: 'clamp(18px,2.6vw,22px)', fontWeight: 600, opacity: 0.95 }}>
              {kpiData.performance.current.toFixed(1)}%
            </div>
          </div>
          <div className={styles.beam} aria-hidden="true" style={{
            background: `linear-gradient(180deg, rgba(212, 91, 255, 0), ${getPerformanceColor(kpiData.performance.current, kpiData.performance.target, kpiData.performance.trend)}, rgba(34, 211, 238, 0))`
          }}></div>
        </BentoCard>

        {/* T4 – Efficiency Bars */}
        <BentoCard className={styles.t4}>
          <div className={styles.content}>
            <div className={`${styles.labelTop} ${styles.eyebrow}`}>Effizienz</div>
            <div className={styles.headline} style={{ fontSize: 'clamp(18px,2.6vw,22px)', fontWeight: 600, opacity: 0.95 }}>
              {kpiData.efficiency.current.toFixed(1)}%
            </div>
            <div className={styles.bars} aria-hidden="true">
              {[...Array(5)].map((_, i) => {
                const height = Math.max(35, Math.min(100, getPercentage(kpiData.efficiency.current, kpiData.efficiency.target) - (i * 15)));
                return (
                  <div 
                    key={i}
                    className={styles.bar} 
                    style={{ 
                      height: `${height}%`,
                      background: `linear-gradient(180deg, ${getPerformanceColor(kpiData.efficiency.current, kpiData.efficiency.target, kpiData.efficiency.trend)}, rgba(20, 198, 198, 0.22))`
                    }}
                  ></div>
                );
              })}
            </div>
          </div>
        </BentoCard>

        {/* T5 – Performance Dial mit echten Daten */}
        <BentoCard className={styles.t5}>
          <div className={styles.dial} style={{
            '--performance-angle': `${(kpiData.performance.current / 100) * 360}deg`
          } as React.CSSProperties} aria-hidden="true"></div>
          <div className={styles.content} style={{ justifyContent: 'center', alignItems: 'flex-start' }}>
            <div className={styles.eyebrow} style={{ marginBottom: '8px' }}>Gesamtperformance</div>
            <div className={styles.big}>{performanceValue.toFixed(1)}%</div>
            <div style={{ 
              fontSize: '11px', 
              opacity: 0.7, 
              color: getPerformanceColor(kpiData.performance.current, kpiData.performance.target, kpiData.performance.trend)
            }}>
              Ziel: {kpiData.performance.target}%
            </div>
          </div>
        </BentoCard>

        {/* T6 – Automation Control */}
        <BentoCard className={styles.t6}>
          <div className={styles.glow} aria-hidden="true"></div>
          <div className={styles.content}>
            <div className={`${styles.labelTop} ${styles.eyebrow}`} style={{ color: '#b8ffe1' }}>Automatisierung</div>
            <div style={{ marginTop: 'auto' }}>
              <div className={styles.headline}>STEUERUNG</div>
              <div className={styles.sliderWrap}>
                <span className={styles.eyebrow} style={{ color: '#b8ffe1', letterSpacing: '.12em' }}>Level</span>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={sliderValue}
                  onChange={handleSliderChange}
                  style={{ '--val': `${sliderValue}%` } as React.CSSProperties}
                  aria-label="Automatisierungsgrad einstellen"
                />
                <span style={{ fontSize: '12px', color: 'var(--accent-lime)', fontWeight: '600', marginTop: '4px' }}>
                  {sliderValue}%
                </span>
              </div>
            </div>
          </div>
        </BentoCard>

        {/* T7 Security Vault */}
        <BentoCard className={styles.t7}>
          <div className={styles.content}>
            <div className={styles.eyebrow}>Security</div>
            <div className={styles.headline}>VAULT</div>
          </div>
          <svg className={styles.lock} viewBox="0 0 64 64" fill="none" aria-hidden="true">
            <defs>
              <linearGradient id="gradLock" x1="0" y1="0" x2="64" y2="64">
                <stop stopColor="#d45bff" />
                <stop offset="1" stopColor="#22d3ee" />
              </linearGradient>
            </defs>
            <path d="M20 30h24a6 6 0 0 1 6 6v14a6 6 0 0 1-6 6H20a6 6 0 0 1-6-6V36a6 6 0 0 1 6-6z" />
            <path d="M24 30v-6a8 8 0 1 1 16 0v6" />
            <path d="M32 40v6" />
          </svg>
        </BentoCard>

        {/* T8 Rotating ring */}
        <BentoCard className={styles.t8}>
          <div className={styles.ring} aria-hidden="true"></div>
        </BentoCard>

        {/* T9 Slim neon line */}
        <BentoCard className={styles.t9}>
          <div className={styles.line} aria-hidden="true"></div>
        </BentoCard>

        {/* T10 Subtle cube */}
        <BentoCard className={styles.t10}>
          <div className={styles.cube} aria-hidden="true"></div>
        </BentoCard>
      </div>
    </div>
  );
};

export default BentoGrid;