/**
 * Delivery Repository Interfaces
 *
 * Type definitions for delivery and logistics data management
 */

import { DeliveryRequest, DeliveryRoute } from '@/types/supply-chain-optimization';

export interface DeliveryStats {
  totalDeliveries: number;
  onTimeDeliveries: number;
  averageDeliveryTime: number; // days
  totalDistance: number; // km
  totalCost: number; // euros
  activeRoutes: number;
}

export interface DeliveryTimeRangeFilter {
  days: number;
}

export interface DeliveryHistoryRecord {
  deliveryId: string;
  supplierId: string;
  orderDate: Date;
  deliveryDate: Date;
  promisedTime: number; // days
  actualTime: number; // days
  wasLate: boolean;
  productType: string;
  urgency: 'low' | 'medium' | 'high';
  quantity: number;
  value: number; // euros
  distance?: number; // km
  route?: string;
  delayReason?: string;
}

export interface DeliveryPerformanceMetrics {
  onTimeRate: number;
  averageDeliveryTime: number;
  totalDeliveries: number;
  delayFrequency: number;
  averageDelay: number;
}

export interface DeliveryTrendData {
  week: Date;
  onTimeRate: number;
  averageDeliveryTime: number;
  totalDeliveries: number;
  totalDistance: number;
  totalCost: number;
}

export interface DelayReasonAnalysis {
  reason: string;
  frequency: number;
  percentage: number;
  averageDelay: number;
  impact: 'high' | 'medium' | 'low';
}

export interface SeasonalPattern {
  season: string;
  months: number[];
  deliveryTimeFactor: number;
  riskFactor: number;
  description: string;
}

/**
 * Delivery Repository Interface
 * Defines the contract for delivery data operations
 */
export interface IDeliveryRepository {
  /**
   * Get all delivery history
   */
  getAll(filter?: DeliveryTimeRangeFilter): Promise<DeliveryHistoryRecord[]>;

  /**
   * Get overall delivery statistics
   */
  getDeliveryStats(): Promise<DeliveryStats>;

  /**
   * Get delivery history for a specific supplier
   */
  getSupplierDeliveryHistory(
    supplierId: string,
    timeRange: { days: number }
  ): Promise<DeliveryHistoryRecord[]>;

  /**
   * Get all delivery history within time range
   */
  getDeliveryHistory(timeRange: { days: number }): Promise<DeliveryHistoryRecord[]>;

  /**
   * Get active delivery routes
   */
  getActiveRoutes(): Promise<DeliveryRoute[]>;

  /**
   * Get delivery performance metrics
   */
  getDeliveryPerformanceMetrics(timeRange: { days: number }): Promise<DeliveryPerformanceMetrics>;

  /**
   * Get delivery trends
   */
  getDeliveryTrends(timeRange: { days: number }): Promise<DeliveryTrendData[]>;

  /**
   * Get common delay reasons
   */
  getDelayReasons(timeRange: { days: number }): Promise<DelayReasonAnalysis[]>;

  /**
   * Create new delivery record
   */
  createDelivery(delivery: Partial<DeliveryHistoryRecord>): Promise<string>;

  /**
   * Update delivery status
   */
  updateDeliveryStatus(deliveryId: string, status: string, actualDeliveryTime?: number): Promise<void>;

  /**
   * Get deliveries by route
   */
  getDeliveriesByRoute(routeId: string): Promise<DeliveryHistoryRecord[]>;

  /**
   * Get seasonal delivery patterns
   */
  getSeasonalPatterns(): Promise<SeasonalPattern[]>;
}

/**
* Delivery Repository Implementation
* Concrete implementation of IDeliveryRepository interface
*/
export class DeliveryRepository implements IDeliveryRepository {
 protected repositoryName = 'DeliveryRepository';

 /**
  * Get all delivery history
  */
 async getAll(filter?: DeliveryTimeRangeFilter): Promise<DeliveryHistoryRecord[]> {
   // Mock implementation - in real app this would fetch from API/database
   const days = filter?.days || 90;
   const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

   return [
     {
       deliveryId: 'del-001',
       supplierId: 'supplier-1',
       orderDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
       deliveryDate: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000),
       promisedTime: 7,
       actualTime: 5,
       wasLate: false,
       productType: 'electronics',
       urgency: 'medium' as const,
       quantity: 100,
       value: 5000,
       distance: 250,
       route: 'route-north'
     },
     {
       deliveryId: 'del-002',
       supplierId: 'supplier-2',
       orderDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
       deliveryDate: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000),
       promisedTime: 5,
       actualTime: 2,
       wasLate: false,
       productType: 'mechanical',
       urgency: 'high' as const,
       quantity: 50,
       value: 7500,
       distance: 180,
       route: 'route-south'
     },
     {
       deliveryId: 'del-003',
       supplierId: 'supplier-1',
       orderDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
       deliveryDate: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000),
       promisedTime: 8,
       actualTime: 3,
       wasLate: false,
       productType: 'electronics',
       urgency: 'low' as const,
       quantity: 200,
       value: 12000,
       distance: 320,
       route: 'route-east'
     }
   ].filter(d => d.orderDate >= cutoffDate);
 }

 /**
  * Get overall delivery statistics
  */
 async getDeliveryStats(): Promise<DeliveryStats> {
   const deliveries = await this.getAll();

   return {
     totalDeliveries: deliveries.length,
     onTimeDeliveries: deliveries.filter(d => !d.wasLate).length,
     averageDeliveryTime: deliveries.reduce((sum, d) => sum + d.actualTime, 0) / deliveries.length,
     totalDistance: deliveries.reduce((sum, d) => sum + (d.distance || 0), 0),
     totalCost: deliveries.reduce((sum, d) => sum + d.value, 0),
     activeRoutes: new Set(deliveries.map(d => d.route).filter(Boolean)).size
   };
 }

 /**
  * Get delivery history for a specific supplier
  */
 async getSupplierDeliveryHistory(
   supplierId: string,
   timeRange: { days: number }
 ): Promise<DeliveryHistoryRecord[]> {
   const deliveries = await this.getAll({ days: timeRange.days });
   return deliveries.filter(d => d.supplierId === supplierId);
 }

 /**
  * Get all delivery history within time range
  */
 async getDeliveryHistory(timeRange: { days: number }): Promise<DeliveryHistoryRecord[]> {
   return this.getAll({ days: timeRange.days });
 }

 /**
  * Get active delivery routes
  */
 async getActiveRoutes(): Promise<DeliveryRoute[]> {
   // Mock implementation
   return [
     {
       routeId: 'route-north',
       deliveries: [],
       totalDistance: 250,
       estimatedTime: 180,
       cost: 1250
     },
     {
       routeId: 'route-south',
       deliveries: [],
       totalDistance: 180,
       estimatedTime: 120,
       cost: 900
     }
   ];
 }

 /**
  * Get delivery performance metrics
  */
 async getDeliveryPerformanceMetrics(timeRange: { days: number }): Promise<DeliveryPerformanceMetrics> {
   const deliveries = await this.getAll({ days: timeRange.days });

   return {
     onTimeRate: deliveries.filter(d => !d.wasLate).length / deliveries.length,
     averageDeliveryTime: deliveries.reduce((sum, d) => sum + d.actualTime, 0) / deliveries.length,
     totalDeliveries: deliveries.length,
     delayFrequency: deliveries.filter(d => d.wasLate).length / deliveries.length,
     averageDelay: deliveries.filter(d => d.wasLate).reduce((sum, d) => sum + (d.actualTime - d.promisedTime), 0) / deliveries.filter(d => d.wasLate).length || 0
   };
 }

 /**
  * Get delivery trends
  */
 async getDeliveryTrends(timeRange: { days: number }): Promise<DeliveryTrendData[]> {
   // Mock implementation - simplified trends
   const deliveries = await this.getAll({ days: timeRange.days });
   const weeks = Math.ceil(timeRange.days / 7);

   const trends: DeliveryTrendData[] = [];
   for (let i = 0; i < weeks; i++) {
     const weekStart = new Date(Date.now() - (i + 1) * 7 * 24 * 60 * 60 * 1000);
     const weekDeliveries = deliveries.filter(d =>
       d.orderDate >= weekStart &&
       d.orderDate < new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000)
     );

     if (weekDeliveries.length > 0) {
       trends.push({
         week: weekStart,
         onTimeRate: weekDeliveries.filter(d => !d.wasLate).length / weekDeliveries.length,
         averageDeliveryTime: weekDeliveries.reduce((sum, d) => sum + d.actualTime, 0) / weekDeliveries.length,
         totalDeliveries: weekDeliveries.length,
         totalDistance: weekDeliveries.reduce((sum, d) => sum + (d.distance || 0), 0),
         totalCost: weekDeliveries.reduce((sum, d) => sum + d.value, 0)
       });
     }
   }

   return trends;
 }

 /**
  * Get common delay reasons
  */
 async getDelayReasons(timeRange: { days: number }): Promise<DelayReasonAnalysis[]> {
   // Mock implementation
   return [
     {
       reason: 'Weather conditions',
       frequency: 0.3,
       percentage: 30,
       averageDelay: 2.5,
       impact: 'medium'
     },
     {
       reason: 'Supplier delays',
       frequency: 0.25,
       percentage: 25,
       averageDelay: 3.2,
       impact: 'high'
     },
     {
       reason: 'Transportation issues',
       frequency: 0.2,
       percentage: 20,
       averageDelay: 1.8,
       impact: 'medium'
     }
   ];
 }

 /**
  * Create new delivery record
  */
 async createDelivery(delivery: Partial<DeliveryHistoryRecord>): Promise<string> {
   const newDeliveryId = `del-${Date.now()}`;
   console.log('Creating new delivery:', { ...delivery, deliveryId: newDeliveryId });
   return newDeliveryId;
 }

 /**
  * Update delivery status
  */
 async updateDeliveryStatus(deliveryId: string, status: string, actualDeliveryTime?: number): Promise<void> {
   console.log(`Updating delivery ${deliveryId} status to ${status}`, actualDeliveryTime ? `with delivery time ${actualDeliveryTime}` : '');
 }

 /**
  * Get deliveries by route
  */
 async getDeliveriesByRoute(routeId: string): Promise<DeliveryHistoryRecord[]> {
   const deliveries = await this.getAll();
   return deliveries.filter(d => d.route === routeId);
 }

 /**
  * Get seasonal delivery patterns
  */
 async getSeasonalPatterns(): Promise<SeasonalPattern[]> {
   return [
     {
       season: 'winter',
       months: [12, 1, 2],
       deliveryTimeFactor: 1.3,
       riskFactor: 1.4,
       description: 'Winter weather can cause delays'
     },
     {
       season: 'summer',
       months: [6, 7, 8],
       deliveryTimeFactor: 1.1,
       riskFactor: 1.2,
       description: 'Summer heat can affect some products'
     },
     {
       season: 'spring',
       months: [3, 4, 5],
       deliveryTimeFactor: 1.0,
       riskFactor: 1.0,
       description: 'Normal delivery conditions'
     },
     {
       season: 'autumn',
       months: [9, 10, 11],
       deliveryTimeFactor: 1.05,
       riskFactor: 1.1,
       description: 'Autumn weather variability'
     }
   ];
 }
}