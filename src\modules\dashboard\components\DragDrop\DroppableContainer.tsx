"use client";

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import styles from './DragDrop.module.css';

export interface DroppableContainerProps {
  /** Eindeutige ID für den Droppable-Container */
  id: string;
  /** Kinder-Komponenten, die im Container gerendert werden */
  children: React.ReactNode;
  /** Zusätzliche CSS-Klassen */
  className?: string;
  /** Ob der Container als Zielbereich hervorgehoben werden soll */
  isOverlay?: boolean;
  /** Ob der Container leer ist (nur für Edit-Modus) */
  isEmpty?: boolean;
}

/**
 * DroppableContainer - Container-Komponente für Drag-and-Drop-Zielbereich
 * 
 * Diese Komponente erstellt einen Bereich, in dem ziehbare Elemente abgelegt werden können.
 * Sie verwendet dnd Kit's useDroppable Hook für die Drop-Funktionalität.
 * 
 * Features:
 * - <PERSON><PERSON><PERSON><PERSON>, wenn ein Element über dem Container schwebt
 * - Visuelle Rückmeldung während des Drag-Vorgangs
 * - Barrierefreie Implementierung
 * - Anpassbare Styling-Optionen
 * 
 * @param id - Eindeutige Identifikation für den Container
 * @param children - Inhalt des Containers
 * @param className - Zusätzliche CSS-Klassen
 * @param isOverlay - Ob der Container als Overlay dargestellt werden soll
 */
export const DroppableContainer: React.FC<DroppableContainerProps> = ({
  id,
  children,
  className = '',
  isOverlay = false,
  isEmpty = false
}) => {
  const {
    setNodeRef,
    isOver,
    active
  } = useDroppable({
    id,
  });

  // Bestimme die CSS-Klassen basierend auf dem Drag-Status
  const containerClasses = [
    styles.droppableContainer,
    className,
    isOver && styles['droppableContainer--over'],
    active && styles['droppableContainer--active'],
    isOverlay && styles['droppableContainer--overlay'],
    isEmpty && 'border-2 border-dashed border-gray-300 bg-gray-50'
  ].filter(Boolean).join(' ');



  return (
    <div
      ref={setNodeRef}
      className={containerClasses}
      role="region"
      aria-label={`Ablagebereich für KPI-Karten ${id}`}
      aria-describedby={isOver ? `drop-hint-${id}` : undefined}
    >
      {children}
      
      {/* Anzeige für leere Positionen im Edit-Modus */}
      {isEmpty && (
        <div className="flex flex-col items-center justify-center h-full p-4 text-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          <p className="text-sm font-medium">Leere Position</p>
          <p className="text-xs mt-1">Karte hier ablegen</p>
        </div>
      )}
      
      {/* Visuelle Hinweise für Benutzer */}
      {isOver && (
        <div
          id={`drop-hint-${id}`}
          className={styles.dropHint}
        >
          <div className={styles.dropHintText}>
            Hier ablegen
          </div>
        </div>
      )}
      
      {/* Accessibility: Versteckter Text für Screenreader */}
      <div className="sr-only">
        {active && isOver && 'Karte kann hier abgelegt werden'}
        {active && !isOver && 'Ziehen Sie die Karte zu einem gültigen Ablagebereich'}
      </div>
    </div>
  );
};

export default DroppableContainer;