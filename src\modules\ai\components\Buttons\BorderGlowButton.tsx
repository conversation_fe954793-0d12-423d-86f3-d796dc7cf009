import { useEffect, useRef, useState, forwardRef } from 'react'
import { cn } from '@/lib/utils'
import { ButtonHTMLAttributes } from 'react'
// CSS-Keyframes für die Border Shine Animation einfügen
if (typeof document !== 'undefined') {
  const styleId = 'border-glow-button-keyframes'
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style')
    style.id = styleId
    style.textContent = `
      @keyframes spin-around {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    `
    document.head.appendChild(style)
  }
}

/**
 * Props für die BorderGlowButton Komponente
 */
export interface BorderGlowButtonProps extends Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'children'> {
  /**
   * Callback für Klick-Events
   */
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
  
  /**
   * Deaktiviert den Button wenn true
   */
  disabled?: boolean;
  
  /**
   * Variante des Buttons - beeinflusst das Verhalten und die Shine-Animation
   * 'default' = aktiver Zustand mit Shine-Animation
   * 'ghost' = inaktiver Zustand ohne Animation
   */
  variant?: 'default' | 'ghost';
  
  /**
   * Zusätzliche CSS-Klassen für das äußere Button-Element
   */
  className?: string;
  
  /**
   * Kinder-Elemente des Buttons (optional - fallback auf ursprünglichen Text)
   */
  children?: React.ReactNode;
}

const BorderGlowButton = forwardRef<HTMLButtonElement, BorderGlowButtonProps>(
  ({ 
    onClick, 
    disabled = false,
    variant = 'default',
    className = '',
    children,
    ...rest
  }, ref) => {
    const buttonRef = useRef<HTMLButtonElement>(null)
    const [mousePosition, setMousePosition] = useState({ x: '-100%', y: '-100%' })

    // Verwende den forwarded ref oder den internen ref
    const combinedRef = (node: HTMLButtonElement) => {
      buttonRef.current = node;
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    };

    useEffect(() => {
      if (disabled) return; // Kein Glow-Effekt wenn disabled
      
      const handleMouseMove = (e: MouseEvent) => {
        if (!buttonRef.current) return
        const rect = buttonRef.current.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top
        setMousePosition({ x: `${x}px`, y: `${y}px` })
      }
      document.addEventListener('mousemove', handleMouseMove)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
      }
    }, [disabled])

    // Aktiver Zustand für Shine-Animation
    const isActive = variant === 'default'

    return (
      <button
        className={cn(
          // Ursprüngliches Design beibehalten
          'relative overflow-hidden rounded-lg bg-[#e5e7eb] transform transition-transform ease-in-out',
          disabled ? 'opacity-50 cursor-not-allowed' : 'active:scale-90',
          className
        )}
        ref={combinedRef}
        onClick={onClick}
        disabled={disabled}
        {...rest}
        style={{
          // CSS Custom Properties für die Shimmer-Animation
          ...rest.style,
          '--shimmer-color': 'rgba(56 189 248)',
          '--shine-duration': '3s'
        } as React.CSSProperties}
      >
        {/* Glow-Effekt (nur wenn nicht disabled) */}
        {!disabled && (
          <span
            className="absolute z-0 h-28 w-28 -translate-x-1/2 -translate-y-1/2 bg-[radial-gradient(#F37021_0%,transparent_70%)]"
            style={{
              left: mousePosition.x,
              top: mousePosition.y,
            }}
          />
        )}

        {/* Spark Effect (nur wenn aktiv und nicht disabled) - aus shimmer-button */}
        {/* Spark Container */}
        {isActive && !disabled && (
          <div 
            className={cn(
              "-z-30 blur-[2px]",
              "absolute inset-0 overflow-visible [container-type:size]",
            )}
          >  
            {/* Spark */}         
            <div className="absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]">
              {/* spark before */}
              <div className="absolute -inset-full w-auto rotate-0 animate-spin-around [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]" />
            </div>
          </div>
        )}
        
        {/* Content Container - ursprüngliches Design */}
        <div className="relative z-10 m-[1px] rounded-[calc(0.5rem-1px)] bg-white/90 px-4 py-1 text-xs text-slate-500 backdrop-blur-sm flex items-center justify-center">
          {children || "AI+ Datenbanksuche"}
        </div>
      </button>
    )
  }
)

BorderGlowButton.displayName = 'BorderGlowButton'

export default BorderGlowButton
