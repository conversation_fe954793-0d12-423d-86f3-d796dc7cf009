/**
 * Hilfsfunktionen für den Export von Dashboard-Daten
 */

import apiService from '@/services/api.service';
import { DateRange } from 'react-day-picker';
import { ChartData } from '@/types/chart.types';

/**
 * Sammelt Daten aus allen Charts für den Export
 * @param dateRange Optionaler Datumsbereich für die Filterung
 * @returns Promise mit gesammelten Chart-Daten
 */
export async function collectAllChartData(dateRange?: DateRange): Promise<{ name: string; data: ChartData[] }[]> {
  const chartData: { name: string; data: ChartData[] }[] = [];

  try {
    // Konvertiere dateRange zu API-Format
    const startDate = dateRange?.from ? dateRange.from.toISOString().split('T')[0] : undefined;
    const endDate = dateRange?.to ? dateRange.to.toISOString().split('T')[0] : undefined;

    // Parallele Datenabfrage für bessere Performance
    const [
      serviceLevelResult,
      tagesleistungResult,
      deliveryResult,
      pickingResult,
      returnsResult
    ] = await Promise.all([
      apiService.getServiceLevelData(startDate, endDate),
      apiService.getTagesleistungData(startDate, endDate),
      apiService.getDeliveryPositionsData(startDate, endDate),
      apiService.getPickingData(startDate, endDate),
      apiService.getReturnsData()
    ]);

    // 1. Servicegrad-Daten
    if (serviceLevelResult && serviceLevelResult.length > 0) {
      const processedServiceLevelData: ChartData[] = serviceLevelResult.map((item: any) => ({
        name: item.datum,
        date: new Date(item.datum),
        servicegrad: item.servicegrad || 0
      }));

      chartData.push({
        name: 'Servicegrad',
        data: processedServiceLevelData
      });
    }

    // 2. Tagesleistung-Daten
    if (tagesleistungResult && tagesleistungResult.length > 0) {
      const processedTagesleistungData: ChartData[] = tagesleistungResult.map((item: any) => ({
        name: item.date,
        date: new Date(item.date),
        produzierte_tonnagen: item.produzierte_tonnagen || 0,
        direktverladung_kiaa: item.direktverladung_kiaa || 0,
        umschlag: item.umschlag || 0,
        kg_pro_colli: item.kg_pro_colli || 0,
        elefanten: item.elefanten || 0
      }));

      chartData.push({
        name: 'Tagesleistung',
        data: processedTagesleistungData
      });
    }

    // 3. Lieferpositionen-Daten
    if (deliveryResult && deliveryResult.length > 0) {
      const processedDeliveryData: ChartData[] = deliveryResult.map((item: any) => ({
        name: item.date,
        date: new Date(item.date),
        ausgeliefert_lup: item.ausgeliefert_lup || 0,
        rueckstaendig: item.rueckstaendig || 0
      }));

      chartData.push({
        name: 'Lieferpositionen',
        data: processedDeliveryData
      });
    }

    // 4. Picking-Daten
    if (pickingResult && pickingResult.length > 0) {
      const processedPickingData: ChartData[] = pickingResult.map((item: any) => ({
        name: item.date,
        date: new Date(item.date),
        atrl: item.atrl || 0,
        aril: item.aril || 0,
        fuellgrad_aril: item.fuellgrad_aril || 0
      }));

      chartData.push({
        name: 'Kommissionierung',
        data: processedPickingData
      });
    }

    // 5. Returns-Daten (Q-Meldungen)
    if (returnsResult && returnsResult.length > 0) {
      // Filtere Returns-Daten nach Datumsbereich
      let filteredReturns = returnsResult;
      if (dateRange?.from || dateRange?.to) {
        const fromDate = dateRange?.from ? new Date(dateRange.from) : null;
        const toDate = dateRange?.to ? new Date(dateRange.to) : null;

        if (fromDate) fromDate.setHours(0, 0, 0, 0);
        if (toDate) toDate.setHours(23, 59, 59, 999);

        filteredReturns = returnsResult.filter((row: any) => {
          if (!row.date) return false;

          try {
            const rowDateStr = String(row.date).trim();
            const rowDate = new Date(rowDateStr);

            if (isNaN(rowDate.getTime())) {
              return false;
            }

            const isAfterFrom = !fromDate || rowDate >= fromDate;
            const isBeforeTo = !toDate || rowDate <= toDate;

            return isAfterFrom && isBeforeTo;
          } catch (err) {
            return false;
          }
        });
      }

      // Gruppiere Returns-Daten nach Name
      const groupedReturns: Record<string, ChartData> = {};
      filteredReturns.forEach((row: any) => {
        if (!groupedReturns[row.name]) {
          groupedReturns[row.name] = {
            name: row.name,
            value: 0,
            fill: row.fill
          };
        }
        const existingEntry = groupedReturns[row.name];
        if (existingEntry && existingEntry.value !== undefined) {
          existingEntry.value += Number(row.value) || 0;
        }
      });

      chartData.push({
        name: 'Q-Meldungen',
        data: Object.values(groupedReturns)
      });
    }

    return chartData;
  } catch (error) {
    console.error('Fehler beim Sammeln der Chart-Daten:', error);
    return chartData; // Return empty array on error
  }
}

/**
 * Sammelt ARiL-Daten für den Export
 * @param dateRange Optionaler Datumsbereich für die Filterung
 * @returns Promise mit gesammelten ARiL Chart-Daten
 */
export async function collectArilChartData(dateRange?: DateRange): Promise<{ name: string; data: ChartData[] }[]> {
  const chartData: { name: string; data: ChartData[] }[] = [];

  try {
    // Konvertiere dateRange zu API-Format
    const startDate = dateRange?.from ? dateRange.from.toISOString().split('T')[0] : undefined;
    const endDate = dateRange?.to ? dateRange.to.toISOString().split('T')[0] : undefined;

    // ARiL-Daten abrufen
    const arilResult = await apiService.getArilData(startDate, endDate);

    if (arilResult && arilResult.length > 0) {
      const processedData: ChartData[] = arilResult.map((row: any) => ({
        name: row.Datum,
        date: new Date(row.Datum),
        waTaPositionen: row.waTaPositionen || 0,
        cuttingLagerKunde: row.cuttingLagerKunde || 0,
        cuttingLagerRest: row.cuttingLagerRest || 0,
        Umlagerungen: row.Umlagerungen || 0,
        lagerCutting: row.lagerCutting || 0
      }));

      chartData.push({
        name: 'ARiL Bewegungen',
        data: processedData
      });
    }

    return chartData;
  } catch (error) {
    console.error('Fehler beim Sammeln der ARiL Chart-Daten:', error);
    return chartData; // Return empty array on error
  }
}

/**
 * Sammelt ATrL-Daten für den Export
 * @param dateRange Optionaler Datumsbereich für die Filterung
 * @returns Promise mit gesammelten ATrL Chart-Daten
 */
export async function collectAtrlChartData(dateRange?: DateRange): Promise<{ name: string; data: ChartData[] }[]> {
  const chartData: { name: string; data: ChartData[] }[] = [];

  try {
    // Konvertiere dateRange zu API-Format
    const startDate = dateRange?.from ? dateRange.from.toISOString().split('T')[0] : undefined;
    const endDate = dateRange?.to ? dateRange.to.toISOString().split('T')[0] : undefined;

    // ATrL-Daten abrufen
    const atrlResult = await apiService.getAtrlData(startDate, endDate);

    if (atrlResult && atrlResult.length > 0) {
      const processedData: ChartData[] = atrlResult.map((row: any) => ({
        name: row.Datum,
        date: new Date(row.Datum),
        weAtrl: row.weAtrl || 0,
        EinlagerungAblKunde: row.EinlagerungAblKunde || 0,
        EinlagerungAblRest: row.EinlagerungAblRest || 0,
        umlagerungen: row.umlagerungen || 0,
        waTaPositionen: row.waTaPositionen || 0,
        AuslagerungAbl: row.AuslagerungAbl || 0
      }));

      chartData.push({
        name: 'ATrL Bewegungen',
        data: processedData
      });
    }

    return chartData;
  } catch (error) {
    console.error('Fehler beim Sammeln der ATrL Chart-Daten:', error);
    return chartData; // Return empty array on error
  }
}

/**
 * Sammelt Maschinen-Effizienz-Daten für den Export
 * @param dateRange Optionaler Datumsbereich für die Filterung
 * @returns Promise mit gesammelten Maschinen Chart-Daten
 */
export async function collectMachinesChartData(dateRange?: DateRange): Promise<{ name: string; data: ChartData[] }[]> {
  const chartData: { name: string; data: ChartData[] }[] = [];

  try {
    // Konvertiere dateRange zu API-Format
    const startDate = dateRange?.from ? dateRange.from.toISOString().split('T')[0] : undefined;
    const endDate = dateRange?.to ? dateRange.to.toISOString().split('T')[0] : undefined;

    // Maschinen-Effizienz-Daten abrufen
    const machinesResult = await apiService.getMaschinenEfficiency();

    if (machinesResult && machinesResult.length > 0) {
      // Filtere nach Datumsbereich falls angegeben
      let filteredData = machinesResult;
      if (dateRange?.from || dateRange?.to) {
        const fromDate = dateRange?.from ? new Date(dateRange.from) : null;
        const toDate = dateRange?.to ? new Date(dateRange.to) : null;

        if (fromDate) fromDate.setHours(0, 0, 0, 0);
        if (toDate) toDate.setHours(23, 59, 59, 999);

        filteredData = machinesResult.filter((row: any) => {
          if (!row.Datum) return false;

          try {
            const rowDateStr = String(row.Datum).trim();
            const rowDate = new Date(rowDateStr);

            if (isNaN(rowDate.getTime())) {
              return false;
            }

            const isAfterFrom = !fromDate || rowDate >= fromDate;
            const isBeforeTo = !toDate || rowDate <= toDate;

            return isAfterFrom && isBeforeTo;
          } catch (err) {
            return false;
          }
        });
      }

      // Gruppiere Daten nach Maschine für bessere Export-Struktur
      const groupedByMachine: Record<string, ChartData[]> = {};

      filteredData.forEach((row: any) => {
        const machineName = row.Machine || 'Unbekannte Maschine';

        if (!groupedByMachine[machineName]) {
          groupedByMachine[machineName] = [];
        }

        groupedByMachine[machineName].push({
          name: machineName,
          date: new Date(row.Datum),
          sollSchnitte: row.sollSchnitte || 0,
          tagesSchnitte: row.tagesSchnitte || 0,
          istSchnitteProStunde: row.istSchnitteProStunde || 0,
          effizienzProzent: row.effizienzProzent || 0,
          maschinenTyp: machineName.includes('H3') ? 'H3' : 'H1'
        });
      });

      // Füge jeden Maschinen-Chart hinzu
      Object.entries(groupedByMachine).forEach(([machineName, machineData]) => {
        chartData.push({
          name: `Maschine ${machineName}`,
          data: machineData
        });
      });

      // Füge auch aggregierte Daten hinzu
      const processedData: ChartData[] = filteredData.map((row: any) => ({
        name: row.Machine || 'Unbekannte Maschine',
        date: new Date(row.Datum),
        sollSchnitte: row.sollSchnitte || 0,
        tagesSchnitte: row.tagesSchnitte || 0,
        istSchnitteProStunde: row.istSchnitteProStunde || 0,
        effizienzProzent: row.effizienzProzent || 0,
        maschinenTyp: (row.Machine || '').includes('H3') ? 'H3' : 'H1'
      }));

      chartData.push({
        name: 'Maschinen Effizienz Gesamt',
        data: processedData
      });
    }

    return chartData;
  } catch (error) {
    console.error('Fehler beim Sammeln der Maschinen Chart-Daten:', error);
    return chartData; // Return empty array on error
  }
}

/**
 * Sammelt WE-Daten (Wareneingang) für den Export
 * @param dateRange Optionaler Datumsbereich für die Filterung
 * @returns Promise mit gesammelten WE Chart-Daten
 */
export async function collectIncomingGoodsChartData(dateRange?: DateRange): Promise<{ name: string; data: ChartData[] }[]> {
  const chartData: { name: string; data: ChartData[] }[] = [];

  try {
    // Konvertiere dateRange zu API-Format
    const startDate = dateRange?.from ? dateRange.from.toISOString().split('T')[0] : undefined;
    const endDate = dateRange?.to ? dateRange.to.toISOString().split('T')[0] : undefined;

    // WE-Daten abrufen
    const weResult = await apiService.getWEData(startDate, endDate);

    if (weResult && weResult.length > 0) {
      const processedWEData: ChartData[] = weResult.map((item: any) => ({
        name: item.datum,
        date: new Date(item.datum),
        weAtrl: item.weAtrl || 0,
        weManl: item.weManl || 0
      }));

      chartData.push({
        name: 'Wareneingang Positionen',
        data: processedWEData
      });
    }

    return chartData;
  } catch (error) {
    console.error('Fehler beim Sammeln der WE Chart-Daten:', error);
    return chartData; // Return empty array on error
  }
}

/**
 * Sammelt Mock-Daten für Cutting-spezifische Charts (da die echten API-Endpunkte noch nicht implementiert sind)
 * @param dateRange Optionaler Datumsbereich für die Filterung
 * @returns Promise mit gesammelten Mock Cutting Chart-Daten
 */
export async function collectCuttingChartData(dateRange?: DateRange): Promise<{ name: string; data: ChartData[] }[]> {
  const chartData: { name: string; data: ChartData[] }[] = [];

  try {
    // Generiere Mock-Daten für die letzten 30 Tage
    const days = 30;
    const mockData: ChartData[] = [];

    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      mockData.push({
        name: date.toISOString().split('T')[0],
        date: date,
        cutTT: Math.floor(Math.random() * 100) + 50,
        cutTR: Math.floor(Math.random() * 80) + 30,
        cutRR: Math.floor(Math.random() * 60) + 20,
        pickCut: Math.floor(Math.random() * 40) + 10,
        total: 0 // wird berechnet
      });
    }

    // Berechne Totals
    mockData.forEach(item => {
      item.total = (item.cutTT || 0) + (item.cutTR || 0) + (item.cutRR || 0) + (item.pickCut || 0);
    });

    chartData.push({
      name: 'Cuttings nach Maschinen',
      data: mockData
    });

    // Mock Lager-Daten
    const lagerData: ChartData[] = [];
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      lagerData.push({
        name: date.toISOString().split('T')[0],
        date: date,
        lagerSumme: Math.floor(Math.random() * 200) + 100,
        cutLagerKSumme: Math.floor(Math.random() * 150) + 50,
        cutLagerRSumme: Math.floor(Math.random() * 100) + 25,
        total: 0
      });
    }

    lagerData.forEach(item => {
      item.total = (item.lagerSumme || 0) + (item.cutLagerKSumme || 0) + (item.cutLagerRSumme || 0);
    });

    chartData.push({
      name: 'Lagerbestand Schnitte',
      data: lagerData
    });

    // Mock Returns-Daten (Q-Meldungen)
    const returnsData: ChartData[] = [];
    const returnTypes = ['Q1 - Maßfehler', 'Q2 - Oberflächenfehler', 'Q3 - Formfehler', 'Q4 - Sonstiges'];

    returnTypes.forEach(type => {
      returnsData.push({
        name: type,
        value: Math.floor(Math.random() * 50) + 10,
        fill: `hsl(${Math.floor(Math.random() * 360)}, 70%, 50%)`
      });
    });

    chartData.push({
      name: 'Retouren',
      data: returnsData
    });

    return chartData;
  } catch (error) {
    console.error('Fehler beim Sammeln der Mock Cutting Chart-Daten:', error);
    return chartData;
  }
}
