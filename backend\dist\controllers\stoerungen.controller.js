"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.imageUploadMiddleware = exports.StoerungenController = void 0;
const multer_1 = __importDefault(require("multer"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const stoerungen_repository_1 = require("../repositories/stoerungen.repository");
// ✅ Migration zu Drizzle ORM abgeschlossen
// Alle Funktionalitäten wurden erfolgreich auf das neue Drizzle-Schema umgestellt
// Repository-Pattern mit Singleton-Instanz für optimale Performance
// Configure multer for image uploads (keeping this as it's file-system related)
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(process.cwd(), "uploads", "stoerungen");
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
        cb(null, file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname));
    },
});
const imageUpload = (0, multer_1.default)({
    storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit for images
    },
    fileFilter: (req, file, cb) => {
        // Check if file is an image
        if (file.mimetype.startsWith("image/")) {
            cb(null, true);
        }
        else {
            cb(new Error("Only image files are allowed!"), false);
        }
    },
});
class StoerungenController {
    constructor() {
        this.repository = stoerungen_repository_1.StoerungenRepository.getInstance();
    }
    /**
     * Alle Störungen mit optionalen Filtern abrufen
     * GET /api/stoerungen?status=NEW&severity=HIGH&limit=10&offset=0
     */
    async getStoerungen(req, res) {
        try {
            const { status, severity, category, affected_system, limit, offset } = req.query;
            const options = {
                status: status,
                severity: severity,
                category: category,
                affected_system: affected_system,
                limit: limit ? parseInt(limit) : undefined,
                offset: offset ? parseInt(offset) : undefined,
            };
            const stoerungen = await this.repository.getStoerungen(options);
            res.json({
                success: true,
                data: stoerungen,
                count: stoerungen.length,
                filters: options,
            });
        }
        catch (error) {
            console.error("Error fetching störungen:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Abrufen der Störungen",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * Einzelne Störung mit Kommentaren abrufen
     * GET /api/stoerungen/:id
     */
    async getStoerungById(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                return res.status(400).json({
                    success: false,
                    error: "Ungültige Störungs-ID",
                });
            }
            const stoerung = await this.repository.getStoerungById(id);
            if (!stoerung) {
                return res.status(404).json({
                    success: false,
                    error: "Störung nicht gefunden",
                });
            }
            res.json({
                success: true,
                data: stoerung,
            });
        }
        catch (error) {
            console.error("Error fetching störung by ID:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Abrufen der Störung",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * Neue Störung erstellen
     * POST /api/stoerungen
     */
    async createStoerung(req, res) {
        try {
            const data = req.body;
            // Validierung der erforderlichen Felder
            if (!data.title || !data.severity) {
                return res.status(400).json({
                    success: false,
                    error: "Titel und Schweregrad sind erforderlich",
                    required: ["title", "severity"],
                });
            }
            // Standardwerte setzen
            if (!data.status) {
                data.status = "NEW";
            }
            const stoerung = await this.repository.createStoerung(data);
            res.status(201).json({
                success: true,
                data: stoerung,
                message: "Störung erfolgreich erstellt",
            });
        }
        catch (error) {
            console.error("Error creating störung:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Erstellen der Störung",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * Störung aktualisieren
     * PUT /api/stoerungen/:id
     */
    async updateStoerung(req, res) {
        console.log("[DEBUG] Controller updateStoerung aufgerufen!");
        console.log("[DEBUG] Request params:", req.params);
        console.log("[DEBUG] Request body:", JSON.stringify(req.body, null, 2));
        try {
            const id = parseInt(req.params.id);
            const data = req.body;
            console.log("[DEBUG] Controller - ID:", id);
            console.log("[DEBUG] Controller - Data:", JSON.stringify(data, null, 2));
            if (isNaN(id)) {
                console.log("[DEBUG] Controller - Ungültige ID:", id);
                return res.status(400).json({
                    success: false,
                    error: "Ungültige Störungs-ID",
                });
            }
            console.log("[DEBUG] Controller - Rufe repository.updateStoerung auf");
            const stoerung = await this.repository.updateStoerung(id, data);
            console.log("[DEBUG] Controller - Repository-Ergebnis:", JSON.stringify(stoerung, null, 2));
            res.json({
                success: true,
                data: stoerung,
                message: "Störung erfolgreich aktualisiert",
            });
        }
        catch (error) {
            console.error("[DEBUG] Controller - Fehler:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Aktualisieren der Störung",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * Störung löschen
     * DELETE /api/stoerungen/:id
     */
    async deleteStoerung(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                return res.status(400).json({
                    success: false,
                    error: "Ungültige Störungs-ID",
                });
            }
            const success = await this.repository.deleteStoerung(id);
            if (!success) {
                return res.status(404).json({
                    success: false,
                    error: "Störung nicht gefunden oder konnte nicht gelöscht werden",
                });
            }
            res.json({
                success: true,
                message: "Störung erfolgreich gelöscht",
            });
        }
        catch (error) {
            console.error("Error deleting störung:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Löschen der Störung",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * Kommentar zu Störung hinzufügen
     * POST /api/stoerungen/:id/comments
     */
    async addComment(req, res) {
        try {
            const stoerung_id = parseInt(req.params.id);
            const { comment, user_id } = req.body;
            if (isNaN(stoerung_id)) {
                return res.status(400).json({
                    success: false,
                    error: "Ungültige Störungs-ID",
                });
            }
            if (!comment) {
                return res.status(400).json({
                    success: false,
                    error: "Kommentar ist erforderlich",
                });
            }
            // Angepasst an die neue Datenbankstruktur
            const commentData = {
                stoerungId: stoerung_id,
                comment: comment, // Geändert von 'content' zu 'comment'
                userId: user_id, // Geändert von 'author' zu 'userId'
            };
            const newComment = await this.repository.addComment(commentData);
            res.status(201).json({
                success: true,
                data: newComment,
                message: "Kommentar erfolgreich hinzugefügt",
            });
        }
        catch (error) {
            console.error("Error adding comment:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Hinzufügen des Kommentars",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * Störungs-Statistiken abrufen
     * GET /api/stoerungen/stats
     */
    async getStoerungsStats(req, res) {
        try {
            const stats = await this.repository.getStoerungsStats();
            res.json({
                success: true,
                data: stats,
                timestamp: new Date().toISOString(),
            });
        }
        catch (error) {
            console.error("Error fetching störungs stats:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Abrufen der Statistiken",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * Bild zu Störung hochladen
     * POST /api/stoerungen/:id/upload-image
     */
    async uploadImage(req, res) {
        try {
            const stoerung_id = parseInt(req.params.id);
            if (isNaN(stoerung_id)) {
                return res.status(400).json({
                    success: false,
                    error: "Ungültige Störungs-ID",
                });
            }
            // Prüfen ob Störung existiert
            const stoerung = await this.repository.getStoerungById(stoerung_id);
            if (!stoerung) {
                return res.status(404).json({
                    success: false,
                    error: "Störung nicht gefunden",
                });
            }
            if (!req.file) {
                return res.status(400).json({
                    success: false,
                    error: "Keine Datei hochgeladen",
                });
            }
            // Create attachment in database with actual DB structure
            const attachment = await this.repository.createAttachment({
                stoerungId: stoerung_id,
                filename: req.file.filename,
                storedName: req.file.filename, // Use filename as stored name
                mimeType: req.file.mimetype,
                fileSize: req.file.size,
                filePath: req.file.path, // Keep file path for reference
                fileType: "image", // Set file type based on mime type
                uploadedBy: "system", // TODO: Use actual user from auth
            });
            // Clean up temporary file
            try {
                fs.unlinkSync(req.file.path);
            }
            catch (cleanupError) {
                console.warn("Warning: Could not clean up temporary file:", req.file.path);
            }
            res.status(201).json({
                success: true,
                data: attachment,
                message: "Datei erfolgreich hochgeladen",
            });
        }
        catch (error) {
            console.error("Error uploading image:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Hochladen der Datei",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * Anhänge einer Störung abrufen
     * GET /api/stoerungen/:id/attachments
     */
    async getAttachments(req, res) {
        console.log("[DEBUG] getAttachments Route aufgerufen mit ID:", req.params.id);
        try {
            const stoerung_id = parseInt(req.params.id);
            console.log("[DEBUG] geparste stoerung_id:", stoerung_id);
            if (isNaN(stoerung_id)) {
                return res.status(400).json({
                    success: false,
                    error: "Ungültige Störungs-ID",
                });
            }
            // Use database instead of filesystem
            const attachments = await this.repository.getAttachments(stoerung_id);
            console.log("[DEBUG] Anhänge aus Datenbank geladen:", attachments.length);
            res.json({
                success: true,
                data: attachments,
                count: attachments.length,
            });
        }
        catch (error) {
            console.error("Error getting attachments:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Abrufen der Anhänge",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * Anhang löschen
     * DELETE /api/stoerungen/attachments/:attachmentId
     */
    async deleteAttachment(req, res) {
        try {
            const attachmentId = parseInt(req.params.attachmentId);
            if (isNaN(attachmentId)) {
                return res.status(400).json({
                    success: false,
                    error: "Ungültige Anhang-ID",
                });
            }
            // Delete attachment from database
            const success = await this.repository.deleteAttachment(attachmentId);
            if (!success) {
                return res.status(404).json({
                    success: false,
                    error: "Anhang nicht gefunden",
                });
            }
            res.json({
                success: true,
                message: "Anhang erfolgreich gelöscht",
            });
        }
        catch (error) {
            console.error("Error deleting attachment:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Löschen des Anhangs",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * Anhang-Datei herunterladen
     * GET /api/stoerungen/attachments/:attachmentId/file
     */
    async getAttachmentFile(req, res) {
        try {
            const attachmentId = parseInt(req.params.attachmentId);
            if (isNaN(attachmentId)) {
                return res.status(400).json({
                    success: false,
                    error: "Ungültige Anhang-ID",
                });
            }
            // Get attachment from database
            const attachment = await this.repository.getAttachmentById(attachmentId);
            if (!attachment) {
                return res.status(404).json({
                    success: false,
                    error: "Anhang nicht gefunden",
                });
            }
            // Serve from filesystem if path exists
            if (attachment.filePath && fs.existsSync(attachment.filePath)) {
                res.sendFile(path.resolve(attachment.filePath));
            }
            // No file data available
            else {
                return res.status(404).json({
                    success: false,
                    error: "Datei-Inhalt nicht verfügbar",
                });
            }
        }
        catch (error) {
            console.error("Error serving attachment file:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Abrufen der Datei",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * Aktive Störungen abrufen (Status: NEW, IN_PROGRESS)
     * GET /api/stoerungen/active
     */
    async getActiveStoerungen(req, res) {
        try {
            const activeStoerungen = await this.repository.getStoerungen({
                status: "NEW,IN_PROGRESS", // Mehrere Status als kommagetrennte Liste
            });
            res.json({
                success: true,
                data: activeStoerungen,
                count: activeStoerungen.length,
                message: "Aktive Störungen erfolgreich abgerufen",
            });
        }
        catch (error) {
            console.error("Error fetching active störungen:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Abrufen der aktiven Störungen",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * System-Status abrufen
     * GET /api/system/status
     */
    async getSystemStatus(req, res) {
        try {
            const systemStatus = await this.repository.getSystemStatus();
            res.json({
                success: true,
                data: systemStatus,
                timestamp: new Date().toISOString(),
            });
        }
        catch (error) {
            console.error("Error fetching system status:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Abrufen des System-Status",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * System-Status aktualisieren
     * PUT /api/system/status
     *
     * Aktualisiert den Status eines Systems in der Datenbank.
     * Hinweis: Nachrichten werden separat in der systemStatusMessage Tabelle gespeichert.
     *
     * @param req - Request mit { status, system_name, updated_by? }
     * @param res - Response mit Erfolgs- oder Fehlermeldung
     */
    async updateSystemStatus(req, res) {
        try {
            const { status, system_name, updated_by } = req.body;
            // Validierung der erforderlichen Parameter
            if (!status || !system_name) {
                return res.status(400).json({
                    success: false,
                    error: "Status und System-Name sind erforderlich",
                });
            }
            // Validierung des Status-Wertes
            const validStatuses = ["OK", "WARNING", "ERROR", "OFF"];
            if (!validStatuses.includes(status)) {
                return res.status(400).json({
                    success: false,
                    error: `Ungültiger Status. Erlaubte Werte: ${validStatuses.join(", ")}`,
                });
            }
            // SystemStatusUpdateData Interface korrekt verwenden
            const updatedStatus = await this.repository.updateSystemStatus({
                system_name: system_name, // Use snake_case to match interface
                status,
            });
            res.json({
                success: true,
                data: updatedStatus,
                message: "System-Status erfolgreich aktualisiert",
            });
        }
        catch (error) {
            console.error("Error updating system status:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Aktualisieren des System-Status",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
    /**
     * Störungskategorien abrufen
     * GET /api/stoerungen/kategorien
     */
    async getStoerungKategorien(req, res) {
        console.log("[DEBUG] getStoerungKategorien Route aufgerufen");
        try {
            // Hardcoded Kategorien-Daten wie im Fallback des Frontend-Service
            const kategorien = [
                {
                    name: "Systeme",
                    systeme: [
                        { name: "SAP" },
                        { name: "ITM" },
                        { name: "Wamas" },
                        { name: "Microsoft Application" },
                        { name: "Citrix" },
                        { name: "MFR" },
                    ],
                },
                {
                    name: "Infrastruktur",
                    systeme: [{ name: "Netzwerk" }, { name: "WLAN" }],
                },
                {
                    name: "Technische Komponenten",
                    systeme: [
                        { name: "Förderband" },
                        { name: "FTS" },
                        { name: "RBG" },
                        { name: "Greifer" },
                        { name: "Schrumpfanlage" },
                        { name: "Ablängmaschinen" },
                    ],
                },
                {
                    name: "Kommissionierung",
                    systeme: [
                        { name: "ATrL" },
                        { name: "ARiL" },
                        { name: "Schäfer Karussell (SCS)" },
                    ],
                },
                {
                    name: "Hardware",
                    systeme: [
                        { name: "Mobile Drucker" },
                        { name: "Label Drucker" },
                        { name: "Lieferschein Drucker" },
                        { name: "Terminal" },
                        { name: "PC" },
                    ],
                },
            ];
            console.log("[DEBUG] Sende Kategorien:", kategorien.length);
            res.json({
                success: true,
                data: kategorien,
                count: kategorien.length,
            });
        }
        catch (error) {
            console.error("Error getting störungskategorien:", error);
            res.status(500).json({
                success: false,
                error: "Fehler beim Abrufen der Störungskategorien",
                message: error instanceof Error ? error.message : "Unbekannter Fehler",
            });
        }
    }
}
exports.StoerungenController = StoerungenController;
// Export the multer middleware (keeping this as it's still functional)
exports.imageUploadMiddleware = imageUpload.single("image");
