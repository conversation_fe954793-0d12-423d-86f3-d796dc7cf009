<user_prompt name="SeniorSoftwareDeveloperTask">
    <task>
        <!-- <PERSON><PERSON><PERSON><PERSON> hier die Aufgabe oder das gewünschte Feature -->
        [AUFGABENBESCHREIBUNG_EINFÜGEN]  
    </task>

    <context>
        <!-- Falls nötig: zusätzliche Infos über bestehenden Code, Dateien, Architektur -->
        [KONTEXT_ODER_RELEVANTE_DETAILS_EINFÜGEN]  
    </context>

    <requirements>
        - Halte dich an den bestehenden Techstack:  
          Electron, Vite, SWC  
          React, Tailwind CSS, shadcn/ui, Recharts  
          React Query, TanStack Router  
          Node.js (Express.js), SQLite (libsql, Drizzle ORM)  
          TypeScript  
          Vitest, Playwright  

        - Schreibe sauberen, modularen und wartbaren Code.  
        - Verwende konsistente Benennungen und Code-Strukturen.  
        - Achte auf Developer Experience (DX) und User Experience (UX).  
        - Integriere Tests (Unit + E2E).  
    </requirements>

    <expected_output>
        - <analysis>Analyse der Aufgabe</analysis>  
        - <design_options>Vorschläge mit Vor-/Nachteilen</design_options>  
        - <final_solution>Gewählte Lösung mit Begründung</final_solution>  
        - <solution_code>TypeScript-Code, kommentiert</solution_code>  
        - <testing_strategy>Konkrete Unit- und E2E-Testschritte</testing_strategy>  
        - <dx_ux_considerations>Empfehlungen zur Verbesserung von DX/UX</dx_ux_considerations>  
    </expected_output>
</user_prompt>
