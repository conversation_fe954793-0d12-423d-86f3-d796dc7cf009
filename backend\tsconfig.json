{"compilerOptions": {"target": "es2018", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "src/tests/**/*", "node_modules/express-rate-limit/tsconfig.json"]}