import { CardCarousel } from "@/components/ui/card-carousel";
import { motion } from "framer-motion";
import { useNavigate } from "@tanstack/react-router";
import { useAuthContext } from "@/contexts/AuthContext";

// Animierte Unterstreichung für den Willkommenstext
const AnimatedTextUnderlign = ({
  firstName,
  lastName,
}: {
  firstName?: string;
  lastName?: string;
}) => {
  // Definiere die Animation für das Zeichnen der Unterstreichung
  const draw = {
    hidden: { pathLength: 0, opacity: 0 },
    visible: () => ({
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: {
          delay: 0.5,
          duration: 2,
          type: "spring" as const,
          bounce: 0,
        },
        opacity: { delay: 0.5, duration: 1 },
      },
    }),
  };
  return (
    <div>
      <h1 className="text-center text-5xl font-bold tracking-tight text-gray-900 sm:text-7xl">
        <div className="text-3xl sm:text-5xl">Willkommen im Leitstand</div>
        <span className="relative text-3xl font-bold whitespace-nowrap text-teal-800 sm:text-5xl">
          <motion.svg
            aria-hidden="true"
            viewBox="0 0 418 42"
            className="absolute top-1/3 left-0 fill-[#00bc7d]"
            preserveAspectRatio="xMidYMid meet"
            width="100%"
            height="100%"
            initial="hidden"
            animate="visible"
          >
            <motion.path
              d="M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421 18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828 1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869 4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874 4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173 2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78 35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721 2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557 1.332"
              variants={draw}
              custom={0}
              strokeWidth="6"
              fill="none"
              stroke="#00bc7d"
            />
          </motion.svg>
          <span className="relative text-6xl font-bold text-[#f67828]">
            {firstName && lastName ? `${firstName} ${lastName}` : "Benutzer"}
          </span>
        </span>
      </h1>
    </div>
  );
};

// Hilfsfunktion zur Bestimmung der Farbe basierend auf dem Modul-Icon oder Titel
const getModuleColor = (iconType: string, title?: string): string => {
  // Verschiedene Farben für verschiedene Modultypen
  if (iconType === "Bot" || title === "AI") return "#6366f1"; // Indigo für AI-Module
  if (iconType === "LayoutDashboard" || title === "Dashboard") return "#10b981"; // Emerald für Dashboard
  if (iconType === "Database" || title === "Backend") return "#f59e0b"; // Amber für Backend
  if (iconType === "AlertTriangle" || title === "Störungsmanagement")
    return "#ef4444"; // Rot für Störungsmanagement
  if (iconType === "Settings" || title?.includes("Settings")) return "#8b5cf6"; // Violett für Einstellungen
  if (iconType === "User" || title?.includes("User")) return "#3b82f6"; // Blau für Benutzer

  // Standard-Farbe, falls keine Übereinstimmung gefunden wurde
  return "#009080"; // Teal als Standardfarbe
};

export default function HomePage() {
  const navigate = useNavigate();
  const { user } = useAuthContext();

  // Simple navigation handler
  const handleModuleNavigation = (route: string) => {
    navigate({ to: route });
  };

  // Simple module cards based on user role
  const getModuleCards = () => {
    const baseModules = [
      {
        id: "dashboard",
        title: "Dashboard",
        description: "Leitstand und KPI-Übersicht",
        icon: "LayoutDashboard",
        route: "/modules/dashboard",
        color: getModuleColor("LayoutDashboard", "Dashboard"),
        isAccessible: true,
        inDevelopment: false,
      },
      {
        id: "stoerungen",
        title: "Störungsmanagement",
        description: "Störungsmanagement und Live-Monitoring",
        icon: "AlertTriangle",
        route: "/modules/stoerungen",
        color: getModuleColor("AlertTriangle", "Störungsmanagement"),
        isAccessible:
          user?.roles?.includes("Benutzer") ||
          user?.roles?.includes("Administrator") ||
          false,
        inDevelopment: false,
      },
      {
        id: "backend",
        title: "Backend",
        description: "System und Workflow-Management",
        icon: "Database",
        route: "/modules/backend",
        color: getModuleColor("Database", "Backend"),
        isAccessible: user?.roles?.includes("Administrator") || false,
        inDevelopment: false,
      },
      {
        id: "ai",
        title: "AI",
        description: "AI-Assistenz und intelligente Analyse",
        icon: "Bot",
        route: "/modules/ai",
        color: getModuleColor("Bot", "AI"),
        isAccessible: true,
        inDevelopment: false,
      },
      {
        id: "settings",
        title: "App-Settings",
        description: "Anwendungseinstellungen und Konfiguration",
        icon: "Settings",
        route: "/settings",
        color: getModuleColor("Settings", "App-Settings"),
        isAccessible: user?.roles?.includes("Administrator") || false,
        inDevelopment: false,
      },
      {
        id: "user-settings",
        title: "User-Settings",
        description: "Benutzereinstellungen und Profil",
        icon: "User",
        route: "/user-settings",
        color: getModuleColor("User", "User-Settings"),
        isAccessible: true,
        inDevelopment: false,
      },
    ];

    return baseModules;
  };

  const moduleCards = getModuleCards();

  return (
    <div className="relative min-h-screen w-full bg-white">
      {/* Magenta Orb Grid Background */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: "white",
          backgroundImage: `
              linear-gradient(to right, rgba(71,85,105,0.15) 1px, transparent 1px),
              linear-gradient(to bottom, rgba(71,85,105,0.15) 1px, transparent 1px),
              radial-gradient(circle at 50% 60%, rgba(236,72,153,0.15) 0%, rgba(168,85,247,0.05) 40%, transparent 70%)
            `,
          backgroundSize: "40px 40px, 40px 40px, 100% 100%",
        }}
      />{" "}
      {/* End of Magenta Orb Grid Background */}
      {/* Your Content/Components - Reduzierte z-index um Toast-Sichtbarkeit zu gewährleisten */}
      <div className="relative z-10 pt-16">
        <div className="mb-10 text-center">
          <AnimatedTextUnderlign
            firstName={user?.firstName}
            lastName={user?.lastName}
          />
        </div>

        <CardCarousel
          modules={moduleCards}
          username={user?.username || "Benutzer"}
          name={user?.name}
          showPagination={true}
          showNavigation={true}
          showUserMenu={true}
          onNavigate={handleModuleNavigation}
        />
      </div>
    </div>
  );
}
