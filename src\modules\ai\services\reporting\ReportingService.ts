/**
 * Automated Reporting Service
 * 
 * Provides automated KPI report generation, template management,
 * insight generation, and report scheduling functionality.
 */

import { AIBaseService, AIServiceConfig } from '../base/AIBaseService';
import { AIServiceError } from '../types';
import { BaseRepository } from '@/repositories/base.repository';
import { DeliveryRepository } from '@/repositories/delivery.repository';
import { ProductionRepository } from '@/repositories/production.repository';
import { WarehouseRepository } from '@/repositories/warehouse.repository';
import { SupplierRepository } from '@/repositories/supplier.repository';
import { SystemRepository } from '@/repositories/system.repository';

/**
 * Report template configuration
 */
export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: 'kpi' | 'performance' | 'analysis' | 'custom';
  department?: 'dispatch' | 'cutting' | 'incoming-goods' | 'all';
  sections: ReportSection[];
  format: 'pdf' | 'excel' | 'html' | 'json';
  schedule?: ReportSchedule;
  recipients?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Report section configuration
 */
export interface ReportSection {
  id: string;
  title: string;
  type: 'chart' | 'table' | 'kpi' | 'text' | 'insights';
  dataSource: string;
  query?: string;
  chartType?: 'line' | 'bar' | 'pie' | 'area' | 'scatter';
  aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max';
  timeRange?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  filters?: Record<string, any>;
  order: number;
}

/**
 * Report scheduling configuration
 */
export interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  time: string; // HH:MM format
  dayOfWeek?: number; // 0-6 for weekly
  dayOfMonth?: number; // 1-31 for monthly
  timezone: string;
  isActive: boolean;
}

/**
 * Generated report data
 */
export interface GeneratedReport {
  id: string;
  templateId: string;
  title: string;
  generatedAt: Date;
  timeRange: {
    start: Date;
    end: Date;
  };
  sections: ReportSectionData[];
  insights: ReportInsight[];
  recommendations: ReportRecommendation[];
  metadata: {
    dataPoints: number;
    processingTime: number;
    sources: string[];
  };
  format: 'pdf' | 'excel' | 'html' | 'json';
  filePath?: string;
  size?: number;
}

/**
 * Report section data
 */
export interface ReportSectionData {
  sectionId: string;
  title: string;
  type: string;
  data: any[];
  chartConfig?: any;
  summary?: {
    total?: number;
    average?: number;
    trend?: 'up' | 'down' | 'stable';
    change?: number;
  };
}

/**
 * AI-generated insights
 */
export interface ReportInsight {
  id: string;
  type: 'trend' | 'anomaly' | 'pattern' | 'correlation';
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  confidence: number;
  dataPoints: string[];
  visualizationSuggestion?: string;
}

/**
 * AI-generated recommendations
 */
export interface ReportRecommendation {
  id: string;
  category: 'efficiency' | 'cost' | 'quality' | 'process' | 'resource';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  expectedBenefit: string;
  actionItems: string[];
  kpiImpact: string[];
}

/**
 * Report generation request
 */
export interface ReportGenerationRequest {
  templateId: string;
  timeRange?: {
    start: Date;
    end: Date;
  };
  format?: 'pdf' | 'excel' | 'html' | 'json';
  includeInsights?: boolean;
  includeRecommendations?: boolean;
  customFilters?: Record<string, any>;
}

/**
 * Report distribution configuration
 */
export interface ReportDistribution {
  id: string;
  reportId: string;
  method: 'email' | 'dashboard' | 'file' | 'api';
  recipients: string[];
  subject?: string;
  message?: string;
  scheduledAt?: Date;
  status: 'pending' | 'sent' | 'failed';
  sentAt?: Date;
  error?: string;
}

/**
 * Automated Reporting Service Implementation
 */
export class ReportingService extends AIBaseService {
  readonly serviceName = 'ReportingService';
  private templates: Map<string, ReportTemplate> = new Map();
  private scheduledReports: Map<string, NodeJS.Timeout> = new Map();
  private repositories: Map<string, any>;

  constructor(config: AIServiceConfig = {}) {
    super({
      ...config,
      enableVectorCache: false, // Reports should be fresh
      vectorCacheTTL: 0
    });

    // Initialize repository mappings
    this.repositories = new Map<string, any>([
      ['delivery', new DeliveryRepository()],
      ['production', new ProductionRepository()],
      ['warehouse', new WarehouseRepository()],
      ['supplier', new SupplierRepository()],
      ['system', new SystemRepository()]
    ]);
  }

  /**
   * Initialize the reporting service
   */
  async initialize(config?: AIServiceConfig): Promise<void> {
    await super.initialize(config);
    await this.loadTemplates();
    await this.scheduleReports();
    this.log('Reporting service initialized');
  }

  /**
   * Create a new report template
   */
  async createTemplate(template: Omit<ReportTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<ReportTemplate> {
    return this.handleAIError(
      async () => {
        const newTemplate: ReportTemplate = {
          ...template,
          id: this.generateId(),
          createdAt: new Date(),
          updatedAt: new Date()
        };

        this.templates.set(newTemplate.id, newTemplate);
        await this.saveTemplate(newTemplate);

        // Schedule if needed
        if (newTemplate.schedule?.isActive) {
          this.scheduleReport(newTemplate);
        }

        this.log(`Report template created: ${newTemplate.name}`);
        return newTemplate;
      },
      async () => {
        throw new Error('Failed to create report template');
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Update an existing report template
   */
  async updateTemplate(templateId: string, updates: Partial<ReportTemplate>): Promise<ReportTemplate> {
    return this.handleAIError(
      async () => {
        const existing = this.templates.get(templateId);
        if (!existing) {
          throw new Error(`Template not found: ${templateId}`);
        }

        const updated: ReportTemplate = {
          ...existing,
          ...updates,
          id: templateId,
          updatedAt: new Date()
        };

        this.templates.set(templateId, updated);
        await this.saveTemplate(updated);

        // Update scheduling
        this.unscheduleReport(templateId);
        if (updated.schedule?.isActive) {
          this.scheduleReport(updated);
        }

        this.log(`Report template updated: ${updated.name}`);
        return updated;
      },
      async () => {
        throw new Error('Failed to update report template');
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Delete a report template
   */
  async deleteTemplate(templateId: string): Promise<boolean> {
    return this.handleAIError(
      async () => {
        const template = this.templates.get(templateId);
        if (!template) {
          return false;
        }

        this.templates.delete(templateId);
        this.unscheduleReport(templateId);
        await this.removeTemplate(templateId);

        this.log(`Report template deleted: ${template.name}`);
        return true;
      },
      async () => false,
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Get all report templates
   */
  async getTemplates(filter?: { department?: string; type?: string; isActive?: boolean }): Promise<ReportTemplate[]> {
    let templates = Array.from(this.templates.values());

    if (filter) {
      templates = templates.filter(template => {
        if (filter.department && template.department !== filter.department && template.department !== 'all') {
          return false;
        }
        if (filter.type && template.type !== filter.type) {
          return false;
        }
        if (filter.isActive !== undefined && template.isActive !== filter.isActive) {
          return false;
        }
        return true;
      });
    }

    return templates.sort((a, b) => a.name.localeCompare(b.name));
  }

  /**
   * Get a specific report template
   */
  async getTemplate(templateId: string): Promise<ReportTemplate | null> {
    return this.templates.get(templateId) || null;
  }

  /**
   * Generate a report from a template
   */
  async generateReport(request: ReportGenerationRequest): Promise<GeneratedReport> {
    return this.handleAIError(
      async () => {
        const template = this.templates.get(request.templateId);
        if (!template) {
          throw new Error(`Template not found: ${request.templateId}`);
        }

        const startTime = Date.now();
        const timeRange = request.timeRange || this.getDefaultTimeRange();

        this.log(`Generating report: ${template.name}`);

        // Collect data for all sections
        const sections: ReportSectionData[] = [];
        const sources: string[] = [];
        let totalDataPoints = 0;

        for (const section of template.sections.sort((a, b) => a.order - b.order)) {
          const sectionData = await this.generateSectionData(section, timeRange, request.customFilters);
          sections.push(sectionData);
          sources.push(section.dataSource);
          totalDataPoints += sectionData.data.length;
        }

        // Generate insights if requested
        const insights = request.includeInsights !== false
          ? await this.generateInsights(sections, template.department)
          : [];

        // Generate recommendations if requested
        const recommendations = request.includeRecommendations !== false
          ? await this.generateRecommendations(sections, insights, template.department)
          : [];

        const report: GeneratedReport = {
          id: this.generateId(),
          templateId: request.templateId,
          title: `${template.name} - ${this.formatDate(new Date())}`,
          generatedAt: new Date(),
          timeRange,
          sections,
          insights,
          recommendations,
          metadata: {
            dataPoints: totalDataPoints,
            processingTime: Date.now() - startTime,
            sources: [...new Set(sources)]
          },
          format: request.format || template.format
        };

        this.log(`Report generated: ${report.title} (${totalDataPoints} data points, ${report.metadata.processingTime}ms)`);
        return report;
      },
      async () => {
        throw new Error('Failed to generate report');
      },
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Generate data for a report section
   */
  private async generateSectionData(
    section: ReportSection,
    timeRange: { start: Date; end: Date },
    customFilters?: Record<string, any>
  ): Promise<ReportSectionData> {
    const repository = this.repositories.get(section.dataSource);
    if (!repository) {
      throw new Error(`Unknown data source: ${section.dataSource}`);
    }

    // Build filters
    const filters = {
      startDate: timeRange.start.toISOString().split('T')[0],
      endDate: timeRange.end.toISOString().split('T')[0],
      ...section.filters,
      ...customFilters
    };

    // Get raw data
    const rawData = await repository.getAll(filters);

    // Process data based on section type
    let processedData = rawData;
    let summary: any = {};

    if (section.aggregation) {
      const result = this.aggregateData(rawData, section.aggregation);
      processedData = result.data;
      summary = result.summary;
    }

    // Generate chart configuration if needed
    let chartConfig: any = undefined;
    if (section.type === 'chart' && section.chartType) {
      chartConfig = this.generateChartConfig(section.chartType, processedData);
    }

    return {
      sectionId: section.id,
      title: section.title,
      type: section.type,
      data: processedData,
      chartConfig,
      summary
    };
  }

  /**
   * Aggregate data based on aggregation type
   */
  private aggregateData(data: any[], aggregationType: string): { data: any[]; summary: any } {
    if (data.length === 0) {
      return { data: [], summary: {} };
    }

    let aggregatedValue: number;
    const numericData = data.filter(item => typeof item.value === 'number').map(item => item.value);

    switch (aggregationType) {
      case 'sum':
        aggregatedValue = numericData.reduce((sum, val) => sum + val, 0);
        break;
      case 'avg':
        aggregatedValue = numericData.length > 0 ? numericData.reduce((sum, val) => sum + val, 0) / numericData.length : 0;
        break;
      case 'count':
        aggregatedValue = data.length;
        break;
      case 'min':
        aggregatedValue = numericData.length > 0 ? Math.min(...numericData) : 0;
        break;
      case 'max':
        aggregatedValue = numericData.length > 0 ? Math.max(...numericData) : 0;
        break;
      default:
        aggregatedValue = data.length;
    }

    // Calculate trend if we have time-series data
    let trend: 'up' | 'down' | 'stable' = 'stable';
    let change = 0;

    if (numericData.length > 1) {
      const firstHalf = numericData.slice(0, Math.floor(numericData.length / 2));
      const secondHalf = numericData.slice(Math.floor(numericData.length / 2));

      const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;

      change = ((secondAvg - firstAvg) / firstAvg) * 100;

      if (Math.abs(change) > 5) {
        trend = change > 0 ? 'up' : 'down';
      }
    }

    return {
      data,
      summary: {
        total: aggregatedValue,
        average: aggregationType === 'avg' ? aggregatedValue : (numericData.length > 0 ? numericData.reduce((sum, val) => sum + val, 0) / numericData.length : 0),
        trend,
        change: Math.round(change * 100) / 100
      }
    };
  }

  /**
   * Generate chart configuration
   */
  private generateChartConfig(chartType: string, data: any[]): any {
    const baseConfig = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: false,
        },
      },
    };

    switch (chartType) {
      case 'line':
        return {
          ...baseConfig,
          scales: {
            y: {
              beginAtZero: true,
            },
          },
        };
      case 'bar':
        return {
          ...baseConfig,
          scales: {
            y: {
              beginAtZero: true,
            },
          },
        };
      case 'pie':
        return {
          ...baseConfig,
          plugins: {
            ...baseConfig.plugins,
            legend: {
              position: 'right' as const,
            },
          },
        };
      default:
        return baseConfig;
    }
  }

  /**
   * Generate AI insights from report data
   */
  private async generateInsights(
    sections: ReportSectionData[],
    department?: string
  ): Promise<ReportInsight[]> {
    const insights: ReportInsight[] = [];

    for (const section of sections) {
      // Trend analysis
      if (section.summary?.trend && section.summary.trend !== 'stable') {
        const changeValue = section.summary.change || 0;
        insights.push({
          id: this.generateId(),
          type: 'trend',
          title: `${section.summary.trend === 'up' ? 'Steigender' : 'Fallender'} Trend in ${section.title}`,
          description: `Die Daten zeigen einen ${section.summary.trend === 'up' ? 'steigenden' : 'fallenden'} Trend von ${Math.abs(changeValue)}% im Vergleich zum vorherigen Zeitraum.`,
          severity: Math.abs(changeValue) > 20 ? 'high' : Math.abs(changeValue) > 10 ? 'medium' : 'low',
          confidence: 0.8,
          dataPoints: [section.sectionId],
          visualizationSuggestion: 'line'
        });
      }

      // Anomaly detection for extreme values
      if (section.data.length > 0) {
        const numericValues = section.data
          .map(item => typeof item.value === 'number' ? item.value : 0)
          .filter(val => val > 0);

        if (numericValues.length > 2) {
          const mean = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length;
          const stdDev = Math.sqrt(numericValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / numericValues.length);

          const anomalies = numericValues.filter(val => Math.abs(val - mean) > 2 * stdDev);

          if (anomalies.length > 0) {
            insights.push({
              id: this.generateId(),
              type: 'anomaly',
              title: `Anomalien in ${section.title} erkannt`,
              description: `${anomalies.length} Datenpunkte weichen signifikant vom Durchschnitt ab (>2 Standardabweichungen).`,
              severity: anomalies.length > numericValues.length * 0.1 ? 'high' : 'medium',
              confidence: 0.9,
              dataPoints: [section.sectionId],
              visualizationSuggestion: 'scatter'
            });
          }
        }
      }
    }

    // Cross-section pattern analysis
    if (sections.length > 1) {
      const correlations = this.findCorrelations(sections);
      for (const correlation of correlations) {
        insights.push({
          id: this.generateId(),
          type: 'correlation',
          title: `Korrelation zwischen ${correlation.section1} und ${correlation.section2}`,
          description: `Starke ${correlation.strength > 0 ? 'positive' : 'negative'} Korrelation (${Math.abs(correlation.strength).toFixed(2)}) zwischen den Datensätzen.`,
          severity: Math.abs(correlation.strength) > 0.8 ? 'high' : 'medium',
          confidence: 0.7,
          dataPoints: [correlation.section1Id, correlation.section2Id],
          visualizationSuggestion: 'scatter'
        });
      }
    }

    return insights;
  }

  /**
   * Find correlations between sections
   */
  private findCorrelations(sections: ReportSectionData[]): Array<{
    section1: string;
    section2: string;
    section1Id: string;
    section2Id: string;
    strength: number;
  }> {
    const correlations: Array<{
      section1: string;
      section2: string;
      section1Id: string;
      section2Id: string;
      strength: number;
    }> = [];

    for (let i = 0; i < sections.length; i++) {
      for (let j = i + 1; j < sections.length; j++) {
        const section1 = sections[i];
        const section2 = sections[j];

        const values1 = section1.data.map(item => typeof item.value === 'number' ? item.value : 0);
        const values2 = section2.data.map(item => typeof item.value === 'number' ? item.value : 0);

        if (values1.length > 2 && values2.length > 2) {
          const correlation = this.calculateCorrelation(values1, values2);

          if (Math.abs(correlation) > 0.6) {
            correlations.push({
              section1: section1.title,
              section2: section2.title,
              section1Id: section1.sectionId,
              section2Id: section2.sectionId,
              strength: correlation
            });
          }
        }
      }
    }

    return correlations;
  }

  /**
   * Calculate Pearson correlation coefficient
   */
  private calculateCorrelation(x: number[], y: number[]): number {
    const n = Math.min(x.length, y.length);
    if (n < 2) return 0;

    const sumX = x.slice(0, n).reduce((sum, val) => sum + val, 0);
    const sumY = y.slice(0, n).reduce((sum, val) => sum + val, 0);
    const sumXY = x.slice(0, n).reduce((sum, val, i) => sum + val * y[i], 0);
    const sumX2 = x.slice(0, n).reduce((sum, val) => sum + val * val, 0);
    const sumY2 = y.slice(0, n).reduce((sum, val) => sum + val * val, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

    return denominator === 0 ? 0 : numerator / denominator;
  }

  /**
   * Generate AI recommendations based on insights and data
   */
  private async generateRecommendations(
    sections: ReportSectionData[],
    insights: ReportInsight[],
    department?: string
  ): Promise<ReportRecommendation[]> {
    const recommendations: ReportRecommendation[] = [];

    // Analyze trends for recommendations
    for (const insight of insights) {
      if (insight.type === 'trend' && insight.severity === 'high') {
        if (insight.title.includes('Fallender')) {
          recommendations.push({
            id: this.generateId(),
            category: 'efficiency',
            title: 'Leistungsabfall untersuchen',
            description: 'Der fallende Trend erfordert eine detaillierte Analyse der Ursachen und sofortige Gegenmaßnahmen.',
            priority: 'high',
            impact: 'high',
            effort: 'medium',
            expectedBenefit: 'Wiederherstellung der ursprünglichen Leistung und Verhinderung weiterer Verschlechterung',
            actionItems: [
              'Detailanalyse der betroffenen Prozesse durchführen',
              'Ursachen für den Leistungsabfall identifizieren',
              'Korrekturmaßnahmen entwickeln und implementieren',
              'Monitoring-System für frühzeitige Warnung einrichten'
            ],
            kpiImpact: ['Effizienz', 'Qualität', 'Kosten']
          });
        }
      }

      if (insight.type === 'anomaly' && insight.severity === 'high') {
        recommendations.push({
          id: this.generateId(),
          category: 'quality',
          title: 'Qualitätskontrolle verstärken',
          description: 'Die erkannten Anomalien deuten auf Qualitätsprobleme hin, die eine verstärkte Kontrolle erfordern.',
          priority: 'high',
          impact: 'medium',
          effort: 'low',
          expectedBenefit: 'Reduzierung von Qualitätsschwankungen und Verbesserung der Prozessstabilität',
          actionItems: [
            'Zusätzliche Qualitätsprüfungen einführen',
            'Schulung der Mitarbeiter zu Qualitätsstandards',
            'Automatische Anomalieerkennung implementieren',
            'Regelmäßige Kalibrierung der Messgeräte'
          ],
          kpiImpact: ['Qualität', 'Kundenzufriedenheit']
        });
      }
    }

    // Department-specific recommendations
    if (department) {
      switch (department) {
        case 'dispatch':
          recommendations.push({
            id: this.generateId(),
            category: 'efficiency',
            title: 'Versandprozess optimieren',
            description: 'Basierend auf den Daten können Versandzeiten durch bessere Routenplanung reduziert werden.',
            priority: 'medium',
            impact: 'medium',
            effort: 'medium',
            expectedBenefit: '15-20% Reduzierung der Versandzeiten',
            actionItems: [
              'Routenoptimierung-Software implementieren',
              'Ladekapazitäten besser ausnutzen',
              'Versandzeiten analysieren und optimieren'
            ],
            kpiImpact: ['Lieferzeit', 'Transportkosten', 'Kundenzufriedenheit']
          });
          break;

        case 'cutting':
          recommendations.push({
            id: this.generateId(),
            category: 'resource',
            title: 'Materialverschnitt reduzieren',
            description: 'Optimierung der Schnittmuster kann Materialverschwendung signifikant reduzieren.',
            priority: 'high',
            impact: 'high',
            effort: 'low',
            expectedBenefit: '10-15% Reduzierung des Materialverschnitts',
            actionItems: [
              'KI-basierte Schnittoptimierung einsetzen',
              'Restlängen besser verwalten',
              'Schnittmuster regelmäßig überprüfen'
            ],
            kpiImpact: ['Materialkosten', 'Effizienz', 'Nachhaltigkeit']
          });
          break;

        case 'incoming-goods':
          recommendations.push({
            id: this.generateId(),
            category: 'process',
            title: 'Wareneingang beschleunigen',
            description: 'Automatisierung der Wareneingangsprozesse kann Durchlaufzeiten verkürzen.',
            priority: 'medium',
            impact: 'medium',
            effort: 'high',
            expectedBenefit: '25-30% Reduzierung der Bearbeitungszeit',
            actionItems: [
              'Barcode-Scanner für alle Eingänge',
              'Automatische Qualitätsprüfung',
              'Digitale Wareneingangsdokumentation'
            ],
            kpiImpact: ['Durchlaufzeit', 'Genauigkeit', 'Personalkosten']
          });
          break;
      }
    }

    // General efficiency recommendations based on data patterns
    const totalDataPoints = sections.reduce((sum, section) => sum + section.data.length, 0);
    if (totalDataPoints > 1000) {
      recommendations.push({
        id: this.generateId(),
        category: 'process',
        title: 'Datenanalyse automatisieren',
        description: 'Die große Datenmenge bietet Potenzial für automatisierte Analysen und Erkenntnisse.',
        priority: 'low',
        impact: 'medium',
        effort: 'high',
        expectedBenefit: 'Kontinuierliche Optimierung durch datengetriebene Entscheidungen',
        actionItems: [
          'Machine Learning Modelle für Vorhersagen entwickeln',
          'Automatische Berichtserstellung implementieren',
          'Echtzeit-Dashboards einrichten',
          'Predictive Analytics einführen'
        ],
        kpiImpact: ['Alle KPIs', 'Entscheidungsgeschwindigkeit', 'Vorhersagegenauigkeit']
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Schedule a report for automatic generation
   */
  private scheduleReport(template: ReportTemplate): void {
    if (!template.schedule?.isActive) return;

    const scheduleId = template.id;
    const schedule = template.schedule;

    // Calculate next execution time
    const now = new Date();
    let nextExecution = new Date();

    switch (schedule.frequency) {
      case 'daily':
        nextExecution.setDate(now.getDate() + 1);
        break;
      case 'weekly':
        const daysUntilNext = (schedule.dayOfWeek || 1) - now.getDay();
        nextExecution.setDate(now.getDate() + (daysUntilNext <= 0 ? daysUntilNext + 7 : daysUntilNext));
        break;
      case 'monthly':
        nextExecution.setMonth(now.getMonth() + 1);
        nextExecution.setDate(schedule.dayOfMonth || 1);
        break;
      case 'quarterly':
        nextExecution.setMonth(now.getMonth() + 3);
        nextExecution.setDate(1);
        break;
    }

    // Set time
    const [hours, minutes] = schedule.time.split(':').map(Number);
    nextExecution.setHours(hours, minutes, 0, 0);

    const delay = nextExecution.getTime() - now.getTime();

    if (delay > 0) {
      const timeout = setTimeout(async () => {
        try {
          await this.generateScheduledReport(template);
          // Reschedule for next occurrence
          this.scheduleReport(template);
        } catch (error) {
          this.log(`Failed to generate scheduled report ${template.name}:`, error);
        }
      }, delay);

      this.scheduledReports.set(scheduleId, timeout);
      this.log(`Report scheduled: ${template.name} at ${nextExecution.toISOString()}`);
    }
  }

  /**
   * Unschedule a report
   */
  private unscheduleReport(templateId: string): void {
    const timeout = this.scheduledReports.get(templateId);
    if (timeout) {
      clearTimeout(timeout);
      this.scheduledReports.delete(templateId);
      this.log(`Report unscheduled: ${templateId}`);
    }
  }

  /**
   * Generate a scheduled report
   */
  private async generateScheduledReport(template: ReportTemplate): Promise<void> {
    try {
      const report = await this.generateReport({
        templateId: template.id,
        includeInsights: true,
        includeRecommendations: true
      });

      // Distribute report if recipients are configured
      if (template.recipients && template.recipients.length > 0) {
        await this.distributeReport(report, template.recipients);
      }

      this.log(`Scheduled report generated and distributed: ${template.name}`);
    } catch (error) {
      this.log(`Failed to generate scheduled report ${template.name}:`, error);
      throw error;
    }
  }

  /**
   * Distribute a report to recipients
   */
  async distributeReport(report: GeneratedReport, recipients: string[]): Promise<ReportDistribution[]> {
    const distributions: ReportDistribution[] = [];

    for (const recipient of recipients) {
      const distribution: ReportDistribution = {
        id: this.generateId(),
        reportId: report.id,
        method: 'email', // Default to email
        recipients: [recipient],
        subject: `Automatischer Bericht: ${report.title}`,
        message: `Anbei finden Sie den automatisch generierten Bericht "${report.title}".`,
        status: 'pending'
      };

      try {
        // In a real implementation, this would send the actual email
        // For now, we'll just mark it as sent
        distribution.status = 'sent';
        distribution.sentAt = new Date();

        this.log(`Report distributed to ${recipient}: ${report.title}`);
      } catch (error) {
        distribution.status = 'failed';
        distribution.error = error instanceof Error ? error.message : String(error);
        this.log(`Failed to distribute report to ${recipient}:`, error);
      }

      distributions.push(distribution);
    }

    return distributions;
  }

  /**
   * Get default time range for reports (last 30 days)
   */
  private getDefaultTimeRange(): { start: Date; end: Date } {
    const end = new Date();
    const start = new Date();
    start.setDate(end.getDate() - 30);

    return { start, end };
  }

  /**
   * Load templates from storage
   */
  private async loadTemplates(): Promise<void> {
    // In a real implementation, this would load from database
    // For now, we'll create some default templates
    const defaultTemplates: ReportTemplate[] = [
      {
        id: 'kpi-dashboard',
        name: 'KPI Dashboard Report',
        description: 'Comprehensive KPI overview for all departments',
        type: 'kpi',
        department: 'all',
        sections: [
          {
            id: 'delivery-kpis',
            title: 'Lieferleistung',
            type: 'kpi',
            dataSource: 'delivery',
            aggregation: 'avg',
            timeRange: 'month',
            order: 1
          },
          {
            id: 'production-kpis',
            title: 'Produktionsleistung',
            type: 'kpi',
            dataSource: 'production',
            aggregation: 'sum',
            timeRange: 'month',
            order: 2
          }
        ],
        format: 'pdf',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    for (const template of defaultTemplates) {
      this.templates.set(template.id, template);
    }
  }

  /**
   * Schedule all active reports
   */
  private async scheduleReports(): Promise<void> {
    for (const template of this.templates.values()) {
      if (template.schedule?.isActive) {
        this.scheduleReport(template);
      }
    }
  }

  /**
   * Save template to storage
   */
  private async saveTemplate(template: ReportTemplate): Promise<void> {
    // In a real implementation, this would save to database
    this.log(`Template saved: ${template.name}`);
  }

  /**
   * Remove template from storage
   */
  private async removeTemplate(templateId: string): Promise<void> {
    // In a real implementation, this would remove from database
    this.log(`Template removed: ${templateId}`);
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Format date for display
   */
  private formatDate(date: Date): string {
    return date.toLocaleDateString('de-DE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Cleanup scheduled reports on destroy
   */
  async destroy(): Promise<void> {
    // Clear all scheduled reports
    for (const timeout of this.scheduledReports.values()) {
      clearTimeout(timeout);
    }
    this.scheduledReports.clear();

    await super.destroy();
  }
}

// Export singleton instance
export const reportingService = new ReportingService();