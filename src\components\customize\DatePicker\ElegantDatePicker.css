image.png/* ElegantDatePicker.css - Optimiert für Leitstand App Design-System */
.elegant-datepicker-container {
  /* Verwende die App-Schriftart aus dem Design-System */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/* Container für den DatePicker - entfernt, da nicht verwendet */
.datepicker.elegant {
  max-width: 800px;
  margin: 0 auto;
  padding: 25px;
  border-radius: var(--radius); /* Verwende App-Radius */
  background: hsl(var(--card)); /* Verwende Card-Hintergrund */
  border: 1px solid hsl(var(--border)); /* Verwende App-Border */
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); /* Subtile<PERSON><PERSON> wie in der App */
}

.datepicker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid hsl(var(--border));
}

.datepicker-title {
  color: hsl(var(--foreground));
  font-weight: 600; /* Konsistent mit Card-Titeln */
  font-size: 1.125rem; /* 18px - konsistent mit CardTitle */
}

.datepicker-icon {
  color: hsl(var(--muted-foreground));
  font-size: 1.25rem;
}

.date-input-container {
  position: relative;
  margin-top: 15px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: hsl(var(--muted-foreground));
  pointer-events: none;
  z-index: 1;
}

.date-input {
  /* Verwende Input-Styling aus dem Design-System */
  width: 100%;
  height: 2.25rem; /* h-9 aus Input-Komponente */
  padding: 0.25rem 2.5rem 0.25rem 0.75rem; /* py-1 px-3 + Platz für Icon */
  font-size: 0.875rem; /* text-sm */
  border-radius: calc(var(--radius) - 2px); /* md radius */
  border: 1px solid hsl(var(--input));
  background: transparent;
  color: hsl(var(--foreground));
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.date-input:focus {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 3px hsl(var(--ring) / 0.5);
}

.date-input::placeholder {
  color: hsl(var(--muted-foreground));
}
  
  .calendar-container {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    z-index: 9999;
    /* Verwende Popover-Schatten aus dem Design-System */
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    border-radius: var(--radius);
    width: min(700px, 90vw);
    max-height: 80vh;
    overflow-y: auto;
  }
  
  .calendar-container.adjust-left {
    right: auto;
    left: 0;
  }
  
  .calendar-container.adjust-center {
    right: auto;
    left: 50%;
    transform: translateX(-50%);
  }

  .calendar {
    padding: 1.5rem; /* 24px */
    border-radius: var(--radius);
    background: rgb(248 250 252); /* slate-50 für bessere Sichtbarkeit */
    border: 1px solid rgb(30 41 59); /* slate-800 für stärkeren Kontrast */
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); /* shadow-md */
    color: rgb(15 23 42); /* slate-900 für besseren Kontrast */
  }
  
  .calendar-custom-header {
    border-bottom: 1px solid hsl(var(--border));
    margin-bottom: 1.25rem; /* 20px */
    padding-bottom: 0.75rem; /* 12px */
  }
  
  .calendar-custom-title {
    font-size: 1.125rem; /* 18px - konsistent mit CardTitle */
    font-weight: 600;
    color: rgb(15 23 42); /* slate-900 für besseren Kontrast */
    margin: 0;
    text-align: center;
  }
  
  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .calendar-nav {
    /* Verwende Button-Styling aus dem Design-System */
    background: transparent;
    border: none;
    padding: 0.5rem 0.75rem; /* py-2 px-3 */
    border-radius: calc(var(--radius) - 2px);
    cursor: pointer;
    font-weight: 500;
    font-size: 1rem;
    color: hsl(var(--muted-foreground));
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 2.25rem; /* h-9 */
    min-width: 2.5rem;
  }
  
  .calendar-nav svg {
    width: 1rem;
    height: 1rem;
  }
  
  .calendar-nav:hover {
    background: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }
  
  .calendar-title {
    font-weight: 600;
    font-size: 1.2rem;
    color: black;
  }
  
  .calendar-months {
    display: flex;
    gap: 30px;
    margin-top: 20px;
  }
  
  .calendar-month {
    flex: 1;
  }
  
  .calendar-month-title {
    text-align: center;
    margin-bottom: 15px;
    font-weight: 600;
    color: black;
    font-size: 1.1rem;
  }
  
  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 6px;
  }
  
  .calendar-day-header {
    text-align: center;
    font-weight: bold;
    padding: 10px 5px;
    font-size: 14px;
    color: black;
  }
  
  .calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: calc(var(--radius) - 2px);
    cursor: pointer;
    font-size: 0.875rem; /* text-sm */
    font-weight: 400;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    background: transparent;
    color: rgb(15 23 42); /* slate-900 für besseren Kontrast */
    min-height: 2.25rem; /* h-9 - konsistent mit Button-Höhe */
    position: relative;
  }
  
  .calendar-day:hover {
    background: rgb(226 232 240); /* slate-200 für Hover-Effekt */
    color: rgb(15 23 42); /* slate-900 */
  }
  
  .calendar-day.selected {
    background: rgb(255 194 112); /* blue-500 für ausgewählte Tage */
    color: rgb(255 255 255); /* white */
    font-weight: 500;
    border: 1px solid rgb(100 116 139); /* blue-500 */
  }
  
  .calendar-day.in-range {
    background: rgb(255 194 112); /* blue-300 für Bereich-Auswahl */
    color: rgb(15 23 42); /* slate-900 */
    border: 1px solid rgb(100 116 139);
  }
  
  .calendar-day.current-month {
    opacity: 1;
  }
  
  .calendar-day.other-month {
    color: rgb(148 163 184); /* slate-400 für andere Monate */
    opacity: 0.6;
  }
  
  .calendar-day.today {
    background: rgb(34 197 94); /* green-500 für heute */
    color: rgb(255 255 255); /* white */
    font-weight: 500;
    border: 1px solid rgb(100 116 139);
  }
  
  .calendar-day.today::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 16px;
    height: 2px;
    background: hsl(var(--primary));
  }
  
  .calendar-day.range-start {
    background: rgb(255 122 5); /* für Bereich-Start */
    color: rgb(255 255 255); /* white */
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  
  .calendar-day.range-end {
    background: rgb(255 122 5); /* für Bereich-Ende */
    color: rgb(255 255 255); /* white */
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  
  .calendar-day.disabled {
    color: rgb(203 213 225); /* slate-300 für deaktivierte Tage */
    opacity: 0.4;
    cursor: not-allowed;
    background: transparent;
  }
  
  .calendar-day.disabled:hover {
    background: transparent;
    color: rgb(203 213 225); /* slate-300 */
    opacity: 0.4;
  }
  
  /* Event-Indikator für Tage mit Events */
  .calendar-day.has-event::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 0.25rem; /* 4px */
    height: 0.125rem; /* 2px */
    background: hsl(var(--primary));
    border-radius: 9999px; /* rounded-full */
    opacity: 1; /* Zeige Indikator für Tage mit Events */
  }
  
  .selected-range {
    margin-top: 1.25rem; /* 20px */
    padding: 1rem; /* 16px */
    background: hsl(var(--muted));
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
  }
  
  .selected-range h4 {
    margin: 0 0 0.625rem 0; /* mb-2.5 */
    color: hsl(var(--foreground));
    font-size: 0.875rem; /* text-sm */
    font-weight: 600;
  }
  
  .selected-range p {
    margin: 0.25rem 0; /* my-1 */
    color: hsl(var(--muted-foreground));
    font-size: 0.75rem; /* text-xs */
  }
  
  .calendar-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e0d6c9;
  }
  
  .calendar-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    font-size: 14px;
    display: flex;
    flex-direction: row; /* Explizit Icons links neben Text positionieren */
    align-items: center;
    justify-content: center;
    gap: 2px;
    min-width: 120px;
  }
  
  .calendar-btn svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }
  
  .calendar-btn.reset {
    background: #e2e8f0;
    color: black;
  }
  
  .calendar-btn.reset:hover {
    background: #94a3b8;
  }
  
  .calendar-btn.today {
    background: #67e8f9;
    color: black;
  }
  
  .calendar-btn.today:hover {
    background: #06b6d4;
  }
  
  .calendar-btn.accept {
    background: #6ee7b7;
    color: black;
  }
  
  .calendar-btn.accept:hover {
    background: #10b981;
  }
  
  /* Responsive Design */
  @media (max-width: 768px) {
    .datepicker.elegant {
      padding: 1.25rem; /* 20px */
    }
    
    .calendar-container {
      width: 95vw;
      max-height: 70vh;
      right: auto;
      left: 50%;
      transform: translateX(-50%);
    }
    
    .calendar-container.adjust-left,
    .calendar-container.adjust-center {
      right: auto;
      left: 50%;
      transform: translateX(-50%);
    }
    
    .calendar {
      padding: 1.25rem; /* 20px */
    }
    
    .calendar-day {
      min-height: 2rem; /* h-8 */
      font-size: 0.75rem; /* text-xs */
    }
    
    .datepicker-title {
      font-size: 1rem; /* text-base */
    }
    
    .calendar-nav-title {
      font-size: 0.875rem; /* text-sm */
    }
    
    .calendar-months {
      flex-direction: column;
      gap: 20px;
    }
    
    .calendar-actions {
      flex-wrap: wrap;
      gap: 10px;
    }
    
    .calendar-btn {
      flex: 1;
      justify-content: center;
      min-width: 120px;
    }
  }
  
  /* Ripple-Effekt Animation für RippleButton im DatePicker */
  @keyframes rippling {
    0% {
      opacity: 1;
      transform: scale(0);
    }
    100% {
      opacity: 0;
      transform: scale(4);
    }
  }
  
  .animate-rippling {
    animation: rippling 600ms ease-out;
  }
  
  /* Dark Mode Support - automatisch durch CSS-Variablen */
  @media (prefers-color-scheme: dark) {
    /* Alle Styles verwenden bereits CSS-Variablen und unterstützen automatisch Dark Mode */
  }