"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.intentRecognitionMetric = exports.QueryPerformanceMetric = exports.queryPerformanceMetric = exports.ResponseTimeMetric = exports.responseTimeMetric = exports.Bestand200 = exports.bestand200 = exports.Runbook = exports.runbook = exports.BereitschaftsKonfiguration = exports.bereitschaftsKonfiguration = exports.BereitschaftsAusnahmen = exports.bereitschaftsAusnahmen = exports.BereitschaftsWochen = exports.bereitschaftsWochen = exports.BereitschaftsPersonen = exports.bereitschaftsPersonen = exports.User = exports.user = exports.SystemStatusMessage = exports.systemStatusMessage = exports.SystemStatus = exports.systemStatus = exports.stoerungsAttachment = exports.StoerungsComments = exports.stoerungsComments = exports.Stoerungen = exports.stoerungen = exports.Trommeldaten = exports.trommeldaten = exports.Materialdaten = exports.materialdaten = exports.Schnitte = exports.schnitte = exports.Maschinen = exports.maschinen = exports.auslastung240 = exports.auslastung200 = exports.WE = exports.we = exports.System = exports.system = exports.Ablaengerei = exports.ablaengerei = exports.ATrL = exports.atrL = exports.ARiL = exports.ariL = exports.DispatchData = exports.dispatchData = void 0;
exports.WorkflowExecution = exports.workflowExecution = exports.WorkflowLog = exports.workflowLog = exports.PerformanceAlert = exports.performanceAlert = exports.EnrichmentPerformanceMetric = exports.enrichmentPerformanceMetric = exports.IntentRecognitionMetric = void 0;
const pg_core_1 = require("drizzle-orm/pg-core");
const drizzle_orm_1 = require("drizzle-orm");
// Drizzle ORM Schema - vollständig migriert von Prisma
// DispatchData table - main table causing the current error
exports.dispatchData = (0, pg_core_1.pgTable)("dispatch_data", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    datum: (0, pg_core_1.text)("datum"), // Datum ist als Text gespeichert (YYYY-MM-DD)
    tag: (0, pg_core_1.integer)("tag"),
    monat: (0, pg_core_1.integer)("monat"),
    kw: (0, pg_core_1.integer)("kw"),
    jahr: (0, pg_core_1.integer)("jahr"),
    servicegrad: (0, pg_core_1.real)("servicegrad"),
    ausgeliefert_lup: (0, pg_core_1.integer)("ausgeliefert_lup"),
    rueckstaendig: (0, pg_core_1.integer)("rueckstaendig"),
    produzierte_tonnagen: (0, pg_core_1.real)("produzierte_tonnagen"),
    direktverladung_kiaa: (0, pg_core_1.integer)("direktverladung_kiaa"),
    umschlag: (0, pg_core_1.integer)("umschlag"),
    kg_pro_colli: (0, pg_core_1.real)("kg_pro_colli"),
    elefanten: (0, pg_core_1.integer)("elefanten"),
    atrl: (0, pg_core_1.integer)("atrl"),
    aril: (0, pg_core_1.integer)("aril"),
    fuellgrad_aril: (0, pg_core_1.real)("fuellgrad_aril"),
    qm_angenommen: (0, pg_core_1.integer)("qm_angenommen"),
    qm_abgelehnt: (0, pg_core_1.integer)("qm_abgelehnt"),
    qm_offen: (0, pg_core_1.integer)("qm_offen"),
    mitarbeiter_std: (0, pg_core_1.real)("mitarbeiter_std"),
}, (table) => ({
    datumIdx: (0, pg_core_1.index)("dispatch_data_datum_idx").on(table.datum),
}));
// Alias für Kompatibilität
exports.DispatchData = exports.dispatchData;
// ARiL table
exports.ariL = (0, pg_core_1.pgTable)("ARiL", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    Datum: (0, pg_core_1.text)("Datum"),
    waTaPositionen: (0, pg_core_1.integer)("waTaPositionen"),
    Umlagerungen: (0, pg_core_1.integer)("Umlagerungen"),
    belegtePlaetze: (0, pg_core_1.integer)("belegtePlaetze"),
    systemtablareRuecksackStk: (0, pg_core_1.integer)("systemtablareRuecksackStk"),
    systemtablareGesamtStk: (0, pg_core_1.integer)("systemtablareGesamtStk"),
    systemtablareEinzelBelegt: (0, pg_core_1.integer)("systemtablareEinzelBelegt"),
    belegtRinge: (0, pg_core_1.integer)("belegtRinge"),
    auslastung: (0, pg_core_1.real)("Auslastung"),
    alleBewegungen: (0, pg_core_1.integer)("alleBewegungen"),
    cuttingLagerKunde: (0, pg_core_1.integer)("cuttingLagerKunde"),
    cuttingLagerRest: (0, pg_core_1.integer)("cuttingLagerRest"),
    lagerCutting: (0, pg_core_1.integer)("lagerCutting"),
}, (table) => ({
    datumIdx: (0, pg_core_1.index)("ARiL_datum_idx").on(table.Datum),
}));
// Alias für Kompatibilität
exports.ARiL = exports.ariL;
// ATrL table - KORRIGIERT: Datum ist TEXT in der DB
exports.atrL = (0, pg_core_1.pgTable)("ATrL", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    Datum: (0, pg_core_1.text)("Datum"), // TEXT in der DB und Großgeschrieben!
    umlagerungen: (0, pg_core_1.integer)("umlagerungen"),
    waTaPositionen: (0, pg_core_1.integer)("waTaPositionen"),
    belegtePlaetze: (0, pg_core_1.integer)("belegtePlaetze"),
    davonSystempaletten: (0, pg_core_1.integer)("davonSystempaletten"),
    SystempalettenstapelRucksackpaetzen: (0, pg_core_1.integer)("SystempalettenstapelRucksackpaetzen"),
    SystempalettenstapelEinzel: (0, pg_core_1.integer)("SystempalettenstapelEinzel"),
    PlaetzeSystempalettenstapelEinzel: (0, pg_core_1.integer)("PlaetzeSystempalettenstapelEinzel"),
    plaetzeMitTrommelBelegt: (0, pg_core_1.integer)("plaetzeMitTrommelBelegt"),
    Auslastung: (0, pg_core_1.real)("Auslastung"),
    Bewegungen: (0, pg_core_1.integer)("Bewegungen"),
    EinlagerungAblKunde: (0, pg_core_1.integer)("EinlagerungAblKunde"),
    EinlagerungAblRest: (0, pg_core_1.integer)("EinlagerungAblRest"),
    AuslagerungAbl: (0, pg_core_1.integer)("AuslagerungAbl"),
    weAtrl: (0, pg_core_1.integer)("weAtrl"),
}, (table) => ({
    datumIdx: (0, pg_core_1.index)("ATrL_Datum_idx").on(table.Datum),
}));
// Alias für Kompatibilität
exports.ATrL = exports.atrL;
// Ablaengerei table
exports.ablaengerei = (0, pg_core_1.pgTable)("Ablaengerei", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    Datum: (0, pg_core_1.text)("Datum"), // WICHTIG: In der DB ist es als TEXT gespeichert, nicht als integer!
    cutLagerK220: (0, pg_core_1.integer)("cutLagerK220").default(0),
    cutLagerR220: (0, pg_core_1.integer)("cutLagerR220").default(0),
    lagerCut220: (0, pg_core_1.integer)("lagerCut220").default(0),
    cutLagerK240: (0, pg_core_1.integer)("cutLagerK240").default(0),
    cutLagerR240: (0, pg_core_1.integer)("cutLagerR240").default(0),
    lagerCut240: (0, pg_core_1.integer)("lagerCut240").default(0),
    cutTT: (0, pg_core_1.integer)("cutTT").default(0), // Korrekt: cutTT mit großem TT
    cutTR: (0, pg_core_1.integer)("cutTR").default(0),
    cutRR: (0, pg_core_1.integer)("cutRR").default(0),
    cutGesamt: (0, pg_core_1.integer)("cutGesamt").default(0),
    pickCut: (0, pg_core_1.integer)("pickCut").default(0),
    cutLager200: (0, pg_core_1.integer)("cutLager200").default(0),
    cutLagerK200: (0, pg_core_1.integer)("cutLagerK200").default(0),
    lagerCut200: (0, pg_core_1.integer)("lagerCut200").default(0),
}, (table) => ({
    datumIdx: (0, pg_core_1.index)("Ablaengerei_datum_idx").on(table.Datum),
}));
// Alias für Kompatibilität
exports.Ablaengerei = exports.ablaengerei;
// System table
exports.system = (0, pg_core_1.pgTable)("System", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    datum: (0, pg_core_1.timestamp)("datum", { mode: "date" }),
    verfuegbarkeitFts: (0, pg_core_1.real)("verfuegbarkeitFTS"),
}, (table) => ({
    datumIdx: (0, pg_core_1.index)("System_datum_idx").on(table.datum),
}));
// Alias für Kompatibilität
exports.System = exports.system;
// WE table (Wareneingang)
exports.we = (0, pg_core_1.pgTable)("WE", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    datum: (0, pg_core_1.text)("Datum"), // WICHTIG: Spaltenname in DB ist "Datum" (großgeschrieben)
    weAtrl: (0, pg_core_1.integer)("weAtrl").default(0),
    weManl: (0, pg_core_1.integer)("weManl").default(0),
}, (table) => ({
    datumIdx: (0, pg_core_1.index)("WE_datum_idx").on(table.datum),
}));
// Alias für Kompatibilität
exports.WE = exports.we;
// Auslastung200 table - KORRIGIERT: aufnahmeDatum ist TEXT in der DB
exports.auslastung200 = (0, pg_core_1.pgTable)("auslastung200", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    aufnahmeDatum: (0, pg_core_1.text)("aufnahmeDatum"), // TEXT in der DB, nicht integer!
    aufnahmeZeit: (0, pg_core_1.text)("aufnahmeZeit"),
    maxPlaetze: (0, pg_core_1.text)("maxPlaetze"),
    auslastung: (0, pg_core_1.text)("auslastung"),
    maxA: (0, pg_core_1.text)("maxA"),
    maxB: (0, pg_core_1.text)("maxB"),
    maxC: (0, pg_core_1.text)("maxC"),
    auslastungA: (0, pg_core_1.text)("auslastungA"), // TEXT in der DB!
    auslastungB: (0, pg_core_1.text)("auslastungB"), // TEXT in der DB!
    auslastungC: (0, pg_core_1.text)("auslastungC"), // TEXT in der DB!
    importTimestamp: (0, pg_core_1.timestamp)("import_timestamp", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
}, (table) => ({
    aufnahmeDatumIdx: (0, pg_core_1.index)("auslastung200_aufnahmeDatum_idx").on(table.aufnahmeDatum),
    importTimestampIdx: (0, pg_core_1.index)("auslastung200_import_timestamp_idx").on(table.importTimestamp),
}));
// Auslastung240 table
exports.auslastung240 = (0, pg_core_1.pgTable)("auslastung240", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    aufnahmeDatum: (0, pg_core_1.text)("aufnahmeDatum"), // TEXT in der DB, nicht integer!
    aufnahmeZeit: (0, pg_core_1.text)("aufnahmeZeit"),
    maxPlaetze: (0, pg_core_1.text)("maxPlaetze"),
    auslastung: (0, pg_core_1.text)("auslastung"),
    maxA: (0, pg_core_1.text)("maxA"),
    maxB: (0, pg_core_1.text)("maxB"),
    maxC: (0, pg_core_1.text)("maxC"),
    auslastungA: (0, pg_core_1.text)("auslastungA"), // TEXT in der DB!
    auslastungB: (0, pg_core_1.text)("auslastungB"), // TEXT in der DB!
    auslastungC: (0, pg_core_1.text)("auslastungC"),
}, (table) => ({
    aufnahmeDatumIdx: (0, pg_core_1.index)("auslastung240_aufnahmeDatum_idx").on(table.aufnahmeDatum),
}));
// Maschinen table
exports.maschinen = (0, pg_core_1.pgTable)("maschinen", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    machine: (0, pg_core_1.text)("Machine").unique(),
    schnitteProStd: (0, pg_core_1.real)("schnitteProStd"),
});
// Alias für Kompatibilität
exports.Maschinen = exports.maschinen;
// Schnitte table - vollständig mit allen Maschinen
exports.schnitte = (0, pg_core_1.pgTable)("schnitte", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    datum: (0, pg_core_1.text)("Datum"),
    // H1-Maschinen (Halle 1)
    m5RH1: (0, pg_core_1.integer)("M5-R-H1"), // Maschine 5 - Ring - Halle 1
    m6TH1: (0, pg_core_1.integer)("M6-T-H1"), // Maschine 6 - Trommel - Halle 1
    m7RH1: (0, pg_core_1.integer)("M7-R-H1"), // Maschine 7 - Ring - Halle 1
    m8TH1: (0, pg_core_1.integer)("M8-T-H1"), // Maschine 8 - Trommel - Halle 1
    m9RH1: (0, pg_core_1.integer)("M9-R-H1"), // Maschine 9 - Ring - Halle 1
    m10TH1: (0, pg_core_1.integer)("M10-T-H1"), // Maschine 10 - Trommel - Halle 1
    m11RH1: (0, pg_core_1.integer)("M11-R-H1"), // Maschine 11 - Ring - Halle 1
    m12TH1: (0, pg_core_1.integer)("M12-T-H1"), // Maschine 12 - Trommel - Halle 1
    m13RH1: (0, pg_core_1.integer)("M13-R-H1"), // Maschine 13 - Ring - Halle 1
    m14TH1: (0, pg_core_1.integer)("M14-T-H1"), // Maschine 14 - Trommel - Halle 1
    m15RH1: (0, pg_core_1.integer)("M15-R-H1"), // Maschine 15 - Ring - Halle 1
    m16TH1: (0, pg_core_1.integer)("M16-T-H1"), // Maschine 16 - Trommel - Halle 1
    m17RH1: (0, pg_core_1.integer)("M17-R-H1"), // Maschine 17 - Ring - Halle 1
    m18TH1: (0, pg_core_1.integer)("M18-T-H1"), // Maschine 18 - Trommel - Halle 1
    m19TH1: (0, pg_core_1.integer)("M19-T-H1"), // Maschine 19 - Trommel - Halle 1
    m20TH1: (0, pg_core_1.integer)("M20-T-H1"), // Maschine 20 - Trommel - Halle 1
    m21RH1: (0, pg_core_1.integer)("M21-R-H1"), // Maschine 21 - Ring - Halle 1
    m23TH1: (0, pg_core_1.integer)("M23-T-H1"), // Maschine 23 - Trommel - Halle 1
    m25RRH1: (0, pg_core_1.integer)("M25-RR-H1"), // Maschine 25 - Ring-Ring - Halle 1
    m26TH1: (0, pg_core_1.integer)("M26-T-H1"), // Maschine 26 - Trommel - Halle 1
    sumH1: (0, pg_core_1.integer)("Sum-H1"), // Summe aller H1-Maschinen
    // H3-Maschinen (Halle 3)
    m1TH3: (0, pg_core_1.integer)("M1-T-H3"), // Maschine 1 - Trommel - Halle 3
    m2TH3: (0, pg_core_1.integer)("M2-T-H3"), // Maschine 2 - Trommel - Halle 3
    m3RH3: (0, pg_core_1.integer)("M3-R-H3"), // Maschine 3 - Ring - Halle 3
    m4TH3: (0, pg_core_1.integer)("M4-T-H3"), // Maschine 4 - Trommel - Halle 3
    m22TH3: (0, pg_core_1.integer)("M22-T-H3"), // Maschine 22 - Trommel - Halle 3
    m24TH3: (0, pg_core_1.integer)("M24-T-H3"), // Maschine 24 - Trommel - Halle 3
    m27RH3: (0, pg_core_1.integer)("M27-R-H3"), // Maschine 27 - Ring - Halle 3
    sumH3: (0, pg_core_1.integer)("Sum-H3"), // Summe aller H3-Maschinen
});
// Alias für Kompatibilität
exports.Schnitte = exports.schnitte;
// Materialdaten table
exports.materialdaten = (0, pg_core_1.pgTable)("materialdaten", {
    matnr: (0, pg_core_1.text)("matnr").primaryKey(),
    materialkurztext: (0, pg_core_1.text)("materialkurztext"),
    kabeldurchmesser: (0, pg_core_1.real)("kabeldurchmesser"),
    zuschlagKabeldurchmesser: (0, pg_core_1.real)("zuschlagKabeldurchmesser"),
    biegefaktor: (0, pg_core_1.real)("biegefaktor"),
    kleinsterErlauberFreiraum: (0, pg_core_1.real)("kleinsterErlauberFreiraum"),
    bruttogewicht: (0, pg_core_1.real)("bruttogewicht"),
    created_at: (0, pg_core_1.text)("created_at"),
    updated_at: (0, pg_core_1.text)("updated_at"),
});
// Alias für Kompatibilität
exports.Materialdaten = exports.materialdaten;
// Trommeldaten table
exports.trommeldaten = (0, pg_core_1.pgTable)("trommeldaten", {
    trommeldaten: (0, pg_core_1.text)("trommeldaten").primaryKey(),
    aussendurchmesser: (0, pg_core_1.real)("aussendurchmesser"),
    kerndurchmesser: (0, pg_core_1.real)("kerndurchmesser"),
});
// Alias für Kompatibilität
exports.Trommeldaten = exports.trommeldaten;
// Stoerungen table - Angepasst an existierende Datenbankstruktur
// Schema entspricht der tatsächlichen Tabelle in sfm_dashboard.db
exports.stoerungen = (0, pg_core_1.pgTable)("Stoerungen", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    title: (0, pg_core_1.text)("title").notNull(),
    description: (0, pg_core_1.text)("description"),
    severity: (0, pg_core_1.text)("severity").notNull(),
    status: (0, pg_core_1.text)("status").notNull(),
    category: (0, pg_core_1.text)("category"),
    assigned_to: (0, pg_core_1.text)("assigned_to"),
    affected_system: (0, pg_core_1.text)("affected_system"),
    location: (0, pg_core_1.text)("location"),
    reported_by: (0, pg_core_1.text)("reported_by"),
    created_at: (0, pg_core_1.text)("created_at").notNull(),
    updated_at: (0, pg_core_1.text)("updated_at").notNull(),
    resolved_at: (0, pg_core_1.text)("resolved_at"),
    mttr_minutes: (0, pg_core_1.integer)("mttr_minutes"),
    tags: (0, pg_core_1.text)("tags"),
    resolution_steps: (0, pg_core_1.text)("resolution_steps"),
    root_cause: (0, pg_core_1.text)("root_cause"),
    lessons_learned: (0, pg_core_1.text)("lessons_learned"),
});
// Alias für Kompatibilität mit dem bestehenden Code
exports.Stoerungen = exports.stoerungen;
// StoerungsComments table - Angepasst an existierende Datenbankstruktur
// Schema entspricht der tatsächlichen Tabelle in sfm_dashboard.db
exports.stoerungsComments = (0, pg_core_1.pgTable)("StoerungsComments", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    // Spaltenname entspricht der existierenden DB: stoerung_id (nicht stoerungId)
    stoerungId: (0, pg_core_1.integer)("stoerung_id").notNull(),
    // Spaltenname entspricht der existierenden DB: user_id (nicht author)
    userId: (0, pg_core_1.text)("user_id"),
    // Spaltenname entspricht der existierenden DB: comment (nicht content)
    comment: (0, pg_core_1.text)("comment").notNull(),
    // Spaltenname entspricht der existierenden DB: created_at (nicht timestamp)
    createdAt: (0, pg_core_1.text)("created_at").notNull(),
    // Zusätzliche Spalte aus der existierenden DB
    caretakerId: (0, pg_core_1.text)("caretaker_id"),
});
// Alias für Kompatibilität
exports.StoerungsComments = exports.stoerungsComments;
// StoerungsAttachment table - Match existing PostgreSQL table structure
exports.stoerungsAttachment = (0, pg_core_1.pgTable)("stoerungsattachment", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    stoerungId: (0, pg_core_1.integer)("stoerung_id").notNull(),
    filename: (0, pg_core_1.text)("filename").notNull(),
    storedName: (0, pg_core_1.text)("stored_name"), // Match actual DB field
    filePath: (0, pg_core_1.text)("file_path"), // Match actual DB field
    fileSize: (0, pg_core_1.integer)("file_size").notNull(),
    mimeType: (0, pg_core_1.text)("mime_type").notNull(),
    fileType: (0, pg_core_1.text)("file_type"), // Match actual DB field
    uploadedBy: (0, pg_core_1.text)("uploaded_by").notNull(),
    createdAt: (0, pg_core_1.text)("created_at").notNull(),
    updatedAt: (0, pg_core_1.text)("updated_at").notNull(),
});
// SystemStatus table - Aligned with actual database structure
exports.systemStatus = (0, pg_core_1.pgTable)("SystemStatus", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    systemName: (0, pg_core_1.text)("system_name").notNull(),
    status: (0, pg_core_1.text)("status").notNull(),
    lastUpdated: (0, pg_core_1.text)("last_check").notNull(),
    metadata: (0, pg_core_1.text)("metadata"),
    createdAt: (0, pg_core_1.text)("created_at").notNull(),
    updatedAt: (0, pg_core_1.text)("updated_at").notNull(),
});
// Alias für Kompatibilität
exports.SystemStatus = exports.systemStatus;
// SystemStatusMessage table - Aligned with actual database structure
exports.systemStatusMessage = (0, pg_core_1.pgTable)("SystemStatusMessage", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    systemStatusId: (0, pg_core_1.integer)("system_status_id"),
    category: (0, pg_core_1.text)("category").notNull(),
    status: (0, pg_core_1.text)("status").notNull(),
    title: (0, pg_core_1.text)("title").notNull(),
    description: (0, pg_core_1.text)("description").notNull(),
    priority: (0, pg_core_1.integer)("priority").default(1),
    isActive: (0, pg_core_1.boolean)("is_active").default(true),
    createdAt: (0, pg_core_1.text)("created_at").notNull(),
    updatedAt: (0, pg_core_1.text)("updated_at").notNull(),
});
// Alias für Kompatibilität
exports.SystemStatusMessage = exports.systemStatusMessage;
// User table
exports.user = (0, pg_core_1.pgTable)("User", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    username: (0, pg_core_1.text)("username").notNull().unique(),
    email: (0, pg_core_1.text)("email").notNull().unique(),
    password: (0, pg_core_1.text)("password").notNull(),
    passwordHash: (0, pg_core_1.text)("passwordHash").notNull(),
    role: (0, pg_core_1.text)("role").notNull().default("user"),
    isActive: (0, pg_core_1.boolean)("isActive").default(true),
    createdAt: (0, pg_core_1.text)("createdAt").notNull(),
    updatedAt: (0, pg_core_1.text)("updatedAt").notNull(),
    lastLogin: (0, pg_core_1.text)("lastLogin"),
    loginAttempts: (0, pg_core_1.integer)("loginAttempts").default(0),
    lockedUntil: (0, pg_core_1.text)("lockedUntil"),
    resetToken: (0, pg_core_1.text)("resetToken"),
    resetTokenExpires: (0, pg_core_1.text)("resetTokenExpires"),
    emailVerified: (0, pg_core_1.boolean)("emailVerified").default(false),
    emailVerificationToken: (0, pg_core_1.text)("emailVerificationToken"),
    twoFactorEnabled: (0, pg_core_1.boolean)("twoFactorEnabled").default(false),
    twoFactorSecret: (0, pg_core_1.text)("twoFactorSecret"),
    preferences: (0, pg_core_1.text)("preferences"),
    avatar: (0, pg_core_1.text)("avatar"),
    firstName: (0, pg_core_1.text)("firstName"),
    lastName: (0, pg_core_1.text)("lastName"),
    phone: (0, pg_core_1.text)("phone"),
    department: (0, pg_core_1.text)("department"),
    position: (0, pg_core_1.text)("position"),
    manager: (0, pg_core_1.text)("manager"),
    location: (0, pg_core_1.text)("location"),
    timezone: (0, pg_core_1.text)("timezone"),
    language: (0, pg_core_1.text)("language").default("de"),
    theme: (0, pg_core_1.text)("theme").default("light"),
    notifications: (0, pg_core_1.text)("notifications"),
});
// Alias für Kompatibilität
exports.User = exports.user;
// Bereitschafts tables - Angepasst an die bestehende Datenbankstruktur
// BereitschaftsPersonen table - Angepasst an die tatsächliche Datenbankstruktur
exports.bereitschaftsPersonen = (0, pg_core_1.pgTable)("bereitschafts_personen", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    name: (0, pg_core_1.text)("name").notNull(),
    telefon: (0, pg_core_1.text)("telefon"), // Optional in der DB
    email: (0, pg_core_1.text)("email").notNull(),
    abteilung: (0, pg_core_1.text)("abteilung"), // Optional in der DB
    aktiv: (0, pg_core_1.boolean)("aktiv").default(true),
    reihenfolge: (0, pg_core_1.integer)("reihenfolge").default(0),
    createdAt: (0, pg_core_1.text)("created_at").notNull(),
    updatedAt: (0, pg_core_1.text)("updated_at").notNull(),
});
// Alias für Kompatibilität
exports.BereitschaftsPersonen = exports.bereitschaftsPersonen;
// BereitschaftsWochen table - Angepasst an die tatsächliche Datenbankstruktur
exports.bereitschaftsWochen = (0, pg_core_1.pgTable)("bereitschafts_wochen", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    personId: (0, pg_core_1.integer)("person_id").notNull(),
    wochenStart: (0, pg_core_1.text)("wochen_start").notNull(),
    wochenEnde: (0, pg_core_1.text)("wochen_ende").notNull(),
    von: (0, pg_core_1.text)("von").notNull(),
    bis: (0, pg_core_1.text)("bis").notNull(),
    aktiv: (0, pg_core_1.boolean)("aktiv").default(true),
    notiz: (0, pg_core_1.text)("notiz"),
    createdAt: (0, pg_core_1.text)("created_at").notNull(),
    updatedAt: (0, pg_core_1.text)("updated_at").notNull(),
});
// Alias für Kompatibilität
exports.BereitschaftsWochen = exports.bereitschaftsWochen;
// BereitschaftsAusnahmen table
exports.bereitschaftsAusnahmen = (0, pg_core_1.pgTable)("bereitschafts_ausnahmen", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    personId: (0, pg_core_1.integer)("person_id").notNull(),
    von: (0, pg_core_1.text)("von").notNull(), // KORRIGIERT: DATETIME in der DB
    bis: (0, pg_core_1.text)("bis").notNull(), // KORRIGIERT: DATETIME in der DB
    grund: (0, pg_core_1.text)("grund").notNull(),
    ersatzPersonId: (0, pg_core_1.integer)("ersatz_person_id"), // Optional
    aktiv: (0, pg_core_1.boolean)("aktiv").default(true), // KORRIGIERT: BOOLEAN in der DB
    createdAt: (0, pg_core_1.text)("created_at").notNull(), // KORRIGIERT: DATETIME mit CURRENT_TIMESTAMP
    updatedAt: (0, pg_core_1.text)("updated_at").notNull(), // KORRIGIERT: DATETIME mit CURRENT_TIMESTAMP
});
// Alias für Kompatibilität
exports.BereitschaftsAusnahmen = exports.bereitschaftsAusnahmen;
// BereitschaftsKonfiguration table
exports.bereitschaftsKonfiguration = (0, pg_core_1.pgTable)("bereitschafts_konfiguration", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    wechselTag: (0, pg_core_1.integer)("wechsel_tag").notNull().default(5), // KORRIGIERT: NOT NULL in der DB
    wechselUhrzeit: (0, pg_core_1.text)("wechsel_uhrzeit").notNull().default("08:00"), // KORRIGIERT: NOT NULL in der DB
    rotationAktiv: (0, pg_core_1.boolean)("rotation_aktiv").notNull().default(true), // KORRIGIERT: BOOLEAN NOT NULL in der DB
    benachrichtigungTage: (0, pg_core_1.integer)("benachrichtigung_tage").notNull().default(2), // KORRIGIERT: NOT NULL in der DB
    emailBenachrichtigung: (0, pg_core_1.boolean)("email_benachrichtigung")
        .notNull()
        .default(true), // KORRIGIERT: BOOLEAN NOT NULL in der DB
    createdAt: (0, pg_core_1.text)("created_at").notNull(), // KORRIGIERT: DATETIME mit CURRENT_TIMESTAMP
    updatedAt: (0, pg_core_1.text)("updated_at").notNull(), // KORRIGIERT: DATETIME mit CURRENT_TIMESTAMP
});
// Alias für Kompatibilität
exports.BereitschaftsKonfiguration = exports.bereitschaftsKonfiguration;
// Runbook table
exports.runbook = (0, pg_core_1.pgTable)("Runbook", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    title: (0, pg_core_1.text)("title").notNull(),
    content: (0, pg_core_1.text)("content").notNull(), // Markdown content
    affected_systems: (0, pg_core_1.text)("affected_systems"), // JSON array as string
    category: (0, pg_core_1.text)("category"), // JSON array as string (formerly tags)
    steps: (0, pg_core_1.text)("steps"), // JSON array of steps
    created_at: (0, pg_core_1.text)("created_at").notNull(),
    updated_at: (0, pg_core_1.text)("updated_at").notNull(),
}, (table) => ({
    createdAtIdx: (0, pg_core_1.index)("Runbook_created_at_idx").on(table.created_at),
    updatedAtIdx: (0, pg_core_1.index)("Runbook_updated_at_idx").on(table.updated_at),
}));
// Alias für Kompatibilität
exports.Runbook = exports.runbook;
// Bestand200 table (Lagerbestand Lager 200)
exports.bestand200 = (0, pg_core_1.pgTable)("bestand200", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    lagertyp: (0, pg_core_1.text)("Lagertyp"),
    lagerplatz: (0, pg_core_1.text)("Lagerplatz"), // DB-Spalte heißt "Lagerplatz" (großes L)
    material: (0, pg_core_1.text)("Material"),
    charge: (0, pg_core_1.text)("Charge"),
    dauer: (0, pg_core_1.real)("Dauer"),
    lagerbereich: (0, pg_core_1.text)("Lagerbereich"),
    lagerplatztyp: (0, pg_core_1.text)("Lagerplatztyp"),
    lagerplatzaufteilung: (0, pg_core_1.text)("Lagerplatzaufteilung"),
    auslagerungssperre: (0, pg_core_1.text)("Auslagerungssperre"),
    einlagerungssperre: (0, pg_core_1.text)("Einlagerungssperre"),
    sperrgrund: (0, pg_core_1.text)("Sperrgrund"),
    letzteBewegung: (0, pg_core_1.text)("Letzte Bewegung"),
    uhrzeit: (0, pg_core_1.text)("Uhrzeit"),
    taNummer: (0, pg_core_1.text)("TA-Nummer"),
    taPosition: (0, pg_core_1.text)("TA-Position"),
    letzterAenderer: (0, pg_core_1.text)("Letzter Änderer"),
    letzteAenderung: (0, pg_core_1.text)("Letzte Änderung"),
    wareneingangsdatum: (0, pg_core_1.text)("Wareneingangsdatum"),
    weNummer: (0, pg_core_1.text)("WE-Nummer"),
    wePosition: (0, pg_core_1.text)("WE-Position"),
    lieferung: (0, pg_core_1.text)("Lieferung"),
    position: (0, pg_core_1.text)("Position"),
    lagereinheitentyp: (0, pg_core_1.text)("Lagereinheitentyp"),
    gesamtbestand: (0, pg_core_1.real)("Gesamtbestand"),
    lagereinheit: (0, pg_core_1.text)("Lagereinheit"),
    aufnahmeDatum: (0, pg_core_1.text)("aufnahmeDatum"),
    aufnahmeZeit: (0, pg_core_1.text)("aufnahmeZeit"),
    maxPlaetze: (0, pg_core_1.text)("maxPlaetze"),
    auslastung: (0, pg_core_1.text)("auslastung"),
    maxA: (0, pg_core_1.text)("maxA"),
    maxB: (0, pg_core_1.text)("maxB"),
    maxC: (0, pg_core_1.text)("maxC"),
    auslastungA: (0, pg_core_1.text)("auslastungA"),
    auslastungB: (0, pg_core_1.text)("auslastungB"),
    auslastungC: (0, pg_core_1.text)("auslastungC"),
    importTimestamp: (0, pg_core_1.timestamp)("import_timestamp", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
}, (table) => ({
    importTimestampIdx: (0, pg_core_1.index)("bestand200_import_timestamp_idx").on(table.importTimestamp),
    aufnahmeDatumIdx: (0, pg_core_1.index)("bestand200_aufnahmeDatum_idx").on(table.aufnahmeDatum),
}));
// Alias für Kompatibilität
exports.Bestand200 = exports.bestand200;
// Performance Monitoring Tables
exports.responseTimeMetric = (0, pg_core_1.pgTable)("response_time_metric", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    timestamp: (0, pg_core_1.timestamp)("timestamp", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
    totalTime: (0, pg_core_1.real)("total_time").notNull(),
    success: (0, pg_core_1.boolean)("success").notNull(),
    enriched: (0, pg_core_1.boolean)("enriched").notNull(),
    dataSize: (0, pg_core_1.integer)("data_size"),
}, (table) => ({
    timestampIdx: (0, pg_core_1.index)("response_time_metric_timestamp_idx").on(table.timestamp),
}));
// Alias für Kompatibilität
exports.ResponseTimeMetric = exports.responseTimeMetric;
// QueryPerformanceMetric table
exports.queryPerformanceMetric = (0, pg_core_1.pgTable)("query_performance_metric", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    timestamp: (0, pg_core_1.timestamp)("timestamp", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
    queryType: (0, pg_core_1.text)("query_type").notNull(),
    duration: (0, pg_core_1.real)("duration").notNull(),
    success: (0, pg_core_1.boolean)("success").notNull(),
    cacheHit: (0, pg_core_1.boolean)("cache_hit").notNull(),
}, (table) => ({
    timestampIdx: (0, pg_core_1.index)("query_performance_metric_timestamp_idx").on(table.timestamp),
}));
// Alias für Kompatibilität
exports.QueryPerformanceMetric = exports.queryPerformanceMetric;
// IntentRecognitionMetric table
exports.intentRecognitionMetric = (0, pg_core_1.pgTable)("intent_recognition_metric", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    timestamp: (0, pg_core_1.timestamp)("timestamp", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
    accuracy: (0, pg_core_1.real)("accuracy").notNull(),
    confidence: (0, pg_core_1.real)("confidence").notNull(),
}, (table) => ({
    timestampIdx: (0, pg_core_1.index)("intent_recognition_metric_timestamp_idx").on(table.timestamp),
}));
// Alias für Kompatibilität
exports.IntentRecognitionMetric = exports.intentRecognitionMetric;
// EnrichmentPerformanceMetric table
exports.enrichmentPerformanceMetric = (0, pg_core_1.pgTable)("enrichment_performance_metric", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    timestamp: (0, pg_core_1.timestamp)("timestamp", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
    enrichmentTime: (0, pg_core_1.real)("enrichment_time").notNull(),
    dataSourcesUsed: (0, pg_core_1.integer)("data_sources_used").notNull(),
    success: (0, pg_core_1.boolean)("success").notNull(),
}, (table) => ({
    timestampIdx: (0, pg_core_1.index)("enrichment_performance_metric_timestamp_idx").on(table.timestamp),
}));
// Alias für Kompatibilität
exports.EnrichmentPerformanceMetric = exports.enrichmentPerformanceMetric;
// PerformanceAlert table
exports.performanceAlert = (0, pg_core_1.pgTable)("performance_alert", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    timestamp: (0, pg_core_1.timestamp)("timestamp", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
    alertType: (0, pg_core_1.text)("alert_type").notNull(),
    severity: (0, pg_core_1.text)("severity").notNull(),
    message: (0, pg_core_1.text)("message").notNull(),
    resolved: (0, pg_core_1.boolean)("resolved").default(false),
}, (table) => ({
    timestampIdx: (0, pg_core_1.index)("performance_alert_timestamp_idx").on(table.timestamp),
}));
// Alias für Kompatibilität
exports.PerformanceAlert = exports.performanceAlert;
// Workflow Tables
exports.workflowLog = (0, pg_core_1.pgTable)("workflow_log", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    timestamp: (0, pg_core_1.timestamp)("timestamp", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
    level: (0, pg_core_1.text)("level").notNull(),
    message: (0, pg_core_1.text)("message").notNull(),
    workflowId: (0, pg_core_1.text)("workflow_id"),
    executionId: (0, pg_core_1.text)("execution_id"),
    details: (0, pg_core_1.text)("details"),
}, (table) => ({
    timestampIdx: (0, pg_core_1.index)("workflow_log_timestamp_idx").on(table.timestamp),
}));
// Alias für Kompatibilität
exports.WorkflowLog = exports.workflowLog;
exports.workflowExecution = (0, pg_core_1.pgTable)("workflow_execution", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    timestamp: (0, pg_core_1.timestamp)("timestamp", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
    workflowId: (0, pg_core_1.text)("workflow_id").notNull(),
    status: (0, pg_core_1.text)("status").notNull(),
    startTime: (0, pg_core_1.timestamp)("start_time", { mode: "date" }),
    endTime: (0, pg_core_1.timestamp)("end_time", { mode: "date" }),
    result: (0, pg_core_1.text)("result"),
}, (table) => ({
    timestampIdx: (0, pg_core_1.index)("workflow_execution_timestamp_idx").on(table.timestamp),
}));
// Alias für Kompatibilität
exports.WorkflowExecution = exports.workflowExecution;
