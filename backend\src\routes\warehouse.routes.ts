import express from 'express';
import { db } from '../db';
import { dispatchData, we, bestand200 } from '../db/schema';
import { gte, lte, and, desc, eq, isNotNull } from 'drizzle-orm';

const router = express.Router();

// Helper function to build Drizzle date conditions
const buildDateConditions = (dateColumn: any, startDate?: string, endDate?: string) => {
  const conditions: any[] = [];
  if (startDate) {
    conditions.push(gte(dateColumn, new Date(startDate)));
  }
  if (endDate) {
    conditions.push(lte(dateColumn, new Date(endDate)));
  }
  return conditions;
};

router.get('/service-level', async (req, res) => {
  console.log('✅ Service-Level Route aufgerufen');
  try {
    const { startDate, endDate } = req.query;
    
    const dateConditions = buildDateConditions(dispatchData.datum, startDate as string, endDate as string);
    
    const serviceData = await db.select({
      datum: dispatchData.datum,
      servicegrad: dispatchData.servicegrad
    })
    .from(dispatchData)
    .where(and(
      isNotNull(dispatchData.datum),
      isNotNull(dispatchData.servicegrad),
      ...dateConditions
    ))
    .orderBy(desc(dispatchData.datum))
    .limit(30);
    
    const transformedData = serviceData.map((item: any) => ({
      datum: item.datum,
      csr: item.servicegrad ? Math.round(item.servicegrad * 100) : 0
    }));
    
    console.log(`✅ Erfolgreich ${transformedData.length} Service-Level Einträge abgerufen`);
    res.json({ 
      success: true, 
      data: transformedData
    });
  } catch (error) {
    console.error('❌ Fehler beim Service-Level Abruf:', error);
    res.status(500).json({
      success: false,
      error: 'Service-Level Fehler'
    });
  }
});

router.get('/atrl', async (req, res) => {
  console.log('✅ ATrL Route aufgerufen');
  try {
    const { startDate, endDate } = req.query;
    const dateConditions = buildDateConditions(dispatchData.datum, startDate as string, endDate as string);
    
    const atrlData = await db.select({
      datum: dispatchData.datum,
      atrl: dispatchData.atrl
    })
    .from(dispatchData)
    .where(and(
      isNotNull(dispatchData.datum),
      isNotNull(dispatchData.atrl),
      ...dateConditions
    ))
    .orderBy(desc(dispatchData.datum))
    .limit(30);
    
    const transformedData = atrlData.map((item: any) => ({
      Datum: item.datum,
      weAtrl: item.atrl || 0
    }));
    
    res.json({ success: true, data: transformedData });
  } catch (error) {
    console.error('❌ Fehler beim ATrL Abruf:', error);
    res.status(500).json({ success: false, error: 'ATrL Fehler' });
  }
});

router.get('/aril', async (req, res) => {
  console.log('✅ ARiL Route aufgerufen');
  try {
    const { startDate, endDate } = req.query;
    
    const dateConditions = buildDateConditions(dispatchData.datum, startDate as string, endDate as string);
    
    const arilData = await db.select({
      datum: dispatchData.datum,
      aril: dispatchData.aril,
      fuellgrad_aril: dispatchData.fuellgrad_aril
    })
    .from(dispatchData)
    .where(and(
      isNotNull(dispatchData.datum),
      isNotNull(dispatchData.aril),
      ...dateConditions
    ))
    .orderBy(desc(dispatchData.datum))
    .limit(30);
    
    const transformedData = arilData.map((item: any) => ({
      Datum: item.datum,
      aril: item.aril || 0,
      fuellgrad: item.fuellgrad_aril || 0
    }));
    
    res.json({ success: true, data: transformedData });
  } catch (error) {
    console.error('❌ Fehler beim ARiL Abruf:', error);
    res.status(500).json({ success: false, error: 'ARiL Fehler' });
  }
});

router.get('/we', async (req, res) => {
  console.log('✅ WE (Wareneingang) Route aufgerufen');
  try {
    const { startDate, endDate } = req.query;
    const conditions = buildDateConditions(we.datum, startDate as string, endDate as string);
    
    let weQuery = db.select({
      datum: we.datum,
      weAtrl: we.weAtrl,
      weManl: we.weManl
    }).from(we);
    
    if (conditions.length > 0) {
      weQuery = weQuery.where(and(...conditions)) as any;
    }
    
    const weData = await weQuery
      .orderBy(desc(we.datum))
      .limit(30);

    const transformedData = weData.map((item: any) => ({
      id: Math.random(), // Temporäre ID
      datum: item.datum,
      weAtrl: item.weAtrl || 0,
      weManl: item.weManl || 0
    }));
    
    res.json({ success: true, data: transformedData });
  } catch (error) {
    console.error('❌ Fehler beim WE Abruf:', error);
    res.status(500).json({ success: false, error: 'WE Fehler' });
  }
});

router.get('/lagerauslastung200', async (req, res) => {
  console.log('✅ Lagerauslastung 200 Route aufgerufen');
  try {
    const { startDate, endDate } = req.query;
    const conditions = buildDateConditions(bestand200.aufnahmeDatum, startDate as string, endDate as string);
    
    let lagerQuery = db.select({
      aufnahmeDatum: bestand200.aufnahmeDatum,
      auslastung: bestand200.auslastung,
      auslastungA: bestand200.auslastungA,
      auslastungB: bestand200.auslastungB,
      auslastungC: bestand200.auslastungC,
      maxPlaetze: bestand200.maxPlaetze
    }).from(bestand200);
    
    if (conditions.length > 0) {
      lagerQuery = lagerQuery.where(and(...conditions)) as any;
    }
    
    const lagerData = await lagerQuery
      .orderBy(desc(bestand200.aufnahmeDatum))
      .limit(100);
    
    const transformedData = lagerData.map((item: any) => ({
      aufnahmeDatum: item.aufnahmeDatum,
      gesamt: parseFloat(item.auslastung) || 0,
      maxPlaetze: parseInt(item.maxPlaetze) || 0,
      auslastungA: parseFloat(item.auslastungA) || 0,
      auslastungB: parseFloat(item.auslastungB) || 0,
      auslastungC: parseFloat(item.auslastungC) || 0
    }));
    
    res.json({ success: true, data: transformedData });
  } catch (error) {
    console.error('❌ Fehler beim Lagerauslastung 200 Abruf:', error);
    res.status(500).json({ success: false, error: 'Lagerauslastung 200 Fehler' });
  }
});

router.get('/daily-performance', async (req, res) => {
  console.log('✅ Daily-Performance Route aufgerufen');
  try {
    const performanceData = await db.select({
      datum: dispatchData.datum,
      produzierte_tonnagen: dispatchData.produzierte_tonnagen
    })
    .from(dispatchData)
    .orderBy(desc(dispatchData.datum))
    .limit(30);
    
    const transformedData = performanceData.map((item: any) => ({
      datum: item.datum,
      value: item.produzierte_tonnagen || 0
    }));
    
    res.json({ success: true, data: transformedData });
  } catch (error) {
    console.error('❌ Fehler beim Daily-Performance Abruf:', error);
    res.status(500).json({ success: false, error: 'Daily-Performance Fehler' });
  }
});

router.get('/picking', async (req, res) => {
  console.log('✅ Picking Route aufgerufen');
  try {
    const pickingData = [
      { name: 'Kategorie A', value: 45 },
      { name: 'Kategorie B', value: 30 },
      { name: 'Kategorie C', value: 25 }
    ];
    
    res.json({ success: true, data: pickingData });
  } catch (error) {
    console.error('❌ Fehler beim Picking Abruf:', error);
    res.status(500).json({ success: false, error: 'Picking Fehler' });
  }
});

router.get('/returns', async (req, res) => {
  console.log('✅ Returns Route aufgerufen');
  try {
    const returnsData = [
      { name: 'Grund A', value: 15 },
      { name: 'Grund B', value: 10 },
      { name: 'Grund C', value: 5 }
    ];
    
    res.json({ success: true, data: returnsData });
  } catch (error) {
    console.error('❌ Fehler beim Returns Abruf:', error);
    res.status(500).json({ success: false, error: 'Returns Fehler' });
  }
});

router.get('/delivery-positions', async (req, res) => {
  console.log('✅ Delivery-Positions Route aufgerufen');
  try {
    const { startDate, endDate } = req.query as { startDate?: string; endDate?: string };
    
    // Basis-Query für dispatch_data mit korrekter Typisierung
    // Verwende bedingte Query-Erstellung statt Neuzuweisung
    let deliveryData;
    
    if (startDate && endDate) {
      const dateConditions = buildDateConditions(dispatchData.datum, startDate, endDate);
      if (dateConditions.length > 0) {
        // Query mit Datumsfilterung - alle Bedingungen in einer where-Klausel
        deliveryData = await db.select({
          datum: dispatchData.datum,
          ausgeliefert_lup: dispatchData.ausgeliefert_lup,
          rueckstaendig: dispatchData.rueckstaendig
        })
        .from(dispatchData)
        .where(
          and(
            isNotNull(dispatchData.ausgeliefert_lup),
            isNotNull(dispatchData.rueckstaendig),
            isNotNull(dispatchData.datum),
            ...dateConditions
          )
        )
        .orderBy(desc(dispatchData.datum))
        .limit(100);
      } else {
        // Fallback ohne Datumsfilterung wenn dateConditions leer
        deliveryData = await db.select({
          datum: dispatchData.datum,
          ausgeliefert_lup: dispatchData.ausgeliefert_lup,
          rueckstaendig: dispatchData.rueckstaendig
        })
        .from(dispatchData)
        .where(
          and(
            isNotNull(dispatchData.ausgeliefert_lup),
            isNotNull(dispatchData.rueckstaendig)
          )
        )
        .orderBy(desc(dispatchData.datum))
        .limit(100);
      }
    } else {
      // Query ohne Datumsfilterung
      deliveryData = await db.select({
        datum: dispatchData.datum,
        ausgeliefert_lup: dispatchData.ausgeliefert_lup,
        rueckstaendig: dispatchData.rueckstaendig
      })
      .from(dispatchData)
      .where(
        and(
          isNotNull(dispatchData.ausgeliefert_lup),
          isNotNull(dispatchData.rueckstaendig)
        )
      )
      .orderBy(desc(dispatchData.datum))
      .limit(100);
    }

    const transformedData = deliveryData.map((item: any) => ({
      date: item.datum,
      ausgeliefert_lup: item.ausgeliefert_lup || 0,
      rueckstaendig: item.rueckstaendig || 0
    }));
    
    console.log(`📊 Delivery-Positions: ${transformedData.length} Datensätze gefunden`);
    res.json({ success: true, data: transformedData });
  } catch (error) {
    console.error('❌ Fehler beim Delivery-Positions Abruf:', error);
    res.status(500).json({ success: false, error: 'Delivery-Positions Fehler' });
  }
});

router.get('/tagesleistung', async (req, res) => {
  console.log('✅ Tagesleistung Route aufgerufen');
  try {
    const tagesleistungData = await db.select({
      datum: dispatchData.datum,
      produzierte_tonnagen: dispatchData.produzierte_tonnagen,
      direktverladung_kiaa: dispatchData.direktverladung_kiaa,
      umschlag: dispatchData.umschlag,
      kg_pro_colli: dispatchData.kg_pro_colli,
      elefanten: dispatchData.elefanten
    })
    .from(dispatchData)
    .orderBy(desc(dispatchData.datum))
    .limit(10);
    
    const transformedData = tagesleistungData.map((item: any) => ({
      date: item.datum,
      produzierte_tonnagen: item.produzierte_tonnagen || 0,
      direktverladung_kiaa: item.direktverladung_kiaa || 0,
      umschlag: item.umschlag || 0,
      kg_pro_colli: item.kg_pro_colli || 0,
      elefanten: item.elefanten || 0
    }));
    
    res.json({ success: true, data: transformedData });
  } catch (error) {
    console.error('❌ Fehler beim Tagesleistung Abruf:', error);
    res.status(500).json({ success: false, error: 'Tagesleistung Fehler' });
  }
});

export default router;