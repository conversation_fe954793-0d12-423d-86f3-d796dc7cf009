import React, { useState } from 'react';
import type { AddCardFunction } from './types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Minus, BarChart3, TrendingUp, Package, Zap, Scissors, Award, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDragDropContext } from './DragDropProvider';
import type { AvailableKPICard } from './types';

/**
 * Props für die KPI-Bibliothek-Komponente
 */
interface KPILibraryProps {
  /** Ob die Bibliothek sichtbar ist */
  isVisible: boolean;
  /** Callback zum Schließen der Bibliothek */
  onClose: () => void;
  /** CSS-Klassen für das Container-Element */
  className?: string;
  /** Callback für ausstehende Änderungen (für externen Button) */
  onPendingChangesUpdate?: (hasPendingChanges: boolean, applyChanges: () => void) => void;
}

/**
 * Mapping von KPI-IDs zu passenden Icons
 */
const getKPIIcon = (id: string) => {
  const iconMap = {
    wareneingang: Package,
    tonnage: BarChart3,
    performance: TrendingUp,
    efficiency: Zap,
    schnitte: Scissors,
    quality: Award,
    delivery: Package,
    picking: BarChart3,
    automatisierung: Zap,
    produktion: Scissors,
    lager: TrendingUp,
    versand: Package,
    service: Award,
  };
  
  const IconComponent = iconMap[id as keyof typeof iconMap] || BarChart3;
  return <IconComponent className="h-5 w-5" />;
};

/**
 * Mapping von Kategorien zu Farben für Badges
 */
const getCategoryColor = (category: string) => {
  const colorMap = {
    wareneingang: 'bg-blue-100 text-blue-800',
    dispatch: 'bg-green-100 text-green-800',
    ablaengerei: 'bg-purple-100 text-purple-800',
    quality: 'bg-orange-100 text-orange-800',
    production: 'bg-red-100 text-red-800',
    logistics: 'bg-yellow-100 text-yellow-800',
    efficiency: 'bg-indigo-100 text-indigo-800',
  };
  
  return colorMap[category as keyof typeof colorMap] || 'bg-gray-100 text-gray-800';
};


/**
 * KPI-Bibliothek-Komponente
 * 
 * Zeigt alle verfügbaren KPI-Karten an und ermöglicht das Hinzufügen/Entfernen
 * von Karten zum/vom Dashboard. Bietet auch die Möglichkeit, Kartengrößen zu ändern.
 */
export const KPILibrary: React.FC<KPILibraryProps> = ({
  isVisible,
  onClose,
  className,
  onPendingChangesUpdate
}) => {
  const {
    availableCards,
    activeCards,
    cardPositions,
    addCard,
    removeCard
  } = useDragDropContext();

  // Lokaler State für temporäre Änderungen vor dem Anwenden
  const [pendingChanges, setPendingChanges] = useState<{
    toAdd: string[];
    toRemove: string[];
  }>({ toAdd: [], toRemove: [] });

  if (!isVisible) return null;

  /**
   * Behandelt das temporäre Hinzufügen einer Karte (noch nicht angewendet)
   */
  const handleAddCard = (card: AvailableKPICard) => {
    setPendingChanges(prev => ({
      toAdd: [...prev.toAdd.filter(id => id !== card.id), card.id],
      toRemove: prev.toRemove.filter(id => id !== card.id)
    }));
  };

  /**
   * Behandelt das temporäre Entfernen einer Karte (noch nicht angewendet)
   */
  const handleRemoveCard = (cardId: string) => {
    setPendingChanges(prev => ({
      toAdd: prev.toAdd.filter(id => id !== cardId),
      toRemove: [...prev.toRemove.filter(id => id !== cardId), cardId]
    }));
  };

  /**
   * Wendet alle ausstehenden Änderungen an
   */
  const handleApplyChanges = () => {
    // Entferne zuerst alle Karten, die entfernt werden sollen
    pendingChanges.toRemove.forEach(cardId => {
      removeCard(cardId);
    });

    // Füge dann alle neuen Karten hinzu
    pendingChanges.toAdd.forEach(cardId => {
      const card = availableCards.find(c => c.id === cardId);
      if (card) {
        // Finde die erste freie Position im 3x4-Raster (0-11)
        const usedPositions = cardPositions.map(pos => pos.position);
        let firstFreePosition = 0;
        
        // Finde die erste freie Position
        while (usedPositions.includes(firstFreePosition) && firstFreePosition < 12) {
          firstFreePosition++;
        }
        
        // Füge die Karte zur ersten freien Position hinzu
        addCard(card.id, firstFreePosition);
      }
    });

    // Setze ausstehende Änderungen zurück
    setPendingChanges({ toAdd: [], toRemove: [] });
  };

  /**
   * Prüft, ob eine Karte als aktiv angezeigt werden soll (berücksichtigt ausstehende Änderungen)
   */
  const isCardActive = (cardId: string): boolean => {
    const currentlyActive = activeCards.includes(cardId);
    const willBeAdded = pendingChanges.toAdd.includes(cardId);
    const willBeRemoved = pendingChanges.toRemove.includes(cardId);
    
    if (willBeAdded) return true;
    if (willBeRemoved) return false;
    return currentlyActive;
  };

  /**
   * Prüft, ob es ausstehende Änderungen gibt
   */
  const hasPendingChanges = pendingChanges.toAdd.length > 0 || pendingChanges.toRemove.length > 0;

  // Informiere Parent-Komponente über Änderungen
  React.useEffect(() => {
    if (onPendingChangesUpdate) {
      onPendingChangesUpdate(hasPendingChanges, handleApplyChanges);
    }
  }, [hasPendingChanges, onPendingChangesUpdate]);


  return (
    <div className={cn(
      "w-full h-full flex flex-col", // Flex-Layout für festen Footer
      className
    )}>
      {/* Content - Scrollbarer Bereich */}
      <div className="flex-1 overflow-y-auto px-2 py-2"> {/* Flex-1 für verfügbaren Platz, eigener Scroll */}
        <div className="grid grid-cols-1 gap-4"> {/* Reduzierter Gap zwischen Karten */}
          {availableCards.map((card) => {
            const isActive = isCardActive(card.id);
            const isPending = pendingChanges.toAdd.includes(card.id) || pendingChanges.toRemove.includes(card.id);
            
            return (
              <Card
                key={card.id}
                className={cn(
                  "transition-all duration-200 hover:shadow-md", // Entfernt zusätzlichen Margin
                  isActive && "ring-2 ring-blue-500 bg-blue-50",
                  isPending && "ring-2 ring-orange-400 bg-orange-50" // Hervorhebung für ausstehende Änderungen
                )}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {getKPIIcon(card.id)}
                      <CardTitle className="text-lg">{card.name}</CardTitle>
                    </div>
                    <Badge
                      className={cn(
                        "text-xs",
                        getCategoryColor(card.category)
                      )}
                    >
                      {card.category}
                    </Badge>
                  </div>
                  <CardDescription className="text-sm">
                    {card.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="pt-0">
                  {/* Add/Remove Button */}
                  <div className="flex justify-end">
                    {isActive ? (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleRemoveCard(card.id)}
                        className="flex items-center gap-2"
                      >
                        <Minus className="h-4 w-4" />
                        Entfernen
                      </Button>
                    ) : (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => handleAddCard(card)}
                        className="flex items-center gap-2"
                      >
                        <Plus className="h-4 w-4" />
                        Hinzufügen
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
      
      {/* Footer mit Statistiken - Fester Footer außerhalb des Scroll-Bereichs */}
      <div className="flex-shrink-0 border-t p-4 bg-slate-100"> {/* flex-shrink-0 verhindert Schrumpfung */}
        <div className="text-sm text-gray-600">
          <div className="mb-1">
            {activeCards.length} von {availableCards.length} Karten aktiv
          </div>
          <div>
            Verfügbare Kategorien: {new Set(availableCards.map(c => c.category)).size}
          </div>
          {hasPendingChanges && (
            <div className="text-orange-600 font-medium mt-2">
              {pendingChanges.toAdd.length + pendingChanges.toRemove.length} ausstehende Änderungen
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default KPILibrary;