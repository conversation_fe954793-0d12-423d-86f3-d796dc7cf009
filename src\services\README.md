# Services Layer

![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=flat&logo=typescript&logoColor=white)
![Service Layer](https://img.shields.io/badge/Architecture-Service%20Layer-green)
![API Integration](https://img.shields.io/badge/API-Backend%20Integration-blue)

Der `src/services` Ordner enthält die Service-Layer-Implementierung der Leitstand-Anwendung. Diese Layer implementiert die Anwendungslogik, verwaltet API-Kommunikation und stellt eine saubere Abstraktion zwischen der UI und den Backend-Systemen bereit.

## 📑 Inhaltsverzeichnis

- [Übersicht & Zweck](#übersicht--zweck)
- [Architektur & Patterns](#architektur--patterns)
- [Services auf einen Blick](#services-auf-einen-blick)
- [Dateistruktur](#dateistruktur)
- [Detaillierte Service-Beschreibungen](#detaillierte-service-beschreibungen)
- [Abhängigkeiten & Verbindungen](#abhängigkeiten--verbindungen)
- [Verwendungsbeispiele](#verwendungsbeispiele)
- [Datenmodelle & Schnittstellen](#datenmodelle--schnittstellen)
- [Testing & Qualitätssicherung](#testing--qualitätssicherung)
- [Entwicklungshinweise](#entwicklungshinweise)

## 🎯 Übersicht & Zweck

Der Service Layer implementiert das **Clean Architecture Pattern** und trennt strikt zwischen:

- **Domain Logic**: Geschäftslogik und Anwendungsregeln
- **Infrastructure**: API-Kommunikation und externe Abhängigkeiten  
- **Application Services**: Koordination und Orchestrierung

### Hauptfunktionen

- 🔌 **API-Integration**: Zentrale Backend-Kommunikation mit Retry-Logik und Fehlerbehandlung
- 🔐 **Authentifizierung**: JWT-basierte Benutzerauthentifizierung und Session-Management
- 📊 **Datenmanagement**: Caching, Transformierung und Validierung von Geschäftsdaten
- 🤖 **Business Intelligence**: Predictive Analytics und KPI-Monitoring
- 🏭 **Produktionsoptimierung**: Warehouse-Optimization und Production-Management
- ⚡ **Performance**: Intelligente Caching-Strategien und Ressourcen-Optimierung

### Rolle im Architekturmuster

```mermaid
graph TB
    UI[UI Components] --> Services[Service Layer]
    Services --> API[Backend APIs]
    Services --> Cache[Cache Layer]
    Services --> DB[Database Abstraction]
    
    subgraph "Service Layer"
        BaseService[Base Service]
        AuthService[Auth Service]
        APIService[API Service]
        BusinessServices[Business Services]
    end
```

## 🏗 Architektur & Patterns

### Design Patterns

1. **Singleton Pattern**: Für globale Service-Instanzen (`apiService`, `authService`)
2. **Factory Pattern**: Service-Konfiguration und -Erstellung
3. **Observer Pattern**: Event-basierte Kommunikation zwischen Services
4. **Strategy Pattern**: Austauschbare Algorithmen für Optimierungen
5. **Repository Pattern**: Datenabstraktion in Business Services

### Service-Kategorien

| Kategorie | Zweck | Beispiele |
|-----------|-------|-----------|
| **Core** | Basis-Funktionalität | `base.service.ts`, `api.service.ts` |
| **Authentication** | Benutzer-Management | `auth.service.ts`, `user.service.ts` |
| **Data Access** | Daten-Integration | `database.service.ts`, `cache.service.ts` |
| **Business Logic** | Domain-spezifische Logik | `production.business.service.ts`, `warehouse-optimization.service.ts` |
| **Infrastructure** | System-Integration | `api-config.service.ts`, `moduleAccess.service.ts` |

## 📋 Services auf einen Blick

| Datei | Zweck | Hauptfunktionen | Abhängigkeiten |
|-------|-------|-----------------|----------------|
| [`base.service.ts`](#base-service) | Service-Basis-Klasse | Health-Check, Retry-Logic, Logging | Keine |
| [`api.service.ts`](#api-service) | Backend-API Integration | HTTP-Client, Response-Handling, Cache | `base.service`, `cache.service` |
| [`auth.service.ts`](#auth-service) | Authentifizierung | Login, JWT-Management, Session | `api.service` |
| [`user.service.ts`](#user-service) | Benutzerverwaltung | Profil-Updates, Passwort-Änderung | `api.service` |
| [`database.service.ts`](#database-service) | Daten-Abstraktion | Frontend-Cache, Backend-Proxy | `api/backend-api.service` |
| [`cache.service.ts`](#cache-service) | In-Memory Caching | TTL-basiert, LRU-Eviction | Keine |
| [`api-config.service.ts`](#api-config-service) | API-Konfiguration | Environment-Management, Headers | Electron IPC |
| [`stoerungen.service.ts`](#stoerungen-service) | Störungsmanagement | CRUD-Operationen, Status-Updates | `api.service` |
| [`moduleAccess.service.ts`](#module-access-service) | Modul-Berechtigungen | Role-basierte Navigation | Keine |
| **Business Services** |
| [`production.business.service.ts`](#production-business-service) | Produktions-Management | KPI-Dashboard, Maintenance-Prediction | `base.service`, Repositories |
| [`warehouse-optimization.service.ts`](#warehouse-optimization-service) | Lager-Optimierung | Route-Optimization, Layout-Analysis | `base.service`, OpenRouter |
| [`predictive-analytics.service.ts`](#predictive-analytics-service) | KI-Analytics | Anomaly-Detection, Forecasting | `base.service`, KPI-Repository |
| **Specialized Services** |
| [`bereitschafts-assignment.service.ts`](#bereitschafts-assignment-service) | Bereitschafts-Zuweisung | Auto-Assignment, Email-Templates | `bereitschaftsService` |
| [`kpi-repository-integration.service.ts`](#kpi-repository-integration-service) | KPI-Integration | Data-Aggregation, Repository-Sync | Repository Layer |
| **API Sub-Layer** |
| [`api/backend-api.service.ts`](#backend-api-service) | Backend-Client | Direct Backend Communication | HTTP Client |
| [`api/database-api.service.ts`](#database-api-service) | Datenbank-API | Database-specific Operations | Backend-API |

## 📁 Dateistruktur

```
src/services/
├── 📄 base.service.ts              # Service-Basis-Klasse mit gemeinsamen Funktionen
├── 📄 api.service.ts               # Haupt-API-Service für Backend-Kommunikation
├── 📄 auth.service.ts              # Authentifizierungs-Service (JWT, Login/Logout)
├── 📄 user.service.ts              # Benutzer-Management Service
├── 📄 database.service.ts          # Datenbank-Abstraktion für Frontend
├── 📄 cache.service.ts             # In-Memory Cache mit TTL und LRU-Eviction
├── 📄 api-config.service.ts        # API-Konfiguration und Environment-Management
├── 📄 stoerungen.service.ts        # Störungsmanagement und -verarbeitung
├── 📄 moduleAccess.service.ts      # Modul-Zugriff und Rollen-Management
├── 📄 bereitschafts-assignment.service.ts  # Bereitschafts-Zuweisung
├── 📄 bereitschaftsService.ts      # Bereitschafts-Verwaltung
├── 📄 kpi-repository-integration.service.ts # KPI-Daten-Integration
├── 📄 predictive-analytics.service.ts       # KI-basierte Predictive Analytics
├── 📄 runbookService.ts           # Runbook-Management für Workflows
├── 📄 stoerung-kategorien.service.ts       # Störungskategorie-Verwaltung
├── 📄 warehouse-optimization.service.ts    # KI-Lager-Optimierung
├── 📄 workflowService.ts          # Workflow-Automatisierung
├── 📁 api/                        # API-spezifische Services
│   ├── 📄 backend-api.service.ts  # Direkter Backend-API-Client
│   └── 📄 database-api.service.ts # Datenbank-spezifische API-Calls
└── 📁 business/                   # Business-Logic Services
    ├── 📄 production.business.service.ts   # Produktions-Management
    ├── 📄 system.business.service.ts       # System-Business-Logic
    └── 📄 warehouse.business.service.ts    # Lager-Business-Logic
```

## 🔧 Detaillierte Service-Beschreibungen

### Base Service

**Datei**: `base.service.ts`

Stellt die Basis-Funktionalität für alle Service-Implementierungen bereit. Implementiert Common Patterns wie Health-Checking, Retry-Logic und strukturiertes Logging.

#### Öffentliche API

```typescript
export abstract class BaseService implements IService {
  abstract readonly serviceName: string;
  
  // Lifecycle Management
  async initialize(config?: ServiceConfig): Promise<void>
  async healthCheck(): Promise<ServiceStatus>
  async destroy(): Promise<void>
  
  // Utility Methods
  protected async withRetry<T>(operation: () => Promise<T>, maxAttempts?: number): Promise<T>
  protected async withTimeout<T>(operation: () => Promise<T>, timeoutMs?: number): Promise<T>
  protected log(message: string, data?: any): void
  protected ensureInitialized(): void
}

export interface ServiceConfig {
  enableCaching?: boolean;
  enableLogging?: boolean; 
  retryAttempts?: number;
  timeout?: number;
}
```

#### Parameter & Rückgabewerte

| Methode | Parameter | Rückgabe | Beschreibung |
|---------|-----------|----------|--------------|
| `initialize` | `config?: ServiceConfig` | `Promise<void>` | Service initialisieren |
| `healthCheck` | Keine | `Promise<ServiceStatus>` | Service-Gesundheit prüfen |
| `withRetry` | `operation: () => Promise<T>`, `maxAttempts?: number` | `Promise<T>` | Operation mit Retry-Logic |
| `withTimeout` | `operation: () => Promise<T>`, `timeoutMs?: number` | `Promise<T>` | Operation mit Timeout |

#### Verwendungsbeispiel

```typescript path=null start=null
import { BaseService, ServiceConfig } from '@/services/base.service';

class MyCustomService extends BaseService {
  readonly serviceName = 'MyCustomService';
  
  protected async onInitialize(): Promise<void> {
    // Service-spezifische Initialisierung
    this.log('MyCustomService initialisiert');
  }
  
  async doSomething(): Promise<string> {
    this.ensureInitialized();
    
    return await this.withRetry(async () => {
      // Operation mit automatischer Wiederholung
      return 'Erfolg';
    });
  }
}

// Verwendung
const service = new MyCustomService({ 
  enableLogging: true, 
  retryAttempts: 5 
});
await service.initialize();
```

### API Service

**Datei**: `api.service.ts`

Zentraler Service für die Kommunikation mit Backend-APIs. Bietet typisierte HTTP-Methoden, automatisches Caching, Retry-Logic und Authentifizierung.

#### Öffentliche API

```typescript
export class ApiService {
  // Generic HTTP Methods
  async get<T>(endpoint: string): Promise<T>
  async post<T>(endpoint: string, data?: any): Promise<T>
  async put<T>(endpoint: string, data?: any): Promise<T>
  async delete<T>(endpoint: string): Promise<T>
  
  // Domain-specific Methods
  async getServiceLevelData(startDate?: string, endDate?: string): Promise<ServiceLevelDataPoint[]>
  async getPickingData(startDate?: string, endDate?: string): Promise<PickingDataPoint[]>
  async getTagesleistungData(startDate?: string, endDate?: string): Promise<TagesleistungDataPoint[]>
  async getSystemStats(startDate?: string, endDate?: string): Promise<any>
  
  // Cache Management
  invalidateCache(dataType: string): void
  invalidateAllCache(): void
  getCacheStats(): unknown
  
  // Configuration
  getBaseUrl(): string
}
```

#### Cache-Strategien

Der API Service implementiert intelligente Cache-Strategien:

- **Statische Daten**: 30 Minuten TTL (Kategorien, Konfiguration)
- **Dynamische Daten**: 5 Minuten TTL (KPIs, Metriken)
- **Echtzeit Daten**: 1 Minute TTL (System-Status, Alerts)

#### Verwendungsbeispiel

```typescript path=null start=null
import apiService from '@/services/api.service';

// Einfacher GET-Request
const data = await apiService.get<MyDataType[]>('/my-endpoint');

// Mit Datumsfiltern
const serviceLevel = await apiService.getServiceLevelData('2024-01-01', '2024-01-31');

// Cache invalidieren
apiService.invalidateCache('service-level');

// POST-Request mit Daten
const result = await apiService.post('/create-item', {
  name: 'Test Item',
  value: 42
});
```

### Auth Service

**Datei**: `auth.service.ts`

Verwaltet Benutzerauthentifizierung, JWT-Token-Handling und Session-Management mit automatischer Token-Refresh-Funktionalität.

#### Öffentliche API

```typescript
export class AuthService {
  // Authentication
  async login(data: LoginRequest): Promise<LoginResponse>
  async register(data: RegisterRequest): Promise<RegisterResponse>
  async refreshToken(): Promise<LoginResponse>
  logout(): void
  
  // Token Management
  getToken(): string | null
  isLoggedIn(): boolean
  isTokenValid(): boolean
  
  // User Data
  getUser(): any | null
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;
    user: UserData;
  };
}
```

#### Sicherheitsfeatures

- 🔒 **JWT-Token-Validation**: Automatische Überprüfung der Token-Gültigkeit
- 🔄 **Auto-Refresh**: Transparente Token-Erneuerung vor Ablauf
- 🛡️ **Secure Storage**: Sichere Speicherung in localStorage mit Expiry-Check
- 🚪 **Auto-Logout**: Automatisches Logout bei Token-Problemen

#### Verwendungsbeispiel

```typescript path=null start=null
import { authService } from '@/services/auth.service';

// Login
const loginResult = await authService.login({
  username: '<EMAIL>',
  password: 'sicheresPasswort'
});

if (loginResult.success) {
  console.log('Anmeldung erfolgreich');
  const user = authService.getUser();
  console.log('Angemeldeter Benutzer:', user.username);
}

// Status prüfen
if (authService.isLoggedIn()) {
  // Benutzer ist angemeldet
  if (!authService.isTokenValid()) {
    // Token läuft bald ab - automatisch erneuern
    await authService.refreshToken();
  }
}

// Logout
authService.logout();
```

### Cache Service

**Datei**: `cache.service.ts`

Implementiert einen effizienten In-Memory-Cache mit TTL-basierter Invalidierung, LRU-Eviction und umfassender Statistik-Erfassung.

#### Öffentliche API

```typescript
export class CacheService {
  // Core Operations
  get<T>(key: string): T | undefined
  set<T>(key: string, data: T, ttl?: number): void
  delete(key: string): boolean
  deleteByPrefix(prefix: string): number
  clear(): void
  
  // Maintenance
  cleanup(): number
  destroy(): void
  
  // Statistics
  getStats(): CacheStats
}

export interface CacheStats {
  hits: number;
  misses: number;
  totalRequests: number;
  hitRate: number;
  memoryUsage: number;
  entryCount: number;
}
```

#### Performance-Features

- ⚡ **LRU-Eviction**: Automatisches Entfernen selten genutzter Einträge
- 📊 **Statistik-Tracking**: Detaillierte Cache-Performance-Metriken
- 🧹 **Auto-Cleanup**: Periodische Bereinigung abgelaufener Einträge
- 💾 **Memory-Monitoring**: Überwachung der Speichernutzung

#### Verwendungsbeispiel

```typescript path=null start=null
import { getCache, CacheKeyGenerator } from '@/services/cache.service';

// Cache-Instanz abrufen
const cache = getCache({
  defaultTTL: 5 * 60 * 1000, // 5 Minuten
  maxEntries: 1000,
  enableLogging: true
});

// Daten cachen
const cacheKey = CacheKeyGenerator.forDataType('user-data', { userId: 123 });
cache.set(cacheKey, userData, 10 * 60 * 1000); // 10 Minuten TTL

// Daten abrufen
const cachedData = cache.get<UserData>(cacheKey);
if (cachedData) {
  console.log('Cache Hit:', cachedData);
} else {
  console.log('Cache Miss - Daten vom Backend laden');
}

// Cache-Statistiken
const stats = cache.getStats();
console.log(`Cache Hit Rate: ${stats.hitRate}%`);
console.log(`Memory Usage: ${stats.memoryUsage} Bytes`);
```

### Production Business Service

**Datei**: `business/production.business.service.ts`

Implementiert komplexe Business-Logic für Produktionsmanagement, einschließlich KPI-Dashboard, Predictive Maintenance und Optimierungs-Empfehlungen.

#### Öffentliche API

```typescript
export class ProductionBusinessService extends BaseService {
  // Dashboard & Monitoring
  async getProductionDashboard(filter?: DateRangeFilter): Promise<ProductionDashboard>
  async getMachineEfficiencyAnalysis(): Promise<MachineEfficiencyAnalysis>
  
  // Predictive Maintenance
  async getMaintenanceRecommendations(): Promise<MaintenanceRecommendation[]>
  
  // Optimization
  async getProductionOptimizations(): Promise<OptimizationRecommendation[]>
  
  // Cache Management
  async invalidateCache(): Promise<void>
}

export interface ProductionDashboard {
  overview: ProductionOverview;
  machines: MachinePerformanceMetrics[];
  cutting: CuttingMetrics;
  alerts: Alert[];
  kpis: ProductionKPIs;
}
```

#### KPI-Berechnung

Der Service berechnet wichtige Produktions-KPIs:

- **OEE (Overall Equipment Effectiveness)**: `Availability × Performance × Quality`
- **MTTR (Mean Time To Repair)**: Durchschnittliche Reparaturzeit
- **Effizienz-Trends**: Maschinelle Leistungstrends über Zeit
- **Produktivitäts-Scores**: Normierte Leistungsbewertung

#### Verwendungsbeispiel

```typescript path=null start=null
import { ProductionBusinessService } from '@/services/business/production.business.service';

const productionService = new ProductionBusinessService();
await productionService.initialize();

// Dashboard abrufen
const dashboard = await productionService.getProductionDashboard({
  startDate: '2024-01-01',
  endDate: '2024-01-31'
});

console.log(`Gesamteffizienz: ${dashboard.overview.efficiency}%`);
console.log(`Aktive Linien: ${dashboard.overview.activeLines}`);
console.log(`OEE: ${dashboard.kpis.oee}%`);

// Wartungsempfehlungen
const maintenance = await productionService.getMaintenanceRecommendations();
const urgentMaintenance = maintenance.filter(m => m.priority === 'urgent');

console.log(`${urgentMaintenance.length} dringende Wartungen erforderlich`);

// Optimierungsvorschläge
const optimizations = await productionService.getProductionOptimizations();
const highImpact = optimizations.filter(o => o.impact === 'high');

for (const opt of highImpact) {
  console.log(`Optimierung: ${opt.title} (ROI: ${opt.roi}%)`);
}
```

## 🔗 Abhängigkeiten & Verbindungen

### Service-Abhängigkeitsdiagramm

```mermaid
graph TB
    BaseService[Base Service] --> AuthService[Auth Service]
    BaseService --> APIService[API Service]
    BaseService --> BusinessServices[Business Services]
    
    APIService --> CacheService[Cache Service]
    APIService --> ConfigService[API Config Service]
    
    AuthService --> APIService
    UserService --> APIService
    
    DatabaseService --> BackendAPIService[Backend API Service]
    
    BusinessServices --> APIService
    BusinessServices --> Repositories[Repository Layer]
    
    PredictiveAnalytics --> KPIIntegration[KPI Integration]
    WarehouseOptimization --> OpenRouter[OpenRouter Service]
    
    StoerungenService --> APIService
    BereitschaftsService --> StoerungenService
```

### Externe Abhängigkeiten

| Service | NPM Pakete | Beschreibung |
|---------|------------|--------------|
| `api.service.ts` | `fetch`, Standard Web API | HTTP-Client für API-Calls |
| `auth.service.ts` | Standard Web API | JWT-Token-Handling, localStorage |
| `cache.service.ts` | Keine externen | Pure TypeScript Implementation |
| `predictive-analytics.service.ts` | Statistik-Libraries | Für Anomaly-Detection und Forecasting |
| `warehouse-optimization.service.ts` | Graph-Algorithmen | Route-Optimization-Implementierung |

### Interne Abhängigkeiten

```typescript path=null start=null
// Typische Import-Struktur
import { BaseService, ServiceConfig } from './base.service';
import apiService from './api.service';
import { getCache, CacheKeyGenerator } from './cache.service';
import { authService } from './auth.service';

// Repository Layer Integration
import { repositories } from '@/repositories';

// Type Definitions
import { KPIDataPoint, ProductionMetrics } from '@/types/production';
import { UserRole, ModuleConfig } from '@/types/module';
```

## 💡 Verwendungsbeispiele

### API-Service mit Caching

```typescript path=null start=null
import apiService, { cacheManager } from '@/services/api.service';

// Daten mit automatischem Caching laden
const loadDashboardData = async (dateRange: DateRange) => {
  try {
    // Mehrere API-Calls parallel ausführen
    const [serviceLevel, picking, production] = await Promise.all([
      apiService.getServiceLevelData(dateRange.start, dateRange.end),
      apiService.getPickingData(dateRange.start, dateRange.end),
      apiService.getTagesleistungData(dateRange.start, dateRange.end)
    ]);
    
    return {
      serviceLevel,
      picking,
      production,
      lastUpdated: new Date()
    };
  } catch (error) {
    console.error('Fehler beim Laden der Dashboard-Daten:', error);
    
    // Fallback: Cache-Invalidierung und Retry
    cacheManager.invalidateAll();
    throw error;
  }
};

// Cache-Statistiken überwachen
const monitorCachePerformance = () => {
  const stats = cacheManager.getStats();
  
  if (stats.hitRate < 0.7) {
    console.warn(`Cache Hit Rate niedrig: ${stats.hitRate * 100}%`);
  }
  
  console.log(`Cache Entries: ${stats.entryCount}`);
  console.log(`Memory Usage: ${(stats.memoryUsage / 1024 / 1024).toFixed(2)} MB`);
};
```

### Authentifizierung mit Auto-Refresh

```typescript path=null start=null
import { authService, setAuthErrorHandler } from '@/services/auth.service';
import { router } from '@/router';

// Auth Error Handler für automatisches Logout
setAuthErrorHandler(() => {
  console.log('Session expired - redirecting to login');
  router.push('/login');
});

// Protected API Call mit automatischem Token-Refresh
const makeAuthenticatedRequest = async <T>(apiCall: () => Promise<T>): Promise<T> => {
  // Token-Gültigkeit prüfen
  if (!authService.isTokenValid()) {
    try {
      console.log('Token läuft ab - erneuere automatisch');
      await authService.refreshToken();
    } catch (refreshError) {
      console.error('Token-Refresh fehlgeschlagen:', refreshError);
      authService.logout();
      throw new Error('Authentifizierung erforderlich');
    }
  }
  
  // Original API-Call ausführen
  return await apiCall();
};

// Verwendung in einer Vue-Komponente
export default {
  async mounted() {
    if (!authService.isLoggedIn()) {
      this.$router.push('/login');
      return;
    }
    
    try {
      this.userData = await makeAuthenticatedRequest(() =>
        userService.getProfile()
      );
    } catch (error) {
      this.showError('Profil konnte nicht geladen werden');
    }
  }
};
```

### Business Service Integration

```typescript path=null start=null
import { ProductionBusinessService } from '@/services/business/production.business.service';
import { PredictiveAnalyticsService } from '@/services/predictive-analytics.service';

// Service-Integration für komplexe Business-Logic
const createProductionAnalytics = async () => {
  const productionService = new ProductionBusinessService({
    enableLogging: true,
    enableCaching: true,
    retryAttempts: 3
  });
  
  const analyticsService = new PredictiveAnalyticsService({
    enableLogging: true
  });
  
  // Services initialisieren
  await Promise.all([
    productionService.initialize(),
    analyticsService.initialize()
  ]);
  
  return {
    // Dashboard mit Predictive Insights
    async getEnhancedDashboard(dateRange?: DateRange) {
      const [dashboard, kpiForecasts] = await Promise.all([
        productionService.getProductionDashboard(dateRange),
        analyticsService.monitorKPIs(['efficiency', 'throughput', 'quality'])
      ]);
      
      // Predictive Alerts hinzufügen
      const predictiveAlerts = kpiForecasts
        .filter(kpi => kpi.anomalies?.length > 0)
        .map(kpi => ({
          type: 'prediction',
          message: `Anomalie in ${kpi.kpi.name} vorhergesagt`,
          prediction: kpi.forecast
        }));
      
      return {
        ...dashboard,
        alerts: [...dashboard.alerts, ...predictiveAlerts],
        predictions: kpiForecasts
      };
    },
    
    // Optimierungs-Pipeline
    async runOptimizationPipeline() {
      const [maintenance, optimizations, efficiency] = await Promise.all([
        productionService.getMaintenanceRecommendations(),
        productionService.getProductionOptimizations(),
        productionService.getMachineEfficiencyAnalysis()
      ]);
      
      // Priorisierte Action Items generieren
      const actionItems = [
        ...maintenance.filter(m => m.priority === 'urgent'),
        ...optimizations.filter(o => o.impact === 'high' && o.roi > 25)
      ].sort((a, b) => {
        // Sortierung nach Criticality und ROI
        if (a.priority === 'urgent') return -1;
        if (b.priority === 'urgent') return 1;
        return (b.roi || 0) - (a.roi || 0);
      });
      
      return {
        actionItems,
        efficiency,
        potentialSavings: optimizations.reduce((sum, opt) => sum + opt.roi, 0)
      };
    }
  };
};
```

## 📊 Datenmodelle & Schnittstellen

### Core Interfaces

```typescript path=null start=null
// Service Configuration
export interface ServiceConfig {
  enableCaching?: boolean;
  enableLogging?: boolean;
  retryAttempts?: number;
  timeout?: number;
}

// Service Status
export interface ServiceStatus {
  isInitialized: boolean;
  isHealthy: boolean;
  lastError?: Error;
  lastChecked: Date;
}

// API Response Wrapper
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Date Range Filter
export interface DateRangeFilter {
  startDate?: string;
  endDate?: string;
}
```

### Business Domain Models

```typescript path=null start=null
// Production Domain
export interface ProductionDashboard {
  overview: {
    totalProduction: number;
    dailyAverage: number;
    efficiency: number;
    activeLines: number;
  };
  machines: MachinePerformanceMetrics[];
  cutting: CuttingMetrics;
  alerts: Alert[];
  kpis: {
    oee: number;
    availability: number;
    performance: number;
    quality: number;
  };
}

export interface MachinePerformanceMetrics {
  machineId: string;
  efficiency: number;
  uptime: number;
  throughput: number;
  qualityRate: number;
  trend: 'improving' | 'declining' | 'stable';
  status: 'operational' | 'maintenance' | 'down';
}

// Störungsmanagement
export interface Stoerung {
  id: number;
  title: string;
  description?: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status: 'NEW' | 'IN_PROGRESS' | 'RESOLVED';
  category?: string;
  affected_system?: string;
  location?: string;
  reported_by?: string;
  assigned_to?: string;
  created_at: string;
  updated_at: string;
  tags: string[];
}

// Warehouse Optimization
export interface WarehouseItem {
  id: string;
  name: string;
  category: string;
  accessFrequency: number;
  currentLocation: StorageLocation;
  dimensions: ItemDimensions;
  weight: number;
}

export interface OptimalPlacement {
  itemId: string;
  currentLocation: StorageLocation;
  recommendedLocation: StorageLocation;
  reason: string;
  expectedBenefit: number;
  confidence: number;
}
```

### Cache-Strukturen

```typescript path=null start=null
// Cache Key Generation
export class CacheKeyGenerator {
  static forDataType(dataType: string, filters?: Record<string, any>): string {
    const baseKey = `data:${dataType}`;
    
    if (!filters || Object.keys(filters).length === 0) {
      return baseKey;
    }
    
    const sortedParams = Object.keys(filters)
      .sort()
      .map(key => `${key}=${filters[key]}`)
      .join('&');
    
    return `${baseKey}:${sortedParams}`;
  }
  
  static prefixFor(category: 'api' | 'data' | 'user'): string {
    return `${category}:`;
  }
}

// Cache Entry Structure  
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}
```

## 🧪 Testing & Qualitätssicherung

### Unit Testing Strategie

Jeder Service sollte umfassende Unit Tests haben:

```typescript path=null start=null
// Beispiel: auth.service.test.ts
import { AuthService } from '@/services/auth.service';
import { vi, describe, it, expect, beforeEach } from 'vitest';

describe('AuthService', () => {
  let authService: AuthService;
  
  beforeEach(() => {
    authService = new AuthService();
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn()
      }
    });
  });
  
  describe('login', () => {
    it('should successfully log in with valid credentials', async () => {
      // Arrange
      global.fetch = vi.fn().mockResolvedValue({
        json: () => Promise.resolve({
          success: true,
          data: {
            token: 'valid-jwt-token',
            user: { id: 1, username: 'testuser' }
          }
        })
      });
      
      // Act
      const result = await authService.login({
        username: 'testuser',
        password: 'password123'
      });
      
      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.token).toBe('valid-jwt-token');
      expect(localStorage.setItem).toHaveBeenCalledWith('auth_token', 'valid-jwt-token');
    });
    
    it('should handle network errors gracefully', async () => {
      // Arrange
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));
      
      // Act
      const result = await authService.login({
        username: 'testuser',
        password: 'password123'
      });
      
      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('NETWORK_ERROR');
    });
  });
});
```

### Integration Testing

```typescript path=null start=null
// Beispiel: api-integration.test.ts
import { ApiService } from '@/services/api.service';
import { CacheService } from '@/services/cache.service';

describe('API Service Integration', () => {
  let apiService: ApiService;
  
  beforeAll(async () => {
    // Setup test environment mit Backend-Mock
    await setupTestBackend();
    apiService = new ApiService();
  });
  
  it('should cache responses correctly', async () => {
    // First call - should hit backend
    const data1 = await apiService.getServiceLevelData();
    expect(mockBackendCalls).toHaveBeenCalledTimes(1);
    
    // Second call - should hit cache
    const data2 = await apiService.getServiceLevelData();
    expect(mockBackendCalls).toHaveBeenCalledTimes(1);
    expect(data1).toEqual(data2);
  });
  
  it('should handle cache invalidation', async () => {
    await apiService.getServiceLevelData();
    
    // Invalidate cache
    apiService.invalidateCache('service-level');
    
    // Next call should hit backend again
    await apiService.getServiceLevelData();
    expect(mockBackendCalls).toHaveBeenCalledTimes(2);
  });
});
```

### Performance Testing

```typescript path=null start=null
// Performance Benchmarks
describe('Service Performance', () => {
  it('should handle concurrent requests efficiently', async () => {
    const startTime = Date.now();
    
    // 100 concurrent API calls
    const requests = Array.from({ length: 100 }, () => 
      apiService.getSystemStats()
    );
    
    await Promise.all(requests);
    
    const duration = Date.now() - startTime;
    expect(duration).toBeLessThan(5000); // Max 5 seconds
  });
  
  it('should maintain cache performance under load', async () => {
    const cache = new CacheService();
    
    // Fill cache with 10,000 entries
    for (let i = 0; i < 10000; i++) {
      cache.set(`key_${i}`, { data: `value_${i}` });
    }
    
    // Measure access time
    const startTime = Date.now();
    cache.get('key_5000');
    const accessTime = Date.now() - startTime;
    
    expect(accessTime).toBeLessThan(1); // < 1ms
  });
});
```

## 🛠 Entwicklungshinweise

### Best Practices

#### 1. Service-Implementierung

```typescript path=null start=null
// ✅ Gut: Service erweitert BaseService
export class MyService extends BaseService {
  readonly serviceName = 'MyService';
  
  protected async onInitialize(): Promise<void> {
    // Initialization logic
  }
  
  async doSomething(): Promise<Result> {
    this.ensureInitialized();
    
    return await this.withRetry(async () => {
      // Business logic with automatic retry
    });
  }
}

// ❌ Schlecht: Keine Basis-Funktionalität
export class BadService {
  doSomething() {
    // No error handling, logging, or retry logic
  }
}
```

#### 2. Error Handling

```typescript path=null start=null
// ✅ Gut: Strukturierte Fehlerbehandlung
async getSomeData(): Promise<DataType[]> {
  try {
    const data = await this.apiCall();
    return data;
  } catch (error) {
    this.log('Error fetching data', error);
    
    if (error instanceof AuthenticationError) {
      // Handle auth errors specifically
      this.handleAuthError();
    }
    
    throw new ServiceError(
      'Failed to fetch data',
      'DATA_FETCH_ERROR',
      error
    );
  }
}

// ❌ Schlecht: Unbehandelte Fehler
async getBadData() {
  return await this.apiCall(); // Kann unhandled exceptions werfen
}
```

#### 3. Caching-Strategien

```typescript path=null start=null
// ✅ Gut: Intelligentes Caching
async getDataWithCaching(params: Params): Promise<Data> {
  const cacheKey = CacheKeyGenerator.forDataType('my-data', params);
  
  return await cachedApiCall(
    cacheKey,
    () => this.fetchFromApi(params),
    this.getCacheTTL(params) // Dynamic TTL based on data type
  );
}

private getCacheTTL(params: Params): number {
  if (params.realTime) return 30 * 1000; // 30 seconds
  if (params.dynamic) return 5 * 60 * 1000; // 5 minutes
  return 30 * 60 * 1000; // 30 minutes for static data
}

// ❌ Schlecht: Statisches Caching ohne Kontext
async getData() {
  return cache.get('data') || await this.fetch();
}
```

### Code-Qualität & Standards

#### TypeScript-Konfiguration

```typescript path=null start=null
// tsconfig.json für Services
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  },
  "include": ["src/services/**/*"]
}
```

#### ESLint-Regeln für Services

```javascript path=null start=null
// .eslintrc.js - Service-spezifische Regeln
module.exports = {
  rules: {
    // Enforce async/await over Promises
    'prefer-async-await': 'error',
    
    // Ensure error handling
    'no-unhandled-promise': 'error',
    
    // Require explicit return types for public methods
    '@typescript-eslint/explicit-function-return-type': [
      'error', 
      { 'allowExpressions': true }
    ],
    
    // Enforce naming conventions
    '@typescript-eslint/naming-convention': [
      'error',
      {
        'selector': 'class',
        'format': ['PascalCase'],
        'suffix': ['Service']
      }
    ]
  }
};
```

### Häufige Fehlerquellen

#### 1. Service-Initialisierung vergessen

```typescript path=null start=null
// ❌ Problem
const service = new MyService();
await service.doSomething(); // Error: Service not initialized

// ✅ Lösung  
const service = new MyService();
await service.initialize();
await service.doSomething();
```

#### 2. Race Conditions bei API-Calls

```typescript path=null start=null
// ❌ Problem: Concurrent calls ohne Synchronisation
async loadData() {
  this.loading = true;
  const data1 = this.api.getData1();
  const data2 = this.api.getData2(); // Race condition possible
  this.loading = false;
}

// ✅ Lösung: Promise.all für parallele Calls
async loadData() {
  this.loading = true;
  try {
    const [data1, data2] = await Promise.all([
      this.api.getData1(),
      this.api.getData2()
    ]);
    return { data1, data2 };
  } finally {
    this.loading = false;
  }
}
```

#### 3. Memory Leaks bei Service-Subscriptions

```typescript path=null start=null
// ❌ Problem: Subscriptions nicht cleaned up
export class ComponentWithService {
  private subscription?: NodeJS.Timeout;
  
  mounted() {
    this.subscription = setInterval(() => {
      this.updateData();
    }, 1000);
  }
  
  // Missing: cleanup in unmounted()
}

// ✅ Lösung: Proper cleanup
export class ComponentWithService {
  private subscription?: NodeJS.Timeout;
  
  mounted() {
    this.subscription = setInterval(() => {
      this.updateData();
    }, 1000);
  }
  
  unmounted() {
    if (this.subscription) {
      clearInterval(this.subscription);
    }
  }
}
```

### TODOs & Verbesserungspotentiale

#### Kurzfristig (Sprint-Ziele)

- [ ] **OpenAPI-Integration**: Automatische Client-Generierung aus Backend-Schema
- [ ] **Service Worker**: Offline-Funktionalität für kritische Services
- [ ] **Metrics Dashboard**: Monitoring von Service-Performance und Cache-Hit-Rates
- [ ] **Error Boundaries**: React/Vue Error Boundaries für Service-Fehler

#### Mittelfristig (Release-Ziele)

- [ ] **GraphQL-Integration**: Migration von REST zu GraphQL für effizientere Datenabfragen
- [ ] **Real-time Updates**: WebSocket-Integration für Live-Daten
- [ ] **Advanced Caching**: Redis-Integration für verteilte Cache-Strategien
- [ ] **Service Mesh**: Istio-Integration für Microservice-Kommunikation

#### Langfristig (Architektur-Evolution)

- [ ] **Event Sourcing**: Event-basierte Architektur für Audit und Replay
- [ ] **CQRS**: Command Query Responsibility Segregation für bessere Performance
- [ ] **Microservice-Migration**: Aufteilung monolithischer Services
- [ ] **AI-Integration**: Maschinelles Lernen für intelligente Service-Optimierung

---

**Entwickelt für:** Leitstand-Anwendung  
**Version:** 1.0.0  
**Letzte Aktualisierung:** 2024-01-09  
**Maintainer:** Development Team

> 💡 **Hinweis**: Diese Dokumentation wird kontinuierlich aktualisiert. Bei Fragen oder Verbesserungsvorschlägen bitte ein Issue erstellen oder direkt mit dem Development Team Kontakt aufnehmen.
