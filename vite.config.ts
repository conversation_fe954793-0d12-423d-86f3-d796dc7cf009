import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

export default defineConfig({
  // Wichtig für Electron: relative Asset-URLs statt "/assets/..." unter file://
  base: "./",
  plugins: [
    react({
      // Standard-Konfiguration für React 18.2.0
      jsxRuntime: "automatic",
      babel: {
        plugins: ["@emotion/babel-plugin"],
      },
    }),
  ],
  server: {
    headers: {
      // Content Security Policy für Entwicklung erweitern
      "Content-Security-Policy": [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: blob: file: http://localhost:3001 https://localhost:3001",
        "connect-src 'self' http://localhost:3001 https://localhost:3001 ws://localhost:3001 wss://localhost:3001",
        "font-src 'self' data:",
        "object-src 'none'",
        "media-src 'self' data: blob:",
        "frame-src 'none'",
        "base-uri 'self'",
        "form-action 'self'",
      ].join("; "),
    },
    port: 5173,
    strictPort: true, // Erlaubt automatisches Port-Hochzählen
    host: true,
    open: false, // Browser nicht automatisch öffnen - nur für Electron-App
    // Kein Proxy - Browser soll keine Backend-Verbindung haben
    // Nur Electron-App nutzt das integrierte Backend auf Port 3001
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      // Wichtige Polyfills für Electron
      path: "path-browserify",
      stream: "stream-browserify",
      util: "util",
      buffer: "buffer/",
    },
  },
  build: {
    outDir: "dist",
    emptyOutDir: true,
    sourcemap: true,
    rollupOptions: {
      external: ["better-sqlite3"],
    },
  },
  define: {
    "process.platform": JSON.stringify(process.platform),
    "process.env.NODE_ENV": JSON.stringify(
      process.env.NODE_ENV || "development",
    ),
  },
  optimizeDeps: {
    exclude: ["better-sqlite3"],
    include: ["react", "react-dom"],
  },
});
