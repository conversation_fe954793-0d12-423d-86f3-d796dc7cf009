/**
 * Export-Service für Excel und PowerPoint Export mit Chart-Unterstützung
 *
 * Dieser Service stellt Funktionen bereit, um Dashboard-Daten
 * sowohl als Excel-Datei als auch als PowerPoint-Präsentation zu exportieren.
 * PowerPoint-Export enthält sowohl echte Chart-Bilder als auch formatierte Tabellen.
 */

import * as XLSX from 'xlsx';
import PptxGenJS from 'pptxgenjs';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

// Type definitions for PptxGenJS table data
type TableRow = TableCell[];
type TableCell = string | { text: string; options?: any };

/**
 * Interface für Chart-Daten
 */
export interface ChartData {
  name: string;
  date?: Date;
  [key: string]: any;
}

/**
 * Interface für Export-Optionen
 */
export interface ExportOptions {
  dateRange?: DateRange;
  includeSummary?: boolean;
  fileName?: string;
  dashboardType?: 'dispatch' | 'cutting' | 'incoming' | 'machines' | 'aril' | 'atrl';
}
export class ExportService {
  /**
   * Exportiert Daten als Excel-Datei
   * @param chartData Array mit Chart-Daten von verschiedenen Charts
   * @param options Export-Optionen
   */
  static async exportToExcel(
    chartData: { name: string; data: ChartData[] }[],
    options: ExportOptions = {}
  ): Promise<void> {
    const {
      dateRange,
      includeSummary = true,
      fileName,
      dashboardType = 'cutting'
    } = options;

    // Erstelle Workbook
    const wb = XLSX.utils.book_new();

    // Date Range Info Sheet
    if (dateRange?.from && dateRange?.to) {
      const infoData = [
        ['Export Information'],
        ['Export Date', format(new Date(), 'dd.MM.yyyy HH:mm', { locale: de })],
        ['Date Range From', format(dateRange.from, 'dd.MM.yyyy', { locale: de })],
        ['Date Range To', format(dateRange.to, 'dd.MM.yyyy', { locale: de })],
        ['Total Days', Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24)) + 1],
        [''],
        ['Charts Included', chartData.length]
      ];

      const infoWs = XLSX.utils.aoa_to_sheet(infoData);
      XLSX.utils.book_append_sheet(wb, infoWs, 'Info');
    }

    // Füge Daten für jeden Chart hinzu
    chartData.forEach((chart, index) => {
      const { name, data } = chart;

      if (data.length === 0) return;

      // Bereite Daten für Excel vor
      const excelData = this.prepareDataForExcel(data, name);

      // Erstelle Worksheet
      const ws = XLSX.utils.aoa_to_sheet(excelData);

      // Setze Spaltenbreite
      const colWidths = this.calculateColumnWidths(excelData);
      ws['!cols'] = colWidths;

      // Füge Sheet hinzu
      XLSX.utils.book_append_sheet(wb, ws, this.sanitizeSheetName(name));
    });

    // Summary Sheet falls gewünscht
    if (includeSummary && chartData.length > 0) {
      const summaryData = this.createSummaryData(chartData, dashboardType);
      const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);
      const sheetName = this.getDashboardSummaryTitle(dashboardType);
      XLSX.utils.book_append_sheet(wb, summaryWs, this.sanitizeSheetName(sheetName));
    }

    // Generiere Dateiname basierend auf Dashboard-Typ
    const baseName = this.getDashboardFileName(dashboardType);
    const finalFileName = fileName || this.generateFileName(baseName, dateRange, 'xlsx');

    // Speichere Datei
    XLSX.writeFile(wb, finalFileName);
  }

  /**
   * Exportiert Daten als PowerPoint-Präsentation mit Tabellen
   * @param chartData Array mit Chart-Daten von verschiedenen Charts
   * @param options Export-Optionen
   */
  static async exportToPowerPoint(
    chartData: { name: string; data: ChartData[] }[],
    options: ExportOptions = {}
  ): Promise<void> {
    const {
      dateRange,
      includeSummary = true,
      fileName,
      dashboardType = 'cutting'
    } = options;

    try {
      // Erstelle neue Präsentation
      const pptx = new PptxGenJS();

      // Setze Layout-Eigenschaften
      pptx.defineLayout({ name: 'A4', width: 11.69, height: 8.27 });
      pptx.layout = 'A4';

      // Titel-Slide
      this.addTitleSlide(pptx, dateRange, dashboardType);

      // Chart-Slides ohne Bilder (nur Tabellen)
      for (const chart of chartData) {
        if (chart.data && chart.data.length > 0) {
          this.addChartSlideWithoutImage(pptx, chart.name, chart.data);
        }
      }

      // Summary-Slide falls gewünscht
      if (includeSummary && chartData.length > 0) {
        this.addSummarySlide(pptx, chartData, dashboardType);
      }

      // Generiere Dateiname basierend auf Dashboard-Typ
      const baseName = this.getDashboardFileName(dashboardType);
      const finalFileName = fileName || this.generateFileName(baseName, dateRange, 'pptx');

      // Speichere Präsentation mit besserer Fehlerbehandlung
      console.log('Starte PowerPoint Export...');
      await pptx.writeFile({ fileName: finalFileName });
      console.log('PowerPoint Export erfolgreich abgeschlossen');

    } catch (error) {
      console.error('PowerPoint Export Fehler:', error);
      throw new Error(`PowerPoint Export fehlgeschlagen: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`);
    }
  }

  /**
   * Fügt Chart-Slide ohne Bild (nur Tabelle) hinzu
   * @param pptx PowerPoint-Objekt
   * @param chartName Name des Charts
   * @param data Chart-Daten
   */
  private static addChartSlideWithoutImage(
    pptx: PptxGenJS,
    chartName: string,
    data: ChartData[]
  ): void {
    const slide = pptx.addSlide();

    // Chart-Titel
    slide.addText(chartName, {
      x: 0.5,
      y: 0.3,
      w: 10,
      h: 0.8,
      fontSize: 24,
      bold: true,
      color: '363636'
    });

    // Daten-Tabelle
    const tableData = this.prepareDataForPowerPoint(data, chartName);
    const tableRows = this.convertToTableRows(tableData) as any;
    slide.addTable(tableRows, {
      x: 0.5,
      y: 1.2,
      w: 10,
      h: 5.5,
      fontSize: 10,
      border: { pt: 1, color: 'CCCCCC' },
      fill: { color: 'F8F8F8' }
    });
  }

  /**
   * Bereitet Daten für Excel-Export vor
   * @param data Chart-Daten
   * @param chartName Name des Charts
   * @returns Formatierte Daten für Excel
   */
  private static prepareDataForExcel(data: ChartData[], chartName: string): any[][] {
    if (data.length === 0) return [];

    // Header-Zeile
    const headers = Object.keys(data[0]).filter(key => key !== 'date');
    const excelData: any[][] = [[`${chartName} - Export`], ['']];

    // Spalten-Header
    excelData.push(['Datum', ...headers.map(h => this.formatHeaderName(h))]);

    // Daten-Zeilen
    data.forEach(item => {
      const row = [
        item.date ? format(item.date, 'dd.MM.yyyy', { locale: de }) : item.name,
        ...headers.map(header => item[header] || 0)
      ];
      excelData.push(row);
    });

    // Zusammenfassung
    excelData.push(['']);
    excelData.push(['ZUSAMMENFASSUNG']);

    // Berechne Summen für numerische Spalten
    const numericHeaders = headers.filter(header =>
      data.every(item => typeof item[header] === 'number')
    );

    numericHeaders.forEach(header => {
      const total = data.reduce((sum, item) => sum + (item[header] || 0), 0);
      const avg = total / data.length;
      excelData.push([`${this.formatHeaderName(header)} Summe`, total]);
      excelData.push([`${this.formatHeaderName(header)} Durchschnitt`, Math.round(avg * 100) / 100]);
    });

    return excelData;
  }

  /**
   * Berechnet Spaltenbreiten für Excel
   * @param data Excel-Daten
   * @returns Spaltenbreiten
   */
  private static calculateColumnWidths(data: any[][]): { wch: number }[] {
    if (data.length === 0) return [];

    return data[0].map((_, colIndex) => {
      const maxLength = Math.max(
        ...data.map(row => String(row[colIndex] || '').length)
      );
      return { wch: Math.min(Math.max(maxLength + 2, 10), 50) };
    });
  }

  /**
   * Gibt den Summary-Titel basierend auf dem Dashboard-Typ zurück
   * @param dashboardType Typ des Dashboards
   * @returns Formatierter Summary-Titel
   */
  private static getDashboardSummaryTitle(dashboardType: 'dispatch' | 'cutting' | 'incoming' | 'machines' | 'aril' | 'atrl' = 'cutting'): string {
    switch (dashboardType) {
      case 'dispatch':
        return 'Dispatch Zusammenfassung';
      case 'incoming':
        return 'Wareneingang Zusammenfassung';
      case 'machines':
        return 'Maschinen Zusammenfassung';
      case 'aril':
        return 'ARiL Zusammenfassung';
      case 'atrl':
        return 'ATrL Zusammenfassung';
      case 'cutting':
      default:
        return 'Cutting Zusammenfassung';
    }
  }

  /**
   * Gibt den Dashboard-Titel basierend auf dem Dashboard-Typ zurück
   * @param dashboardType Typ des Dashboards
   * @returns Formatierter Dashboard-Titel
   */
  private static getDashboardTitle(dashboardType: 'dispatch' | 'cutting' | 'incoming' | 'machines' | 'aril' | 'atrl' = 'cutting'): string {
    switch (dashboardType) {
      case 'dispatch':
        return 'DISPATCH DASHBOARD';
      case 'incoming':
        return 'WARENEINGANG DASHBOARD';
      case 'machines':
        return 'MASCHINEN DASHBOARD';
      case 'aril':
        return 'ARIL DASHBOARD';
      case 'atrl':
        return 'ATRL DASHBOARD';
      case 'cutting':
      default:
        return 'CUTTING DASHBOARD';
    }
  }

  /**
   * Gibt den Summary-Slide-Titel basierend auf dem Dashboard-Typ zurück
   * @param dashboardType Typ des Dashboards
   * @returns Formatierter Summary-Slide-Titel
   */
  private static getDashboardSummarySlideTitle(dashboardType: 'dispatch' | 'cutting' | 'incoming' | 'machines' | 'aril' | 'atrl' = 'cutting'): string {
    switch (dashboardType) {
      case 'dispatch':
        return 'DISPATCH ZUSAMMENFASSUNG';
      case 'incoming':
        return 'WARENEINGANG ZUSAMMENFASSUNG';
      case 'machines':
        return 'MASCHINEN ZUSAMMENFASSUNG';
      case 'aril':
        return 'ARIL ZUSAMMENFASSUNG';
      case 'atrl':
        return 'ATRL ZUSAMMENFASSUNG';
      case 'cutting':
      default:
        return 'CUTTING ZUSAMMENFASSUNG';
    }
  }

  /**
   * Gibt den Dateinamen-Prefix basierend auf dem Dashboard-Typ zurück
   * @param dashboardType Typ des Dashboards
   * @returns Dateinamen-Prefix für Export-Dateien
   */
  private static getDashboardFileName(dashboardType: 'dispatch' | 'cutting' | 'incoming' | 'machines' | 'aril' | 'atrl' = 'cutting'): string {
    switch (dashboardType) {
      case 'dispatch':
        return 'Dispatch_Export';
      case 'incoming':
        return 'Wareneingang_Export';
      case 'machines':
        return 'Maschinen_Export';
      case 'aril':
        return 'ARiL_Export';
      case 'atrl':
        return 'ATrL_Export';
      case 'cutting':
      default:
        return 'Cutting_Dashboard';
    }
  }

  /**
   * Erstellt Zusammenfassungsdaten für Excel
   * @param chartData Array mit Chart-Daten
   * @param dashboardType Typ des Dashboards ('dispatch' | 'cutting' | 'incoming' | 'machines' | 'aril' | 'atrl')
   * @returns Zusammenfassungsdaten
   */
  private static createSummaryData(chartData: { name: string; data: ChartData[] }[], dashboardType: 'dispatch' | 'cutting' | 'incoming' | 'machines' | 'aril' | 'atrl' = 'cutting'): any[][] {
    const dashboardTitle = this.getDashboardTitle(dashboardType);
    const summary: any[][] = [
      [`${dashboardTitle} - ZUSAMMENFASSUNG`],
      [''],
      ['Chart Name', 'Data Points', 'Date Range', 'Total Records']
    ];

    chartData.forEach(chart => {
      const { name, data } = chart;
      const dateRange = data.length > 0 ?
        `${format(data[0].date || new Date(), 'dd.MM.', { locale: de })} - ${format(data[data.length - 1].date || new Date(), 'dd.MM.', { locale: de })}` :
        'No Data';

      summary.push([
        name,
        data.length,
        dateRange,
        data.length
      ]);
    });

    return summary;
  }

  /**
   * Fügt Titel-Slide zur PowerPoint-Präsentation hinzu
   * @param pptx PowerPoint-Objekt
   * @param dateRange Datumsbereich
   * @param dashboardType Typ des Dashboards ('dispatch' | 'cutting' | 'incoming' | 'machines' | 'aril' | 'atrl')
   */
  private static addTitleSlide(pptx: PptxGenJS, dateRange?: DateRange, dashboardType: 'dispatch' | 'cutting' | 'incoming' | 'machines' | 'aril' | 'atrl' = 'cutting'): void {
    const slide = pptx.addSlide();

    const dashboardTitle = this.getDashboardTitle(dashboardType);
    slide.addText(dashboardTitle, {
      x: 1,
      y: 1,
      w: 8,
      h: 1,
      fontSize: 32,
      bold: true,
      color: '363636'
    });

    slide.addText('Export Report mit Tabellen', {
      x: 1,
      y: 2.5,
      w: 8,
      h: 0.8,
      fontSize: 24,
      color: '666666'
    });

    if (dateRange?.from && dateRange?.to) {
      slide.addText(
        `Zeitraum: ${format(dateRange.from, 'dd.MM.yyyy', { locale: de })} - ${format(dateRange.to, 'dd.MM.yyyy', { locale: de })}`,
        {
          x: 1,
          y: 4,
          w: 8,
          h: 0.6,
          fontSize: 16,
          color: '888888'
        }
      );
    }

    slide.addText(`Exportiert am: ${format(new Date(), 'dd.MM.yyyy HH:mm', { locale: de })}`, {
      x: 1,
      y: 5,
      w: 8,
      h: 0.5,
      fontSize: 12,
      color: 'AAAAAA'
    });
  }

  /**
   * Fügt Zusammenfassungs-Slide zur PowerPoint-Präsentation hinzu
   * @param pptx PowerPoint-Objekt
   * @param chartData Array mit Chart-Daten
   * @param dashboardType Typ des Dashboards ('dispatch' | 'cutting' | 'incoming' | 'machines' | 'aril' | 'atrl')
   */
  private static addSummarySlide(pptx: PptxGenJS, chartData: { name: string; data: ChartData[] }[], dashboardType: 'dispatch' | 'cutting' | 'incoming' | 'machines' | 'aril' | 'atrl' = 'cutting'): void {
    const slide = pptx.addSlide();

    const summaryTitle = this.getDashboardSummarySlideTitle(dashboardType);
    slide.addText(summaryTitle, {
      x: 0.5,
      y: 0.5,
      w: 9,
      h: 0.8,
      fontSize: 24,
      bold: true,
      color: '363636'
    });

    const summaryData = [
      ['Chart', 'Datensätze', 'Zeitraum', 'Status']
    ];

    chartData.forEach(chart => {
      const { name, data } = chart;
      const status = data.length > 0 ? '✓' : '✗';
      const dateRange = data.length > 0 ?
        `${format(data[0].date || new Date(), 'dd.MM.', { locale: de })} - ${format(data[data.length - 1].date || new Date(), 'dd.MM.', { locale: de })}` :
        'Keine Daten';

      summaryData.push([name, data.length.toString(), dateRange, status]);
    });

    const tableRows = this.convertToTableRows(summaryData) as any;
    slide.addTable(tableRows, {
      x: 0.5,
      y: 1.5,
      w: 9,
      h: 5,
      fontSize: 10,
      border: { pt: 1, color: 'CCCCCC' },
      fill: { color: 'F8F8F8' }
    });
  }

  /**
   * Bereitet Daten für PowerPoint-Tabelle vor
   * @param data Chart-Daten
   * @param chartName Name des Charts
   * @returns Formatierte Daten für PowerPoint
   */
  private static prepareDataForPowerPoint(data: ChartData[], chartName: string): any[][] {
    if (data.length === 0) return [];

    const headers = Object.keys(data[0]).filter(key => key !== 'date');
    const tableData: any[][] = [['Datum', ...headers.map(h => this.formatHeaderName(h))]];

    // Limitiere auf 8 Zeilen für PowerPoint (um Platz für Chart-Bild zu lassen)
    const limitedData = data.slice(0, 8);

    limitedData.forEach(item => {
      const row = [
        item.date ? format(item.date, 'dd.MM.yyyy', { locale: de }) : item.name,
        ...headers.map(header => item[header] || 0)
      ];
      tableData.push(row);
    });

    if (data.length > 8) {
      tableData.push(['...', `... (${data.length - 8} weitere Zeilen)`, '', '']);
    }

    return tableData;
  }

  /**
   * Konvertiert String-Array zu TableRow-Format für PowerPoint
   * @param data String-Array-Daten
   * @returns TableRow-Array für PowerPoint
   */
  private static convertToTableRows(data: string[][]): TableRow[] {
    return data.map(row =>
      row.map(cell => ({
        text: String(cell),
        options: { fontSize: 10 }
      }))
    );
  }

  /**
   * Formatiert Header-Namen für bessere Lesbarkeit
   * @param header Header-Name
   * @returns Formatierter Header-Name
   */
  private static formatHeaderName(header: string): string {
    const formatMap: { [key: string]: string } = {
      'cutTT': 'Trommel-Trommel',
      'cutTR': 'Trommel-Ring',
      'cutRR': 'Ring-Ring',
      'pickCut': 'PickCut',
      'returns': 'Retouren',
      'efficiency': 'Effizienz',
      'lagerSumme': 'Lager → Ablaengerei',
      'cutLagerKSumme': 'Kunden Schnitte → Lager',
      'cutLagerRSumme': 'Rest Schnitte → Lager',
      'sumH1': 'Summe H1',
      'sumH3': 'Summe H3',
      'total': 'Gesamt',
      'weAtrl': 'WE Automatisch',
      'weManl': 'WE Manuell',
      'sollSchnitte': 'Soll-Schnitte',
      'tagesSchnitte': 'Tages-Schnitte',
      'istSchnitteProStunde': 'Ist-Schnitte/h',
      'effizienzProzent': 'Effizienz (%)',
      'maschinenTyp': 'Maschinen-Typ',
      // ARiL-spezifische Bezeichnungen
      'waTaPositionen': 'WaTa Positionen',
      'cuttingLagerKunde': 'Cutting Lager Kunde',
      'cuttingLagerRest': 'Cutting Lager Rest',
      'Umlagerungen': 'Umlagerungen',
      'lagerCutting': 'Lager Cutting',
      // ATrL-spezifische Bezeichnungen
      'EinlagerungAblKunde': 'Einlagerung Kunde',
      'EinlagerungAblRest': 'Einlagerung Rest',
      'umlagerungen': 'Umlagerungen',
      'AuslagerungAbl': 'Auslagerung'
    };

    return formatMap[header] || header.replace(/([A-Z])/g, ' $1').trim();
  }

  /**
   * Bereinigt Sheet-Namen für Excel (entfernt ungültige Zeichen)
   * @param name Sheet-Name
   * @returns Bereinigter Sheet-Name
   */
  private static sanitizeSheetName(name: string): string {
    return name
      .replace(/[\\\/*\[\]?:]/g, '')
      .substring(0, 31); // Excel-Limit für Sheet-Namen
  }

  /**
   * Generiert Dateiname basierend auf Typ und Datumsbereich
   * @param baseName Basis-Name
   * @param dateRange Datumsbereich
   * @param extension Datei-Extension
   * @returns Generierter Dateiname
   */
  private static generateFileName(baseName: string, dateRange?: DateRange, extension?: string): string {
    const timestamp = format(new Date(), 'yyyy-MM-dd_HH-mm', { locale: de });

    if (dateRange?.from && dateRange?.to) {
      const fromDate = format(dateRange.from, 'dd-MM-yyyy', { locale: de });
      const toDate = format(dateRange.to, 'dd-MM-yyyy', { locale: de });
      return `${baseName}_${fromDate}_${toDate}_${timestamp}.${extension}`;
    }

    return `${baseName}_${timestamp}.${extension}`;
  }
}

export default ExportService;