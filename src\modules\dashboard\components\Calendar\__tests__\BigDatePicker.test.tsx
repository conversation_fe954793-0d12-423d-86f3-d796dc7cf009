import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import BigDatePicker from '../BigDatePicker';

// Mock für Timer-Tests
vi.useFakeTimers();

describe('BigDatePicker', () => {
  const mockOnDateChange = vi.fn();
  const testDate = new Date(2025, 8, 3); // 3. September 2025

  beforeEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
  });

  it('rendert initial korrekt', () => {
    render(
      <BigDatePicker 
        initialDate={testDate}
        onDateChange={mockOnDateChange}
      />
    );

    // Überprüfen, ob das initiale Datum angezeigt wird
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('SEPTEMBER')).toBeInTheDocument();
    expect(screen.getByText('2025')).toBeInTheDocument();
  });

  it('öffnet und schließt den Kalender beim Klick auf den Toggle-Button', () => {
    render(
      <BigDatePicker 
        initialDate={testDate}
        onDateChange={mockOnDateChange}
      />
    );

    const toggleButton = screen.getByRole('button', { name: /kalender öffnen/i });
    
    // Kalender sollte initial geschlossen sein
    expect(screen.queryByText('Sep 2025')).not.toBeVisible();
    
    // Kalender öffnen
    fireEvent.click(toggleButton);
    expect(screen.getByText('Sep 2025')).toBeVisible();
    
    // Kalender schließen
    fireEvent.click(toggleButton);
    expect(screen.queryByText('Sep 2025')).not.toBeVisible();
  });

  it('markiert den ausgewählten Tag sofort beim Klick (ohne Delay)', async () => {
    render(
      <BigDatePicker 
        initialDate={testDate}
        onDateChange={mockOnDateChange}
      />
    );

    // Kalender öffnen
    const toggleButton = screen.getByRole('button', { name: /kalender öffnen/i });
    fireEvent.click(toggleButton);

    // Ursprünglich sollte Tag 3 markiert sein
    const day3Button = screen.getByRole('button', { name: '3' });
    expect(day3Button).toHaveClass('bg-[#ff7a05]', 'text-white');

    // Auf Tag 15 klicken
    const day15Button = screen.getByRole('button', { name: '15' });
    fireEvent.click(day15Button);

    // Sofortige Überprüfung: Tag 15 sollte SOFORT markiert sein (ohne 800ms warten)
    expect(day15Button).toHaveClass('bg-[#ff7a05]', 'text-white');
    expect(day3Button).not.toHaveClass('bg-[#ff7a05]', 'text-white');
    
    // onDateChange sollte aufgerufen worden sein
    expect(mockOnDateChange).toHaveBeenCalledTimes(1);
  });

  it('aktualisiert die rechte Datumsanzeige erst nach der Animation (800ms)', async () => {
    render(
      <BigDatePicker 
        initialDate={testDate}
        onDateChange={mockOnDateChange}
      />
    );

    // Kalender öffnen
    const toggleButton = screen.getByRole('button', { name: /kalender öffnen/i });
    fireEvent.click(toggleButton);

    // Auf Tag 15 klicken
    const day15Button = screen.getByRole('button', { name: '15' });
    fireEvent.click(day15Button);

    // Sofort nach dem Klick sollte die rechte Anzeige noch das alte Datum zeigen
    expect(screen.getAllByText('3')).toHaveLength(2); // Beide refs zeigen noch 3

    // Nach 800ms sollte die Animation abgeschlossen sein
    vi.advanceTimersByTime(800);
    
    await waitFor(() => {
      // Jetzt sollte die rechte Anzeige aktualisiert sein
      expect(screen.getAllByText('15')).toHaveLength(2); // Beide refs zeigen jetzt 15
    });
  });

  it('navigiert zwischen Monaten korrekt', () => {
    render(
      <BigDatePicker 
        initialDate={testDate}
        onDateChange={mockOnDateChange}
      />
    );

    // Kalender öffnen
    const toggleButton = screen.getByRole('button', { name: /kalender öffnen/i });
    fireEvent.click(toggleButton);

    // Nächster Monat
    const nextButton = screen.getByRole('button', { name: /nächster monat/i });
    fireEvent.click(nextButton);
    expect(screen.getByText('Okt 2025')).toBeInTheDocument();

    // Vorheriger Monat
    const prevButton = screen.getByRole('button', { name: /vorheriger monat/i });
    fireEvent.click(prevButton);
    expect(screen.getByText('Sep 2025')).toBeInTheDocument();
  });

  it('behält die Markierung bei Monatsnavigation', () => {
    render(
      <BigDatePicker 
        initialDate={testDate}
        onDateChange={mockOnDateChange}
      />
    );

    // Kalender öffnen
    const toggleButton = screen.getByRole('button', { name: /kalender öffnen/i });
    fireEvent.click(toggleButton);

    // Tag 15 auswählen
    const day15Button = screen.getByRole('button', { name: '15' });
    fireEvent.click(day15Button);

    // Zum nächsten Monat navigieren und zurück
    const nextButton = screen.getByRole('button', { name: /nächster monat/i });
    fireEvent.click(nextButton);
    
    const prevButton = screen.getByRole('button', { name: /vorheriger monat/i });
    fireEvent.click(prevButton);

    // Tag 15 sollte immer noch markiert sein
    const day15ButtonAfterNav = screen.getByRole('button', { name: '15' });
    expect(day15ButtonAfterNav).toHaveClass('bg-[#ff7a05]', 'text-white');
  });
});
