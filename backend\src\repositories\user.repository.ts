import { db } from '../db';
import { user } from '../db/schema';
import { eq, or } from 'drizzle-orm';

type User = typeof user.$inferSelect;

export interface UserSettings {
  bio?: string;
  fontSize?: string;
  highContrast?: boolean;
  animations?: boolean;
  compactMode?: boolean;
  theme?: string;
  language?: string;
  notifications?: any;
  preferences?: any;
}

export class UserRepository {
  private db = db;

  constructor() {
    // Drizzle DB wird direkt importiert
  }

  async findByEmailOrUsername(email: string, username: string): Promise<User | null> {
    const result = await this.db.select().from(user)
      .where(or(
        eq(user.email, email),
        eq(user.username, username)
      ))
      .limit(1);
    return result[0] || null;
  }

  async findByEmail(email: string): Promise<User | null> {
    const result = await this.db.select().from(user)
      .where(eq(user.email, email))
      .limit(1);
    return result[0] || null;
  }

  async findByUsername(username: string): Promise<User | null> {
    const result = await this.db.select().from(user)
      .where(eq(user.username, username))
      .limit(1);
    return result[0] || null;
  }

  async createUser(userData: {
    email: string;
    username: string;
    firstName?: string;
    lastName?: string;
    passwordHash: string;
  }): Promise<User> {
    const result = await this.db.insert(user).values({
      email: userData.email,
      username: userData.username,
      firstName: userData.firstName,
      lastName: userData.lastName,
      password: userData.passwordHash,
      passwordHash: userData.passwordHash,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }).returning();
    return result[0];
  }

  async findById(id: number): Promise<User | null> {
    const result = await this.db.select().from(user)
      .where(eq(user.id, id))
      .limit(1);
    return result[0] || null;
  }

  async updateUserSettings(userId: number, settings: UserSettings): Promise<User | null> {
    try {
      const result = await this.db.update(user)
        .set({
          ...settings,
          updatedAt: new Date().toISOString()
        })
        .where(eq(user.id, userId))
        .returning();

      return result[0] || null;
    } catch (error) {
      console.error('Error updating user settings:', error);
      throw error;
    }
  }

  async getUserSettings(userId: number): Promise<Partial<User> | null> {
    try {
      const result = await this.db.select({
        bio: user.bio,
        fontSize: user.fontSize,
        highContrast: user.highContrast,
        animations: user.animations,
        compactMode: user.compactMode,
        theme: user.theme,
        language: user.language,
        notifications: user.notifications,
        preferences: user.preferences
      }).from(user)
        .where(eq(user.id, userId))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      console.error('Error getting user settings:', error);
      throw error;
    }
  }

  async updateUserProfile(userId: number, profileData: {
    username?: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    bio?: string;
    avatar?: string | null;
  }): Promise<User | null> {
    try {
      const result = await this.db.update(user)
        .set({
          ...profileData,
          updatedAt: new Date().toISOString()
        })
        .where(eq(user.id, userId))
        .returning();

      return result[0] || null;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }
}