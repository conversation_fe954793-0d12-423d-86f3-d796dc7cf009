"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const router = express_1.default.Router();
// Debug-Route für ARiL-Daten
router.get('/debug', async (req, res) => {
    try {
        console.log('🔍 ARiL Debug-Route aufgerufen');
        // Teste direkte Drizzle-Abfrage für ARiL-Daten
        const result = await db_1.db.select().from(schema_1.ariL)
            .limit(5) // Nur erste 5 Datensätze für Debug
            .orderBy((0, drizzle_orm_1.desc)(schema_1.ariL.Datum));
        console.log('✅ ARiL Daten aus Datenbank:', result);
        res.json({
            success: true,
            count: result.length,
            data: result,
            fields: result.length > 0 ? Object.keys(result[0]) : [],
        });
    }
    catch (error) {
        console.error('❌ ARiL Debug Fehler:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unbekannter Fehler',
            details: error
        });
    }
});
exports.default = router;
