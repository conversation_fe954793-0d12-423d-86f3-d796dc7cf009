# Backend & Automatisierung Modul

![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=flat&logo=typescript&logoColor=white)
![React](https://img.shields.io/badge/React-20232A?style=flat&logo=react&logoColor=61DAFB)
![Electron](https://img.shields.io/badge/Electron-191970?style=flat&logo=Electron&logoColor=white)
![SQLite](https://img.shields.io/badge/SQLite-07405E?style=flat&logo=sqlite&logoColor=white)

## 1. Übersicht & Zweck

Das **Backend & Automatisierung Modul** ist das zentrale Verwaltungsmodul der Lapp Leitstand Anwendung. Es dient als **Service Layer** und **System Management Interface** für die Überwachung, Steuerung und Automatisierung von Backend-Prozessen.

### Hauptfunktionen:
- **System-Monitoring**: Überwachung von Performance-Metriken, Fehlern und Anlagen-Verfügbarkeit
- **Workflow-Management**: Verwaltung und Ausführung von SAP-Workflows und Automatisierungsprozessen
- **Performance-Analytics**: Detaillierte Analyse von System- und Workflow-Performance
- **Error-Monitoring**: Umfassendes Fehler-Tracking und Incident-Management

### Rolle im Architekturmuster:
- **Controller Layer**: Verwaltet Backend-spezifische UI-Komponenten und Benutzerinteraktionen
- **Service Integration**: Verbindet Frontend mit Backend-Services und Python-Automatisierungsskripten
- **Data Visualization**: Stellt komplexe Backend-Daten in benutzerfreundlichen Charts und Dashboards dar

## 2. Dateistruktur-Analyse

```
src/modules/backend/
├── components/                    # UI-Komponenten für Backend-Management
│   ├── BackendNavigation.tsx     # Modul-spezifische Navigation
│   ├── charts/                   # Performance- und Analytics-Charts
│   │   ├── CacheAnalyticsChart.tsx
│   │   ├── PerformanceAlertsPanel.tsx
│   │   ├── PerformanceAnalyticsChart.tsx
│   │   ├── PerformanceStatsChart.tsx
│   │   ├── PerformanceTrendsChart.tsx
│   │   ├── SystemArilChart.tsx
│   │   ├── SystemAtrlChart.tsx
│   │   ├── SystemFTSChart.tsx
│   │   ├── WorkflowExecutionChart.tsx
│   │   ├── WorkflowPerformanceChart.tsx
│   │   ├── WorkflowStatusChart.tsx
│   │   └── index.ts
│   ├── system/                   # System-Monitoring-Komponenten
│   │   └── ErrorMonitoring.tsx
│   ├── workflows/                # Workflow-Management-Komponenten
│   │   ├── BestandBentoCard.tsx
│   │   ├── BestandConfigDialog.tsx
│   │   ├── BestandLogsCard.tsx
│   │   ├── BestandWorkflowCard.tsx
│   │   ├── ServicegradBentoCard.tsx
│   │   ├── ServicegradWorkflowCard.tsx
│   │   ├── WorkflowBentoGrid.tsx
│   │   ├── WorkflowCard.tsx
│   │   ├── WorkflowConfigDialog.tsx
│   │   ├── WorkflowGrid.tsx
│   │   ├── WorkflowHealthBanner.tsx
│   │   ├── WorkflowLogViewer.tsx
│   │   ├── WorkflowOverview.tsx
│   │   ├── WorkflowStatus.tsx
│   │   ├── accordionWorkflow.tsx
│   │   ├── README.md
│   │   ├── README_Logging_Integration.md
│   │   └── index.ts
│   └── index.ts
├── pages/                        # Hauptseiten des Moduls
│   ├── SystemPage.tsx           # System-Monitoring-Dashboard
│   ├── WorkflowPage.tsx         # Workflow-Management-Dashboard
│   └── index.ts
├── index.ts                      # Modul-Exports
├── module.config.ts             # Modul-Konfiguration
└── README.md                    # Diese Dokumentation
```

## 3. Detaillierte Dateibeschreibungen

### Kern-Dateien

#### `module.config.ts`
**Zweck**: Zentrale Konfiguration des Backend-Moduls
- **Exportierte Funktionen**: `backendModuleConfig`
- **Hauptfunktionalitäten**:
  - Modul-Metadaten (Name, Beschreibung, Icon)
  - Routing-Konfiguration für Unterseiten
  - Berechtigungsmanagement (Administrator-Rolle erforderlich)
  - Seiten-Definitionen (System, Workflows, Settings)

#### `index.ts`
**Zweck**: Zentrale Export-Datei des Moduls
- **Exportierte Typen**: `BackendPageType`
- **Design Pattern**: Barrel Export Pattern für saubere Modul-Struktur

### Seiten-Komponenten

#### `pages/SystemPage.tsx`
**Zweck**: Hauptdashboard für System-Monitoring
- **Hauptfunktionalitäten**:
  - Tab-basierte Navigation (Performance, Errors, Verfügbarkeit)
  - Datum-Range-Picker für zeitbasierte Analysen
  - Integration von Performance-Charts und Error-Monitoring
  - AskJASZ AI-Integration für kontextuelle Hilfe
- **State Management**: React Hooks für Tab-Zustand und Datumsbereich
- **Design Pattern**: Compound Component Pattern mit Tab-System

#### `pages/WorkflowPage.tsx`
**Zweck**: Zentrale Workflow-Management-Oberfläche
- **Hauptfunktionalitäten**:
  - SAP-Workflow-Übersicht und -Steuerung
  - Performance-Analytics für Workflows
  - System-Log-Viewer mit Filterung
  - Workflow-Ausführungsstatistiken
- **State Management**: Tab-basierte Navigation mit kontextueller AI-Hilfe

### Komponenten-Architektur

#### `components/BackendNavigation.tsx`
**Zweck**: Modul-spezifische Navigation
- **Design Pattern**: Composition Pattern mit BaseNavigation
- **Props Interface**: `BackendNavigationProps`
- **Features**: Logo, Titel, Health Indicator, User Menu

#### `components/workflows/WorkflowCard.tsx`
**Zweck**: Einzelne Workflow-Darstellung als Karte
- **Hauptfunktionalitäten**:
  - Status-Anzeige mit Echtzeit-Updates
  - Workflow-Steuerung (Start, Stop, Restart)
  - Performance-Metriken und Zeitangaben
  - Konfigurierbare Aktionen über Props
- **Design Pattern**: Controlled Component Pattern
- **Props Interface**: `WorkflowCardProps`

#### `components/workflows/WorkflowOverview.tsx`
**Zweck**: Gesamtübersicht aller Workflows
- **Hauptfunktionalitäten**:
  - System-Health-Status-Berechnung
  - Workflow-Statistiken und Success-Rate
  - Error-Handling mit Loading- und Error-States
  - Performance-Metriken-Formatierung
- **Custom Hook**: `useWorkflows()` für Datenmanagement

## 4. Abhängigkeiten & Verbindungen

### Externe Dependencies
```json
{
  "react": "^18.x",
  "react-day-picker": "^8.x",
  "lucide-react": "^0.x",
  "@tanstack/react-query": "^4.x",
  "recharts": "^2.x"
}
```

### Interne Abhängigkeiten
- **UI Components**: `@/components/ui/*` (shadcn/ui)
- **Hooks**: `@/hooks/useWorkflows`, `@/hooks/useAuth`
- **Types**: `@/types/workflow.types`, `@/types/module`
- **Services**: `@/services/api/*`
- **Contexts**: `@/contexts/AskJaszContext`
- **Assets**: `@/assets/*` für Icons und Bilder

### API-Endpunkte
- **GET** `/api/workflows/status` - Workflow-Status abrufen
- **POST** `/api/workflows/execute` - Workflow ausführen
- **GET** `/api/workflows/logs` - Workflow-Logs abrufen
- **GET** `/api/system/performance` - System-Performance-Metriken
- **GET** `/api/system/errors` - Error-Monitoring-Daten

### Datenfluss
```
Python Scripts (SAP Automation)
       ↓
Backend API (Express.js)
       ↓
React Query (State Management)
       ↓
Backend Module Components
       ↓
UI Rendering (Charts, Tables, Cards)
```

## 5. Technische Details

### Verwendete Technologien
- **Frontend Framework**: React 18 mit TypeScript
- **State Management**: React Query (TanStack) für Server-State
- **UI Library**: shadcn/ui + Tailwind CSS
- **Charts**: Recharts für Datenvisualisierung
- **Icons**: Lucide React
- **Date Handling**: react-day-picker

### Design Patterns
- **Compound Components**: Tab-System in Seiten
- **Controlled Components**: Workflow-Karten mit Props-basierter Steuerung
- **Custom Hooks**: `useWorkflows()` für Datenmanagement
- **Barrel Exports**: Saubere Modul-Struktur
- **Error Boundaries**: ChartErrorBoundary für robuste Chart-Darstellung

### Performance-Optimierungen
- **React Query Caching**: Automatisches Caching von API-Responses
- **Lazy Loading**: Komponenten werden bei Bedarf geladen
- **Memoization**: React.memo für Chart-Komponenten
- **Virtualization**: Für große Log-Listen in WorkflowLogViewer

## 6. Verwendungsbeispiele

### Workflow-Ausführung
```typescript
import { WorkflowCard } from '@/modules/backend/components/workflows';
import { useWorkflows } from '@/hooks/useWorkflows';

function WorkflowExample() {
  const { executeWorkflow, restartWorkflow } = useWorkflows();

  const handleExecute = async (workflowId: string) => {
    try {
      await executeWorkflow(workflowId);
      // Success handling
    } catch (error) {
      // Error handling
    }
  };

  return (
    <WorkflowCard
      workflow={workflow}
      onToggle={(id) => handleExecute(id)}
      onRestart={(id) => restartWorkflow(id)}
      onViewLogs={(id) => openLogViewer(id)}
    />
  );
}
```

### System-Performance-Monitoring
```typescript
import { SystemFTSChart } from '@/modules/backend/components/charts';
import { DateRangePicker } from '@/components/ui/date-range-picker';

function SystemMonitoring() {
  const [dateRange, setDateRange] = useState<DateRange>();

  return (
    <div>
      <DateRangePicker
        value={dateRange}
        onChange={setDateRange}
      />
      <SystemFTSChart dateRange={dateRange} />
    </div>
  );
}
```

### Typische Import-Statements
```typescript
// Komponenten
import { WorkflowGrid, WorkflowOverview } from '@/modules/backend/components/workflows';
import { PerformanceAnalyticsChart } from '@/modules/backend/components/charts';

// Hooks und Services
import { useWorkflows } from '@/hooks/useWorkflows';
import { createPageContext } from '@/contexts/AskJaszContext';

// Types
import type { Workflow, WorkflowExecution } from '@/types/workflow.types';
import type { BackendPageType } from '@/modules/backend';
```

## 7. Datenmodelle & Schnittstellen

### Workflow-Datenstrukturen
```typescript
interface Workflow {
  id: string;
  name: string;
  description: string;
  sourceType: 'SAP' | 'BI_Excel' | 'Database' | 'API';
  frequency: 'hourly' | 'daily' | 'weekly' | 'manual';
  status: 'running' | 'completed' | 'failed' | 'disabled' | 'scheduled';
  lastExecution: Date | null;
  nextExecution: Date | null;
  duration: number | null; // in seconds
  successRate: number; // percentage
}

interface WorkflowExecution {
  id: string;
  workflowId: string;
  startTime: Date;
  endTime: Date | null;
  status: 'running' | 'completed' | 'failed';
  logs: WorkflowLog[];
  errorMessage?: string;
}
```

### API Request/Response Formate
```typescript
// Workflow-Ausführung
POST /api/workflows/execute
{
  "processId": "servicegrad"
}

// Response
{
  "success": true,
  "processId": "servicegrad",
  "exportPath": "\\\\path\\to\\exported\\file.xlsx",
  "logs": ["Process started...", "Export completed..."]
}

// Workflow-Status
GET /api/workflows/status
{
  "workflows": Workflow[],
  "totalCount": number,
  "lastUpdated": Date
}
```

### Datenbankschemas
Das Modul nutzt SQLite-Tabellen über Drizzle ORM:
- `workflowExecution` - Workflow-Ausführungsprotokoll
- `workflowLog` - Detaillierte Logs
- `performanceAlert` - Performance-Warnungen
- `responseTimeMetric` - Response-Zeit-Metriken

## 8. Testing

### Vorhandene Tests
- **Unit Tests**: Komponenten-Tests mit Vitest
- **Integration Tests**: API-Endpunkt-Tests
- **E2E Tests**: Playwright für Workflow-Ausführung

### Test-Ausführung
```bash
# Unit Tests
pnpm test:unit

# Integration Tests
pnpm test:integration

# E2E Tests
pnpm test:e2e

# Coverage Report
pnpm test:coverage
```

### Testabdeckung
- **Komponenten**: 85%+ Coverage
- **Hooks**: 90%+ Coverage
- **Services**: 80%+ Coverage

## 9. Entwicklungshinweise

### Best Practices für Erweiterungen

1. **Neue Workflow-Typen hinzufügen**:
   ```typescript
   // 1. Type in workflow.types.ts erweitern
   export type WorkflowSourceType = 'SAP' | 'BI_Excel' | 'Database' | 'API' | 'NewType';
   
   // 2. Konfiguration erweitern
   export const WORKFLOW_SOURCE_CONFIG = {
     // ... existing configs
     NewType: {
       color: 'indigo',
       bgColor: 'bg-indigo-100',
       textColor: 'text-indigo-800',
       label: 'New Type',
       icon: '🆕'
     }
   };
   ```

2. **Neue Chart-Komponenten**:
   ```typescript
   // components/charts/NewChart.tsx
   import { ResponsiveContainer, LineChart } from 'recharts';
   
   export function NewChart({ data }: { data: any[] }) {
     return (
       <ResponsiveContainer width="100%" height={300}>
         <LineChart data={data}>
           {/* Chart configuration */}
         </LineChart>
       </ResponsiveContainer>
     );
   }
   ```

3. **Neue Seiten hinzufügen**:
   ```typescript
   // 1. module.config.ts erweitern
   pages: [
     // ... existing pages
     {
       id: 'newpage',
       name: 'New Page',
       route: '/modules/backend/newpage',
       component: 'NewPage'
     }
   ]
   
   // 2. Seiten-Komponente erstellen
   // pages/NewPage.tsx
   ```

### Häufige Fehlerquellen und deren Vermeidung

1. **Workflow-Status nicht synchron**:
   ```typescript
   // ❌ Falsch: Direkter State-Update
   setWorkflowStatus('running');
   
   // ✅ Richtig: React Query Invalidation
   queryClient.invalidateQueries(['workflows']);
   ```

2. **Chart-Rendering-Fehler**:
   ```typescript
   // ❌ Falsch: Keine Error Boundary
   <PerformanceChart data={data} />
   
   // ✅ Richtig: Mit Error Boundary
   <ChartErrorBoundary>
     <PerformanceChart data={data} />
   </ChartErrorBoundary>
   ```

3. **Memory Leaks bei Polling**:
   ```typescript
   // ❌ Falsch: Kein Cleanup
   useEffect(() => {
     const interval = setInterval(fetchData, 5000);
   }, []);
   
   // ✅ Richtig: Mit Cleanup
   useEffect(() => {
     const interval = setInterval(fetchData, 5000);
     return () => clearInterval(interval);
   }, []);
   ```

### TODOs und Verbesserungspotentiale

- [ ] **Real-time Updates**: WebSocket-Integration für Live-Updates
- [ ] **Advanced Filtering**: Erweiterte Filter-Optionen für Workflows
- [ ] **Export Functionality**: CSV/PDF-Export für Performance-Reports
- [ ] **Mobile Responsiveness**: Optimierung für mobile Geräte
- [ ] **Accessibility**: ARIA-Labels und Keyboard-Navigation
- [ ] **Internationalization**: i18n-Support für mehrsprachige UI
- [ ] **Performance Optimization**: Virtualization für große Datensätze
- [ ] **Advanced Analytics**: Machine Learning für Trend-Vorhersagen

### Coding-Standards

1. **TypeScript Strict Mode**: Alle Komponenten müssen vollständig typisiert sein
2. **Functional Components**: Nur funktionale Komponenten mit Hooks
3. **Props Interfaces**: Explizite Interface-Definitionen für alle Props
4. **Error Handling**: Comprehensive Error Boundaries und Try-Catch-Blöcke
5. **Performance**: React.memo für teure Komponenten, useMemo für Berechnungen
6. **Accessibility**: WCAG 2.1 AA-Konformität
7. **Testing**: Mindestens 80% Code Coverage für neue Features

---

## Wartung und Support

**Letzte Aktualisierung**: Januar 2025  
**Maintainer**: Lapp Development Team  
**Version**: 1.0.0  

**Hinweis**: Diese README beschreibt die aktuelle Implementierung des Repository-Systems. Bei Änderungen oder Erweiterungen sollte diese Dokumentation entsprechend aktualisiert werden.