"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const cutting_repository_1 = require("../cutting.repository");
// Mock für die Datenbank und Cache
vitest_1.vi.mock('../../db', () => ({
    db: {
        select: vitest_1.vi.fn().mockReturnThis(),
        from: vitest_1.vi.fn().mockReturnThis(),
        where: vitest_1.vi.fn().mockReturnThis(),
        orderBy: vitest_1.vi.fn().mockReturnThis(),
        limit: vitest_1.vi.fn().mockReturnThis()
    }
}));
vitest_1.vi.mock('../../utils/cache/backend-cache', () => ({
    getBackendCache: () => ({
        cachedQuery: vitest_1.vi.fn()
    })
}));
(0, vitest_1.describe)('CuttingRepository', () => {
    let repository;
    let mockCachedQuery;
    (0, vitest_1.beforeEach)(() => {
        repository = new cutting_repository_1.CuttingRepositoryImpl();
        // Zugriff auf die gemockte cachedQuery Funktion
        mockCachedQuery = repository.cache.cachedQuery;
    });
    (0, vitest_1.describe)('getWEData', () => {
        (0, vitest_1.it)('sollte datum-Feld korrekt aus dem Query-Ergebnis übernehmen (ARRANGE-ACT-ASSERT)', async () => {
            // ARRANGE: Bereite Mock-Daten vor mit korrektem Feldnamen 'datum' (kleingeschrieben)
            const mockWEData = [
                {
                    id: 1,
                    datum: '2025-04-15',
                    weAtrl: 409,
                    weManl: 56
                },
                {
                    id: 2,
                    datum: '2025-04-16',
                    weAtrl: 713,
                    weManl: 74
                },
                {
                    id: 3,
                    datum: '2025-04-17',
                    weAtrl: 550,
                    weManl: 20
                }
            ];
            // Mock die cachedQuery Funktion, um unsere Testdaten zurückzugeben
            mockCachedQuery.mockImplementation(async (key, queryFn) => {
                return mockWEData;
            });
            // ACT: Rufe die getWEData Methode auf
            const result = await repository.getWEData({
                startDate: '2025-04-15',
                endDate: '2025-04-17'
            });
            // ASSERT: Verifiziere, dass die Daten korrekt gemappt wurden
            (0, vitest_1.expect)(result).toHaveLength(3);
            // Prüfe das erste Element im Detail
            (0, vitest_1.expect)(result[0]).toEqual({
                id: 1,
                datum: '2025-04-15',
                weAtrl: 409,
                weManl: 56
            });
            // Verifiziere, dass alle datum-Felder korrekt übernommen wurden
            result.forEach((item, index) => {
                (0, vitest_1.expect)(item.datum).toBe(mockWEData[index].datum);
                (0, vitest_1.expect)(item.datum).not.toBe(''); // Stelle sicher, dass das Datum nicht leer ist
            });
        });
        (0, vitest_1.it)('sollte leeren String als Fallback verwenden, wenn datum fehlt', async () => {
            // ARRANGE: Mock-Daten mit fehlendem datum-Feld
            const mockWEDataWithMissingDatum = [
                {
                    id: 1,
                    // datum fehlt hier absichtlich
                    weAtrl: 409,
                    weManl: 56
                },
                {
                    id: 2,
                    datum: null, // explizit null
                    weAtrl: 713,
                    weManl: 74
                },
                {
                    id: 3,
                    datum: undefined, // explizit undefined
                    weAtrl: 550,
                    weManl: 20
                }
            ];
            mockCachedQuery.mockImplementation(async (key, queryFn) => {
                return mockWEDataWithMissingDatum;
            });
            // ACT: Rufe die getWEData Methode auf
            const result = await repository.getWEData();
            // ASSERT: Verifiziere, dass leere Strings als Fallback verwendet werden
            (0, vitest_1.expect)(result[0].datum).toBe('');
            (0, vitest_1.expect)(result[1].datum).toBe('');
            (0, vitest_1.expect)(result[2].datum).toBe('');
        });
        (0, vitest_1.it)('sollte die Daten nach Datum in absteigender Reihenfolge zurückgeben', async () => {
            // ARRANGE: Mock-Daten in zufälliger Reihenfolge
            const mockWEData = [
                { id: 2, datum: '2025-04-16', weAtrl: 713, weManl: 74 },
                { id: 3, datum: '2025-04-17', weAtrl: 550, weManl: 20 },
                { id: 1, datum: '2025-04-15', weAtrl: 409, weManl: 56 }
            ];
            mockCachedQuery.mockImplementation(async (key, queryFn) => {
                // Simuliere absteigende Sortierung
                return mockWEData.sort((a, b) => b.datum.localeCompare(a.datum));
            });
            // ACT: Rufe die getWEData Methode auf
            const result = await repository.getWEData();
            // ASSERT: Verifiziere die absteigende Sortierung
            (0, vitest_1.expect)(result[0].datum).toBe('2025-04-17');
            (0, vitest_1.expect)(result[1].datum).toBe('2025-04-16');
            (0, vitest_1.expect)(result[2].datum).toBe('2025-04-15');
        });
    });
});
