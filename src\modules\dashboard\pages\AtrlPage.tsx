import React, { useState } from "react";
import { DateRange } from "react-day-picker";
import { AtrlDataChart } from "@/modules/dashboard/components/charts/AtrlDataChart";
import { Lagerauslastung200Chart } from "@/modules/dashboard/components/charts/Lagerauslastung200Chart";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Drum, FileSpreadsheet, FileText } from "lucide-react";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import drumIcon from "@/assets/iconATRL.png";
import { AskJaszButton } from "@/modules/ai/components";
import { createPageContext } from "@/contexts/AskJaszContext";
import { ExportService } from "@/services/export.service";
import { collectAtrlChartData } from "@/services/chart-data-collector.service";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

/**
 * ATrL-Bereich Dashboard
 *
 * Zeigt verschiedene Kennzahlen für den ATrL-Bereich an:
 * - ATrL-Daten als kombiniertes Diagramm mit Balken und Linien
 * - Umlagerungen, Ein- und Auslagerungen
 * - Lagerauslastung der AAA, BBB und CCC Artikel
 */
export default function AtrlPage() {
  // Zustand für den ausgewählten Datumsbereich - Zeitraum mit verfügbaren Daten
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 5, 1), // 1. Juni 2025 (Daten verfügbar ab Juni)
    to: new Date(2025, 6, 15), // 15. Juli 2025 (Daten verfügbar bis August)
  });

  // Zustand für Export-Loading
  const [isExporting, setIsExporting] = useState<{ excel: boolean; powerpoint: boolean }>({
    excel: false,
    powerpoint: false
  });

  // Export-Funktionen
  const handleExcelExport = async () => {
    if (!dateRange?.from || !dateRange?.to) {
      toast.error('Bitte wählen Sie einen Datumsbereich aus');
      return;
    }

    setIsExporting(prev => ({ ...prev, excel: true }));

    try {
      const chartData = await collectAtrlChartData(dateRange);

      if (chartData.length === 0) {
        toast.error('Keine Daten für den Export verfügbar');
        return;
      }

      await ExportService.exportToExcel(chartData, {
        dateRange,
        includeSummary: true,
        dashboardType: 'atrl'
      });

      toast.success('Excel-Datei wurde erfolgreich erstellt');
    } catch (error) {
      console.error('Excel-Export Fehler:', error);
      toast.error('Fehler beim Erstellen der Excel-Datei');
    } finally {
      setIsExporting(prev => ({ ...prev, excel: false }));
    }
  };

  const handlePowerPointExport = async () => {
    if (!dateRange?.from || !dateRange?.to) {
      toast.error('Bitte wählen Sie einen Datumsbereich aus');
      return;
    }

    setIsExporting(prev => ({ ...prev, powerpoint: true }));

    try {
      const chartData = await collectAtrlChartData(dateRange);

      if (chartData.length === 0) {
        toast.error('Keine Daten für den Export verfügbar');
        return;
      }

      await ExportService.exportToPowerPoint(chartData, {
        dateRange,
        includeSummary: true,
        dashboardType: 'atrl'
      });

      toast.success('PowerPoint-Datei wurde erfolgreich erstellt');
    } catch (error) {
      console.error('PowerPoint-Export Fehler:', error);
      toast.error('Fehler beim Erstellen der PowerPoint-Datei');
    } finally {
      setIsExporting(prev => ({ ...prev, powerpoint: false }));
    }
  };

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <img
            src={drumIcon}
            alt="Automatisches Trommellager"
            className="mr-2 h-10 w-10 object-contain"
          />
          <h1 className="text-4xl font-bold text-black">
            AUTOMATISCHES TROMMELLAGER
          </h1>
          <div className="-mt-7">
            <AskJaszButton
              context={createPageContext(
                "ATrL Dashboard",
                [
                  "Automatisches Trommellager Übersicht",
                  "Umlagerungen, Ein- und Auslagerungen",
                  "Lagerauslastung AAA, BBB und CCC Artikel",
                  "Lagerbewegungen und Kapazitätsanalyse",
                ],
                "ATrL Bereich Performance Dashboard",
              )}
              position="inline"
              size="md"
              variant="default"
              tooltip="Hilfe zum ATrL Dashboard erhalten"
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex gap-2">
            <Button
              onClick={handleExcelExport}
              disabled={isExporting.excel}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <img src="/src/assets/excel-pn.png" alt="Excel Export" className="h-8 w-8" />
            </Button>
            <Button
              onClick={handlePowerPointExport}
              disabled={isExporting.powerpoint}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <img src="/src/assets/pp.png" alt="PowerPoint Export" className="h-8 w-8" />
            </Button>
          </div>
          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
            label="Datumsauswahl"
          />
        </div>
      </div>

      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 gap-2">
          {/* ATrL-Daten Chart */}
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <AtrlDataChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>

          {/* Lagerauslastung Chart */}
          <div className="mt-2 xl:col-span-1">
            <ChartErrorBoundary>
              <Lagerauslastung200Chart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
}
