import React from 'react';
import { PackageCheck } from 'lucide-react';
import { BaseKPICardProps } from './types';

/**
 * KPI-Karte für Qualität
 * 
 * Zeigt die Qualitätsrate aus den Dispatch-Daten an.
 * Verwendet rotes Farbschema mit PackageCheck-Icon aus Lucide.
 */
export const QualityCard: React.FC<BaseKPICardProps> = ({ 
  departmentKPIs, 
  className = '' 
}) => {
  return (
    <>
      <div className="flex items-center mb-2">
          <PackageCheck className="h-6 w-6 text-red-600 mr-3" />
        <h3 className="text-xl text-gray-800">Q-MELDUNGEN</h3>
      </div>
      
      <div className="text-2xl font-bold text-gray-900">
        {departmentKPIs?.dispatch.qualityRate.toFixed(1) || '0.0'}
        <span className="text-sm font-normal text-gray-500 ml-1">%</span>
      </div>
      
      <div className="text-sm text-gray-600 mt-1">
        Ziel: 95.0%
      </div>
    </>
  );
};

export default QualityCard;