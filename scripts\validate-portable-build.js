#!/usr/bin/env node

/**
 * Portable Build Validation Script
 * 
 * Dieses Skript validiert und testet die portable Build-Funktionalität
 * des Lapp Dashboards für verschiedene Deployment-Szenarien.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

// Konfiguration
const config = {
  appName: 'LappDashboard',
  outputDir: 'portable-dist',
  validationTests: [
    'fileStructure',
    'dependencies',
    'configuration',
    'startupScripts',
    'documentation',
    'citrixCompatibility',
    'databaseConnection'
  ],
  criticalFiles: [
    'LappDashboard.exe',
    'portable-config.json',
    'start-app.bat',
    'start-citrix.bat',
    'README-PORTABLE.md',
    'README-DEPLOYMENT.md',
    'resources/app/backend/dist/server.js',
    'resources/app/backend/package.json'
  ],
  optionalFiles: [
    'logs/',
    '.portable-build-info.json',
    'resources/app/backend/node_modules/',
    'resources/app/backend/drizzle.config.ts'
  ]
};

// Logging-Funktionen
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '[❌ ERROR]' : 
                type === 'warn' ? '[⚠️  WARN]' : 
                type === 'success' ? '[✅ SUCCESS]' : 
                '[ℹ️  INFO]';
  console.log(`${timestamp} ${prefix} ${message}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  console.log(`  ${title.toUpperCase()}`);
  console.log('='.repeat(60));
}

// Test-Funktionen
class PortableBuildValidator {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: {}
    };
    this.appDir = null;
  }

  async run() {
    try {
      logSection('Portable Build Validation');
      log('Initiating comprehensive validation of portable build...');
      
      // Finde das App-Verzeichnis
      await this.findAppDirectory();
      
      if (!this.appDir) {
        throw new Error('Could not find portable app directory. Please run build first.');
      }
      
      log(`Found portable app directory: ${this.appDir}`);
      
      // Führe alle Tests aus
      for (const testName of config.validationTests) {
        await this.runTest(testName);
      }
      
      // Zeige Zusammenfassung
      this.showSummary();
      
      return this.results.failed === 0;
      
    } catch (error) {
      log(`Validation failed: ${error.message}`, 'error');
      return false;
    }
  }
  
  async findAppDirectory() {
    const possiblePaths = [
      path.join(config.outputDir, `${config.appName}-win32-x64`),
      path.join(config.outputDir, config.appName),
      path.join('.', config.appName),
    ];
    
    for (const testPath of possiblePaths) {
      if (fs.existsSync(testPath)) {
        const exePath = path.join(testPath, `${config.appName}.exe`);
        if (fs.existsSync(exePath)) {
          this.appDir = testPath;
          return;
        }
      }
    }
  }
  
  async runTest(testName) {
    logSection(`Test: ${testName}`);
    
    try {
      const methodName = `test_${testName}`;
      if (typeof this[methodName] === 'function') {
        const result = await this[methodName]();
        this.results.details[testName] = result;
        
        if (result.passed) {
          this.results.passed++;
          log(`${testName} test passed`, 'success');
        } else {
          this.results.failed++;
          log(`${testName} test failed: ${result.message}`, 'error');
        }
        
        if (result.warnings && result.warnings.length > 0) {
          this.results.warnings += result.warnings.length;
          result.warnings.forEach(warning => log(warning, 'warn'));
        }
        
      } else {
        log(`Test method ${methodName} not found`, 'warn');
        this.results.warnings++;
      }
      
    } catch (error) {
      this.results.failed++;
      log(`${testName} test error: ${error.message}`, 'error');
      this.results.details[testName] = { passed: false, message: error.message };
    }
  }
  
  // Test: Dateistruktur
  async test_fileStructure() {
    const result = { passed: true, warnings: [], details: {} };
    
    log('Validating file structure...');
    
    // Kritische Dateien prüfen
    const missingCritical = [];
    for (const file of config.criticalFiles) {
      const filePath = path.join(this.appDir, file);
      if (!fs.existsSync(filePath)) {
        missingCritical.push(file);
      } else {
        const stats = fs.statSync(filePath);
        result.details[file] = {
          exists: true,
          size: stats.size,
          modified: stats.mtime
        };
        log(`✓ Found: ${file} (${Math.round(stats.size / 1024)}KB)`);
      }
    }
    
    if (missingCritical.length > 0) {
      result.passed = false;
      result.message = `Missing critical files: ${missingCritical.join(', ')}`;
      return result;
    }
    
    // Optionale Dateien prüfen
    const missingOptional = [];
    for (const file of config.optionalFiles) {
      const filePath = path.join(this.appDir, file);
      if (!fs.existsSync(filePath)) {
        missingOptional.push(file);
      } else {
        log(`✓ Optional file found: ${file}`);
      }
    }
    
    if (missingOptional.length > 0) {
      result.warnings.push(`Optional files missing: ${missingOptional.join(', ')}`);
    }
    
    return result;
  }
  
  // Test: Dependencies
  async test_dependencies() {
    const result = { passed: true, warnings: [], details: {} };
    
    log('Validating dependencies...');
    
    // Backend package.json prüfen
    const backendPkgPath = path.join(this.appDir, 'resources/app/backend/package.json');
    if (fs.existsSync(backendPkgPath)) {
      try {
        const backendPkg = JSON.parse(fs.readFileSync(backendPkgPath, 'utf8'));
        result.details.backendPackage = backendPkg;
        log(`✓ Backend package.json found (${Object.keys(backendPkg.dependencies || {}).length} dependencies)`);
      } catch (error) {
        result.warnings.push('Backend package.json could not be parsed');
      }
    }
    
    // Node modules prüfen
    const backendNodeModules = path.join(this.appDir, 'resources/app/backend/node_modules');
    if (fs.existsSync(backendNodeModules)) {
      const moduleCount = fs.readdirSync(backendNodeModules).length;
      log(`✓ Backend node_modules found (${moduleCount} modules)`);
      result.details.backendModuleCount = moduleCount;
      
      // Kritische Module prüfen
      const criticalModules = ['express', 'drizzle-orm', 'cors', 'helmet'];
      const missingModules = [];
      
      for (const module of criticalModules) {
        const modulePath = path.join(backendNodeModules, module);
        if (!fs.existsSync(modulePath)) {
          missingModules.push(module);
        }
      }
      
      if (missingModules.length > 0) {
        result.warnings.push(`Missing critical backend modules: ${missingModules.join(', ')}`);
      }
    } else {
      result.warnings.push('Backend node_modules directory not found');
    }
    
    return result;
  }
  
  // Test: Konfiguration
  async test_configuration() {
    const result = { passed: true, warnings: [], details: {} };
    
    log('Validating configuration...');
    
    // Portable config prüfen
    const configPath = path.join(this.appDir, 'portable-config.json');
    if (fs.existsSync(configPath)) {
      try {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        result.details.portableConfig = config;
        
        // Basis-Eigenschaften prüfen
        if (!config.portable) {
          result.warnings.push('portable flag not set to true in config');
        }
        
        if (!config.database) {
          result.passed = false;
          result.message = 'Database configuration missing from portable config';
          return result;
        }
        
        // Database config validieren
        const dbConfig = config.database;
        if (!dbConfig.host || !dbConfig.database || !dbConfig.username) {
          result.warnings.push('Incomplete database configuration');
        }
        
        log(`✓ Portable config valid (database: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database})`);
        
        // Citrix-Optimierungen prüfen
        if (config.citrix) {
          log(`✓ Citrix optimizations configured (memory: ${config.citrix.memoryLimit}MB)`);
          result.details.citrixOptimized = true;
        } else {
          result.warnings.push('Citrix optimizations not configured');
        }
        
      } catch (error) {
        result.passed = false;
        result.message = `Invalid portable configuration: ${error.message}`;
        return result;
      }
    } else {
      result.passed = false;
      result.message = 'portable-config.json not found';
      return result;
    }
    
    return result;
  }
  
  // Test: Startup Scripts
  async test_startupScripts() {
    const result = { passed: true, warnings: [], details: {} };
    
    log('Validating startup scripts...');
    
    const scripts = [
      { name: 'start-app.bat', required: true },
      { name: 'start-citrix.bat', required: false },
      { name: '../scripts/start-citrix-advanced.ps1', required: false }
    ];
    
    for (const script of scripts) {
      const scriptPath = path.join(this.appDir, script.name);
      if (fs.existsSync(scriptPath)) {
        const content = fs.readFileSync(scriptPath, 'utf8');
        result.details[script.name] = {
          exists: true,
          size: content.length,
          hasAppName: content.includes(config.appName)
        };
        
        log(`✓ Startup script found: ${script.name} (${content.length} chars)`);
        
        // Inhalt validieren
        if (!content.includes(config.appName)) {
          result.warnings.push(`${script.name} does not reference app name`);
        }
        
      } else if (script.required) {
        result.passed = false;
        result.message = `Required startup script missing: ${script.name}`;
        return result;
      } else {
        result.warnings.push(`Optional startup script missing: ${script.name}`);
      }
    }
    
    return result;
  }
  
  // Test: Dokumentation
  async test_documentation() {
    const result = { passed: true, warnings: [], details: {} };
    
    log('Validating documentation...');
    
    const docs = [
      { name: 'README-PORTABLE.md', required: true },
      { name: 'README-DEPLOYMENT.md', required: false }
    ];
    
    for (const doc of docs) {
      const docPath = path.join(this.appDir, doc.name);
      if (fs.existsSync(docPath)) {
        const content = fs.readFileSync(docPath, 'utf8');
        result.details[doc.name] = {
          exists: true,
          size: content.length,
          sections: (content.match(/^##/gm) || []).length
        };
        
        log(`✓ Documentation found: ${doc.name} (${Math.round(content.length/1024)}KB, ${result.details[doc.name].sections} sections)`);
        
        // Inhalt prüfen
        if (!content.includes('Citrix')) {
          result.warnings.push(`${doc.name} doesn't mention Citrix optimizations`);
        }
        
      } else if (doc.required) {
        result.passed = false;
        result.message = `Required documentation missing: ${doc.name}`;
        return result;
      } else {
        result.warnings.push(`Optional documentation missing: ${doc.name}`);
      }
    }
    
    return result;
  }
  
  // Test: Citrix-Kompatibilität
  async test_citrixCompatibility() {
    const result = { passed: true, warnings: [], details: {} };
    
    log('Validating Citrix compatibility...');
    
    // Prüfe ob Citrix-spezifische Startskripte vorhanden sind
    const citrixScript = path.join(this.appDir, 'start-citrix.bat');
    if (fs.existsSync(citrixScript)) {
      const content = fs.readFileSync(citrixScript, 'utf8');
      
      // Wichtige Citrix-Optimierungen prüfen
      const optimizations = {
        'ELECTRON_DISABLE_SANDBOX': content.includes('ELECTRON_DISABLE_SANDBOX'),
        'memory optimization': content.includes('max-old-space-size'),
        'no-sandbox flag': content.includes('--no-sandbox'),
        'disable-web-security': content.includes('--disable-web-security')
      };
      
      result.details.citrixOptimizations = optimizations;
      
      let optimizationCount = 0;
      for (const [opt, present] of Object.entries(optimizations)) {
        if (present) {
          optimizationCount++;
          log(`✓ Citrix optimization: ${opt}`);
        } else {
          result.warnings.push(`Missing Citrix optimization: ${opt}`);
        }
      }
      
      if (optimizationCount >= 2) {
        log(`✓ Citrix compatibility validated (${optimizationCount}/4 optimizations)`);
      } else {
        result.warnings.push('Insufficient Citrix optimizations');
      }
      
    } else {
      result.warnings.push('Citrix startup script not found');
    }
    
    // Prüfe portable config für Citrix-Einstellungen
    const configPath = path.join(this.appDir, 'portable-config.json');
    if (fs.existsSync(configPath)) {
      try {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        if (config.citrix) {
          result.details.citrixConfig = config.citrix;
          log('✓ Citrix configuration found in portable config');
        } else {
          result.warnings.push('No Citrix configuration in portable config');
        }
      } catch (error) {
        result.warnings.push('Could not parse portable config for Citrix settings');
      }
    }
    
    return result;
  }
  
  // Test: Datenbank-Verbindung (Mock-Test)
  async test_databaseConnection() {
    const result = { passed: true, warnings: [], details: {} };
    
    log('Validating database connection configuration...');
    
    const configPath = path.join(this.appDir, 'portable-config.json');
    if (!fs.existsSync(configPath)) {
      result.passed = false;
      result.message = 'Cannot test database connection without portable config';
      return result;
    }
    
    try {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      const dbConfig = config.database;
      
      if (!dbConfig) {
        result.passed = false;
        result.message = 'No database configuration found';
        return result;
      }
      
      // Basis-Validierung der DB-Config
      const requiredFields = ['host', 'port', 'database', 'username'];
      const missingFields = [];
      
      for (const field of requiredFields) {
        if (!dbConfig[field]) {
          missingFields.push(field);
        }
      }
      
      if (missingFields.length > 0) {
        result.passed = false;
        result.message = `Missing database config fields: ${missingFields.join(', ')}`;
        return result;
      }
      
      result.details.databaseConfig = {
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database,
        ssl: dbConfig.ssl || false,
        hasPassword: !!dbConfig.password
      };
      
      log(`✓ Database config valid: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
      log(`✓ SSL enabled: ${dbConfig.ssl || false}`);
      log(`✓ Password configured: ${!!dbConfig.password}`);
      
      // Warnung für Standard-Passwörter
      if (dbConfig.password && ['password', 'admin', '123456'].includes(dbConfig.password.toLowerCase())) {
        result.warnings.push('Database password appears to be a default/weak password');
      }
      
    } catch (error) {
      result.passed = false;
      result.message = `Database config validation failed: ${error.message}`;
    }
    
    return result;
  }
  
  showSummary() {
    logSection('Validation Summary');
    
    console.log(`📊 Test Results:`);
    console.log(`   ✅ Passed: ${this.results.passed}`);
    console.log(`   ❌ Failed: ${this.results.failed}`);
    console.log(`   ⚠️  Warnings: ${this.results.warnings}`);
    console.log();
    
    if (this.results.failed === 0) {
      log('🎉 All critical tests passed! The portable build is ready for deployment.', 'success');
      
      if (this.results.warnings > 0) {
        log(`💡 Consider addressing the ${this.results.warnings} warning(s) for optimal performance.`, 'warn');
      }
      
      console.log();
      console.log('🚀 Deployment Instructions:');
      console.log(`   1. Distribute the directory: ${this.appDir}`);
      console.log(`   2. For standard environments, use: start-app.bat`);
      console.log(`   3. For Citrix environments, use: start-citrix.bat`);
      console.log(`   4. Configure database connection in portable-config.json`);
      console.log(`   5. See README-DEPLOYMENT.md for detailed instructions`);
      
    } else {
      log('❌ Build validation failed! Please fix the issues before deployment.', 'error');
      
      console.log();
      console.log('🔧 Failed Tests:');
      for (const [testName, result] of Object.entries(this.results.details)) {
        if (!result.passed) {
          console.log(`   ❌ ${testName}: ${result.message}`);
        }
      }
    }
    
    console.log();
    log('Validation completed.');
  }
}

// Hauptfunktion
async function main() {
  const validator = new PortableBuildValidator();
  const success = await validator.run();
  
  process.exit(success ? 0 : 1);
}

// Skript ausführen falls direkt aufgerufen
if (require.main === module) {
  main().catch(error => {
    console.error('Validation script error:', error);
    process.exit(1);
  });
}

module.exports = { PortableBuildValidator };