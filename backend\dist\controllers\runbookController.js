"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RunbookController = void 0;
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
// Hilfsfunktion zum sicheren Parsen von JSON
function safeJsonParse(jsonString) {
    if (!jsonString)
        return [];
    try {
        const parsed = JSON.parse(jsonString);
        return Array.isArray(parsed) ? parsed : [];
    }
    catch (e) {
        console.error('[RunbookController] <PERSON><PERSON> beim <PERSON> von <PERSON>:', e);
        return [];
    }
}
// Hilfsfunktion zum Prüfen, ob die steps-Spalte existiert
let stepsColumnExists = null;
async function hasStepsColumn() {
    if (stepsColumnExists !== null) {
        return stepsColumnExists;
    }
    try {
        // Versuche eine einfache Query mit der steps-Spalte
        await db_1.db.execute((0, drizzle_orm_1.sql) `SELECT 1 FROM ${schema_1.runbook} LIMIT 1`);
        stepsColumnExists = true;
        return true;
    }
    catch (error) {
        // Wenn die Spalte nicht existiert, wird ein Fehler geworfen
        console.log('[RunbookController] Steps-Spalte existiert noch nicht, verwende content-Fallback');
        stepsColumnExists = false;
        return false;
    }
}
// Hilfsfunktion zum Parsen von Schritten aus Markdown-Content
function parseStepsFromContent(content) {
    const steps = [];
    const lines = content.split('\n');
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        const stepMatch = line.match(/^(\*\*)?Schritt (\d+):(\*\*)?(.*)/i);
        if (stepMatch) {
            const stepNumber = parseInt(stepMatch[2], 10);
            const description = stepMatch[4].trim();
            if (description) {
                steps.push({
                    id: `step-${Date.now()}-${i}`,
                    description: description,
                    order: stepNumber
                });
            }
        }
    }
    return steps.sort((a, b) => a.order - b.order);
}
class RunbookController {
    /**
     * GET /api/runbooks
     * Holt alle Runbooks
     */
    async getAllRunbooks(req, res) {
        try {
            const hasSteps = await hasStepsColumn();
            const runbooks = await db_1.db.select().from(schema_1.runbook).orderBy((0, drizzle_orm_1.desc)(schema_1.runbook.updated_at));
            const formattedRunbooks = runbooks.map(rb => {
                const steps = hasSteps && rb.steps ? safeJsonParse(rb.steps) : parseStepsFromContent(rb.content);
                return {
                    id: rb.id,
                    title: rb.title,
                    content: rb.content,
                    affected_systems: safeJsonParse(rb.affected_systems),
                    category: safeJsonParse(rb.category),
                    steps: steps,
                    created_at: rb.created_at,
                    updated_at: rb.updated_at
                };
            });
            res.json({
                success: true,
                data: formattedRunbooks
            });
        }
        catch (error) {
            console.error('[RunbookController] Fehler beim Laden der Runbooks:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Laden der Runbooks',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * GET /api/runbooks/:id
     * Holt ein spezifisches Runbook
     */
    async getRunbookById(req, res) {
        try {
            const { id } = req.params;
            const runbookId = parseInt(id, 10);
            if (isNaN(runbookId)) {
                res.status(400).json({
                    success: false,
                    error: 'Ungültige Runbook-ID'
                });
                return;
            }
            const runbooks = await db_1.db.select().from(schema_1.runbook).where((0, drizzle_orm_1.eq)(schema_1.runbook.id, runbookId));
            const foundRunbook = runbooks[0];
            if (!foundRunbook) {
                res.status(404).json({
                    success: false,
                    error: 'Runbook nicht gefunden'
                });
                return;
            }
            const hasSteps = await hasStepsColumn();
            const steps = hasSteps && foundRunbook.steps ? safeJsonParse(foundRunbook.steps) : parseStepsFromContent(foundRunbook.content);
            const formattedRunbook = {
                id: foundRunbook.id,
                title: foundRunbook.title,
                content: foundRunbook.content,
                affected_systems: safeJsonParse(foundRunbook.affected_systems),
                category: safeJsonParse(foundRunbook.category),
                steps: steps,
                created_at: foundRunbook.created_at,
                updated_at: foundRunbook.updated_at
            };
            res.json({
                success: true,
                data: formattedRunbook
            });
        }
        catch (error) {
            console.error('[RunbookController] Fehler beim Laden des Runbooks:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Laden des Runbooks',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * POST /api/runbooks
     * Erstellt ein neues Runbook
     */
    async createRunbook(req, res) {
        try {
            const { title, content, affected_systems, tags, steps } = req.body;
            if (!title || !content) {
                res.status(400).json({
                    success: false,
                    error: 'Titel und Inhalt sind erforderlich'
                });
                return;
            }
            const now = new Date().toISOString();
            const hasSteps = await hasStepsColumn();
            const insertData = {
                title: title.trim(),
                content: content.trim(),
                affected_systems: affected_systems ? JSON.stringify(affected_systems) : null,
                tags: tags ? JSON.stringify(tags) : null,
                created_at: now,
                updated_at: now
            };
            if (hasSteps && steps) {
                insertData.steps = JSON.stringify(steps);
            }
            const insertResult = await db_1.db.insert(schema_1.runbook).values(insertData).returning();
            const createdRunbook = insertResult[0];
            const formattedRunbook = {
                id: createdRunbook.id,
                title: createdRunbook.title,
                content: createdRunbook.content,
                affected_systems: safeJsonParse(createdRunbook.affected_systems),
                category: safeJsonParse(createdRunbook.category),
                created_at: createdRunbook.created_at,
                updated_at: createdRunbook.updated_at
            };
            res.status(201).json({
                success: true,
                data: formattedRunbook
            });
        }
        catch (error) {
            console.error('[RunbookController] Fehler beim Erstellen des Runbooks:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Erstellen des Runbooks',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * PUT /api/runbooks/:id
     * Aktualisiert ein bestehendes Runbook
     */
    async updateRunbook(req, res) {
        try {
            const { id } = req.params;
            const runbookId = parseInt(id, 10);
            const { title, content, affected_systems, tags, steps } = req.body;
            if (isNaN(runbookId)) {
                res.status(400).json({
                    success: false,
                    error: 'Ungültige Runbook-ID'
                });
                return;
            }
            const existingRunbooks = await db_1.db.select().from(schema_1.runbook).where((0, drizzle_orm_1.eq)(schema_1.runbook.id, runbookId));
            const existingRunbook = existingRunbooks[0];
            if (!existingRunbook) {
                res.status(404).json({
                    success: false,
                    error: 'Runbook nicht gefunden'
                });
                return;
            }
            const hasSteps = await hasStepsColumn();
            const updateData = {
                title: (title === null || title === void 0 ? void 0 : title.trim()) || existingRunbook.title,
                content: (content === null || content === void 0 ? void 0 : content.trim()) || existingRunbook.content,
                affected_systems: affected_systems ? JSON.stringify(affected_systems) : existingRunbook.affected_systems,
                category: tags ? JSON.stringify(tags) : existingRunbook.category,
                updated_at: new Date().toISOString()
            };
            if (hasSteps) {
                updateData.steps = steps ? JSON.stringify(steps) : existingRunbook.steps;
            }
            const updateResult = await db_1.db.update(schema_1.runbook)
                .set(updateData)
                .where((0, drizzle_orm_1.eq)(schema_1.runbook.id, runbookId))
                .returning();
            const updatedRunbook = updateResult[0];
            const formattedRunbook = {
                id: updatedRunbook.id,
                title: updatedRunbook.title,
                content: updatedRunbook.content,
                affected_systems: safeJsonParse(updatedRunbook.affected_systems),
                category: safeJsonParse(updatedRunbook.category),
                created_at: updatedRunbook.created_at,
                updated_at: updatedRunbook.updated_at
            };
            res.json({
                success: true,
                data: formattedRunbook
            });
        }
        catch (error) {
            console.error('[RunbookController] Fehler beim Aktualisieren des Runbooks:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Aktualisieren des Runbooks',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * DELETE /api/runbooks/:id
     * Löscht ein Runbook
     */
    async deleteRunbook(req, res) {
        try {
            const { id } = req.params;
            const runbookId = parseInt(id, 10);
            if (isNaN(runbookId)) {
                res.status(400).json({
                    success: false,
                    error: 'Ungültige Runbook-ID'
                });
                return;
            }
            const existingRunbooks = await db_1.db.select().from(schema_1.runbook).where((0, drizzle_orm_1.eq)(schema_1.runbook.id, runbookId));
            const existingRunbook = existingRunbooks[0];
            if (!existingRunbook) {
                res.status(404).json({
                    success: false,
                    error: 'Runbook nicht gefunden'
                });
                return;
            }
            await db_1.db.delete(schema_1.runbook).where((0, drizzle_orm_1.eq)(schema_1.runbook.id, runbookId));
            res.json({
                success: true,
                message: 'Runbook erfolgreich gelöscht'
            });
        }
        catch (error) {
            console.error('[RunbookController] Fehler beim Löschen des Runbooks:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Löschen des Runbooks',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * GET /api/runbooks/search
     * Sucht nach Runbooks basierend auf Query-Parameter
     */
    async searchRunbooks(req, res) {
        try {
            const { q } = req.query;
            if (!q || typeof q !== 'string') {
                res.status(400).json({
                    success: false,
                    error: 'Suchbegriff ist erforderlich'
                });
                return;
            }
            const searchTerm = q.trim().toLowerCase();
            const runbooks = await db_1.db.select().from(schema_1.runbook)
                .where((0, drizzle_orm_1.or)((0, drizzle_orm_1.like)(schema_1.runbook.title, `%${searchTerm}%`), (0, drizzle_orm_1.like)(schema_1.runbook.content, `%${searchTerm}%`), (0, drizzle_orm_1.like)(schema_1.runbook.category, `%${searchTerm}%`)))
                .orderBy((0, drizzle_orm_1.desc)(schema_1.runbook.updated_at));
            const formattedRunbooks = runbooks.map(rb => {
                const hasSteps = stepsColumnExists !== null ? stepsColumnExists : true; // Assume true for search
                const steps = hasSteps && rb.steps ? safeJsonParse(rb.steps) : parseStepsFromContent(rb.content);
                return {
                    id: rb.id,
                    title: rb.title,
                    content: rb.content,
                    affected_systems: safeJsonParse(rb.affected_systems),
                    category: safeJsonParse(rb.category),
                    steps: steps,
                    created_at: rb.created_at,
                    updated_at: rb.updated_at
                };
            });
            res.json({
                success: true,
                data: formattedRunbooks,
                count: formattedRunbooks.length
            });
        }
        catch (error) {
            console.error('[RunbookController] Fehler bei der Runbook-Suche:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler bei der Runbook-Suche',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
}
exports.RunbookController = RunbookController;
exports.default = RunbookController;
