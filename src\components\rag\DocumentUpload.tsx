/**
 * DocumentUpload - RAG Document Upload Component
 * 
 * Allows users to upload documents for RAG processing
 */

import React, { useState, useCallback } from 'react';
import { Upload, FileText, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import FileUpload from '@/components/Animation/kokonutui/file-upload';

interface UploadResult {
  success: boolean;
  documentId?: string;
  chunksCreated?: number;
  embeddingsGenerated?: number;
  title?: string;
  category?: string;
  error?: string;
}

interface DocumentUploadProps {
  onUploadComplete?: (result: UploadResult) => void;
  onUploadSuccess?: () => void;
  className?: string;
}

const CATEGORIES = [
  { value: 'dispatch', label: 'Versand' },
  { value: 'cutting', label: 'Ablängerei' },
  { value: 'incoming-goods', label: 'Wareneingang' },
  { value: 'system', label: 'System' },
  { value: 'quality', label: 'Qualität' },
  { value: 'maintenance', label: 'Wartung' },
  { value: 'app', label: 'App Leitfaden' },
  { value: 'procedures', label: 'Verfahren' },
  { value: 'kpi', label: 'KPI & Metriken' }
];

const SUPPORTED_TYPES = [
  'text/plain',
  'text/markdown',
  'text/x-markdown',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

const SUPPORTED_EXTENSIONS = ['.txt', '.md', '.pdf', '.doc', '.docx'];

// File type patterns for FileUpload component
const ACCEPTED_FILE_TYPES = [
  'text/plain',
  'text/markdown',
  'text/x-markdown',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export const DocumentUpload: React.FC<DocumentUploadProps> = ({
  onUploadComplete,
  onUploadSuccess,
  className = ''
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [title, setTitle] = useState('');
  const [category, setCategory] = useState('');
  const [description, setDescription] = useState('');
  const [language, setLanguage] = useState('de');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [result, setResult] = useState<UploadResult | null>(null);
  // File validation function for kokonutui FileUpload
  const validateFile = useCallback((file: File) => {
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    
    if (!SUPPORTED_TYPES.includes(file.type) && !SUPPORTED_EXTENSIONS.includes(fileExtension)) {
      return {
        message: `Nicht unterstützter Dateityp. Bitte laden Sie TXT, MD, PDF, DOC oder DOCX Dateien hoch.`,
        code: 'INVALID_FILE_TYPE'
      };
    }
    
    return null;
  }, []);

  // Handle successful file selection from kokonutui FileUpload
  const handleFileUploadSuccess = useCallback((uploadedFile: File) => {
    setFile(uploadedFile);
    if (!title) {
      setTitle(uploadedFile.name.replace(/\.[^/.]+$/, ''));
    }
    setResult(null); // Clear any previous errors
  }, [title]);

  // Handle file upload errors from kokonutui FileUpload
  const handleFileUploadError = useCallback((error: { message: string; code: string }) => {
    setResult({
      success: false,
      error: error.message
    });
  }, []);

  const handleUpload = async () => {
    if (!file || !title || !category) {
      setResult({
        success: false,
        error: 'Bitte füllen Sie alle erforderlichen Felder aus'
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('document', file);
      formData.append('title', title);
      formData.append('category', category);
      formData.append('language', language);
      if (description) {
        formData.append('description', description);
      }

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      // Use correct API base URL for development
      const API_BASE_URL = import.meta.env.DEV
        ? 'http://localhost:3001'
        : '';

      const response = await fetch(`${API_BASE_URL}/api/rag/documents/upload`, {
        method: 'POST',
        headers: {
          ...(localStorage.getItem('token') && {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          })
        },
        body: formData
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      const data = await response.json();

      if (response.ok && data.success) {
        const uploadResult: UploadResult = {
          success: true,
          documentId: data.data.documentId,
          chunksCreated: data.data.chunksCreated,
          embeddingsGenerated: data.data.embeddingsGenerated,
          title: data.data.title,
          category: data.data.category
        };

        setResult(uploadResult);
        onUploadComplete?.(uploadResult);
        onUploadSuccess?.();

        // Reset form after a delay to show success message
        setTimeout(() => {
          resetForm();
        }, 3000);
      } else {
        console.error('Upload failed:', data);
        setResult({
          success: false,
          error: data.error || data.details || 'Upload fehlgeschlagen'
        });
      }
    } catch (error) {
      console.error('Upload error:', error);
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Netzwerkfehler beim Upload'
      });
    } finally {
      setIsUploading(false);
    }
  };

  const resetForm = () => {
    setFile(null);
    setTitle('');
    setCategory('');
    setDescription('');
    setResult(null);
    setUploadProgress(0);
  };

  return (
    <Card className={`w-full ${className} border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200`} >
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Dokument hochladen
        </CardTitle>
        <CardDescription className="text-gray-600">
          Laden Sie Dokumente für die Wissensdatenbank hoch.
          Unterstützte Formate: TXT, MD, PDF, DOC, DOCX
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:divide-x lg:divide-gray-200">
          {/* Left Side - File Upload Zone */}
          <div className="space-y-4 lg:pr-8">
            <h3 className="text-lg font-semibold">Datei auswählen</h3>
            <div className="flex flex-col items-center">
              <FileUpload
                onUploadSuccess={handleFileUploadSuccess}
                onUploadError={handleFileUploadError}
                acceptedFileTypes={ACCEPTED_FILE_TYPES}
                maxFileSize={MAX_FILE_SIZE}
                currentFile={file}
                onFileRemove={() => setFile(null)}
                uploadDelay={0} // No simulation, we handle upload manually
                validateFile={validateFile}
                className="w-full max-w-md"
              />
              {/* Pacman Animationen und Text in einer horizontalen Linie */}
              <div className="flex items-center justify-center gap-3 my-2">
                {/* Linke Pacman Animation */}
                <div className="w-10 h-10 flex-shrink-0">
                  <svg
                    viewBox="0 0 100 100"
                    preserveAspectRatio="xMidYMid"
                    className="w-full h-full"
                    style={{ shapeRendering: 'auto', display: 'block', background: 'transparent' }}
                  >
                    <g>
                      <g>
                        <circle
                          fill="#ff7a05"
                          r="4"
                          cy="50"
                          cx="60"
                        >
                          <animate
                            begin="-0.67s"
                            keyTimes="0;1"
                            values="95;35"
                            dur="1s"
                            repeatCount="indefinite"
                            attributeName="cx"
                          />
                          <animate
                            begin="-0.67s"
                            keyTimes="0;0.2;1"
                            values="0;1;1"
                            dur="1s"
                            repeatCount="indefinite"
                            attributeName="fill-opacity"
                          />
                        </circle>
                        <circle
                          fill="#ff7a05"
                          r="4"
                          cy="50"
                          cx="60"
                        >
                          <animate
                            begin="-0.33s"
                            keyTimes="0;1"
                            values="95;35"
                            dur="1s"
                            repeatCount="indefinite"
                            attributeName="cx"
                          />
                          <animate
                            begin="-0.33s"
                            keyTimes="0;0.2;1"
                            values="0;1;1"
                            dur="1s"
                            repeatCount="indefinite"
                            attributeName="fill-opacity"
                          />
                        </circle>
                        <circle
                          fill="#ff7a05"
                          r="4"
                          cy="50"
                          cx="60"
                        >
                          <animate
                            begin="0s"
                            keyTimes="0;1"
                            values="95;35"
                            dur="1s"
                            repeatCount="indefinite"
                            attributeName="cx"
                          />
                          <animate
                            begin="0s"
                            keyTimes="0;0.2;1"
                            values="0;1;1"
                            dur="1s"
                            repeatCount="indefinite"
                            attributeName="fill-opacity"
                          />
                        </circle>
                      </g>
                      <g transform="translate(-15 0)">
                        <path
                          fill="none"
                          stroke="#080808"
                          strokeWidth="2"
                          d="M 20,50 C 20,66.568554 33.431458,79.999987 50,79.999987 66.568542,79.999987 80,66.568554 80,50 H 50"
                        >
                          <animateTransform
                            keyTimes="0;0.5;1"
                            values="0 50 50;45 50 50;0 50 50"
                            dur="1s"
                            repeatCount="indefinite"
                            type="rotate"
                            attributeName="transform"
                          />
                        </path>
                        <path
                          fill="none"
                          stroke="#080808"
                          strokeWidth="2"
                          d="M 20,50 C 20,33.431446 33.431458,20.000013 50,20.000013 66.568542,20.000013 80,33.431446 80,50 m -0.123916,0 H 50"
                        >
                          <animateTransform
                            keyTimes="0;0.5;1"
                            values="0 50 50;-45 50 50;0 50 50"
                            dur="1s"
                            repeatCount="indefinite"
                            type="rotate"
                            attributeName="transform"
                          />
                        </path>
                      </g>
                      <circle
                        fill="#0a0a0a"
                        stroke="#000000"
                        strokeWidth="0"
                        cx="35.344486"
                        cy="32.289963"
                        r="4.5"
                      />
                    </g>
                  </svg>
                </div>
                
                {/* Text in der Mitte */}
                <CardDescription className="text-gray-600 text-center flex-1">
                  JASZ zerlegt das Dokument in Segmente, vektorisiert sie und lernt daraus, um seinen Wissensstand zu erweitern.
                </CardDescription>
                
                {/* Rechte Pacman Animation - horizontal gespiegelt */}
                <div className="w-10 h-10 flex-shrink-0">
                  <svg
                    viewBox="0 0 100 100"
                    preserveAspectRatio="xMidYMid"
                    className="w-full h-full"
                    style={{ shapeRendering: 'auto', display: 'block', background: 'transparent', transform: 'scaleX(-1)' }}
                  >
                    <g>
                      <g>
                        <circle
                          fill="#ff7a05"
                          r="4"
                          cy="50"
                          cx="60"
                        >
                          <animate
                            begin="-0.67s"
                            keyTimes="0;1"
                            values="95;35"
                            dur="1s"
                            repeatCount="indefinite"
                            attributeName="cx"
                          />
                          <animate
                            begin="-0.67s"
                            keyTimes="0;0.2;1"
                            values="0;1;1"
                            dur="1s"
                            repeatCount="indefinite"
                            attributeName="fill-opacity"
                          />
                        </circle>
                        <circle
                          fill="#ff7a05"
                          r="4"
                          cy="50"
                          cx="60"
                        >
                          <animate
                            begin="-0.33s"
                            keyTimes="0;1"
                            values="95;35"
                            dur="1s"
                            repeatCount="indefinite"
                            attributeName="cx"
                          />
                          <animate
                            begin="-0.33s"
                            keyTimes="0;0.2;1"
                            values="0;1;1"
                            dur="1s"
                            repeatCount="indefinite"
                            attributeName="fill-opacity"
                          />
                        </circle>
                        <circle
                          fill="#ff7a05"
                          r="4"
                          cy="50"
                          cx="60"
                        >
                          <animate
                            begin="0s"
                            keyTimes="0;1"
                            values="95;35"
                            dur="1s"
                            repeatCount="indefinite"
                            attributeName="cx"
                          />
                          <animate
                            begin="0s"
                            keyTimes="0;0.2;1"
                            values="0;1;1"
                            dur="1s"
                            repeatCount="indefinite"
                            attributeName="fill-opacity"
                          />
                        </circle>
                      </g>
                      <g transform="translate(-15 0)">
                        <path
                          fill="none"
                          stroke="#080808"
                          strokeWidth="2"
                          d="M 20,50 C 20,66.568554 33.431458,79.999987 50,79.999987 66.568542,79.999987 80,66.568554 80,50 H 50"
                        >
                          <animateTransform
                            keyTimes="0;0.5;1"
                            values="0 50 50;45 50 50;0 50 50"
                            dur="1s"
                            repeatCount="indefinite"
                            type="rotate"
                            attributeName="transform"
                          />
                        </path>
                        <path
                          fill="none"
                          stroke="#080808"
                          strokeWidth="2"
                          d="M 20,50 C 20,33.431446 33.431458,20.000013 50,20.000013 66.568542,20.000013 80,33.431446 80,50 m -0.123916,0 H 50"
                        >
                          <animateTransform
                            keyTimes="0;0.5;1"
                            values="0 50 50;-45 50 50;0 50 50"
                            dur="1s"
                            repeatCount="indefinite"
                            type="rotate"
                            attributeName="transform"
                          />
                        </path>
                      </g>
                      <circle
                        fill="#0a0a0a"
                        stroke="#000000"
                        strokeWidth="0"
                        cx="35.344486"
                        cy="32.289963"
                        r="4.5"
                      />
                    </g>
                  </svg>
                </div>
              </div>
              {/* File Info Display */}
              {file && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg w-full max-w-md">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-green-800 truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-green-600">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setFile(null)}
                      className="text-green-600 hover:text-green-800 hover:bg-green-100"
                    >
                      Entfernen
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Side - Form Fields */}
          <div className="space-y-6 lg:pl-8">
            <h3 className="text-lg font-semibold">Dokumentinformationen</h3>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Titel *</Label>
                <Input
                  className="bg-white"
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Dokumenttitel eingeben"
                  required
                />
              </div>

              <div className="flex gap-4">
                <div className="flex-1 space-y-2">
                  <Label htmlFor="category">Kategorie *</Label>
                  <Select value={category} onValueChange={setCategory} required>
                    <SelectTrigger>
                      <SelectValue placeholder="Kategorie auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {CATEGORIES.map((cat) => (
                        <SelectItem key={cat.value} value={cat.value}>
                          {cat.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex-1 space-y-2">
                  <Label htmlFor="language">Sprache</Label>
                  <Select value={language} onValueChange={setLanguage}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="de">Deutsch</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Beschreibung (optional)</Label>
                <Textarea
                  className="bg-white"
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Kurze Beschreibung des Dokuments"
                  rows={4}
                />
              </div>

              {/* Upload Progress */}
              {isUploading && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Upload läuft...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="w-full" />
                </div>
              )}

              {/* Result Messages */}
              {result && (
                <Alert className={result.success ? 'border-green-500' : 'border-red-500'}>
                  {result.success ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                  <AlertDescription>
                    {result.success ? (
                      <div>
                        <p className="font-medium">Dokument erfolgreich hochgeladen!</p>
                        <p className="text-sm text-gray-600 mt-1">
                          {result.chunksCreated} Textabschnitte erstellt, {result.embeddingsGenerated} Embeddings generiert
                        </p>
                      </div>
                    ) : (
                      <p>{result.error}</p>
                    )}
                  </AlertDescription>
                </Alert>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  onClick={handleUpload}
                  disabled={!file || !title || !category || isUploading}
                  className="flex-1"
                  size="lg"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Wird hochgeladen...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Dokument hochladen
                    </>
                  )}
                </Button>

                <Button
                  variant="warning"
                  onClick={resetForm}
                  disabled={isUploading}
                  size="lg"
                >
                  Zurücksetzen
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DocumentUpload;