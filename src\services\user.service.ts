import { ApiService } from './api.service';

export interface UpdateProfileRequest {
  username?: string;
  email?: string;
  name?: string;
  bio?: string;
}

export interface UserSettings {
  bio?: string;
  fontSize?: string;
  highContrast?: boolean;
  animations?: boolean;
  compactMode?: boolean;
  theme?: string;
  language?: string;
  notifications?: {
    emailNotifications?: boolean;
    pushNotifications?: boolean;
    desktopNotifications?: boolean;
    soundNotifications?: boolean;
    newMessages?: boolean;
    newMentions?: boolean;
    newOrders?: boolean;
    newAlerts?: boolean;
  };
  preferences?: any;
}

export interface UpdateSettingsRequest {
  bio?: string;
  fontSize?: string;
  highContrast?: boolean;
  animations?: boolean;
  compactMode?: boolean;
  theme?: string;
  language?: string;
  notifications?: UserSettings['notifications'];
  preferences?: any;
}

export interface UpdateSettingsResponse {
  success: boolean;
  message: string;
  data?: UserSettings;
  error?: string;
  code?: string;
}

export interface UpdateProfileResponse {
  success: boolean;
  message: string;
  data?: {
    id: number;
    username: string;
    email: string;
    name?: string;
    bio?: string;
    avatar?: string;
    roles: string[];
    updatedAt: string;
  };
  error?: string;
  code?: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface ChangePasswordResponse {
  success: boolean;
  message: string;
  error?: string;
  code?: string;
}

export interface UploadAvatarResponse {
  success: boolean;
  message: string;
  data?: {
    avatar: string;
  };
  error?: string;
  code?: string;
}

export interface DeleteAvatarResponse {
  success: boolean;
  message: string;
  error?: string;
  code?: string;
}

export class UserService {
  private apiService: ApiService;

  constructor() {
    this.apiService = new ApiService();
  }

  /**
   * Updates the current user's profile information
   * @param data Profile update data
   * @returns Promise with update response
   */
  async updateProfile(data: UpdateProfileRequest): Promise<UpdateProfileResponse> {
    try {
      const response = await fetch(`${this.apiService.getBaseUrl()}/user/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      
      // Handle successful API response
      if (result && result.success) {
        // Update stored user data if profile update was successful
        if (result.data) {
          const currentUser = JSON.parse(localStorage.getItem('user_data') || '{}');
          const updatedUser = { ...currentUser, ...result.data };
          localStorage.setItem('user_data', JSON.stringify(updatedUser));
        }
        
        return {
          success: true,
          message: result.message || 'Profil erfolgreich aktualisiert',
          data: result.data
        };
      }
      
      // Handle API error response
      return {
        success: false,
        message: result?.error || result?.message || 'Profil-Update fehlgeschlagen',
        error: result?.error || 'UNKNOWN_ERROR'
      };
    } catch (error: any) {
      // Handle network or other errors
      return {
        success: false,
        message: error.message || 'Profil-Update fehlgeschlagen. Bitte versuchen Sie es später erneut.',
        error: 'NETWORK_ERROR',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Changes the current user's password
   * @param data Password change data
   * @returns Promise with change password response
   */
  async changePassword(data: ChangePasswordRequest): Promise<ChangePasswordResponse> {
    try {
      const response = await fetch(`${this.apiService.getBaseUrl()}/user/change-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      
      // Handle successful API response
      if (result && result.success) {
        return {
          success: true,
          message: result.message || 'Passwort erfolgreich geändert'
        };
      }
      
      // Handle API error response
      return {
        success: false,
        message: result?.error || result?.message || 'Passwort-Änderung fehlgeschlagen',
        error: result?.error || 'UNKNOWN_ERROR'
      };
    } catch (error: any) {
      // Handle network or other errors
      return {
        success: false,
        message: error.message || 'Passwort-Änderung fehlgeschlagen. Bitte versuchen Sie es später erneut.',
        error: 'NETWORK_ERROR',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Gets the current user's profile information
   * @returns Promise with user profile data
   */
  async getProfile(): Promise<UpdateProfileResponse> {
    try {
      const response = await fetch(`${this.apiService.getBaseUrl()}/user/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      const result = await response.json();

      // Handle successful API response
      if (result && result.success) {
        return {
          success: true,
          message: result.message || 'Profil erfolgreich geladen',
          data: result.data
        };
      }

      // Handle API error response
      return {
        success: false,
        message: result?.error || result?.message || 'Profil konnte nicht geladen werden',
        error: result?.error || 'UNKNOWN_ERROR'
      };
    } catch (error: any) {
      // Handle network or other errors
      return {
        success: false,
        message: error.message || 'Profil konnte nicht geladen werden. Bitte versuchen Sie es später erneut.',
        error: 'NETWORK_ERROR',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Gets the current user's settings
   * @returns Promise with user settings data
   */
  async getSettings(): Promise<UpdateSettingsResponse> {
    try {
      const response = await fetch(`${this.apiService.getBaseUrl()}/user/settings`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      const result = await response.json();

      // Handle successful API response
      if (result && result.success) {
        return {
          success: true,
          message: result.message || 'Einstellungen erfolgreich geladen',
          data: result.data
        };
      }

      // Handle API error response
      return {
        success: false,
        message: result?.error || result?.message || 'Einstellungen konnten nicht geladen werden',
        error: result?.error || 'UNKNOWN_ERROR'
      };
    } catch (error: any) {
      // Handle network or other errors
      return {
        success: false,
        message: error.message || 'Einstellungen konnten nicht geladen werden. Bitte versuchen Sie es später erneut.',
        error: 'NETWORK_ERROR',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Updates the current user's settings
   * @param settings Settings update data
   * @returns Promise with update response
   */
  async updateSettings(settings: UpdateSettingsRequest): Promise<UpdateSettingsResponse> {
    try {
      const response = await fetch(`${this.apiService.getBaseUrl()}/user/settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(settings),
      });

      const result = await response.json();

      // Handle successful API response
      if (result && result.success) {
        return {
          success: true,
          message: result.message || 'Einstellungen erfolgreich aktualisiert',
          data: result.data
        };
      }

      // Handle API error response
      return {
        success: false,
        message: result?.error || result?.message || 'Einstellungen-Update fehlgeschlagen',
        error: result?.error || 'UNKNOWN_ERROR'
      };
    } catch (error: any) {
      // Handle network or other errors
      return {
        success: false,
        message: error.message || 'Einstellungen-Update fehlgeschlagen. Bitte versuchen Sie es später erneut.',
        error: 'NETWORK_ERROR',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Uploads an avatar for the current user
   * @param file The avatar file to upload
   * @returns Promise with upload response
   */
  async uploadAvatar(file: File): Promise<UploadAvatarResponse> {
    try {
      const formData = new FormData();
      formData.append('avatar', file);

      const response = await fetch(`${this.apiService.getBaseUrl()}/user/avatar`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: formData,
      });

      const result = await response.json();

      // Handle successful API response
      if (result && result.success) {
        // Update stored user data if avatar upload was successful
        if (result.data?.avatar) {
          const currentUser = JSON.parse(localStorage.getItem('user_data') || '{}');
          const updatedUser = { ...currentUser, avatar: result.data.avatar };
          localStorage.setItem('user_data', JSON.stringify(updatedUser));
        }

        return {
          success: true,
          message: result.message || 'Avatar erfolgreich hochgeladen',
          data: result.data
        };
      }

      // Handle API error response
      return {
        success: false,
        message: result?.error || result?.message || 'Avatar-Upload fehlgeschlagen',
        error: result?.error || 'UNKNOWN_ERROR'
      };
    } catch (error: any) {
      // Handle network or other errors
      return {
        success: false,
        message: error.message || 'Avatar-Upload fehlgeschlagen. Bitte versuchen Sie es später erneut.',
        error: 'NETWORK_ERROR',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Deletes the current user's avatar
   * @returns Promise with delete response
   */
  async deleteAvatar(): Promise<DeleteAvatarResponse> {
    try {
      const response = await fetch(`${this.apiService.getBaseUrl()}/user/avatar`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      const result = await response.json();

      // Handle successful API response
      if (result && result.success) {
        // Update stored user data if avatar deletion was successful
        const currentUser = JSON.parse(localStorage.getItem('user_data') || '{}');
        const updatedUser = { ...currentUser, avatar: null };
        localStorage.setItem('user_data', JSON.stringify(updatedUser));

        return {
          success: true,
          message: result.message || 'Avatar erfolgreich entfernt'
        };
      }

      // Handle API error response
      return {
        success: false,
        message: result?.error || result?.message || 'Avatar-Löschung fehlgeschlagen',
        error: result?.error || 'UNKNOWN_ERROR'
      };
    } catch (error: any) {
      // Handle network or other errors
      return {
        success: false,
        message: error.message || 'Avatar-Löschung fehlgeschlagen. Bitte versuchen Sie es später erneut.',
        error: 'NETWORK_ERROR',
        code: 'NETWORK_ERROR'
      };
    }
  }
}

export const userService = new UserService();