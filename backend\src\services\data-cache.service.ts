/**
 * Data Cache Service
 * 
 * Implements intelligent caching for frequently requested data combinations
 * to improve performance of the AI chatbot database integration.
 */

import { EventEmitter } from 'events';
import { QueryIntent, QueryResult, DateRange } from '../types/data-enrichment.types';
import performanceMonitor from './performance-monitoring.service';

export interface CacheEntry<T = any> {
  key: string;
  data: T;
  timestamp: Date;
  ttl: number; // Time to live in milliseconds
  accessCount: number;
  lastAccessed: Date;
  size: number; // Approximate size in bytes
  tags: string[]; // For cache invalidation
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  averageAccessTime: number;
  topKeys: Array<{ key: string; accessCount: number; hitRate: number }>;
}

export interface CacheConfig {
  maxSize: number; // Maximum cache size in bytes
  maxEntries: number; // Maximum number of entries
  defaultTTL: number; // Default TTL in milliseconds
  cleanupInterval: number; // Cleanup interval in milliseconds
  enableMetrics: boolean;
}

const DEFAULT_CONFIG: CacheConfig = {
  maxSize: 200 * 1024 * 1024, // 200MB - increased for better performance
  maxEntries: 2000, // Increased from 1000
  defaultTTL: 30 * 60 * 1000, // 30 minutes - increased for better cache hit rate
  cleanupInterval: 60 * 1000, // 1 minute
  enableMetrics: true
};

export class DataCacheService extends EventEmitter {
  private cache = new Map<string, CacheEntry>();
  private config: CacheConfig;
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalAccessTime: 0,
    accessCount: 0
  };
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: Partial<CacheConfig> = {}) {
    super();
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.startCleanupTimer();
  }

  /**
   * Generate cache key for query intents
   */
  generateQueryKey(intents: QueryIntent[], additionalParams: Record<string, any> = {}): string {
    const intentSignature = intents
      .map(intent => ({
        type: intent.type,
        timeRange: intent.timeRange,
        specificMetrics: intent.specificMetrics?.sort()
      }))
      .sort((a, b) => a.type.localeCompare(b.type));

    const keyData = {
      intents: intentSignature,
      params: additionalParams
    };

    return `query_${this.hashObject(keyData)}`;
  }

  /**
   * Generate cache key for enriched context
   */
  generateContextKey(message: string, intents: QueryIntent[]): string {
    const normalizedMessage = message.toLowerCase().trim();
    const intentTypes = intents.map(i => i.type).sort();
    const timeRanges = intents.map(i => i.timeRange).filter(Boolean);

    const keyData = {
      messageHash: this.hashString(normalizedMessage),
      intentTypes,
      timeRanges
    };

    return `context_${this.hashObject(keyData)}`;
  }

  /**
   * Get cached data
   */
  async get<T>(key: string): Promise<T | null> {
    const startTime = Date.now();
    
    try {
      const entry = this.cache.get(key);
      
      if (!entry) {
        this.stats.misses++;
        this.recordAccessTime(Date.now() - startTime);
        
        if (this.config.enableMetrics) {
          performanceMonitor.recordQueryPerformance('combined', Date.now() - startTime, false, {
            cacheHit: false
          });
        }
        
        return null;
      }

      // Check if entry has expired
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        this.stats.misses++;
        this.recordAccessTime(Date.now() - startTime);
        
        if (this.config.enableMetrics) {
          performanceMonitor.recordQueryPerformance('combined', Date.now() - startTime, false, {
            cacheHit: false
          });
        }
        
        return null;
      }

      // Update access statistics
      entry.accessCount++;
      entry.lastAccessed = new Date();
      
      this.stats.hits++;
      this.recordAccessTime(Date.now() - startTime);
      
      if (this.config.enableMetrics) {
        performanceMonitor.recordQueryPerformance('combined', Date.now() - startTime, true, {
          cacheHit: true,
          dataSize: entry.size
        });
      }

      this.emit('hit', { key, entry });
      
      return entry.data as T;
      
    } catch (error) {
      console.error(`[CACHE] Error retrieving key ${key}:`, error);
      this.stats.misses++;
      this.recordAccessTime(Date.now() - startTime);
      return null;
    }
  }

  /**
   * Set cached data
   */
  async set<T>(
    key: string, 
    data: T, 
    options: {
      ttl?: number;
      tags?: string[];
    } = {}
  ): Promise<boolean> {
    try {
      const ttl = options.ttl || this.config.defaultTTL;
      const tags = options.tags || [];
      const size = this.estimateSize(data);

      // Check if we need to make space
      if (this.shouldEvict(size)) {
        this.evictEntries(size);
      }

      const entry: CacheEntry<T> = {
        key,
        data,
        timestamp: new Date(),
        ttl,
        accessCount: 0,
        lastAccessed: new Date(),
        size,
        tags
      };

      this.cache.set(key, entry);
      this.emit('set', { key, entry });
      
      return true;
      
    } catch (error) {
      console.error(`[CACHE] Error setting key ${key}:`, error);
      return false;
    }
  }

  /**
   * Cache query results with intelligent key generation
   */
  async cacheQueryResults(
    intents: QueryIntent[], 
    results: QueryResult[], 
    additionalParams: Record<string, any> = {}
  ): Promise<string> {
    const key = this.generateQueryKey(intents, additionalParams);
    
    // Determine TTL based on data type and freshness requirements
    let ttl = this.config.defaultTTL;
    
    // Störungen data changes more frequently
    if (intents.some(i => i.type === 'stoerungen')) {
      ttl = Math.min(ttl, 2 * 60 * 1000); // 2 minutes max
    }
    
    // Time-specific queries should have shorter TTL
    if (intents.some(i => i.timeRange)) {
      ttl = Math.min(ttl, 3 * 60 * 1000); // 3 minutes max
    }

    const tags = [
      ...intents.map(i => `intent:${i.type}`),
      ...results.map(r => `dataType:${r.dataType}`),
      'queryResults'
    ];

    await this.set(key, results, { ttl, tags });
    return key;
  }

  /**
   * Cache enriched context
   */
  async cacheEnrichedContext(
    message: string,
    intents: QueryIntent[],
    context: any,
    ttl?: number
  ): Promise<string> {
    const key = this.generateContextKey(message, intents);
    
    const tags = [
      ...intents.map(i => `intent:${i.type}`),
      'enrichedContext'
    ];

    await this.set(key, context, { ttl, tags });
    return key;
  }

  /**
   * Get cached query results
   */
  async getCachedQueryResults(
    intents: QueryIntent[], 
    additionalParams: Record<string, any> = {}
  ): Promise<QueryResult[] | null> {
    const key = this.generateQueryKey(intents, additionalParams);
    return this.get<QueryResult[]>(key);
  }

  /**
   * Get cached enriched context
   */
  async getCachedEnrichedContext(
    message: string,
    intents: QueryIntent[]
  ): Promise<any | null> {
    const key = this.generateContextKey(message, intents);
    return this.get(key);
  }

  /**
   * Invalidate cache entries by tags
   */
  invalidateByTags(tags: string[]): number {
    let invalidatedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key);
        invalidatedCount++;
      }
    }
    
    this.emit('invalidate', { tags, count: invalidatedCount });
    return invalidatedCount;
  }

  /**
   * Invalidate cache entries by data type
   */
  invalidateByDataType(dataType: 'stoerungen' | 'dispatch' | 'cutting'): number {
    return this.invalidateByTags([`dataType:${dataType}`, `intent:${dataType}`]);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    const count = this.cache.size;
    this.cache.clear();
    this.emit('clear', { count });
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? this.stats.hits / totalRequests : 0;
    const missRate = totalRequests > 0 ? this.stats.misses / totalRequests : 0;
    
    const totalSize = Array.from(this.cache.values())
      .reduce((sum, entry) => sum + entry.size, 0);
    
    const averageAccessTime = this.stats.accessCount > 0 
      ? this.stats.totalAccessTime / this.stats.accessCount 
      : 0;

    // Calculate top keys by access count
    const topKeys = Array.from(this.cache.entries())
      .map(([key, entry]) => ({
        key,
        accessCount: entry.accessCount,
        hitRate: entry.accessCount > 0 ? 1 : 0 // Simplified hit rate per key
      }))
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, 10);

    return {
      totalEntries: this.cache.size,
      totalSize,
      hitRate,
      missRate,
      evictionCount: this.stats.evictions,
      averageAccessTime,
      topKeys
    };
  }

  /**
   * Get frequently requested data patterns
   */
  getFrequentPatterns(): Array<{
    pattern: string;
    frequency: number;
    averageResponseTime: number;
    cacheEffectiveness: number;
  }> {
    const patterns = new Map<string, {
      count: number;
      totalTime: number;
      cacheHits: number;
    }>();

    // Analyze cache entries to identify patterns
    for (const [key, entry] of this.cache.entries()) {
      let pattern = 'unknown';
      
      if (key.startsWith('query_')) {
        if (entry.tags.includes('intent:stoerungen')) pattern = 'störungen_queries';
        else if (entry.tags.includes('intent:dispatch')) pattern = 'dispatch_queries';
        else if (entry.tags.includes('intent:cutting')) pattern = 'cutting_queries';
        else pattern = 'mixed_queries';
      } else if (key.startsWith('context_')) {
        pattern = 'context_enrichment';
      }

      if (!patterns.has(pattern)) {
        patterns.set(pattern, { count: 0, totalTime: 0, cacheHits: 0 });
      }

      const patternData = patterns.get(pattern)!;
      patternData.count++;
      patternData.cacheHits += entry.accessCount;
    }

    return Array.from(patterns.entries()).map(([pattern, data]) => ({
      pattern,
      frequency: data.count,
      averageResponseTime: data.count > 0 ? data.totalTime / data.count : 0,
      cacheEffectiveness: data.count > 0 ? data.cacheHits / data.count : 0
    }));
  }

  /**
   * Optimize cache based on usage patterns
   */
  optimizeCache(): {
    evicted: number;
    optimizations: string[];
  } {
    const optimizations: string[] = [];
    let evicted = 0;

    // Remove rarely accessed entries
    const cutoffTime = new Date(Date.now() - 30 * 60 * 1000); // 30 minutes ago
    const lowAccessThreshold = 2;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < cutoffTime && entry.accessCount < lowAccessThreshold) {
        this.cache.delete(key);
        evicted++;
      }
    }

    if (evicted > 0) {
      optimizations.push(`Removed ${evicted} rarely accessed entries`);
    }

    // Adjust TTL for frequently accessed items with improved strategy
    let ttlAdjustments = 0;
    for (const [key, entry] of this.cache.entries()) {
      if (entry.accessCount > 10) {
        // Extend TTL for frequently accessed items with exponential backoff
        const multiplier = Math.min(1 + (entry.accessCount / 100), 3); // Max 3x multiplier
        entry.ttl = Math.min(entry.ttl * multiplier, 60 * 60 * 1000); // Max 1 hour
        ttlAdjustments++;
      }
    }

    if (ttlAdjustments > 0) {
      optimizations.push(`Extended TTL for ${ttlAdjustments} frequently accessed entries`);
    }

    this.emit('optimize', { evicted, optimizations });

    return { evicted, optimizations };
  }

  /**
   * Get cache performance insights
   */
  getPerformanceInsights(): {
    efficiency: 'excellent' | 'good' | 'poor';
    recommendations: string[];
    bottlenecks: string[];
  } {
    const stats = this.getStats();
    const recommendations: string[] = [];
    const bottlenecks: string[] = [];

    // Analyze cache efficiency
    let efficiency: 'excellent' | 'good' | 'poor' = 'poor';
    if (stats.hitRate >= 0.8) {
      efficiency = 'excellent';
    } else if (stats.hitRate >= 0.5) {
      efficiency = 'good';
    }

    // Generate recommendations based on stats
    if (stats.hitRate === 0) {
      recommendations.push('CRITICAL: Cache hit rate is 0% - investigate cache key consistency');
      bottlenecks.push('Cache keys do not match query patterns');
    }

    if (stats.hitRate < 0.5) {
      recommendations.push('Improve cache hit rate through better key strategies');
      bottlenecks.push('Low cache utilization');
    }

    if (stats.totalSize > this.config.maxSize * 0.8) {
      recommendations.push('Consider increasing cache size or implementing eviction policies');
      bottlenecks.push('High memory usage');
    }

    if (stats.evictionCount > stats.totalEntries * 0.1) {
      recommendations.push('Reduce cache churn through longer TTL values');
      bottlenecks.push('High entry turnover');
    }

    return {
      efficiency,
      recommendations,
      bottlenecks
    };
  }

  /**
   * Pre-warm cache with common queries
   */
  async prewarmCache(): Promise<void> {
    try {
      // Pre-warm with common query patterns using simple objects
      const commonQueries = [
        { type: 'stoerungen', timeRange: '24hours' },
        { type: 'dispatch', timeRange: '24hours' },
        { type: 'cutting', timeRange: '24hours' }
      ];

      for (const query of commonQueries) {
        const key = this.generateQueryKey([query as any], {});
        // Only cache if not already present
        const existing = await this.get(key);
        if (!existing) {
          // Simulate query result for pre-warming
          const mockResult = [{
            id: `prewarm_${query.type}_${Date.now()}`,
            type: query.type,
            title: `Pre-warmed ${query.type} data`,
            description: 'Pre-warmed cache entry',
            priority: 'low',
            status: 'completed',
            timestamp: new Date().toISOString()
          }];

          await this.set(key, mockResult, {
            ttl: 60 * 60 * 1000, // 1 hour for pre-warmed data
            tags: [`intent:${query.type}`, 'prewarmed', 'queryResults']
          });
        }
      }

      console.log('Cache pre-warming completed');
    } catch (error) {
      console.warn('Cache pre-warming failed:', error);
    }
  }

  // Private methods

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp.getTime() > entry.ttl;
  }

  private shouldEvict(newEntrySize: number): boolean {
    const currentSize = Array.from(this.cache.values())
      .reduce((sum, entry) => sum + entry.size, 0);
    
    return (
      this.cache.size >= this.config.maxEntries ||
      currentSize + newEntrySize > this.config.maxSize
    );
  }

  private evictEntries(requiredSpace: number): void {
    // LRU eviction strategy with size consideration
    const entries = Array.from(this.cache.entries())
      .sort((a, b) => a[1].lastAccessed.getTime() - b[1].lastAccessed.getTime());

    let freedSpace = 0;
    let evictedCount = 0;

    for (const [key, entry] of entries) {
      if (freedSpace >= requiredSpace && evictedCount >= 10) {
        break; // Don't evict too many at once
      }

      this.cache.delete(key);
      freedSpace += entry.size;
      evictedCount++;
      this.stats.evictions++;
    }

    this.emit('evict', { count: evictedCount, freedSpace });
  }

  private estimateSize(data: any): number {
    try {
      return JSON.stringify(data).length * 2; // Rough estimate (UTF-16)
    } catch {
      return 1024; // Default size if estimation fails
    }
  }

  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  private hashObject(obj: any): string {
    return this.hashString(JSON.stringify(obj));
  }

  private recordAccessTime(time: number): void {
    this.stats.totalAccessTime += time;
    this.stats.accessCount++;
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  private cleanup(): void {
    let cleanedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.emit('cleanup', { count: cleanedCount });
    }
  }

  /**
   * Destroy the cache service
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.clear();
    this.removeAllListeners();
  }
}

// Singleton instance
export const dataCache = new DataCacheService();
export default dataCache;