"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const router = express_1.default.Router();
// Helper function to build Drizzle date conditions
const buildDateConditions = (dateColumn, startDate, endDate) => {
    const conditions = [];
    if (startDate) {
        conditions.push((0, drizzle_orm_1.gte)(dateColumn, new Date(startDate)));
    }
    if (endDate) {
        conditions.push((0, drizzle_orm_1.lte)(dateColumn, new Date(endDate)));
    }
    return conditions;
};
router.get('/service-level', async (req, res) => {
    console.log('✅ Service-Level Route aufgerufen');
    try {
        const { startDate, endDate } = req.query;
        const dateConditions = buildDateConditions(schema_1.dispatchData.datum, startDate, endDate);
        const serviceData = await db_1.db.select({
            datum: schema_1.dispatchData.datum,
            servicegrad: schema_1.dispatchData.servicegrad
        })
            .from(schema_1.dispatchData)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.datum), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.servicegrad), ...dateConditions))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
            .limit(30);
        const transformedData = serviceData.map((item) => ({
            datum: item.datum,
            csr: item.servicegrad ? Math.round(item.servicegrad * 100) : 0
        }));
        console.log(`✅ Erfolgreich ${transformedData.length} Service-Level Einträge abgerufen`);
        res.json({
            success: true,
            data: transformedData
        });
    }
    catch (error) {
        console.error('❌ Fehler beim Service-Level Abruf:', error);
        res.status(500).json({
            success: false,
            error: 'Service-Level Fehler'
        });
    }
});
router.get('/atrl', async (req, res) => {
    console.log('✅ ATrL Route aufgerufen');
    try {
        const { startDate, endDate } = req.query;
        const dateConditions = buildDateConditions(schema_1.dispatchData.datum, startDate, endDate);
        const atrlData = await db_1.db.select({
            datum: schema_1.dispatchData.datum,
            atrl: schema_1.dispatchData.atrl
        })
            .from(schema_1.dispatchData)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.datum), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.atrl), ...dateConditions))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
            .limit(30);
        const transformedData = atrlData.map((item) => ({
            Datum: item.datum,
            weAtrl: item.atrl || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim ATrL Abruf:', error);
        res.status(500).json({ success: false, error: 'ATrL Fehler' });
    }
});
router.get('/aril', async (req, res) => {
    console.log('✅ ARiL Route aufgerufen');
    try {
        const { startDate, endDate } = req.query;
        const dateConditions = buildDateConditions(schema_1.dispatchData.datum, startDate, endDate);
        const arilData = await db_1.db.select({
            datum: schema_1.dispatchData.datum,
            aril: schema_1.dispatchData.aril,
            fuellgrad_aril: schema_1.dispatchData.fuellgrad_aril
        })
            .from(schema_1.dispatchData)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.datum), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.aril), ...dateConditions))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
            .limit(30);
        const transformedData = arilData.map((item) => ({
            Datum: item.datum,
            aril: item.aril || 0,
            fuellgrad: item.fuellgrad_aril || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim ARiL Abruf:', error);
        res.status(500).json({ success: false, error: 'ARiL Fehler' });
    }
});
router.get('/we', async (req, res) => {
    console.log('✅ WE (Wareneingang) Route aufgerufen');
    try {
        const { startDate, endDate } = req.query;
        const conditions = buildDateConditions(schema_1.we.datum, startDate, endDate);
        let weQuery = db_1.db.select({
            datum: schema_1.we.datum,
            weAtrl: schema_1.we.weAtrl,
            weManl: schema_1.we.weManl
        }).from(schema_1.we);
        if (conditions.length > 0) {
            weQuery = weQuery.where((0, drizzle_orm_1.and)(...conditions));
        }
        const weData = await weQuery
            .orderBy((0, drizzle_orm_1.desc)(schema_1.we.datum))
            .limit(30);
        const transformedData = weData.map((item) => ({
            id: Math.random(), // Temporäre ID
            datum: item.datum,
            weAtrl: item.weAtrl || 0,
            weManl: item.weManl || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim WE Abruf:', error);
        res.status(500).json({ success: false, error: 'WE Fehler' });
    }
});
router.get('/lagerauslastung200', async (req, res) => {
    console.log('✅ Lagerauslastung 200 Route aufgerufen');
    try {
        const { startDate, endDate } = req.query;
        const conditions = buildDateConditions(schema_1.bestand200.aufnahmeDatum, startDate, endDate);
        let lagerQuery = db_1.db.select({
            aufnahmeDatum: schema_1.bestand200.aufnahmeDatum,
            auslastung: schema_1.bestand200.auslastung,
            auslastungA: schema_1.bestand200.auslastungA,
            auslastungB: schema_1.bestand200.auslastungB,
            auslastungC: schema_1.bestand200.auslastungC,
            maxPlaetze: schema_1.bestand200.maxPlaetze
        }).from(schema_1.bestand200);
        if (conditions.length > 0) {
            lagerQuery = lagerQuery.where((0, drizzle_orm_1.and)(...conditions));
        }
        const lagerData = await lagerQuery
            .orderBy((0, drizzle_orm_1.desc)(schema_1.bestand200.aufnahmeDatum))
            .limit(100);
        const transformedData = lagerData.map((item) => ({
            aufnahmeDatum: item.aufnahmeDatum,
            gesamt: parseFloat(item.auslastung) || 0,
            maxPlaetze: parseInt(item.maxPlaetze) || 0,
            auslastungA: parseFloat(item.auslastungA) || 0,
            auslastungB: parseFloat(item.auslastungB) || 0,
            auslastungC: parseFloat(item.auslastungC) || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim Lagerauslastung 200 Abruf:', error);
        res.status(500).json({ success: false, error: 'Lagerauslastung 200 Fehler' });
    }
});
router.get('/daily-performance', async (req, res) => {
    console.log('✅ Daily-Performance Route aufgerufen');
    try {
        const performanceData = await db_1.db.select({
            datum: schema_1.dispatchData.datum,
            produzierte_tonnagen: schema_1.dispatchData.produzierte_tonnagen
        })
            .from(schema_1.dispatchData)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
            .limit(30);
        const transformedData = performanceData.map((item) => ({
            datum: item.datum,
            value: item.produzierte_tonnagen || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim Daily-Performance Abruf:', error);
        res.status(500).json({ success: false, error: 'Daily-Performance Fehler' });
    }
});
router.get('/picking', async (req, res) => {
    console.log('✅ Picking Route aufgerufen');
    try {
        const pickingData = [
            { name: 'Kategorie A', value: 45 },
            { name: 'Kategorie B', value: 30 },
            { name: 'Kategorie C', value: 25 }
        ];
        res.json({ success: true, data: pickingData });
    }
    catch (error) {
        console.error('❌ Fehler beim Picking Abruf:', error);
        res.status(500).json({ success: false, error: 'Picking Fehler' });
    }
});
router.get('/returns', async (req, res) => {
    console.log('✅ Returns Route aufgerufen');
    try {
        const returnsData = [
            { name: 'Grund A', value: 15 },
            { name: 'Grund B', value: 10 },
            { name: 'Grund C', value: 5 }
        ];
        res.json({ success: true, data: returnsData });
    }
    catch (error) {
        console.error('❌ Fehler beim Returns Abruf:', error);
        res.status(500).json({ success: false, error: 'Returns Fehler' });
    }
});
router.get('/delivery-positions', async (req, res) => {
    console.log('✅ Delivery-Positions Route aufgerufen');
    try {
        const { startDate, endDate } = req.query;
        // Basis-Query für dispatch_data mit korrekter Typisierung
        // Verwende bedingte Query-Erstellung statt Neuzuweisung
        let deliveryData;
        if (startDate && endDate) {
            const dateConditions = buildDateConditions(schema_1.dispatchData.datum, startDate, endDate);
            if (dateConditions.length > 0) {
                // Query mit Datumsfilterung - alle Bedingungen in einer where-Klausel
                deliveryData = await db_1.db.select({
                    datum: schema_1.dispatchData.datum,
                    ausgeliefert_lup: schema_1.dispatchData.ausgeliefert_lup,
                    rueckstaendig: schema_1.dispatchData.rueckstaendig
                })
                    .from(schema_1.dispatchData)
                    .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.ausgeliefert_lup), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.rueckstaendig), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.datum), ...dateConditions))
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
                    .limit(100);
            }
            else {
                // Fallback ohne Datumsfilterung wenn dateConditions leer
                deliveryData = await db_1.db.select({
                    datum: schema_1.dispatchData.datum,
                    ausgeliefert_lup: schema_1.dispatchData.ausgeliefert_lup,
                    rueckstaendig: schema_1.dispatchData.rueckstaendig
                })
                    .from(schema_1.dispatchData)
                    .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.ausgeliefert_lup), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.rueckstaendig)))
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
                    .limit(100);
            }
        }
        else {
            // Query ohne Datumsfilterung
            deliveryData = await db_1.db.select({
                datum: schema_1.dispatchData.datum,
                ausgeliefert_lup: schema_1.dispatchData.ausgeliefert_lup,
                rueckstaendig: schema_1.dispatchData.rueckstaendig
            })
                .from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.ausgeliefert_lup), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.rueckstaendig)))
                .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
                .limit(100);
        }
        const transformedData = deliveryData.map((item) => ({
            date: item.datum,
            ausgeliefert_lup: item.ausgeliefert_lup || 0,
            rueckstaendig: item.rueckstaendig || 0
        }));
        console.log(`📊 Delivery-Positions: ${transformedData.length} Datensätze gefunden`);
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim Delivery-Positions Abruf:', error);
        res.status(500).json({ success: false, error: 'Delivery-Positions Fehler' });
    }
});
router.get('/tagesleistung', async (req, res) => {
    console.log('✅ Tagesleistung Route aufgerufen');
    try {
        const tagesleistungData = await db_1.db.select({
            datum: schema_1.dispatchData.datum,
            produzierte_tonnagen: schema_1.dispatchData.produzierte_tonnagen,
            direktverladung_kiaa: schema_1.dispatchData.direktverladung_kiaa,
            umschlag: schema_1.dispatchData.umschlag,
            kg_pro_colli: schema_1.dispatchData.kg_pro_colli,
            elefanten: schema_1.dispatchData.elefanten
        })
            .from(schema_1.dispatchData)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
            .limit(10);
        const transformedData = tagesleistungData.map((item) => ({
            date: item.datum,
            produzierte_tonnagen: item.produzierte_tonnagen || 0,
            direktverladung_kiaa: item.direktverladung_kiaa || 0,
            umschlag: item.umschlag || 0,
            kg_pro_colli: item.kg_pro_colli || 0,
            elefanten: item.elefanten || 0
        }));
        res.json({ success: true, data: transformedData });
    }
    catch (error) {
        console.error('❌ Fehler beim Tagesleistung Abruf:', error);
        res.status(500).json({ success: false, error: 'Tagesleistung Fehler' });
    }
});
exports.default = router;
