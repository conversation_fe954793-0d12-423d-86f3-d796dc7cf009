/**
 * Frontend Database Service
 * 
 * Ersetzt den direkten Datenbankzugriff durch API-Aufrufe an das Backend.
 * Stellt eine kompatible Schnittstelle für bestehende Frontend-Komponenten bereit.
 */

import { backendApiService, SystemFTSDataPoint, KnowledgeBase, ServiceHealth } from './api/backend-api.service';

/**
 * Simple Cache für Frontend-Daten
 */
const cache = new Map<string, { data: any; expires: number }>();

const getCached = <T>(key: string): T | null => {
  const entry = cache.get(key);
  if (entry && entry.expires > Date.now()) {
    return entry.data;
  }
  cache.delete(key);
  return null;
};

const setCache = <T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void => {
  cache.set(key, {
    data,
    expires: Date.now() + ttl
  });
};

/**
 * Frontend Database Service Klasse
 * 
 * Stellt eine kompatible API für bestehende Frontend-Komponenten bereit,
 * leitet aber alle Anfragen an das Backend weiter.
 */
export class DatabaseService {
  private static instance: DatabaseService;

  private constructor() {}

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * Testet die Verbindung zum Backend
   */
  async connect(): Promise<boolean> {
    try {
      console.log('Teste Backend-API-Verbindung...');
      const isConnected = await backendApiService.connect();
      
      if (isConnected) {
        console.log('✅ Backend-API-Verbindung erfolgreich');
      } else {
        console.log('❌ Backend-API-Verbindung fehlgeschlagen');
      }
      
      return isConnected;
    } catch (error) {
      console.error('Fehler bei Backend-API-Verbindung:', error);
      return false;
    }
  }

  /**
   * System FTS Verfügbarkeitsdaten mit Cache-Support
   */
  async getSystemFTSData(dateRange?: { startDate?: string; endDate?: string }): Promise<SystemFTSDataPoint[]> {
    const cacheKey = `system-fts-${JSON.stringify(dateRange || {})}`;
    const cached = getCached<SystemFTSDataPoint[]>(cacheKey);
    if (cached) return cached;

    try {
      console.log('System FTS Daten von Backend-API laden...');
      const data = await backendApiService.getSystemFTSData(dateRange);
      
      setCache(cacheKey, data, 5 * 60 * 1000); // 5 Minuten Cache
      console.log(`✅ ${data.length} System FTS Datensätze geladen`);
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der System FTS Daten:', error);
      throw error;
    }
  }

  /**
   * Knowledge Bases abrufen (RAG Database)
   */
  async getKnowledgeBases(): Promise<KnowledgeBase[]> {
    const cacheKey = 'knowledge-bases';
    const cached = getCached<KnowledgeBase[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await backendApiService.getKnowledgeBases();
      setCache(cacheKey, data, 5 * 60 * 1000); // 5 Minuten Cache
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Knowledge Bases:', error);
      throw error;
    }
  }

  /**
   * Service Health Status abrufen
   */
  async getServiceHealth(): Promise<ServiceHealth> {
    try {
      return await backendApiService.getServiceHealth();
    } catch (error) {
      console.error('Fehler beim Abrufen des Service Health Status:', error);
      throw error;
    }
  }

  /**
   * System-Statistiken abrufen
   */
  async getSystemStats(): Promise<any> {
    const cacheKey = 'system-stats';
    const cached = getCached<any>(cacheKey);
    if (cached) return cached;

    try {
      const data = await backendApiService.getSystemStats();
      setCache(cacheKey, data, 2 * 60 * 1000); // 2 Minuten Cache
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Systemstatistiken:', error);
      throw error;
    }
  }

  /**
   * Cache-Statistiken abrufen
   */
  getCacheStats(): any {
    return {
      size: cache.size,
      entries: Array.from(cache.keys()),
      frontend_cache: true
    };
  }

  /**
   * Service Level Daten abrufen
   */
  async getServiceLevelData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const cacheKey = `service-level-${JSON.stringify(dateRange || {})}`;
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await backendApiService.getServiceLevelData(dateRange);
      setCache(cacheKey, data, 2 * 60 * 1000); // 2 Minuten Cache
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Service Level Daten:', error);
      throw error;
    }
  }

  /**
   * Picking Daten abrufen
   */
  async getPickingData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const cacheKey = `picking-${JSON.stringify(dateRange || {})}`;
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await backendApiService.getPickingData(dateRange);
      setCache(cacheKey, data, 2 * 60 * 1000); // 2 Minuten Cache
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Picking Daten:', error);
      throw error;
    }
  }

  /**
   * Returns Daten abrufen
   */
  async getReturnsData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const cacheKey = `returns-${JSON.stringify(dateRange || {})}`;
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await backendApiService.getReturnsData(dateRange);
      setCache(cacheKey, data, 2 * 60 * 1000); // 2 Minuten Cache
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Returns Daten:', error);
      throw error;
    }
  }

  /**
   * Delivery Positions Daten abrufen
   */
  async getDeliveryPositionsData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const cacheKey = `delivery-positions-${JSON.stringify(dateRange || {})}`;
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await backendApiService.getDeliveryPositionsData(dateRange);
      setCache(cacheKey, data, 2 * 60 * 1000); // 2 Minuten Cache
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Delivery Positions Daten:', error);
      throw error;
    }
  }

  /**
   * Tagesleistung Daten abrufen
   */
  async getTagesleistungData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const cacheKey = `tagesleistung-${JSON.stringify(dateRange || {})}`;
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await backendApiService.getTagesleistungData(dateRange);
      setCache(cacheKey, data, 2 * 60 * 1000); // 2 Minuten Cache
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Tagesleistung Daten:', error);
      throw error;
    }
  }

  /**
   * Ablaengerei Daten abrufen
   */
  async getAblaengereiData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const cacheKey = `ablaengerei-${JSON.stringify(dateRange || {})}`;
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await backendApiService.getAblaengereiData(dateRange);
      setCache(cacheKey, data, 5 * 60 * 1000); // 5 Minuten Cache
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Ablaengerei Daten:', error);
      throw error;
    }
  }

  /**
   * WE Daten abrufen
   */
  async getWEData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const cacheKey = `we-data-${JSON.stringify(dateRange || {})}`;
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await backendApiService.getWEData(dateRange);
      setCache(cacheKey, data, 5 * 60 * 1000); // 5 Minuten Cache
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der WE Daten:', error);
      throw error;
    }
  }

  /**
   * Materialdaten abrufen
   */
  async getMaterialdaten(): Promise<any[]> {
    const cacheKey = 'materialdaten';
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await backendApiService.getMaterialdaten();
      setCache(cacheKey, data, 15 * 60 * 1000); // 15 Minuten Cache für Stammdaten
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Materialdaten:', error);
      throw error;
    }
  }

  /**
   * Trommeldaten abrufen
   */
  async getTrommeldaten(): Promise<any[]> {
    const cacheKey = 'trommeldaten';
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await backendApiService.getTrommeldaten();
      setCache(cacheKey, data, 15 * 60 * 1000); // 15 Minuten Cache für Stammdaten
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Trommeldaten:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();
export default databaseService;
