"use strict";
/**
 * Dispatch Repository Implementation
 *
 * Implementiert die DispatchRepository-Interface mit Caching
 * und optimierten Datenbankabfragen für Versanddaten.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DispatchRepositoryImpl = void 0;
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const cache_service_1 = require("../services/cache.service");
/**
 * Cache-TTL für Dispatch-Daten (verschiedene Datentypen)
 */
const DISPATCH_CACHE_TTL = {
    SERVICE_LEVEL: 1 * 60 * 1000, // 1 Minute - häufig aktualisiert
    PERFORMANCE: 2 * 60 * 1000, // 2 Minuten - moderate Aktualisierung
    PICKING: 3 * 60 * 1000, // 3 Minuten - weniger häufig
    DELIVERY: 2 * 60 * 1000, // 2 Minuten
    RETURNS: 5 * 60 * 1000, // 5 Minuten - seltene Änderungen
    METRICS: 10 * 60 * 1000 // 10 Minuten - Aggregierte Daten
};
class DispatchRepositoryImpl {
    constructor(database) {
        this.db = db_1.db;
        this.cache = (0, cache_service_1.getBackendCache)();
        this.stats = {
            totalQueries: 0,
            cacheHits: 0,
            cacheMisses: 0,
            hitRate: 0,
            avgQueryTime: 0,
            lastAccessed: new Date()
        };
        // Drizzle DB wird direkt importiert - Parameter für Kompatibilität
    }
    /**
     * Service Level Daten mit Cache-Optimierung
     */
    async getServiceLevelData(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getServiceLevelData', dateRange);
        return await this.cachedQuery(cacheKey, async () => {
            const drizzleConditions = this.buildDrizzleWhereClause(dateRange);
            const result = await this.db.select({
                datum: schema_1.dispatchData.datum,
                servicegrad: schema_1.dispatchData.servicegrad,
            })
                .from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.and)(...(drizzleConditions.length > 0 ? drizzleConditions : [])));
            return result.map((item) => ({
                datum: item.datum || new Date().toISOString().split('T')[0], // Fallback: heute's Datum
                servicegrad: item.servicegrad || 0,
            }));
        }, DISPATCH_CACHE_TTL.SERVICE_LEVEL);
    }
    /**
     * Tägliche Performance-Daten
     */
    async getDailyPerformanceData(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getDailyPerformanceData', dateRange);
        return await this.cachedQuery(cacheKey, async () => {
            const drizzleConditions = this.buildDrizzleWhereClause(dateRange);
            const result = await this.db.select({
                datum: schema_1.dispatchData.datum,
                produzierte_tonnagen: schema_1.dispatchData.produzierte_tonnagen,
            })
                .from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.and)(...(drizzleConditions.length > 0 ? drizzleConditions : [])));
            return result.map((item) => ({
                datum: item.datum,
                value: item.produzierte_tonnagen || 0,
            }));
        }, DISPATCH_CACHE_TTL.PERFORMANCE);
    }
    /**
     * Kommissionierungsdaten
     */
    async getPickingData(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getPickingData', dateRange);
        return await this.cachedQuery(cacheKey, async () => {
            const drizzleConditions = this.buildDrizzleWhereClause(dateRange);
            const result = await this.db.select({
                datum: schema_1.dispatchData.datum,
                atrl: schema_1.dispatchData.atrl,
                aril: schema_1.dispatchData.aril,
                fuellgrad_aril: schema_1.dispatchData.fuellgrad_aril,
            })
                .from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.and)(...(drizzleConditions.length > 0 ? drizzleConditions : [])));
            return result.map((item) => ({
                date: item.datum || new Date().toISOString().split('T')[0], // Fallback: heute's Datum
                atrl: item.atrl || 0,
                aril: item.aril || 0,
                fuellgrad_aril: item.fuellgrad_aril || 0,
            }));
        }, DISPATCH_CACHE_TTL.PICKING);
    }
    /**
     * Lieferpositionsdaten
     */
    async getDeliveryPositionsData(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getDeliveryPositionsData', dateRange);
        return await this.cachedQuery(cacheKey, async () => {
            const drizzleConditions = this.buildDrizzleWhereClause(dateRange);
            const result = await this.db.select({
                datum: schema_1.dispatchData.datum,
                ausgeliefert_lup: schema_1.dispatchData.ausgeliefert_lup,
                rueckstaendig: schema_1.dispatchData.rueckstaendig,
            })
                .from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.and)(...(drizzleConditions.length > 0 ? drizzleConditions : [])));
            return result.map((item) => ({
                date: item.datum || new Date().toISOString().split('T')[0], // Fallback: heute's Datum
                ausgeliefert_lup: item.ausgeliefert_lup || 0,
                rueckstaendig: item.rueckstaendig || 0,
            }));
        }, DISPATCH_CACHE_TTL.DELIVERY);
    }
    /**
     * Tagesleistungsdaten
     */
    async getTagesleistungData(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getTagesleistungData', dateRange);
        return await this.cachedQuery(cacheKey, async () => {
            const drizzleConditions = this.buildDrizzleWhereClause(dateRange);
            const result = await this.db.select({
                datum: schema_1.dispatchData.datum,
                produzierte_tonnagen: schema_1.dispatchData.produzierte_tonnagen,
                direktverladung_kiaa: schema_1.dispatchData.direktverladung_kiaa,
                umschlag: schema_1.dispatchData.umschlag,
                kg_pro_colli: schema_1.dispatchData.kg_pro_colli,
                elefanten: schema_1.dispatchData.elefanten,
            })
                .from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.and)(...(drizzleConditions.length > 0 ? drizzleConditions : [])));
            return result.map((item) => ({
                date: item.datum || new Date().toISOString().split('T')[0], // Fallback: heute's Datum
                produzierte_tonnagen: item.produzierte_tonnagen || 0,
                direktverladung_kiaa: item.direktverladung_kiaa || 0,
                umschlag: item.umschlag || 0,
                kg_pro_colli: item.kg_pro_colli || 0,
                elefanten: item.elefanten || 0,
            }));
        }, DISPATCH_CACHE_TTL.PERFORMANCE);
    }
    /**
     * Retourendaten aggregiert
     */
    async getReturnsData() {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getReturnsData');
        return await this.cachedQuery(cacheKey, async () => {
            const result = await this.db.select({
                qm_angenommen: schema_1.dispatchData.qm_angenommen,
                qm_abgelehnt: schema_1.dispatchData.qm_abgelehnt,
                qm_offen: schema_1.dispatchData.qm_offen,
            })
                .from(schema_1.dispatchData);
            const angenommen = result.reduce((sum, item) => sum + (item.qm_angenommen || 0), 0);
            const abgelehnt = result.reduce((sum, item) => sum + (item.qm_abgelehnt || 0), 0);
            const offen = result.reduce((sum, item) => sum + (item.qm_offen || 0), 0);
            return [
                { name: 'Angenommen', value: angenommen },
                { name: 'Abgelehnt', value: abgelehnt },
                { name: 'Offen', value: offen },
            ];
        }, DISPATCH_CACHE_TTL.RETURNS);
    }
    /**
     * Performance-Metriken für einen bestimmten Zeitraum
     */
    async getPerformanceMetrics(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getPerformanceMetrics', dateRange);
        return await this.cachedQuery(cacheKey, async () => {
            var _a, _b, _c, _d, _e, _f;
            const drizzleConditions = this.buildDrizzleWhereClause(dateRange);
            const whereClause = drizzleConditions.length > 0 ? (0, drizzle_orm_1.and)(...drizzleConditions) : undefined;
            const [sumResult, avgResult, countResult] = await Promise.all([
                this.db.select({
                    produzierte_tonnagen: (0, drizzle_orm_1.sum)(schema_1.dispatchData.produzierte_tonnagen),
                    ausgeliefert_lup: (0, drizzle_orm_1.sum)(schema_1.dispatchData.ausgeliefert_lup),
                    atrl: (0, drizzle_orm_1.sum)(schema_1.dispatchData.atrl),
                })
                    .from(schema_1.dispatchData)
                    .where(whereClause),
                this.db.select({
                    servicegrad: (0, drizzle_orm_1.avg)(schema_1.dispatchData.servicegrad),
                    atrl: (0, drizzle_orm_1.avg)(schema_1.dispatchData.atrl),
                })
                    .from(schema_1.dispatchData)
                    .where(whereClause),
                this.db.select({ count: (0, drizzle_orm_1.count)(schema_1.dispatchData.datum) })
                    .from(schema_1.dispatchData)
                    .where(whereClause)
            ]);
            const result = {
                _sum: {
                    produzierte_tonnagen: ((_a = sumResult[0]) === null || _a === void 0 ? void 0 : _a.produzierte_tonnagen) || 0,
                    ausgeliefert_lup: ((_b = sumResult[0]) === null || _b === void 0 ? void 0 : _b.ausgeliefert_lup) || 0,
                    atrl: ((_c = sumResult[0]) === null || _c === void 0 ? void 0 : _c.atrl) || 0,
                },
                _avg: {
                    servicegrad: ((_d = avgResult[0]) === null || _d === void 0 ? void 0 : _d.servicegrad) || 0,
                    atrl: ((_e = avgResult[0]) === null || _e === void 0 ? void 0 : _e.atrl) || 0,
                },
                _count: {
                    datum: ((_f = countResult[0]) === null || _f === void 0 ? void 0 : _f.count) || 0,
                },
            };
            // Berechne Periode
            const startDate = dateRange.startDate || '';
            const endDate = dateRange.endDate || '';
            const daysDiff = startDate && endDate
                ? Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 3600 * 24))
                : result._count.datum || 0;
            return {
                totalTonnage: Number(result._sum.produzierte_tonnagen || 0),
                averageServiceLevel: Number(result._avg.servicegrad || 0) * 100,
                totalDeliveries: Number(result._sum.ausgeliefert_lup || 0),
                averagePickingRate: Number(result._avg.atrl || 0),
                period: {
                    startDate: startDate,
                    endDate: endDate,
                    days: daysDiff,
                },
            };
        }, DISPATCH_CACHE_TTL.METRICS);
    }
    /**
     * Top-Performance-Tage ermitteln
     */
    async getTopPerformanceDays(limit = 10) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('dispatch', 'getTopPerformanceDays', { limit });
        return await this.cachedQuery(cacheKey, async () => {
            const result = await this.db.select({
                datum: schema_1.dispatchData.datum,
                produzierte_tonnagen: schema_1.dispatchData.produzierte_tonnagen,
                servicegrad: schema_1.dispatchData.servicegrad,
            })
                .from(schema_1.dispatchData)
                .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.produzierte_tonnagen), (0, drizzle_orm_1.desc)(schema_1.dispatchData.servicegrad))
                .limit(limit);
            return result.map((item) => ({
                // Konvertiere Date zu string für Konsistenz mit Interface
                datum: item.datum instanceof Date ? item.datum.toISOString().split('T')[0] : (item.datum || new Date().toISOString().split('T')[0]),
                produzierte_tonnagen: item.produzierte_tonnagen || 0,
                servicegrad: (item.servicegrad || 0) * 100,
            }));
        }, DISPATCH_CACHE_TTL.METRICS);
    }
    // Base Repository Implementation (simplified for key methods)
    async findById(id) {
        const numericId = typeof id === 'string' ? parseInt(id) : id;
        const result = await this.db.select().from(schema_1.dispatchData).where((0, drizzle_orm_1.eq)(schema_1.dispatchData.id, numericId)).limit(1);
        return result[0] ? this.mapToDispatchDataEntity(result[0]) : null;
    }
    async findAll(options) {
        const result = await this.db.select().from(schema_1.dispatchData)
            .limit((options === null || options === void 0 ? void 0 : options.limit) || 1000)
            .offset((options === null || options === void 0 ? void 0 : options.offset) || 0);
        return result.map(row => this.mapToDispatchDataEntity(row));
    }
    async findWhere(criteria) {
        const result = await this.db.select().from(schema_1.dispatchData);
        return result.map(row => this.mapToDispatchDataEntity(row));
    }
    async findOneWhere(criteria) {
        const result = await this.db.select().from(schema_1.dispatchData).limit(1);
        return result[0] ? this.mapToDispatchDataEntity(result[0]) : null;
    }
    async create(data) {
        const result = await this.db.insert(schema_1.dispatchData).values(data).returning();
        await this.invalidateCache();
        return result[0];
    }
    async update(id, data) {
        const numericId = typeof id === 'string' ? parseInt(id) : id;
        const result = await this.db.update(schema_1.dispatchData)
            .set(data)
            .where((0, drizzle_orm_1.eq)(schema_1.dispatchData.id, numericId))
            .returning();
        await this.invalidateCache();
        return result[0] || null;
    }
    async delete(id) {
        const numericId = typeof id === 'string' ? parseInt(id) : id;
        try {
            await this.db.delete(schema_1.dispatchData).where((0, drizzle_orm_1.eq)(schema_1.dispatchData.id, numericId));
            await this.invalidateCache();
            return true;
        }
        catch (_a) {
            return false;
        }
    }
    async count(criteria) {
        var _a;
        const result = await this.db.select({ count: (0, drizzle_orm_1.count)() }).from(schema_1.dispatchData);
        return ((_a = result[0]) === null || _a === void 0 ? void 0 : _a.count) || 0;
    }
    async invalidateCache(key) {
        if (key) {
            // Invalidate specific key
            // Implementation depends on cache service
        }
        else {
            // Invalidate all dispatch-related cache entries
            this.cache.invalidateByDataTypes(['dispatch']);
        }
    }
    async getStats() {
        return this.stats;
    }
    /**
     * Private helper methods
     */
    async cachedQuery(cacheKey, queryFn, ttl) {
        this.stats.totalQueries++;
        this.stats.lastAccessed = new Date();
        return await this.cache.cachedQuery(cacheKey, queryFn, ttl);
    }
    buildDrizzleWhereClause(dateRange) {
        if (!dateRange)
            return [];
        const conditions = [];
        // Datumswerte sind als Text in der DB gespeichert (YYYY-MM-DD) - String-Vergleich
        if (dateRange.startDate) {
            // Konvertiere zu String für Datenbank-Vergleich
            const startDateString = this.dateToString(dateRange.startDate);
            conditions.push((0, drizzle_orm_1.gte)(schema_1.dispatchData.datum, startDateString));
        }
        if (dateRange.endDate) {
            // Konvertiere zu String für Datenbank-Vergleich
            const endDateString = this.dateToString(dateRange.endDate);
            conditions.push((0, drizzle_orm_1.lte)(schema_1.dispatchData.datum, endDateString));
        }
        return conditions;
    }
    dateToString(date) {
        if (!date)
            return '';
        if (typeof date === 'string')
            return date;
        if (date instanceof Date)
            return date.toISOString().split('T')[0];
        return String(date);
    }
    /**
     * Maps Drizzle result to DispatchDataEntity
     */
    mapToDispatchDataEntity(row) {
        return {
            id: row.id,
            datum: row.datum,
            servicegrad: row.servicegrad,
            produzierte_tonnagen: row.produzierte_tonnagen,
            atrl: row.atrl,
            aril: row.aril,
            fuellgrad_aril: row.fuellgrad_aril,
            qm_angenommen: row.qm_angenommen,
            qm_abgelehnt: row.qm_abgelehnt,
            qm_offen: row.qm_offen,
            ausgeliefert_lup: row.ausgeliefert_lup,
            rueckstaendig: row.rueckstaendig,
            direktverladung_kiaa: row.direktverladung_kiaa,
            umschlag: row.umschlag,
            kg_pro_colli: row.kg_pro_colli,
            elefanten: row.elefanten,
        };
    }
}
exports.DispatchRepositoryImpl = DispatchRepositoryImpl;
