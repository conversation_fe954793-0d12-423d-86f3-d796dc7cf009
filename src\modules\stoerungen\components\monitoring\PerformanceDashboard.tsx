import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import {
  Activity,
  Database,
  Zap,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import apiService from "@/services/api.service";

interface PerformanceMetrics {
  overview: {
    healthScore: number;
    totalQueries: number;
    averageQueryTime: number;
    cacheHitRate: number;
    systemUptime: number;
  };
  database: {
    totalQueries: number;
    averageQueryTime: number;
    slowQueries: number;
    failedQueries: number;
  };
  cache: {
    hitRate: number;
    missRate: number;
    memoryUsage: number;
    totalEntries: number;
  };
  system: {
    memoryUsage: {
      heapUsed: number;
      heapTotal: number;
      external: number;
      rss: number;
    };
    cpuUsage: number;
    uptime: number;
    eventLoopLag: number;
  };
}

export const PerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const [healthResponse, statsResponse, cacheResponse, systemResponse] = await Promise.all([
        apiService.get<any>("/performance/health"),
        apiService.get<any>("/performance/stats"),
        apiService.get<any>("/performance/cache"),
        apiService.get<any>("/performance/system")
      ]);

      // Extract data from API responses
      const health = healthResponse.data || healthResponse;
      const stats = statsResponse.data || statsResponse;
      const cache = cacheResponse.data || cacheResponse;
      const system = systemResponse.data || systemResponse;

      // DEBUG: Log all API responses to understand the data
      console.log('=== PERFORMANCE DASHBOARD DEBUG ===');
      console.log('Health API:', health);
      console.log('Stats API:', stats);
      console.log('Cache API:', cache);
      console.log('System API:', system);



      // Get system info from Electron's process if available
      const getSystemInfo = () => {
        try {
          if (typeof window !== 'undefined' && (window as any).electronAPI) {
            // Use Electron's exposed API if available
            return {
              uptime: (window as any).electronAPI.getUptime?.() || 0,
              memoryUsage: (window as any).electronAPI.getMemoryUsage?.() || {
                heapUsed: 0,
                heapTotal: 0,
                external: 0,
                rss: 0
              }
            };
          } else if (typeof process !== 'undefined' && process.uptime) {
            // Direct access to process (if nodeIntegration is enabled)
            return {
              uptime: process.uptime(),
              memoryUsage: process.memoryUsage()
            };
          }
        } catch (e) {
          console.warn('Could not access system info:', e);
        }
        return {
          uptime: 0,
          memoryUsage: { heapUsed: 0, heapTotal: 0, external: 0, rss: 0 }
        };
      };

      const systemInfo = getSystemInfo();

      // Calculate health score with debug logging
      let healthScore = 95; // Default healthy score

      if (health.status === 'warning') {
        healthScore = 75;
      } else if (health.status === 'critical') {
        healthScore = 45;
      }

      console.log('PerformanceDashboard - Base Health Score:', healthScore, 'from status:', health.status);

      // Apply the same adjustments as HealthIndicator
      if (stats.averageResponseTime > 3000) {
        healthScore -= 10;
        console.log('PerformanceDashboard - Response time > 3000ms, reducing by 10:', healthScore);
      } else if (stats.averageResponseTime > 2000) {
        healthScore -= 5;
        console.log('PerformanceDashboard - Response time > 2000ms, reducing by 5:', healthScore);
      }

      if (stats.successRate < 0.95) {
        healthScore -= 10;
        console.log('PerformanceDashboard - Success rate < 95%, reducing by 10:', healthScore);
      }

      if (cache.stats?.hitRate < 0.5) {
        healthScore -= 10;
        console.log('PerformanceDashboard - Cache hit rate < 50%, reducing by 10:', healthScore);
      }

      healthScore = Math.max(0, Math.min(100, healthScore));
      console.log('PerformanceDashboard - Final Health Score:', healthScore);

      setMetrics({
        overview: {
          healthScore: healthScore,
          totalQueries: health.performance?.totalRequests || stats.totalRequests || 0,
          averageQueryTime: health.performance?.averageResponseTime || stats.averageResponseTime || 0,
          cacheHitRate: health.cache?.hitRate || cache.stats?.hitRate || 0,
          systemUptime: systemInfo.uptime
        },
        database: {
          totalQueries: health.performance?.totalRequests || stats.totalRequests || 0,
          averageQueryTime: health.performance?.averageResponseTime || stats.averageResponseTime || 0,
          slowQueries: stats.slowQueries || 0,
          failedQueries: stats.failedRequests || 0
        },
        cache: {
          hitRate: health.cache?.hitRate || cache.stats?.hitRate || 0,
          missRate: cache.stats?.missRate || 0,
          memoryUsage: health.cache?.totalSize || cache.stats?.totalSize || 0,
          totalEntries: health.cache?.totalEntries || cache.stats?.totalEntries || 0
        },
        system: {
          memoryUsage: system.memoryUsage || systemInfo.memoryUsage,
          cpuUsage: system.cpuUsage || 0,
          uptime: system.uptime || systemInfo.uptime,
          eventLoopLag: system.eventLoopLag || 0
        }
      });
    } catch (err) {
      setError("Fehler beim Laden der Performance-Metriken");
      console.error("Performance metrics error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
    // Longer interval to reduce load when backend is offline
    const interval = setInterval(fetchMetrics, 60000); // Update every 60 seconds
    return () => clearInterval(interval);
  }, []);

  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return "text-green-500";
    if (score >= 70) return "text-yellow-500";
    return "text-red-500";
  };

  const getHealthScoreBadge = (score: number) => {
    if (score >= 90) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>;
    if (score >= 70) return <Badge className="bg-yellow-100 text-yellow-800">Good</Badge>;
    return <Badge className="bg-red-100 text-red-800">Needs Attention</Badge>;
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };



  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="animate-pulse border-slate-800">
            <CardHeader className="h-20 bg-gray-200 rounded-t-lg"></CardHeader>
            <CardContent className="h-32 bg-gray-100"></CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !metrics) {
    return (
      <Card className="border-red-200 border-slate-800">
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span>{error || "Performance-Daten nicht verfügbar"}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.12,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: 'spring' as const, damping: 25 },
    },
  };

  const PerformanceCard = ({
    children,
    className,
    icon,
    title,
    color = "blue"
  }: {
    children: React.ReactNode;
    className?: string;
    icon: React.ReactNode;
    title: string;
    color?: string;
  }) => (
    <motion.div
      variants={itemVariants}
      whileHover={{ 
        scale: 1.02,
        y: -4
      }}
      transition={{ 
        duration: 0.15,
        ease: "easeInOut"
      }}
      className={cn(
        'group border-slate-800 bg-background hover:border-primary/30 hover:shadow-lg relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border px-6 pt-6 pb-6 shadow-md transition-all duration-150',
        className,
      )}
    >
      <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>

      <div className={cn(
        "text-primary/5 group-hover:text-primary/15 absolute right-1 bottom-3 scale-[6] transition-all duration-200 group-hover:scale-[6.5]",
        color === "blue" && "text-blue-500/5 group-hover:text-blue-500/15",
        color === "green" && "text-green-500/5 group-hover:text-green-500/15",
        color === "purple" && "text-purple-500/5 group-hover:text-purple-500/15",
        color === "orange" && "text-orange-500/5 group-hover:text-orange-500/15"
      )}>
        {icon}
      </div>

      <div className="relative z-10 flex h-full flex-col justify-between">
        <div>
          <motion.div 
            className={cn(
              "bg-primary/10 text-primary shadow-primary/10 group-hover:bg-primary/25 group-hover:shadow-primary/25 mb-4 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-150",
              color === "blue" && "bg-blue-500/10 text-blue-600 shadow-blue-500/10 group-hover:bg-blue-500/25 group-hover:shadow-blue-500/25",
              color === "green" && "bg-green-500/10 text-green-600 shadow-green-500/10 group-hover:bg-green-500/25 group-hover:shadow-green-500/25",
              color === "purple" && "bg-purple-500/10 text-purple-600 shadow-purple-500/10 group-hover:bg-purple-500/25 group-hover:shadow-purple-500/25",
              color === "orange" && "bg-orange-500/10 text-orange-600 shadow-orange-500/10 group-hover:bg-orange-500/25 group-hover:shadow-orange-500/25"
            )}
            whileHover={{ 
              scale: 1.02,
              rotate: 4
            }}
            transition={{ 
              duration: 0.15,
              ease: "easeInOut"
            }}
          >
            {icon}
          </motion.div>
          <h3 className="mb-2 text-xl font-semibold tracking-tight">{title}</h3>
          {children}
        </div>
      </div>
      <div className={cn(
        "from-primary to-primary/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-150 group-hover:blur-sm group-hover:h-2",
        color === "blue" && "from-blue-500 to-blue-500/30",
        color === "green" && "from-green-500 to-green-500/30",
        color === "purple" && "from-purple-500 to-purple-500/30",
        color === "orange" && "from-orange-500 to-orange-500/30"
      )} />
    </motion.div>
  );

  return (
    <div className="space-y-6">
      {/* Metrics Grid */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* System Health Score */}
        <PerformanceCard
          icon={<Activity className="h-5 w-5" />}
          title="Health Score"
          color="blue"
        >
          <p className="text-muted-foreground text-sm mb-2">System-Gesamtzustand</p>
          <p className="text-xs text-gray-500 mb-4">
            Bewertung basierend auf CPU-Auslastung, Speicherverbrauch, Event Loop Performance und Cache-Effizienz
          </p>
         <div className="text-center space-y-3">
          <div className={`text-3xl font-bold ${getHealthScoreColor(Number(metrics.overview?.healthScore || 0))}`}>
            {metrics.overview?.healthScore || 0}%
          </div>
          {getHealthScoreBadge(Number(metrics.overview?.healthScore || 0))}
          <Progress
            value={Number(metrics.overview?.healthScore || 0)}
            className="h-2"
          />

          {/* Detailed Health Analysis */}
          <div className="text-xs text-gray-600 space-y-2 mt-4">
            <div className="text-sm font-medium text-gray-700 mb-2">Health Score Details:</div>

            {/* Response Time Analysis */}
            <div className="p-2 bg-gray-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>Response Time:</span>
                <div className="flex items-center gap-1">
                  <span className={Number(metrics.database?.averageQueryTime || 0) < 1000 ? "text-green-600" : Number(metrics.database?.averageQueryTime || 0) < 2000 ? "text-yellow-600" : "text-red-600"}>
                    {Number(metrics.database?.averageQueryTime || 0).toFixed(0)}ms
                  </span>
                  {Number(metrics.database?.averageQueryTime || 0) > 2000 && (
                    <span className="text-red-500 text-xs">⚠️ Slow</span>
                  )}
                </div>
              </div>
              <div className="text-xs text-gray-500">
                {Number(metrics.database?.averageQueryTime || 0) < 1000 ?
                  "✅ Ausgezeichnete Antwortzeit" :
                  Number(metrics.database?.averageQueryTime || 0) < 2000 ?
                  "⚠️ Akzeptabel, aber könnte schneller sein" :
                  "❌ Datenbankabfragen sind zu langsam"
                }
              </div>
            </div>

            {/* Cache Hit Rate Analysis */}
            <div className="p-2 bg-gray-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>Cache Hit Rate:</span>
                <div className="flex items-center gap-1">
                  <span className={Number(metrics.cache?.hitRate || 0) >= 80 ? "text-green-600" : Number(metrics.cache?.hitRate || 0) >= 50 ? "text-yellow-600" : "text-red-600"}>
                    {Number(metrics.cache?.hitRate || 0).toFixed(1)}%
                  </span>
                  {Number(metrics.cache?.hitRate || 0) < 50 && (
                    <span className="text-red-500 text-xs">⚠️ Low</span>
                  )}
                </div>
              </div>
              <div className="text-xs text-gray-500">
                {Number(metrics.cache?.hitRate || 0) === 0 ?
                  "❌ Cache existiert aber wird nie verwendet - alle Anfragen gehen zur Datenbank" :
                  Number(metrics.cache?.hitRate || 0) < 50 ?
                  "⚠️ Die meisten Anfragen umgehen den Cache - schlechte Cache-Nutzung" :
                  "✅ Gute Cache-Performance - die meisten Anfragen werden aus dem Cache bedient"
                }
              </div>
            </div>

            {/* Database Queries Analysis */}
            <div className="p-2 bg-gray-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>Database Queries:</span>
                <div className="flex items-center gap-1">
                  <span className={metrics.database?.totalQueries ? "text-green-600" : "text-red-600"}>
                    {metrics.database?.totalQueries ? "✓ Active" : "✗ No Data"}
                  </span>
                </div>
              </div>
              <div className="text-xs text-gray-500">
                {metrics.database?.totalQueries ?
                  `${metrics.database?.totalQueries} Abfragen verarbeitet` :
                  "Keine Datenbankaktivität erkannt"
                }
              </div>
            </div>

            {/* Cache Entries Analysis */}
            <div className="p-2 bg-gray-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>Cache Entries:</span>
                <div className="flex items-center gap-1">
                  <span className={(metrics.cache?.totalEntries || 0) > 0 ? "text-green-600" : "text-red-600"}>
                    {(metrics.cache?.totalEntries || 0) > 0 ? `${metrics.cache?.totalEntries} entries` : "✗ Empty"}
                  </span>
                </div>
              </div>
              <div className="text-xs text-gray-500">
                {Number(metrics.cache?.hitRate || 0) === 0 && (metrics.cache?.totalEntries || 0) > 0 ?
                  "⚠️ Cache ist gefüllt aber wird nicht verwendet - Abfrage-Muster prüfen" :
                  "Cache-Speicher-Status"
                }
              </div>
            </div>

            {/* Cache Performance Issue Detection */}
            {Number(metrics.cache?.hitRate || 0) === 0 && (metrics.cache?.totalEntries || 0) > 0 && (
              <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded text-xs">
                <div className="font-medium text-red-800 mb-2">🚨 Kritisches Cache-Problem erkannt</div>
                <div className="text-red-700 space-y-1">
                  <div><strong>Problem:</strong> Cache-Trefferquote ist 0% trotz {metrics.cache?.totalEntries || 0} gecachter Einträge</div>
                  <div><strong>Auswirkung:</strong> Alle Anfragen treffen die Datenbank direkt - langsame Performance</div>
                  <div><strong>Possible Causes:</strong></div>
                  <ul className="ml-2 space-y-1 text-red-600">
                    <li>• Cache keys don't match query patterns</li>
                    <li>• Cache TTL (Time To Live) too short</li>
                    <li>• Different query parameters used</li>
                    <li>• Cache invalidation too aggressive</li>
                  </ul>
                </div>
              </div>
            )}

            {/* Recommendations */}
            <div className="mt-3 p-2 bg-blue-50 rounded text-xs">
              <div className="font-medium text-blue-800 mb-1">💡 Empfehlungen:</div>
              <div className="text-blue-700 space-y-1">
                {Number(metrics.database?.averageQueryTime || 0) > 2000 && (
                  <div>• Datenbankabfragen optimieren (aktuell {Number(metrics.database?.averageQueryTime || 0).toFixed(0)}ms)</div>
                )}
                {Number(metrics.cache?.hitRate || 0) < 50 && (
                  <div className="text-red-700 font-medium">
                    • 🚨 KRITISCH: Cache-Trefferquote ist {Number(metrics.cache?.hitRate || 0).toFixed(1)}% - Alle Anfragen gehen zur Datenbank
                  </div>
                )}
                {Number(metrics.cache?.hitRate || 0) === 0 && (
                  <div className="text-red-600 text-xs">
                    • Cache existiert ({metrics.cache?.totalEntries || 0} Einträge) wird aber nie getroffen - Cache-Key-Konsistenz prüfen
                  </div>
                )}
                {Number(metrics.database?.failedQueries || 0) > 0 && (
                  <div>• {metrics.database?.failedQueries} fehlgeschlagene Datenbankabfragen beheben</div>
                )}
                {Number(metrics.overview?.healthScore || 0) >= 70 && (
                  <div>• Gesamte System-Performance ist gut</div>
                )}
              </div>
            </div>
          </div>
         </div>
        </PerformanceCard>
        {/* Database Performance */}
        <PerformanceCard
          icon={<Database className="h-5 w-5" />}
          title="Database"
          color="green"
        >
          <p className="text-muted-foreground text-sm mb-2">Abfrageleistung & Zuverlässigkeit</p>
          <p className="text-xs text-gray-500 mb-4">
            Überwacht Datenbankabfragen für Störungen, Dispatch und Cutting. Misst durchschnittliche Antwortzeiten, Gesamtzahl der Abfragen und Fehlerquoten
          </p>

          {/* Database Metrics */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Ø Abfragezeit</span>
              <span className="font-bold text-green-600">
                {Number(metrics.database?.averageQueryTime || 0).toFixed(1)}ms
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Abfragen</span>
              <span className="font-bold">{(metrics.database?.totalQueries || 0).toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Fehler</span>
              <div className="flex items-center gap-1">
                {Number(metrics.database?.failedQueries || 0) === 0 ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                )}
                <span className={Number(metrics.database?.failedQueries || 0) === 0 ? "text-green-600" : "text-red-600"}>
                  {metrics.database?.failedQueries || 0}
                </span>
              </div>
            </div>
          </div>

          {/* Database Performance Analysis */}
          <div className="mt-4 space-y-2 text-xs">
            <div className="font-medium text-gray-700 mb-2">📊 Datenbank-Details:</div>

            {/* Query Performance Analysis */}
            <div className="p-2 bg-green-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>Abfrage-Performance:</span>
                <div className="flex items-center gap-1">
                  <span className={Number(metrics.database?.averageQueryTime || 0) < 100 ? "text-green-600" : Number(metrics.database?.averageQueryTime || 0) < 500 ? "text-yellow-600" : "text-red-600"}>
                    {Number(metrics.database?.averageQueryTime || 0) < 100 ? "Ausgezeichnet" : Number(metrics.database?.averageQueryTime || 0) < 500 ? "Gut" : "Verbesserungswürdig"}
                  </span>
                </div>
              </div>
              <div className="text-xs text-gray-600">
                {Number(metrics.database?.averageQueryTime || 0) < 100 ?
                  "✅ Datenbankabfragen sind sehr schnell" :
                  Number(metrics.database?.averageQueryTime || 0) < 500 ?
                  "⚠️ Abfragezeiten sind akzeptabel, aber optimierbar" :
                  "❌ Datenbankabfragen sind zu langsam - Optimierung erforderlich"
                }
              </div>
            </div>

            {/* Query Volume Analysis */}
            <div className="p-2 bg-blue-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>Abfrage-Volumen:</span>
                <span className="font-medium">
                  {(metrics.database?.totalQueries || 0).toLocaleString()} Abfragen
                </span>
              </div>
              <div className="text-xs text-gray-600">
                {metrics.database?.totalQueries ?
                  `System verarbeitet ${(metrics.database?.totalQueries || 0).toLocaleString()} Datenbankabfragen` :
                  "Keine Datenbankaktivität erkannt"
                }
              </div>
            </div>

            {/* Error Analysis */}
            <div className="p-2 bg-gray-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>Fehleranalyse:</span>
                <div className="flex items-center gap-1">
                  {Number(metrics.database?.failedQueries || 0) === 0 ? (
                    <span className="text-green-600 font-medium">Keine Fehler</span>
                  ) : (
                    <span className="text-red-600 font-medium">{metrics.database?.failedQueries} Fehler</span>
                  )}
                </div>
              </div>
              <div className="text-xs text-gray-600">
                {Number(metrics.database?.failedQueries || 0) === 0 ?
                  "✅ Alle Datenbankoperationen erfolgreich" :
                  `❌ ${metrics.database?.failedQueries} fehlgeschlagene Abfragen - Überprüfung erforderlich`
                }
              </div>
            </div>

            {/* Database Recommendations */}
            <div className="mt-3 p-2 bg-green-50 rounded text-xs">
              <div className="font-medium text-green-800 mb-1">💡 Datenbank-Empfehlungen:</div>
              <div className="text-green-700 space-y-1">
                {Number(metrics.database?.averageQueryTime || 0) > 500 && (
                  <div>• Datenbankabfragen optimieren (aktuell {Number(metrics.database?.averageQueryTime || 0).toFixed(0)}ms)</div>
                )}
                {Number(metrics.database?.failedQueries || 0) > 0 && (
                  <div>• {metrics.database?.failedQueries} fehlgeschlagene Abfragen beheben</div>
                )}
                {Number(metrics.database?.totalQueries || 0) > 1000 && (
                  <div>• Hohes Abfragevolumen - Connection Pool prüfen</div>
                )}
                {Number(metrics.database?.averageQueryTime || 0) < 100 && (
                  <div>• Datenbank-Performance ist ausgezeichnet</div>
                )}
              </div>
            </div>
          </div>
        </PerformanceCard>

        {/* Cache Performance */}
        <PerformanceCard
          icon={<Zap className="h-5 w-5" />}
          title="Cache"
          color="purple"
        >
          <p className="text-muted-foreground text-sm mb-2">Trefferquote & Speicherauslastung</p>
          <p className="text-xs text-gray-500 mb-4">
            Intelligentes Caching-System für häufig angeforderte Daten. Misst Cache-Trefferquoten, Speicherverbrauch und Anzahl gecachter Einträge für optimale Performance
          </p>

          {/* Cache Metrics */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Trefferquote</span>
              <div className="flex items-center gap-1">
                {Number(metrics.cache?.hitRate || 0) >= 80 ? (
                  <TrendingUp className="h-4 w-4 text-green-500" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500" />
                )}
                <span className={`font-bold ${Number(metrics.cache?.hitRate || 0) >= 80 ? "text-green-600" : "text-red-600"}`}>
                  {Number(metrics.cache?.hitRate || 0).toFixed(1)}%
                </span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Speicher</span>
              <span className="font-bold text-purple-600">
                {formatBytes(metrics.cache?.memoryUsage || 0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Einträge</span>
              <span className="font-bold">{(metrics.cache?.totalEntries || 0).toLocaleString()}</span>
            </div>
          </div>

          {/* Cache Performance Analysis */}
          <div className="mt-4 space-y-2 text-xs">
            <div className="font-medium text-gray-700 mb-2">⚡ Cache-Details:</div>

            {/* Cache Hit Rate Analysis */}
            <div className="p-2 bg-purple-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>Cache-Effizienz:</span>
                <div className="flex items-center gap-1">
                  <span className={Number(metrics.cache?.hitRate || 0) >= 80 ? "text-green-600" : Number(metrics.cache?.hitRate || 0) >= 50 ? "text-yellow-600" : "text-red-600"}>
                    {Number(metrics.cache?.hitRate || 0) >= 80 ? "Ausgezeichnet" : Number(metrics.cache?.hitRate || 0) >= 50 ? "Gut" : "Schlecht"}
                  </span>
                </div>
              </div>
              <div className="text-xs text-gray-600">
                {Number(metrics.cache?.hitRate || 0) >= 80 ?
                  "✅ Cache arbeitet sehr effizient - die meisten Anfragen werden aus dem Cache bedient" :
                  Number(metrics.cache?.hitRate || 0) >= 50 ?
                  "⚠️ Cache-Performance ist akzeptabel, aber optimierbar" :
                  "❌ Cache-Effizienz ist niedrig - die meisten Anfragen gehen zur Datenbank"
                }
              </div>
            </div>

            {/* Cache Memory Analysis */}
            <div className="p-2 bg-blue-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>Speicher-Auslastung:</span>
                <span className="font-medium">
                  {formatBytes(metrics.cache?.memoryUsage || 0)}
                </span>
              </div>
              <div className="text-xs text-gray-600">
                {Number(metrics.cache?.memoryUsage || 0) < 1024 * 1024 ?
                  "Niedriger Speicherverbrauch - Cache ist effizient" :
                  Number(metrics.cache?.memoryUsage || 0) < 10 * 1024 * 1024 ?
                  "Moderater Speicherverbrauch - akzeptabel" :
                  "Hoher Speicherverbrauch - Optimierung empfohlen"
                }
              </div>
            </div>

            {/* Cache Entries Analysis */}
            <div className="p-2 bg-gray-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>Cache-Größe:</span>
                <div className="flex items-center gap-1">
                  <span className={(metrics.cache?.totalEntries || 0) > 0 ? "text-green-600" : "text-red-600"}>
                    {(metrics.cache?.totalEntries || 0) > 0 ? `${metrics.cache?.totalEntries} Einträge` : "Leer"}
                  </span>
                </div>
              </div>
              <div className="text-xs text-gray-600">
                {Number(metrics.cache?.hitRate || 0) === 0 && (metrics.cache?.totalEntries || 0) > 0 ?
                  "⚠️ Cache ist gefüllt aber wird nicht verwendet - Cache-Keys prüfen" :
                  (metrics.cache?.totalEntries || 0) > 0 ?
                  `Cache enthält ${(metrics.cache?.totalEntries || 0).toLocaleString()} gecachte Einträge` :
                  "Cache ist leer - keine Daten zwischengespeichert"
                }
              </div>
            </div>

            {/* Cache Critical Issues */}
            {Number(metrics.cache?.hitRate || 0) === 0 && (metrics.cache?.totalEntries || 0) > 0 && (
              <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded text-xs">
                <div className="font-medium text-red-800 mb-2">🚨 Kritisches Cache-Problem erkannt</div>
                <div className="text-red-700 space-y-1">
                  <div><strong>Problem:</strong> Cache-Trefferquote ist 0% trotz {metrics.cache?.totalEntries || 0} gecachter Einträge</div>
                  <div><strong>Auswirkung:</strong> Alle Anfragen treffen die Datenbank direkt - langsame Performance</div>
                  <div><strong>Mögliche Ursachen:</strong></div>
                  <ul className="ml-2 space-y-1 text-red-600">
                    <li>• Cache-Keys stimmen nicht mit Abfrage-Mustern überein</li>
                    <li>• Cache-TTL (Time To Live) ist zu kurz eingestellt</li>
                    <li>• Unterschiedliche Abfrage-Parameter werden verwendet</li>
                    <li>• Cache-Invalidierung ist zu aggressiv konfiguriert</li>
                  </ul>
                </div>
              </div>
            )}

            {/* Cache Recommendations */}
            <div className="mt-3 p-2 bg-purple-50 rounded text-xs">
              <div className="font-medium text-purple-800 mb-1">💡 Cache-Empfehlungen:</div>
              <div className="text-purple-700 space-y-1">
                {Number(metrics.cache?.hitRate || 0) < 50 && (
                  <div className="text-red-700 font-medium">
                    • 🚨 KRITISCH: Cache-Trefferquote ist {Number(metrics.cache?.hitRate || 0).toFixed(1)}% - Alle Anfragen gehen zur Datenbank
                  </div>
                )}
                {Number(metrics.cache?.hitRate || 0) === 0 && (
                  <div className="text-red-600 text-xs">
                    • Cache existiert ({metrics.cache?.totalEntries || 0} Einträge) wird aber nie getroffen - Cache-Key-Konsistenz prüfen
                  </div>
                )}
                {Number(metrics.cache?.memoryUsage || 0) > 10 * 1024 * 1024 && (
                  <div>• Cache-Speicherverbrauch optimieren ({formatBytes(metrics.cache?.memoryUsage || 0)})</div>
                )}
                {Number(metrics.cache?.hitRate || 0) >= 80 && (
                  <div>• Cache-Performance ist ausgezeichnet - keine Aktion erforderlich</div>
                )}
              </div>
            </div>
          </div>
        </PerformanceCard>

        {/* System Resources */}
        <PerformanceCard
          icon={<Clock className="h-5 w-5" />}
          title="System"
          color="orange"
        >
          <p className="text-muted-foreground text-sm mb-2">Systemressourcen</p>
          <p className="text-xs text-gray-500 mb-4">
            Überwacht Node.js und Betriebssystem-Metriken: CPU-Auslastung, Heap-Speicherverbrauch, Event Loop Performance und System-Betriebszeit
          </p>

          {/* System Metrics */}
          <div className="space-y-3">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Speicher</span>
                <span className="font-bold text-orange-600">
                  {formatBytes(metrics.system?.memoryUsage?.heapUsed || 0)}
                </span>
              </div>
              <Progress
                value={metrics.system?.memoryUsage?.heapTotal ?
                  (metrics.system.memoryUsage.heapUsed / metrics.system.memoryUsage.heapTotal) * 100 : 0}
                className="h-2"
              />
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Event Loop</span>
              <span className={`font-bold ${Number(metrics.system?.eventLoopLag || 0) < 10 ? "text-green-600" : Number(metrics.system?.eventLoopLag || 0) < 50 ? "text-yellow-600" : "text-red-600"}`}>
                {Number(metrics.system?.eventLoopLag || 0).toFixed(1)}ms
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Uptime</span>
              <span className="font-bold">{formatUptime(metrics.system?.uptime || 0)}</span>
            </div>
          </div>

          {/* System Performance Analysis */}
          <div className="mt-4 space-y-2 text-xs">
            <div className="font-medium text-gray-700 mb-2">🔧 System-Details:</div>

            {/* Memory Analysis */}
            <div className="p-2 bg-orange-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>Speicher-Auslastung:</span>
                <div className="flex items-center gap-1">
                  <span className={metrics.system?.memoryUsage?.heapTotal && (metrics.system.memoryUsage.heapUsed / metrics.system.memoryUsage.heapTotal) < 0.7 ? "text-green-600" : (metrics.system.memoryUsage.heapUsed / metrics.system.memoryUsage.heapTotal) < 0.9 ? "text-yellow-600" : "text-red-600"}>
                    {metrics.system?.memoryUsage?.heapTotal ?
                      `${((metrics.system.memoryUsage.heapUsed / metrics.system.memoryUsage.heapTotal) * 100).toFixed(1)}%` :
                      "Unbekannt"
                    }
                  </span>
                </div>
              </div>
              <div className="text-xs text-gray-600">
                {metrics.system?.memoryUsage?.heapTotal ?
                  (metrics.system.memoryUsage.heapUsed / metrics.system.memoryUsage.heapTotal) < 0.7 ?
                    "✅ Speicherverbrauch ist optimal" :
                    (metrics.system.memoryUsage.heapUsed / metrics.system.memoryUsage.heapTotal) < 0.9 ?
                    "⚠️ Hoher Speicherverbrauch - Überwachung empfohlen" :
                    "❌ Kritischer Speicherverbrauch - sofortige Aktion erforderlich" :
                  "Speicher-Informationen nicht verfügbar"
                }
              </div>
            </div>

            {/* Event Loop Analysis */}
            <div className="p-2 bg-blue-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>Event Loop Performance:</span>
                <div className="flex items-center gap-1">
                  <span className={Number(metrics.system?.eventLoopLag || 0) < 10 ? "text-green-600" : Number(metrics.system?.eventLoopLag || 0) < 50 ? "text-yellow-600" : "text-red-600"}>
                    {Number(metrics.system?.eventLoopLag || 0) < 10 ? "Ausgezeichnet" : Number(metrics.system?.eventLoopLag || 0) < 50 ? "Akzeptabel" : "Schlecht"}
                  </span>
                </div>
              </div>
              <div className="text-xs text-gray-600">
                {Number(metrics.system?.eventLoopLag || 0) < 10 ?
                  "✅ Event Loop arbeitet sehr effizient" :
                  Number(metrics.system?.eventLoopLag || 0) < 50 ?
                  "⚠️ Event Loop ist langsam - Performance-Impact möglich" :
                  "❌ Event Loop ist blockiert - kritische Performance-Probleme"
                }
              </div>
            </div>

            {/* System Uptime Analysis */}
            <div className="p-2 bg-gray-50 rounded">
              <div className="flex justify-between items-center mb-1">
                <span>System-Stabilität:</span>
                <div className="flex items-center gap-1">
                  <span className={Number(metrics.system?.uptime || 0) > 3600 ? "text-green-600" : Number(metrics.system?.uptime || 0) > 600 ? "text-yellow-600" : "text-red-600"}>
                    {Number(metrics.system?.uptime || 0) > 3600 ? "Stabil" : Number(metrics.system?.uptime || 0) > 600 ? "Instabil" : "Kritisch"}
                  </span>
                </div>
              </div>
              <div className="text-xs text-gray-600">
                {Number(metrics.system?.uptime || 0) > 3600 ?
                  `✅ System läuft seit ${formatUptime(metrics.system?.uptime || 0)} stabil` :
                  Number(metrics.system?.uptime || 0) > 600 ?
                  `⚠️ System läuft seit ${formatUptime(metrics.system?.uptime || 0)} - Neustarts beobachtet` :
                  "❌ System unstabil - häufige Neustarts erforderlich"
                }
              </div>
            </div>

            {/* System Resource Warnings */}
            {((metrics.system?.memoryUsage?.heapUsed || 0) / (metrics.system?.memoryUsage?.heapTotal || 1)) > 0.9 && (
              <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded text-xs">
                <div className="font-medium text-red-800 mb-2">🚨 Kritischer Speicherzustand</div>
                <div className="text-red-700 space-y-1">
                  <div><strong>Problem:</strong> Speicherverbrauch über 90% - System-Instabilität droht</div>
                  <div><strong>Auswirkung:</strong> Performance-Einbußen, potenzielle Abstürze</div>
                  <div><strong>Sofortmaßnahmen:</strong></div>
                  <ul className="ml-2 space-y-1 text-red-600">
                    <li>• Garbage Collection Trigger prüfen</li>
                    <li>• Memory Leaks identifizieren</li>
                    <li>• Cache-Größe reduzieren</li>
                    <li>• Nicht-benötigte Services stoppen</li>
                  </ul>
                </div>
              </div>
            )}

            {/* Event Loop Warnings */}
            {Number(metrics.system?.eventLoopLag || 0) > 50 && (
              <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded text-xs">
                <div className="font-medium text-yellow-800 mb-2">⚠️ Event Loop Verzögerung</div>
                <div className="text-yellow-700 space-y-1">
                  <div><strong>Problem:</strong> Event Loop Lag von {Number(metrics.system?.eventLoopLag || 0).toFixed(1)}ms</div>
                  <div><strong>Auswirkung:</strong> Verzögerte Anfrage-Verarbeitung, langsame Response-Zeiten</div>
                  <div><strong>Mögliche Ursachen:</strong></div>
                  <ul className="ml-2 space-y-1 text-yellow-600">
                    <li>• CPU-intensive Operationen blockieren Event Loop</li>
                    <li>• Zu viele parallele Anfragen</li>
                    <li>• Ineffiziente Datenbankabfragen</li>
                    <li>• Memory-intensive Operationen</li>
                  </ul>
                </div>
              </div>
            )}

            {/* System Recommendations */}
            <div className="mt-3 p-2 bg-orange-50 rounded text-xs">
              <div className="font-medium text-orange-800 mb-1">💡 System-Empfehlungen:</div>
              <div className="text-orange-700 space-y-1">
                {((metrics.system?.memoryUsage?.heapUsed || 0) / (metrics.system?.memoryUsage?.heapTotal || 1)) > 0.8 && (
                  <div>• Speicherverbrauch reduzieren ({formatBytes(metrics.system?.memoryUsage?.heapUsed || 0)} von {formatBytes(metrics.system?.memoryUsage?.heapTotal || 0)})</div>
                )}
                {Number(metrics.system?.eventLoopLag || 0) > 50 && (
                  <div>• Event Loop Performance verbessern (aktuell {Number(metrics.system?.eventLoopLag || 0).toFixed(1)}ms Lag)</div>
                )}
                {Number(metrics.system?.uptime || 0) < 3600 && (
                  <div>• System-Stabilität überwachen - häufige Neustarts erkannt</div>
                )}
                {((metrics.system?.memoryUsage?.heapUsed || 0) / (metrics.system?.memoryUsage?.heapTotal || 1)) < 0.7 && Number(metrics.system?.eventLoopLag || 0) < 10 && (
                  <div>• System-Performance ist optimal - keine Aktion erforderlich</div>
                )}
              </div>
            </div>
          </div>
        </PerformanceCard>
      </motion.div>
    </div>
  );
};