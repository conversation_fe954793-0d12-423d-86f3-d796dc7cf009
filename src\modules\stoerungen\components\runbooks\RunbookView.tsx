import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { X, BookOpen, Tag, AlertTriangle } from 'lucide-react';
import { Runbook } from '@/types/runbooks.types';

interface RunbookViewProps {
  runbook: Runbook | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const RunbookView: React.FC<RunbookViewProps> = ({
  runbook,
  open,
  onOpenChange
}) => {
  if (!runbook) return null;

  // Helper function to parse steps from content
  const parseStepsFromContent = (content: string) => {
    const steps: { id: string; order: number; description: string }[] = [];
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const stepMatch = line.match(/^(\*\*)?Schritt (\d+):(\*\*)?(.*)/i);

      if (stepMatch) {
        const stepNumber = parseInt(stepMatch[2], 10);
        const description = stepMatch[4].trim();

        if (description) {
          steps.push({
            id: `parsed-step-${i}`,
            order: stepNumber,
            description: description
          });
        }
      }
    }

    return steps.sort((a, b) => a.order - b.order);
  };

  const steps = runbook.steps && runbook.steps.length > 0
    ? runbook.steps
    : parseStepsFromContent(runbook.content || '');

  const renderContent = () => {
    if (steps.length > 0) {
      return (
        <div className="space-y-4">
          {runbook.overallDescription && (
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Allgemeine Beschreibung</h3>
              <p className="text-blue-800 whitespace-pre-wrap">{runbook.overallDescription}</p>
            </div>
          )}

          <div className="space-y-3">
            <h3 className="font-semibold text-gray-900 flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              Schritte ({steps.length})
            </h3>
            {steps.map((step, index) => (
              <div key={step.id || index} className="border border-gray-200 rounded-lg p-4 bg-white">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center font-semibold text-sm">
                    {step.order}
                  </div>
                  <div className="flex-1">
                    <p className="text-gray-900 whitespace-pre-wrap">{step.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    }

    // Fallback: Render content as markdown
    return (
      <div className="prose prose-sm max-w-none">
        <div className="whitespace-pre-wrap text-gray-900">{runbook.content}</div>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col [&>button]:hidden">
        {/* Header mit grauem Hintergrund wie in StoerungsForm - füllt den ganzen Bereich */}
        <div className="flex items-center justify-between py-4 px-6 border-b bg-gray-100 -m-6 mb-0 rounded-t-lg">
          <h2 className="text-3xl flex items-center gap-2">
            <BookOpen className="h-6 w-6 text-blue-600" />
            {runbook.title}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onOpenChange(false)}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Meta information */}
        <div className="flex items-center gap-4 text-sm text-gray-600 mt-2 px-6">
          {runbook.affected_systems && runbook.affected_systems.length > 0 && (
            <div className="flex items-center gap-1">
              <AlertTriangle className="h-4 w-4" />
              <span>Betroffene Systeme:</span>
              <div className="flex gap-1">
                {runbook.affected_systems.map((system, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {system}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {runbook.category && runbook.category.length > 0 && (
             <div className="flex items-center gap-1">
               <Tag className="h-4 w-4" />
               <span>Kategorie:</span>
               <div className="flex gap-1">
                 {runbook.category.map((category, index) => (
                   <Badge key={index} variant="outline" className="text-xs">
                     {category}
                   </Badge>
                 ))}
               </div>
             </div>
           )}
        </div>

        <ScrollArea className="flex-1 px-1">
          <div className="py-4">
            {renderContent()}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};
