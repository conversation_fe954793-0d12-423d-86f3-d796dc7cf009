{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "6", "dialect": "sqlite", "tables": {"alle_daten": {"name": "alle_daten", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": false}, "datum": {"autoincrement": false, "name": "datum", "type": "numeric", "primaryKey": false, "notNull": true}, "we_atrl": {"autoincrement": false, "name": "we_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "cutk_lager_atrl": {"autoincrement": false, "name": "cutk_lager_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "cutr_lager_atrl": {"autoincrement": false, "name": "cutr_lager_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "lo_lo_atrl": {"autoincrement": false, "name": "lo_lo_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "uml_atrl": {"autoincrement": false, "name": "uml_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "wa_pos_atrl": {"autoincrement": false, "name": "wa_pos_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "lager_cut_atrl": {"autoincrement": false, "name": "lager_cut_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "plaetze_bel_atrl": {"autoincrement": false, "name": "plaetze_bel_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "sysp_atrl": {"autoincrement": false, "name": "sysp_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "sysp_rucks_atrl": {"autoincrement": false, "name": "sysp_rucks_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "sysst_einz_atrl": {"autoincrement": false, "name": "sysst_einz_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "sysp_einz_bel_atrl": {"autoincrement": false, "name": "sysp_einz_bel_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "trom_bel_atrl": {"autoincrement": false, "name": "trom_bel_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "fuellgrad_atrl": {"autoincrement": false, "name": "fuellgrad_atrl", "type": "real", "primaryKey": false, "notNull": false}, "all_bew_atrl": {"autoincrement": false, "name": "all_bew_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "we_gesamt_atrl": {"autoincrement": false, "name": "we_gesamt_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "we_manl": {"autoincrement": false, "name": "we_manl", "type": "integer", "primaryKey": false, "notNull": false}, "cut_lager_kunde_manl": {"autoincrement": false, "name": "cut_lager_kunde_manl", "type": "integer", "primaryKey": false, "notNull": false}, "cut_lager_rest_995_manl": {"autoincrement": false, "name": "cut_lager_rest_995_manl", "type": "integer", "primaryKey": false, "notNull": false}, "lagerol_lagero_312_manl": {"autoincrement": false, "name": "lagerol_lagero_312_manl", "type": "integer", "primaryKey": false, "notNull": false}, "ausl_kommi_manl": {"autoincrement": false, "name": "ausl_kommi_manl", "type": "integer", "primaryKey": false, "notNull": false}, "rueck_kommi_manl": {"autoincrement": false, "name": "rue<PERSON>_kommi_manl", "type": "integer", "primaryKey": false, "notNull": false}, "wa_pos_601_manl": {"autoincrement": false, "name": "wa_pos_601_manl", "type": "integer", "primaryKey": false, "notNull": false}, "lager_cut_994_manl": {"autoincrement": false, "name": "lager_cut_994_manl", "type": "integer", "primaryKey": false, "notNull": false}, "plaetze_bel_manl": {"autoincrement": false, "name": "plaetze_bel_manl", "type": "integer", "primaryKey": false, "notNull": false}, "fuellgrad_manl": {"autoincrement": false, "name": "fuellgrad_manl", "type": "real", "primaryKey": false, "notNull": false}, "all_bew_manl": {"autoincrement": false, "name": "all_bew_manl", "type": "integer", "primaryKey": false, "notNull": false}, "we_manl_2": {"autoincrement": false, "name": "we_manl_2", "type": "integer", "primaryKey": false, "notNull": false}, "cut_lager_kunde_996_aril": {"autoincrement": false, "name": "cut_lager_kunde_996_aril", "type": "integer", "primaryKey": false, "notNull": false}, "cut_lager_rest_aril": {"autoincrement": false, "name": "cut_lager_rest_aril", "type": "integer", "primaryKey": false, "notNull": false}, "wa_pos_601_aril": {"autoincrement": false, "name": "wa_pos_601_aril", "type": "integer", "primaryKey": false, "notNull": false}, "uml_aril": {"autoincrement": false, "name": "uml_aril", "type": "integer", "primaryKey": false, "notNull": false}, "lager_cut_aril": {"autoincrement": false, "name": "lager_cut_aril", "type": "integer", "primaryKey": false, "notNull": false}, "plaetze_bel_aril": {"autoincrement": false, "name": "plaetze_bel_aril", "type": "integer", "primaryKey": false, "notNull": false}, "systab_stk_aril": {"autoincrement": false, "name": "systab_stk_aril", "type": "integer", "primaryKey": false, "notNull": false}, "systab_rucks_stk_aril": {"autoincrement": false, "name": "systab_rucks_stk_aril", "type": "integer", "primaryKey": false, "notNull": false}, "systab_einzel_stk_aril": {"autoincrement": false, "name": "systab_einzel_stk_aril", "type": "integer", "primaryKey": false, "notNull": false}, "systab_belegt_einzel_aril": {"autoincrement": false, "name": "systab_belegt_einzel_aril", "type": "integer", "primaryKey": false, "notNull": false}, "ring_belegt_aril": {"autoincrement": false, "name": "ring_belegt_aril", "type": "integer", "primaryKey": false, "notNull": false}, "fuellgrad_aril": {"autoincrement": false, "name": "fuellgrad_aril", "type": "real", "primaryKey": false, "notNull": false}, "bew_aril": {"autoincrement": false, "name": "bew_aril", "type": "integer", "primaryKey": false, "notNull": false}, "ta_sesamt_abl": {"autoincrement": false, "name": "ta_sesamt_abl", "type": "integer", "primaryKey": false, "notNull": false}, "ta_tt_abl": {"autoincrement": false, "name": "ta_tt_abl", "type": "integer", "primaryKey": false, "notNull": false}, "ta_tr_abl": {"autoincrement": false, "name": "ta_tr_abl", "type": "integer", "primaryKey": false, "notNull": false}, "ta_rr_abl": {"autoincrement": false, "name": "ta_rr_abl", "type": "integer", "primaryKey": false, "notNull": false}, "schnitte_ges_abl": {"autoincrement": false, "name": "schnitte_ges_abl", "type": "integer", "primaryKey": false, "notNull": false}, "cuttopick_abl": {"autoincrement": false, "name": "cuttopick_abl", "type": "integer", "primaryKey": false, "notNull": false}, "atrl_sap_ni": {"autoincrement": false, "name": "atrl_sap_ni", "type": "integer", "primaryKey": false, "notNull": false}, "atrl_witron_nio": {"autoincrement": false, "name": "atrl_witron_nio", "type": "integer", "primaryKey": false, "notNull": false}, "atrl_siemens_nio": {"autoincrement": false, "name": "atrl_siemens_nio", "type": "integer", "primaryKey": false, "notNull": false}, "atrl_prozess_nio": {"autoincrement": false, "name": "atrl_prozess_nio", "type": "integer", "primaryKey": false, "notNull": false}, "atrl_sonst_nio": {"autoincrement": false, "name": "atrl_sonst_nio", "type": "integer", "primaryKey": false, "notNull": false}, "aril_sap_nio": {"autoincrement": false, "name": "aril_sap_nio", "type": "integer", "primaryKey": false, "notNull": false}, "aril_witron_nio": {"autoincrement": false, "name": "aril_witron_nio", "type": "integer", "primaryKey": false, "notNull": false}, "aril_siemens_nio": {"autoincrement": false, "name": "aril_siemens_nio", "type": "integer", "primaryKey": false, "notNull": false}, "aril_nio": {"autoincrement": false, "name": "aril_nio", "type": "integer", "primaryKey": false, "notNull": false}, "aril2_nio": {"autoincrement": false, "name": "aril2_nio", "type": "integer", "primaryKey": false, "notNull": false}, "atrl_störung_verf": {"autoincrement": false, "name": "atrl_störung_verf", "type": "integer", "primaryKey": false, "notNull": false}, "aril_störung_verf": {"autoincrement": false, "name": "aril_störung_verf", "type": "integer", "primaryKey": false, "notNull": false}, "fts_rbg_mfr_störung_verf": {"autoincrement": false, "name": "fts_rbg_mfr_störung_verf", "type": "integer", "primaryKey": false, "notNull": false}, "fts_rbg_störung_verf": {"autoincrement": false, "name": "fts_rbg_störung_verf", "type": "integer", "primaryKey": false, "notNull": false}, "fts_störung_verf": {"autoincrement": false, "name": "fts_störung_verf", "type": "integer", "primaryKey": false, "notNull": false}, "aril_atrl_fts_störung_verf": {"autoincrement": false, "name": "aril_atrl_fts_störung_verf", "type": "integer", "primaryKey": false, "notNull": false}, "itm_störung_verf": {"autoincrement": false, "name": "itm_störung_verf", "type": "integer", "primaryKey": false, "notNull": false}, "sap_störung_verf": {"autoincrement": false, "name": "sap_störung_verf", "type": "integer", "primaryKey": false, "notNull": false}, "servicegrad": {"autoincrement": false, "name": "servicegrad", "type": "real", "primaryKey": false, "notNull": false}, "wa_einzel_pos": {"autoincrement": false, "name": "wa_einzel_pos", "type": "integer", "primaryKey": false, "notNull": false}, "we": {"autoincrement": false, "name": "we", "type": "integer", "primaryKey": false, "notNull": false}, "wa": {"autoincrement": false, "name": "wa", "type": "integer", "primaryKey": false, "notNull": false}, "wa_lagebest": {"autoincrement": false, "name": "wa_lagebest", "type": "integer", "primaryKey": false, "notNull": false}, "wa_mehr_pos": {"autoincrement": false, "name": "wa_mehr_pos", "type": "integer", "primaryKey": false, "notNull": false}, "we_ges_manl_atrl": {"autoincrement": false, "name": "we_ges_manl_atrl", "type": "integer", "primaryKey": false, "notNull": false}, "wa_ges_abl_manl_atrl_aril": {"autoincrement": false, "name": "wa_ges_abl_manl_atrl_aril", "type": "integer", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"alle_daten_datum_idx": {"name": "alle_daten_datum_idx", "columns": ["datum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "ManL": {"name": "ManL", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": false}, "Datum": {"autoincrement": false, "name": "Datum", "type": "numeric", "primaryKey": false, "notNull": false}, "Wareneingang": {"autoincrement": false, "name": "Wareneingang", "type": "integer", "primaryKey": false, "notNull": false}, "cuttingLagerKunde": {"autoincrement": false, "name": "cuttingLagerKunde", "type": "integer", "primaryKey": false, "notNull": false}, "cuttingLagerRest": {"autoincrement": false, "name": "cuttingLagerRest", "type": "integer", "primaryKey": false, "notNull": false}, "ruecklagerungVonKommi": {"autoincrement": false, "name": "ruecklagerungVonKommi", "type": "integer", "primaryKey": false, "notNull": false}, "waTaPos": {"autoincrement": false, "name": "waTaPos", "type": "integer", "primaryKey": false, "notNull": false}, "lagerCutting": {"autoincrement": false, "name": "lagerCutting", "type": "integer", "primaryKey": false, "notNull": false}, "belegtePlaetze": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "Auslastung": {"autoincrement": false, "name": "Auslastung", "type": "real", "primaryKey": false, "notNull": false}, "alleBewegungen": {"autoincrement": false, "name": "alleBewegungen", "type": "integer", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"ManL_Datum_idx": {"name": "ManL_Datum_idx", "columns": ["Datum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "schnitte": {"name": "schnitte", "columns": {"id": {"autoincrement": false, "name": "id", "type": "integer", "primaryKey": true, "notNull": false}, "Datum": {"autoincrement": false, "name": "Datum", "type": "text", "primaryKey": false, "notNull": false}, "M5-R-H1": {"autoincrement": false, "name": "M5-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M6-T-H1": {"autoincrement": false, "name": "M6-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M7-R-H1": {"autoincrement": false, "name": "M7-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M8-T-H1": {"autoincrement": false, "name": "M8-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M9-R-H1": {"autoincrement": false, "name": "M9-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M10-T-H1": {"autoincrement": false, "name": "M10-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M11-R-H1": {"autoincrement": false, "name": "M11-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M12-T-H1": {"autoincrement": false, "name": "M12-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M13-R-H1": {"autoincrement": false, "name": "M13-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M14-T-H1": {"autoincrement": false, "name": "M14-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M15-R-H1": {"autoincrement": false, "name": "M15-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M16-T-H1": {"autoincrement": false, "name": "M16-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M17-R-H1": {"autoincrement": false, "name": "M17-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M18-T-H1": {"autoincrement": false, "name": "M18-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M19-T-H1": {"autoincrement": false, "name": "M19-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M20-T-H1": {"autoincrement": false, "name": "M20-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M21-R-H1": {"autoincrement": false, "name": "M21-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M23-T-H1": {"autoincrement": false, "name": "M23-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M25-RR-H1": {"autoincrement": false, "name": "M25-RR-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M26-T-H1": {"autoincrement": false, "name": "M26-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "Sum-H1": {"autoincrement": false, "name": "Sum-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M1-T-H3": {"autoincrement": false, "name": "M1-T-H3", "type": "integer", "primaryKey": false, "notNull": false}, "M2-T-H3": {"autoincrement": false, "name": "M2-T-H3", "type": "integer", "primaryKey": false, "notNull": false}, "M3-R-H3": {"autoincrement": false, "name": "M3-R-H3", "type": "integer", "primaryKey": false, "notNull": false}, "M4-T-H3": {"autoincrement": false, "name": "M4-T-H3", "type": "integer", "primaryKey": false, "notNull": false}, "M22-T-H3": {"autoincrement": false, "name": "M22-T-H3", "type": "integer", "primaryKey": false, "notNull": false}, "M24-T-H3": {"autoincrement": false, "name": "M24-T-H3", "type": "integer", "primaryKey": false, "notNull": false}, "M27-R-H3": {"autoincrement": false, "name": "M27-R-H3", "type": "integer", "primaryKey": false, "notNull": false}, "Sum-H3": {"autoincrement": false, "name": "Sum-H3", "type": "integer", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"schnitte_Datum_idx": {"name": "schnitte_Datum_idx", "columns": ["Datum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "maschinen": {"name": "<PERSON><PERSON><PERSON>", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": false}, "Machine": {"autoincrement": false, "name": "Machine", "type": "text", "primaryKey": false, "notNull": false}, "schnitteProStd": {"autoincrement": false, "name": "schnitteProStd", "type": "real", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "bestandRest": {"name": "bestandRest", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": false}, "Lagertyp": {"autoincrement": false, "name": "Lagertyp", "type": "text", "primaryKey": false, "notNull": false}, "Lagerplatz": {"autoincrement": false, "name": "Lagerplatz", "type": "text", "primaryKey": false, "notNull": false}, "Material": {"autoincrement": false, "name": "Material", "type": "text", "primaryKey": false, "notNull": false}, "Charge": {"autoincrement": false, "name": "Charge", "type": "text", "primaryKey": false, "notNull": false}, "Dauer": {"autoincrement": false, "name": "<PERSON><PERSON>", "type": "real", "primaryKey": false, "notNull": false}, "Lagerbereich": {"autoincrement": false, "name": "Lagerbereich", "type": "text", "primaryKey": false, "notNull": false}, "Lagerplatztyp": {"autoincrement": false, "name": "Lagerplatztyp", "type": "text", "primaryKey": false, "notNull": false}, "Lagerplatzaufteilung": {"autoincrement": false, "name": "Lagerplatzaufteilung", "type": "text", "primaryKey": false, "notNull": false}, "Auslagerungssperre": {"autoincrement": false, "name": "Auslagerungssperre", "type": "text", "primaryKey": false, "notNull": false}, "Einlagerungssperre": {"autoincrement": false, "name": "Einlagerungssperre", "type": "text", "primaryKey": false, "notNull": false}, "Sperrgrund": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "Letzte Bewegung": {"autoincrement": false, "name": "Letzte Bewegung", "type": "text", "primaryKey": false, "notNull": false}, "Uhrzeit": {"autoincrement": false, "name": "Uhrzeit", "type": "text", "primaryKey": false, "notNull": false}, "TA-Nummer": {"autoincrement": false, "name": "TA-Nummer", "type": "text", "primaryKey": false, "notNull": false}, "TA-Position": {"autoincrement": false, "name": "TA-Position", "type": "text", "primaryKey": false, "notNull": false}, "Letzter Änderer": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "Letzte Änderung": {"autoincrement": false, "name": "Letzte Änderung", "type": "text", "primaryKey": false, "notNull": false}, "Wareneingangsdatum": {"autoincrement": false, "name": "Wareneingangsdatum", "type": "text", "primaryKey": false, "notNull": false}, "WE-Nummer": {"autoincrement": false, "name": "WE-<PERSON><PERSON>mer", "type": "text", "primaryKey": false, "notNull": false}, "WE-Position": {"autoincrement": false, "name": "WE-Position", "type": "text", "primaryKey": false, "notNull": false}, "Lieferung": {"autoincrement": false, "name": "Lieferung", "type": "text", "primaryKey": false, "notNull": false}, "Position": {"autoincrement": false, "name": "Position", "type": "text", "primaryKey": false, "notNull": false}, "Lagereinheitentyp": {"autoincrement": false, "name": "Lagereinheitentyp", "type": "text", "primaryKey": false, "notNull": false}, "Gesamtbestand": {"autoincrement": false, "name": "Gesamtbestand", "type": "real", "primaryKey": false, "notNull": false}, "Lagereinheit": {"autoincrement": false, "name": "Lagereinheit", "type": "text", "primaryKey": false, "notNull": false}, "aufnahmeDatum": {"autoincrement": false, "name": "aufnahmeDatum", "type": "text", "primaryKey": false, "notNull": false}, "aufnahmeZeit": {"autoincrement": false, "name": "aufnahmeZeit", "type": "text", "primaryKey": false, "notNull": false}, "import_timestamp": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "import_timestamp", "type": "numeric", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"bestandRest_import_timestamp_idx": {"name": "bestandRest_import_timestamp_idx", "columns": ["import_timestamp"], "isUnique": false}, "bestandRest_aufnahmeDatum_idx": {"name": "bestandRest_aufnahmeDatum_idx", "columns": ["aufnahmeDatum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "auslastung240": {"name": "auslastung240", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": false}, "aufnahmeDatum": {"autoincrement": false, "name": "aufnahmeDatum", "type": "text", "primaryKey": false, "notNull": false}, "aufnahmeZeit": {"autoincrement": false, "name": "aufnahmeZeit", "type": "text", "primaryKey": false, "notNull": false}, "maxPlaetze": {"autoincrement": false, "name": "max<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "auslastung": {"autoincrement": false, "name": "auslastung", "type": "text", "primaryKey": false, "notNull": false}, "maxA": {"autoincrement": false, "name": "maxA", "type": "text", "primaryKey": false, "notNull": false}, "maxB": {"autoincrement": false, "name": "maxB", "type": "text", "primaryKey": false, "notNull": false}, "maxC": {"autoincrement": false, "name": "maxC", "type": "text", "primaryKey": false, "notNull": false}, "auslastungA": {"autoincrement": false, "name": "auslastungA", "type": "text", "primaryKey": false, "notNull": false}, "auslastungB": {"autoincrement": false, "name": "auslastungB", "type": "text", "primaryKey": false, "notNull": false}, "auslastungC": {"autoincrement": false, "name": "auslastungC", "type": "text", "primaryKey": false, "notNull": false}, "import_timestamp": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "import_timestamp", "type": "numeric", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"auslastung240_import_timestamp_idx": {"name": "auslastung240_import_timestamp_idx", "columns": ["import_timestamp"], "isUnique": false}, "auslastung240_aufnahmeDatum_idx": {"name": "auslastung240_aufnahmeDatum_idx", "columns": ["aufnahmeDatum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "auslastung200": {"name": "auslastung200", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": false}, "aufnahmeDatum": {"autoincrement": false, "name": "aufnahmeDatum", "type": "text", "primaryKey": false, "notNull": false}, "aufnahmeZeit": {"autoincrement": false, "name": "aufnahmeZeit", "type": "text", "primaryKey": false, "notNull": false}, "maxPlaetze": {"autoincrement": false, "name": "max<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "auslastung": {"autoincrement": false, "name": "auslastung", "type": "text", "primaryKey": false, "notNull": false}, "maxA": {"autoincrement": false, "name": "maxA", "type": "text", "primaryKey": false, "notNull": false}, "maxB": {"autoincrement": false, "name": "maxB", "type": "text", "primaryKey": false, "notNull": false}, "maxC": {"autoincrement": false, "name": "maxC", "type": "text", "primaryKey": false, "notNull": false}, "auslastungA": {"autoincrement": false, "name": "auslastungA", "type": "text", "primaryKey": false, "notNull": false}, "auslastungB": {"autoincrement": false, "name": "auslastungB", "type": "text", "primaryKey": false, "notNull": false}, "auslastungC": {"autoincrement": false, "name": "auslastungC", "type": "text", "primaryKey": false, "notNull": false}, "import_timestamp": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "import_timestamp", "type": "numeric", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"auslastung200_import_timestamp_idx": {"name": "auslastung200_import_timestamp_idx", "columns": ["import_timestamp"], "isUnique": false}, "auslastung200_aufnahmeDatum_idx": {"name": "auslastung200_aufnahmeDatum_idx", "columns": ["aufnahmeDatum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "Stoerungen": {"name": "St<PERSON>rung<PERSON>", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "text", "primaryKey": false, "notNull": false}, "severity": {"autoincrement": false, "name": "severity", "type": "text", "primaryKey": false, "notNull": true}, "status": {"autoincrement": false, "name": "status", "type": "text", "primaryKey": false, "notNull": true}, "category": {"autoincrement": false, "name": "category", "type": "text", "primaryKey": false, "notNull": false}, "affected_system": {"autoincrement": false, "name": "affected_system", "type": "text", "primaryKey": false, "notNull": false}, "location": {"autoincrement": false, "name": "location", "type": "text", "primaryKey": false, "notNull": false}, "reported_by": {"autoincrement": false, "name": "reported_by", "type": "text", "primaryKey": false, "notNull": false}, "assigned_to": {"autoincrement": false, "name": "assigned_to", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}, "resolved_at": {"autoincrement": false, "name": "resolved_at", "type": "numeric", "primaryKey": false, "notNull": false}, "mttr_minutes": {"autoincrement": false, "name": "mttr_minutes", "type": "integer", "primaryKey": false, "notNull": false}, "tags": {"autoincrement": false, "name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "resolution_steps": {"autoincrement": false, "name": "resolution_steps", "type": "text", "primaryKey": false, "notNull": false}, "root_cause": {"autoincrement": false, "name": "root_cause", "type": "text", "primaryKey": false, "notNull": false}, "lessons_learned": {"autoincrement": false, "name": "lessons_learned", "type": "text", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"Stoerungen_affected_system_idx": {"name": "St<PERSON>rungen_affected_system_idx", "columns": ["affected_system"], "isUnique": false}, "Stoerungen_severity_idx": {"name": "Stoerungen_severity_idx", "columns": ["severity"], "isUnique": false}, "Stoerungen_status_idx": {"name": "Stoerungen_status_idx", "columns": ["status"], "isUnique": false}, "Stoerungen_created_at_idx": {"name": "<PERSON><PERSON><PERSON><PERSON>_created_at_idx", "columns": ["created_at"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "StoerungsComments": {"name": "StoerungsComments", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "stoerung_id": {"autoincrement": false, "name": "stoerung_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "comment": {"autoincrement": false, "name": "comment", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "caretaker_id": {"default": 3, "autoincrement": false, "name": "caretaker_id", "type": "text", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"StoerungsComments_created_at_idx": {"name": "StoerungsComments_created_at_idx", "columns": ["created_at"], "isUnique": false}, "StoerungsComments_stoerung_id_idx": {"name": "StoerungsComments_stoerung_id_idx", "columns": ["stoerung_id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "ATrL": {"name": "ATrL", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "Datum": {"autoincrement": false, "name": "Datum", "type": "text", "primaryKey": false, "notNull": false}, "umlagerungen": {"autoincrement": false, "name": "umlagerungen", "type": "integer", "primaryKey": false, "notNull": false}, "waTaPositionen": {"autoincrement": false, "name": "waTaPositionen", "type": "integer", "primaryKey": false, "notNull": false}, "belegtePlaetze": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "davonSystempaletten": {"autoincrement": false, "name": "davonSystempaletten", "type": "integer", "primaryKey": false, "notNull": false}, "SystempalettenstapelRucksackpaetzen": {"autoincrement": false, "name": "SystempalettenstapelRucksackpaetzen", "type": "integer", "primaryKey": false, "notNull": false}, "SystempalettenstapelEinzel": {"autoincrement": false, "name": "SystempalettenstapelEinzel", "type": "integer", "primaryKey": false, "notNull": false}, "PlaetzeSystempalettenstapelEinzel": {"autoincrement": false, "name": "PlaetzeSystempalettenstapelEinzel", "type": "integer", "primaryKey": false, "notNull": false}, "plaetzeMitTrommelBelegt": {"autoincrement": false, "name": "plaetzeMitTrommelBelegt", "type": "integer", "primaryKey": false, "notNull": false}, "Auslastung": {"autoincrement": false, "name": "Auslastung", "type": "real", "primaryKey": false, "notNull": false}, "Bewegungen": {"autoincrement": false, "name": "Bewegungen", "type": "integer", "primaryKey": false, "notNull": false}, "EinlagerungAblKunde": {"autoincrement": false, "name": "EinlagerungAblKunde", "type": "integer", "primaryKey": false, "notNull": false}, "EinlagerungAblRest": {"autoincrement": false, "name": "EinlagerungAblRest", "type": "integer", "primaryKey": false, "notNull": false}, "AuslagerungAbl": {"autoincrement": false, "name": "AuslagerungAbl", "type": "integer", "primaryKey": false, "notNull": false}, "weAtrl": {"autoincrement": false, "name": "weAtrl", "type": "integer", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"ATrL_Datum_idx": {"name": "ATrL_Datum_idx", "columns": ["Datum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "Ablaengerei": {"name": "Ablaengerei", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "Datum": {"autoincrement": false, "name": "Datum", "type": "text", "primaryKey": false, "notNull": false}, "cutLagerK220": {"default": 0, "autoincrement": false, "name": "cutLagerK220", "type": "integer", "primaryKey": false, "notNull": false}, "cutLagerR220": {"default": 0, "autoincrement": false, "name": "cutLagerR220", "type": "integer", "primaryKey": false, "notNull": false}, "lagerCut220": {"default": 0, "autoincrement": false, "name": "lagerCut220", "type": "integer", "primaryKey": false, "notNull": false}, "cutLagerK240": {"default": 0, "autoincrement": false, "name": "cutLagerK240", "type": "integer", "primaryKey": false, "notNull": false}, "cutLagerR240": {"default": 0, "autoincrement": false, "name": "cutLagerR240", "type": "integer", "primaryKey": false, "notNull": false}, "lagerCut240": {"default": 0, "autoincrement": false, "name": "lagerCut240", "type": "integer", "primaryKey": false, "notNull": false}, "cutTT": {"default": 0, "autoincrement": false, "name": "cutTT", "type": "integer", "primaryKey": false, "notNull": false}, "cutTR": {"default": 0, "autoincrement": false, "name": "cutTR", "type": "integer", "primaryKey": false, "notNull": false}, "cutRR": {"default": 0, "autoincrement": false, "name": "cutRR", "type": "integer", "primaryKey": false, "notNull": false}, "cutGesamt": {"default": 0, "autoincrement": false, "name": "cutGesamt", "type": "integer", "primaryKey": false, "notNull": false}, "pickCut": {"default": 0, "autoincrement": false, "name": "pickCut", "type": "integer", "primaryKey": false, "notNull": false}, "cutLager200": {"default": 0, "autoincrement": false, "name": "cutLager200", "type": "integer", "primaryKey": false, "notNull": false}, "cutLagerK200": {"default": 0, "autoincrement": false, "name": "cutLagerK200", "type": "integer", "primaryKey": false, "notNull": false}, "lagerCut200": {"default": 0, "autoincrement": false, "name": "lagerCut200", "type": "integer", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"Ablaengerei_Datum_idx": {"name": "Ablaengerei_Datum_idx", "columns": ["Datum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "System": {"name": "System", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "Datum": {"autoincrement": false, "name": "Datum", "type": "text", "primaryKey": false, "notNull": false}, "nioSapAtrl": {"autoincrement": false, "name": "nioSapAtrl", "type": "integer", "primaryKey": false, "notNull": false}, "nioWitronAtrl": {"autoincrement": false, "name": "nioWitronAtrl", "type": "integer", "primaryKey": false, "notNull": false}, "nioSiemensAtrl": {"autoincrement": false, "name": "nioSiemensAtrl", "type": "integer", "primaryKey": false, "notNull": false}, "nioProzessAtrl": {"autoincrement": false, "name": "nioProzessAtrl", "type": "integer", "primaryKey": false, "notNull": false}, "nioSonstigesAtrl": {"autoincrement": false, "name": "nioSonstigesAtrl", "type": "integer", "primaryKey": false, "notNull": false}, "nioSapAril": {"autoincrement": false, "name": "nioSapAril", "type": "integer", "primaryKey": false, "notNull": false}, "nioWitronAril": {"autoincrement": false, "name": "nioWitronAril", "type": "integer", "primaryKey": false, "notNull": false}, "nioSiemensAril": {"autoincrement": false, "name": "nioSiemensAril", "type": "integer", "primaryKey": false, "notNull": false}, "nioProzessAril": {"autoincrement": false, "name": "nioProzessAril", "type": "integer", "primaryKey": false, "notNull": false}, "nioSonstigesAril": {"autoincrement": false, "name": "nioSonstigesAril", "type": "integer", "primaryKey": false, "notNull": false}, "verfuegbarkeitAnzahlStoerungenAtrl": {"autoincrement": false, "name": "verfuegbarkeitAnzahlStoerungenAtrl", "type": "real", "primaryKey": false, "notNull": false}, "verfuegbarkeitAnzahlStoerungenAril": {"autoincrement": false, "name": "verfuegbarkeitAnzahlStoerungenAril", "type": "real", "primaryKey": false, "notNull": false}, "verfuegbarkeitAtrl_FT_RBG_MFR1": {"autoincrement": false, "name": "verfuegbarkeitAtrl_FT_RBG_MFR1", "type": "real", "primaryKey": false, "notNull": false}, "verfuegbarkeitAril_FT_RBG": {"autoincrement": false, "name": "verfuegbarkeitAril_FT_RBG", "type": "real", "primaryKey": false, "notNull": false}, "verfuegbarkeitFTS": {"autoincrement": false, "name": "verfuegbarkeitFTS", "type": "real", "primaryKey": false, "notNull": false}, "gesamtverfuegbarkeit_AtrL_ARiL_FTS": {"autoincrement": false, "name": "gesamtverfuegbarkeit_AtrL_ARiL_FTS", "type": "real", "primaryKey": false, "notNull": false}, "verfuegbarkeitITM": {"autoincrement": false, "name": "verfuegbarkeitITM", "type": "real", "primaryKey": false, "notNull": false}, "verfuegbarkeitSAP": {"autoincrement": false, "name": "verfuegbarkeitSAP", "type": "real", "primaryKey": false, "notNull": false}, "verfuegbarkeitServicegrad": {"autoincrement": false, "name": "verfuegbarkeitServicegrad", "type": "real", "primaryKey": false, "notNull": false}, "weGesamtAtrl_Manl": {"autoincrement": false, "name": "weGesamtAtrl_Manl", "type": "integer", "primaryKey": false, "notNull": false}, "waTaPosGesamt_Atrl_Manl_Aril_Abl": {"autoincrement": false, "name": "waTaPosGesamt_Atrl_Manl_Aril_Abl", "type": "integer", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"System_Datum_idx": {"name": "System_Datum_idx", "columns": ["Datum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "WE": {"name": "WE", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "Datum": {"autoincrement": false, "name": "Datum", "type": "text", "primaryKey": false, "notNull": false}, "weAtrl": {"default": 0, "autoincrement": false, "name": "weAtrl", "type": "integer", "primaryKey": false, "notNull": false}, "weManl": {"default": 0, "autoincrement": false, "name": "weManl", "type": "integer", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"WE_Datum_idx": {"name": "WE_Datum_idx", "columns": ["Datum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "dispatch_data": {"name": "dispatch_data", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "datum": {"autoincrement": false, "name": "datum", "type": "text", "primaryKey": false, "notNull": false}, "tag": {"autoincrement": false, "name": "tag", "type": "integer", "primaryKey": false, "notNull": false}, "monat": {"autoincrement": false, "name": "monat", "type": "integer", "primaryKey": false, "notNull": false}, "kw": {"autoincrement": false, "name": "kw", "type": "integer", "primaryKey": false, "notNull": false}, "jahr": {"autoincrement": false, "name": "jahr", "type": "integer", "primaryKey": false, "notNull": false}, "servicegrad": {"autoincrement": false, "name": "servicegrad", "type": "real", "primaryKey": false, "notNull": false}, "ausgeliefert_lup": {"autoincrement": false, "name": "ausgeliefert_lup", "type": "integer", "primaryKey": false, "notNull": false}, "rueckstaendig": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "produzierte_tonnagen": {"autoincrement": false, "name": "produzierte_tonnagen", "type": "real", "primaryKey": false, "notNull": false}, "direktverladung_kiaa": {"autoincrement": false, "name": "direktverladung_kiaa", "type": "integer", "primaryKey": false, "notNull": false}, "umschlag": {"autoincrement": false, "name": "umschlag", "type": "integer", "primaryKey": false, "notNull": false}, "kg_pro_colli": {"autoincrement": false, "name": "kg_pro_colli", "type": "real", "primaryKey": false, "notNull": false}, "elefanten": {"autoincrement": false, "name": "elefanten", "type": "integer", "primaryKey": false, "notNull": false}, "atrl": {"autoincrement": false, "name": "atrl", "type": "integer", "primaryKey": false, "notNull": false}, "aril": {"autoincrement": false, "name": "aril", "type": "integer", "primaryKey": false, "notNull": false}, "fuellgrad_aril": {"autoincrement": false, "name": "fuellgrad_aril", "type": "real", "primaryKey": false, "notNull": false}, "qm_angenommen": {"autoincrement": false, "name": "qm_angenommen", "type": "integer", "primaryKey": false, "notNull": false}, "qm_abgelehnt": {"autoincrement": false, "name": "qm_ab<PERSON><PERSON>nt", "type": "integer", "primaryKey": false, "notNull": false}, "qm_offen": {"autoincrement": false, "name": "qm_offen", "type": "integer", "primaryKey": false, "notNull": false}, "mitarbeiter_std": {"autoincrement": false, "name": "mitarbeiter_std", "type": "real", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"dispatch_data_datum_idx": {"name": "dispatch_data_datum_idx", "columns": ["datum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "SystemStatus": {"name": "SystemStatus", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "system_name": {"autoincrement": false, "name": "system_name", "type": "text", "primaryKey": false, "notNull": true}, "status": {"autoincrement": false, "name": "status", "type": "text", "primaryKey": false, "notNull": true}, "last_check": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "last_check", "type": "numeric", "primaryKey": false, "notNull": true}, "metadata": {"autoincrement": false, "name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "_prisma_migrations": {"name": "_prisma_migrations", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "checksum": {"autoincrement": false, "name": "checksum", "type": "text", "primaryKey": false, "notNull": true}, "finished_at": {"autoincrement": false, "name": "finished_at", "type": "numeric", "primaryKey": false, "notNull": false}, "migration_name": {"autoincrement": false, "name": "migration_name", "type": "text", "primaryKey": false, "notNull": true}, "logs": {"autoincrement": false, "name": "logs", "type": "text", "primaryKey": false, "notNull": false}, "rolled_back_at": {"autoincrement": false, "name": "rolled_back_at", "type": "numeric", "primaryKey": false, "notNull": false}, "started_at": {"default": "(current_timestamp)", "autoincrement": false, "name": "started_at", "type": "numeric", "primaryKey": false, "notNull": true}, "applied_steps_count": {"default": 0, "autoincrement": false, "name": "applied_steps_count", "type": "integer", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "Role": {"name": "Role", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "text", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"Role_name_key": {"name": "Role_name_key", "columns": ["name"], "isUnique": true}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "_RoleToUser": {"name": "_RoleToUser", "columns": {"A": {"autoincrement": false, "name": "A", "type": "integer", "primaryKey": false, "notNull": true}, "B": {"autoincrement": false, "name": "B", "type": "integer", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"_RoleToUser_B_index": {"name": "_RoleToUser_B_index", "columns": ["B"], "isUnique": false}, "_RoleToUser_AB_unique": {"name": "_RoleToUser_AB_unique", "columns": ["A", "B"], "isUnique": true}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "cache_statistics": {"name": "cache_statistics", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "totalEntries": {"autoincrement": false, "name": "totalEntries", "type": "integer", "primaryKey": false, "notNull": true}, "totalSize": {"autoincrement": false, "name": "totalSize", "type": "integer", "primaryKey": false, "notNull": true}, "hitRate": {"autoincrement": false, "name": "hitRate", "type": "real", "primaryKey": false, "notNull": true}, "missRate": {"autoincrement": false, "name": "missRate", "type": "real", "primaryKey": false, "notNull": true}, "evictionCount": {"autoincrement": false, "name": "evictionCount", "type": "integer", "primaryKey": false, "notNull": true}, "averageAccessTime": {"autoincrement": false, "name": "averageAccessTime", "type": "real", "primaryKey": false, "notNull": true}, "timestamp": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "timestamp", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"cache_statistics_timestamp_idx": {"name": "cache_statistics_timestamp_idx", "columns": ["timestamp"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "performance_metrics": {"name": "performance_metrics", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "metricId": {"autoincrement": false, "name": "metricId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"autoincrement": false, "name": "type", "type": "text", "primaryKey": false, "notNull": true}, "timestamp": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "timestamp", "type": "numeric", "primaryKey": false, "notNull": true}, "duration": {"autoincrement": false, "name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "success": {"autoincrement": false, "name": "success", "type": "numeric", "primaryKey": false, "notNull": true}, "details": {"autoincrement": false, "name": "details", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "createdAt", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"performance_metrics_success_idx": {"name": "performance_metrics_success_idx", "columns": ["success"], "isUnique": false}, "performance_metrics_type_idx": {"name": "performance_metrics_type_idx", "columns": ["type"], "isUnique": false}, "performance_metrics_timestamp_idx": {"name": "performance_metrics_timestamp_idx", "columns": ["timestamp"], "isUnique": false}, "performance_metrics_metricId_key": {"name": "performance_metrics_metricId_key", "columns": ["metricId"], "isUnique": true}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "response_time_metrics": {"name": "response_time_metrics", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "metricId": {"autoincrement": false, "name": "metricId", "type": "text", "primaryKey": false, "notNull": true}, "endpoint": {"autoincrement": false, "name": "endpoint", "type": "text", "primaryKey": false, "notNull": true}, "enriched": {"autoincrement": false, "name": "enriched", "type": "numeric", "primaryKey": false, "notNull": true}, "totalTime": {"autoincrement": false, "name": "totalTime", "type": "integer", "primaryKey": false, "notNull": true}, "enrichmentTime": {"autoincrement": false, "name": "enrichmentTime", "type": "integer", "primaryKey": false, "notNull": true}, "llmTime": {"autoincrement": false, "name": "llmTime", "type": "integer", "primaryKey": false, "notNull": true}, "dataSize": {"autoincrement": false, "name": "dataSize", "type": "integer", "primaryKey": false, "notNull": true}, "success": {"autoincrement": false, "name": "success", "type": "numeric", "primaryKey": false, "notNull": true}, "timestamp": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "timestamp", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"response_time_metrics_success_idx": {"name": "response_time_metrics_success_idx", "columns": ["success"], "isUnique": false}, "response_time_metrics_metricId_key": {"name": "response_time_metrics_metricId_key", "columns": ["metricId"], "isUnique": true}, "response_time_metrics_timestamp_idx": {"name": "response_time_metrics_timestamp_idx", "columns": ["timestamp"], "isUnique": false}, "response_time_metrics_endpoint_idx": {"name": "response_time_metrics_endpoint_idx", "columns": ["endpoint"], "isUnique": false}, "response_time_metrics_enriched_idx": {"name": "response_time_metrics_enriched_idx", "columns": ["enriched"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "intent_recognition_metrics": {"name": "intent_recognition_metrics", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "metricId": {"autoincrement": false, "name": "metricId", "type": "text", "primaryKey": false, "notNull": true}, "messageHash": {"autoincrement": false, "name": "messageHash", "type": "text", "primaryKey": false, "notNull": true}, "detectedIntents": {"autoincrement": false, "name": "detectedIntents", "type": "text", "primaryKey": false, "notNull": true}, "confidence": {"autoincrement": false, "name": "confidence", "type": "real", "primaryKey": false, "notNull": true}, "accuracy": {"autoincrement": false, "name": "accuracy", "type": "real", "primaryKey": false, "notNull": false}, "keywords": {"autoincrement": false, "name": "keywords", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"autoincrement": false, "name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "timestamp": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "timestamp", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"intent_recognition_metrics_timestamp_idx": {"name": "intent_recognition_metrics_timestamp_idx", "columns": ["timestamp"], "isUnique": false}, "intent_recognition_metrics_confidence_idx": {"name": "intent_recognition_metrics_confidence_idx", "columns": ["confidence"], "isUnique": false}, "intent_recognition_metrics_metricId_key": {"name": "intent_recognition_metrics_metricId_key", "columns": ["metricId"], "isUnique": true}, "intent_recognition_metrics_accuracy_idx": {"name": "intent_recognition_metrics_accuracy_idx", "columns": ["accuracy"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "User": {"name": "User", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "email": {"autoincrement": false, "name": "email", "type": "text", "primaryKey": false, "notNull": true}, "username": {"autoincrement": false, "name": "username", "type": "text", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "text", "primaryKey": false, "notNull": false}, "passwordHash": {"autoincrement": false, "name": "passwordHash", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "createdAt", "type": "numeric", "primaryKey": false, "notNull": true}, "updatedAt": {"autoincrement": false, "name": "updatedAt", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"User_username_key": {"name": "User_username_key", "columns": ["username"], "isUnique": true}, "User_email_key": {"name": "User_email_key", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "workflow_logs": {"name": "workflow_logs", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "timestamp": {"autoincrement": false, "name": "timestamp", "type": "text", "primaryKey": false, "notNull": true}, "level": {"autoincrement": false, "name": "level", "type": "text", "primaryKey": false, "notNull": true}, "message": {"autoincrement": false, "name": "message", "type": "text", "primaryKey": false, "notNull": true}, "workflow_id": {"autoincrement": false, "name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "execution_id": {"autoincrement": false, "name": "execution_id", "type": "text", "primaryKey": false, "notNull": false}, "details": {"autoincrement": false, "name": "details", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_workflow_logs_created_at": {"name": "idx_workflow_logs_created_at", "columns": ["created_at"], "isUnique": false}, "idx_workflow_logs_execution_id": {"name": "idx_workflow_logs_execution_id", "columns": ["execution_id"], "isUnique": false}, "idx_workflow_logs_level": {"name": "idx_workflow_logs_level", "columns": ["level"], "isUnique": false}, "idx_workflow_logs_timestamp": {"name": "idx_workflow_logs_timestamp", "columns": ["timestamp"], "isUnique": false}, "idx_workflow_logs_workflow_id": {"name": "idx_workflow_logs_workflow_id", "columns": ["workflow_id"], "isUnique": false}, "workflow_logs_created_at_idx": {"name": "workflow_logs_created_at_idx", "columns": ["created_at"], "isUnique": false}, "workflow_logs_execution_id_idx": {"name": "workflow_logs_execution_id_idx", "columns": ["execution_id"], "isUnique": false}, "workflow_logs_level_idx": {"name": "workflow_logs_level_idx", "columns": ["level"], "isUnique": false}, "workflow_logs_timestamp_idx": {"name": "workflow_logs_timestamp_idx", "columns": ["timestamp"], "isUnique": false}, "workflow_logs_workflow_id_idx": {"name": "workflow_logs_workflow_id_idx", "columns": ["workflow_id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "workflow_executions": {"name": "workflow_executions", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workflow_id": {"autoincrement": false, "name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "start_time": {"autoincrement": false, "name": "start_time", "type": "numeric", "primaryKey": false, "notNull": true}, "end_time": {"autoincrement": false, "name": "end_time", "type": "numeric", "primaryKey": false, "notNull": false}, "status": {"autoincrement": false, "name": "status", "type": "text", "primaryKey": false, "notNull": true}, "duration_seconds": {"autoincrement": false, "name": "duration_seconds", "type": "integer", "primaryKey": false, "notNull": false}, "export_path": {"autoincrement": false, "name": "export_path", "type": "text", "primaryKey": false, "notNull": false}, "error_message": {"autoincrement": false, "name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "records_processed": {"autoincrement": false, "name": "records_processed", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_workflow_executions_created_at": {"name": "idx_workflow_executions_created_at", "columns": ["created_at"], "isUnique": false}, "idx_workflow_executions_status": {"name": "idx_workflow_executions_status", "columns": ["status"], "isUnique": false}, "idx_workflow_executions_start_time": {"name": "idx_workflow_executions_start_time", "columns": ["start_time"], "isUnique": false}, "idx_workflow_executions_workflow_id": {"name": "idx_workflow_executions_workflow_id", "columns": ["workflow_id"], "isUnique": false}, "workflow_executions_created_at_idx": {"name": "workflow_executions_created_at_idx", "columns": ["created_at"], "isUnique": false}, "workflow_executions_status_idx": {"name": "workflow_executions_status_idx", "columns": ["status"], "isUnique": false}, "workflow_executions_start_time_idx": {"name": "workflow_executions_start_time_idx", "columns": ["start_time"], "isUnique": false}, "workflow_executions_workflow_id_idx": {"name": "workflow_executions_workflow_id_idx", "columns": ["workflow_id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "workflow_configs": {"name": "workflow_configs", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "text", "primaryKey": false, "notNull": false}, "tcode": {"autoincrement": false, "name": "tcode", "type": "text", "primaryKey": false, "notNull": false}, "export_dir": {"autoincrement": false, "name": "export_dir", "type": "text", "primaryKey": false, "notNull": false}, "export_basename": {"autoincrement": false, "name": "export_basename", "type": "text", "primaryKey": false, "notNull": false}, "db_table": {"autoincrement": false, "name": "db_table", "type": "text", "primaryKey": false, "notNull": false}, "enabled": {"default": true, "autoincrement": false, "name": "enabled", "type": "numeric", "primaryKey": false, "notNull": true}, "schedule_cron": {"autoincrement": false, "name": "schedule_cron", "type": "text", "primaryKey": false, "notNull": false}, "last_run": {"autoincrement": false, "name": "last_run", "type": "numeric", "primaryKey": false, "notNull": false}, "next_run": {"autoincrement": false, "name": "next_run", "type": "numeric", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_workflow_configs_last_run": {"name": "idx_workflow_configs_last_run", "columns": ["last_run"], "isUnique": false}, "idx_workflow_configs_next_run": {"name": "idx_workflow_configs_next_run", "columns": ["next_run"], "isUnique": false}, "idx_workflow_configs_enabled": {"name": "idx_workflow_configs_enabled", "columns": ["enabled"], "isUnique": false}, "workflow_configs_last_run_idx": {"name": "workflow_configs_last_run_idx", "columns": ["last_run"], "isUnique": false}, "workflow_configs_next_run_idx": {"name": "workflow_configs_next_run_idx", "columns": ["next_run"], "isUnique": false}, "workflow_configs_enabled_idx": {"name": "workflow_configs_enabled_idx", "columns": ["enabled"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "enrichment_performance_metrics": {"name": "enrichment_performance_metrics", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "metricId": {"autoincrement": false, "name": "metricId", "type": "text", "primaryKey": false, "notNull": true}, "requestId": {"autoincrement": false, "name": "requestId", "type": "text", "primaryKey": false, "notNull": true}, "intentCount": {"autoincrement": false, "name": "intentCount", "type": "integer", "primaryKey": false, "notNull": true}, "queryCount": {"autoincrement": false, "name": "queryCount", "type": "integer", "primaryKey": false, "notNull": true}, "successfulQueries": {"autoincrement": false, "name": "successfulQueries", "type": "integer", "primaryKey": false, "notNull": true}, "fallbackUsed": {"default": false, "autoincrement": false, "name": "fallbackUsed", "type": "numeric", "primaryKey": false, "notNull": true}, "dataTypes": {"autoincrement": false, "name": "dataTypes", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"autoincrement": false, "name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "success": {"autoincrement": false, "name": "success", "type": "numeric", "primaryKey": false, "notNull": true}, "timestamp": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "timestamp", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"enrichment_performance_metrics_fallbackUsed_idx": {"name": "enrichment_performance_metrics_fallbackUsed_idx", "columns": ["fallbackUsed"], "isUnique": false}, "enrichment_performance_metrics_success_idx": {"name": "enrichment_performance_metrics_success_idx", "columns": ["success"], "isUnique": false}, "enrichment_performance_metrics_timestamp_idx": {"name": "enrichment_performance_metrics_timestamp_idx", "columns": ["timestamp"], "isUnique": false}, "enrichment_performance_metrics_metricId_key": {"name": "enrichment_performance_metrics_metricId_key", "columns": ["metricId"], "isUnique": true}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "performance_alerts": {"name": "performance_alerts", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "type": {"autoincrement": false, "name": "type", "type": "text", "primaryKey": false, "notNull": true}, "message": {"autoincrement": false, "name": "message", "type": "text", "primaryKey": false, "notNull": true}, "metric": {"autoincrement": false, "name": "metric", "type": "text", "primaryKey": false, "notNull": true}, "value": {"autoincrement": false, "name": "value", "type": "real", "primaryKey": false, "notNull": true}, "threshold": {"autoincrement": false, "name": "threshold", "type": "real", "primaryKey": false, "notNull": true}, "resolved": {"default": false, "autoincrement": false, "name": "resolved", "type": "numeric", "primaryKey": false, "notNull": true}, "resolvedAt": {"autoincrement": false, "name": "resolvedAt", "type": "numeric", "primaryKey": false, "notNull": false}, "createdAt": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "createdAt", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"performance_alerts_createdAt_idx": {"name": "performance_alerts_createdAt_idx", "columns": ["createdAt"], "isUnique": false}, "performance_alerts_resolved_idx": {"name": "performance_alerts_resolved_idx", "columns": ["resolved"], "isUnique": false}, "performance_alerts_type_idx": {"name": "performance_alerts_type_idx", "columns": ["type"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "query_performance_metrics": {"name": "query_performance_metrics", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "metricId": {"autoincrement": false, "name": "metricId", "type": "text", "primaryKey": false, "notNull": true}, "queryType": {"autoincrement": false, "name": "queryType", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"autoincrement": false, "name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "success": {"autoincrement": false, "name": "success", "type": "numeric", "primaryKey": false, "notNull": true}, "dataSize": {"autoincrement": false, "name": "dataSize", "type": "integer", "primaryKey": false, "notNull": false}, "cacheHit": {"default": false, "autoincrement": false, "name": "cacheHit", "type": "numeric", "primaryKey": false, "notNull": true}, "retryCount": {"default": 0, "autoincrement": false, "name": "retryCount", "type": "integer", "primaryKey": false, "notNull": true}, "errorType": {"autoincrement": false, "name": "errorType", "type": "text", "primaryKey": false, "notNull": false}, "timestamp": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "timestamp", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"query_performance_metrics_success_idx": {"name": "query_performance_metrics_success_idx", "columns": ["success"], "isUnique": false}, "query_performance_metrics_cacheHit_idx": {"name": "query_performance_metrics_cacheHit_idx", "columns": ["cacheHit"], "isUnique": false}, "query_performance_metrics_timestamp_idx": {"name": "query_performance_metrics_timestamp_idx", "columns": ["timestamp"], "isUnique": false}, "query_performance_metrics_queryType_idx": {"name": "query_performance_metrics_queryType_idx", "columns": ["queryType"], "isUnique": false}, "query_performance_metrics_metricId_key": {"name": "query_performance_metrics_metricId_key", "columns": ["metricId"], "isUnique": true}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "ARiL": {"name": "ARiL", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "Datum": {"autoincrement": false, "name": "Datum", "type": "text", "primaryKey": false, "notNull": false}, "waTaPositionen": {"autoincrement": false, "name": "waTaPositionen", "type": "integer", "primaryKey": false, "notNull": false}, "Umlagerungen": {"autoincrement": false, "name": "Umlagerungen", "type": "integer", "primaryKey": false, "notNull": false}, "belegtePlaetze": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "systemtablareRuecksackStk": {"autoincrement": false, "name": "systemtablareRuecksackStk", "type": "integer", "primaryKey": false, "notNull": false}, "systemtablareGesamtStk": {"autoincrement": false, "name": "systemtablareGesamtStk", "type": "integer", "primaryKey": false, "notNull": false}, "systemtablareEinzelBelegt": {"autoincrement": false, "name": "systemtablareEinzelBelegt", "type": "integer", "primaryKey": false, "notNull": false}, "belegtRinge": {"autoincrement": false, "name": "belegtRinge", "type": "integer", "primaryKey": false, "notNull": false}, "Auslastung": {"autoincrement": false, "name": "Auslastung", "type": "real", "primaryKey": false, "notNull": false}, "alleBewegungen": {"autoincrement": false, "name": "alleBewegungen", "type": "integer", "primaryKey": false, "notNull": false}, "cuttingLagerKunde": {"autoincrement": false, "name": "cuttingLagerKunde", "type": "integer", "primaryKey": false, "notNull": false}, "cuttingLagerRest": {"autoincrement": false, "name": "cuttingLagerRest", "type": "integer", "primaryKey": false, "notNull": false}, "lagerCutting": {"autoincrement": false, "name": "lagerCutting", "type": "integer", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"ARiL_Datum_idx": {"name": "ARiL_Datum_idx", "columns": ["Datum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "bereitschafts_personen": {"name": "bereitschafts_personen", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": false}, "name": {"autoincrement": false, "name": "name", "type": "text", "primaryKey": false, "notNull": true}, "telefon": {"autoincrement": false, "name": "telefon", "type": "text", "primaryKey": false, "notNull": true}, "email": {"autoincrement": false, "name": "email", "type": "text", "primaryKey": false, "notNull": true}, "abteilung": {"autoincrement": false, "name": "abteilung", "type": "text", "primaryKey": false, "notNull": true}, "aktiv": {"default": 1, "autoincrement": false, "name": "aktiv", "type": "numeric", "primaryKey": false, "notNull": true}, "reihenfolge": {"default": 0, "autoincrement": false, "name": "reihenfolge", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_bereitschafts_personen_reihenfolge": {"name": "idx_bereitschafts_personen_reihenfolge", "columns": ["reihenfolge"], "isUnique": false}, "idx_bereitschafts_personen_aktiv": {"name": "idx_bereitschafts_personen_aktiv", "columns": ["aktiv"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "bereitschafts_konfiguration": {"name": "bereitschafts_konfiguration", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": false}, "wechsel_tag": {"default": 5, "autoincrement": false, "name": "wechsel_tag", "type": "integer", "primaryKey": false, "notNull": true}, "wechsel_uhrzeit": {"default": "'08:00'", "autoincrement": false, "name": "wechsel_uhrzeit", "type": "text", "primaryKey": false, "notNull": true}, "rotation_aktiv": {"default": 1, "autoincrement": false, "name": "rotation_aktiv", "type": "numeric", "primaryKey": false, "notNull": true}, "benachrichtigung_tage": {"default": 2, "autoincrement": false, "name": "benachrichtigung_tage", "type": "integer", "primaryKey": false, "notNull": true}, "email_benachrichtigung": {"default": 1, "autoincrement": false, "name": "email_benachrichtigung", "type": "numeric", "primaryKey": false, "notNull": true}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "bestand200": {"name": "bestand200", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": false}, "Lagertyp": {"autoincrement": false, "name": "Lagertyp", "type": "text", "primaryKey": false, "notNull": false}, "Lagerplatz": {"autoincrement": false, "name": "Lagerplatz", "type": "text", "primaryKey": false, "notNull": false}, "Material": {"autoincrement": false, "name": "Material", "type": "text", "primaryKey": false, "notNull": false}, "Charge": {"autoincrement": false, "name": "Charge", "type": "text", "primaryKey": false, "notNull": false}, "Dauer": {"autoincrement": false, "name": "<PERSON><PERSON>", "type": "real", "primaryKey": false, "notNull": false}, "Lagerbereich": {"autoincrement": false, "name": "Lagerbereich", "type": "text", "primaryKey": false, "notNull": false}, "Lagerplatztyp": {"autoincrement": false, "name": "Lagerplatztyp", "type": "text", "primaryKey": false, "notNull": false}, "Lagerplatzaufteilung": {"autoincrement": false, "name": "Lagerplatzaufteilung", "type": "text", "primaryKey": false, "notNull": false}, "Auslagerungssperre": {"autoincrement": false, "name": "Auslagerungssperre", "type": "text", "primaryKey": false, "notNull": false}, "Einlagerungssperre": {"autoincrement": false, "name": "Einlagerungssperre", "type": "text", "primaryKey": false, "notNull": false}, "Sperrgrund": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "Letzte_Bewegung": {"autoincrement": false, "name": "Letzte_Bewegung", "type": "text", "primaryKey": false, "notNull": false}, "Uhrzeit": {"autoincrement": false, "name": "Uhrzeit", "type": "text", "primaryKey": false, "notNull": false}, "TA_Nummer": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "TA_Position": {"autoincrement": false, "name": "TA_Position", "type": "text", "primaryKey": false, "notNull": false}, "Letzter_änderer": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "Letzte_änderung": {"autoincrement": false, "name": "Letzte_änderung", "type": "text", "primaryKey": false, "notNull": false}, "Wareneingangsdatum": {"autoincrement": false, "name": "Wareneingangsdatum", "type": "text", "primaryKey": false, "notNull": false}, "WE_Nummer": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "WE_Position": {"autoincrement": false, "name": "WE_Position", "type": "text", "primaryKey": false, "notNull": false}, "Lieferung": {"autoincrement": false, "name": "Lieferung", "type": "text", "primaryKey": false, "notNull": false}, "Position": {"autoincrement": false, "name": "Position", "type": "text", "primaryKey": false, "notNull": false}, "Lagereinheitentyp": {"autoincrement": false, "name": "Lagereinheitentyp", "type": "text", "primaryKey": false, "notNull": false}, "Gesamtbestand": {"autoincrement": false, "name": "Gesamtbestand", "type": "real", "primaryKey": false, "notNull": false}, "Lagereinheit": {"autoincrement": false, "name": "Lagereinheit", "type": "text", "primaryKey": false, "notNull": false}, "aufnahmeDatum": {"autoincrement": false, "name": "aufnahmeDatum", "type": "text", "primaryKey": false, "notNull": false}, "aufnahmeZeit": {"autoincrement": false, "name": "aufnahmeZeit", "type": "text", "primaryKey": false, "notNull": false}, "maxPlaetze": {"autoincrement": false, "name": "max<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "auslastung": {"autoincrement": false, "name": "auslastung", "type": "text", "primaryKey": false, "notNull": false}, "maxA": {"autoincrement": false, "name": "maxA", "type": "text", "primaryKey": false, "notNull": false}, "maxB": {"autoincrement": false, "name": "maxB", "type": "text", "primaryKey": false, "notNull": false}, "maxC": {"autoincrement": false, "name": "maxC", "type": "text", "primaryKey": false, "notNull": false}, "auslastungA": {"autoincrement": false, "name": "auslastungA", "type": "text", "primaryKey": false, "notNull": false}, "auslastungB": {"autoincrement": false, "name": "auslastungB", "type": "text", "primaryKey": false, "notNull": false}, "auslastungC": {"autoincrement": false, "name": "auslastungC", "type": "text", "primaryKey": false, "notNull": false}, "import_timestamp": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "import_timestamp", "type": "numeric", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"ux_bestand200_Lagerplatz_Material_Charge_TA_Nummer_WE_Nummer_WE_Position_aufnahmeDatum_aufnahmeZeit": {"name": "ux_bestand200_Lagerplatz_Material_Charge_TA_Nummer_WE_Nummer_WE_Position_aufnahmeDatum_aufnahmeZeit", "columns": ["Lagerplatz", "Material", "Charge", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WE_Position", "aufnahmeDatum", "aufnahmeZeit"], "isUnique": true}, "bestand200_import_timestamp_idx": {"name": "bestand200_import_timestamp_idx", "columns": ["import_timestamp"], "isUnique": false}, "bestand200_aufnahmeDatum_idx": {"name": "bestand200_aufnahmeDatum_idx", "columns": ["aufnahmeDatum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "bestand240": {"name": "bestand240", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": false}, "Lagertyp": {"autoincrement": false, "name": "Lagertyp", "type": "text", "primaryKey": false, "notNull": false}, "Lagerplatz": {"autoincrement": false, "name": "Lagerplatz", "type": "text", "primaryKey": false, "notNull": false}, "Material": {"autoincrement": false, "name": "Material", "type": "text", "primaryKey": false, "notNull": false}, "Charge": {"autoincrement": false, "name": "Charge", "type": "text", "primaryKey": false, "notNull": false}, "Dauer": {"autoincrement": false, "name": "<PERSON><PERSON>", "type": "real", "primaryKey": false, "notNull": false}, "Lagerbereich": {"autoincrement": false, "name": "Lagerbereich", "type": "text", "primaryKey": false, "notNull": false}, "Lagerplatztyp": {"autoincrement": false, "name": "Lagerplatztyp", "type": "text", "primaryKey": false, "notNull": false}, "Lagerplatzaufteilung": {"autoincrement": false, "name": "Lagerplatzaufteilung", "type": "text", "primaryKey": false, "notNull": false}, "Auslagerungssperre": {"autoincrement": false, "name": "Auslagerungssperre", "type": "text", "primaryKey": false, "notNull": false}, "Einlagerungssperre": {"autoincrement": false, "name": "Einlagerungssperre", "type": "text", "primaryKey": false, "notNull": false}, "Sperrgrund": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "Letzte_Bewegung": {"autoincrement": false, "name": "Letzte_Bewegung", "type": "text", "primaryKey": false, "notNull": false}, "Uhrzeit": {"autoincrement": false, "name": "Uhrzeit", "type": "text", "primaryKey": false, "notNull": false}, "TA_Nummer": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "TA_Position": {"autoincrement": false, "name": "TA_Position", "type": "text", "primaryKey": false, "notNull": false}, "Letzter_Änderer": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON>_<PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "Letzte_Änderung": {"autoincrement": false, "name": "Letzte_Änderung", "type": "text", "primaryKey": false, "notNull": false}, "Wareneingangsdatum": {"autoincrement": false, "name": "Wareneingangsdatum", "type": "text", "primaryKey": false, "notNull": false}, "WE_Nummer": {"autoincrement": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "WE_Position": {"autoincrement": false, "name": "WE_Position", "type": "text", "primaryKey": false, "notNull": false}, "Lieferung": {"autoincrement": false, "name": "Lieferung", "type": "text", "primaryKey": false, "notNull": false}, "Position": {"autoincrement": false, "name": "Position", "type": "text", "primaryKey": false, "notNull": false}, "Lagereinheitentyp": {"autoincrement": false, "name": "Lagereinheitentyp", "type": "text", "primaryKey": false, "notNull": false}, "Gesamtbestand": {"autoincrement": false, "name": "Gesamtbestand", "type": "real", "primaryKey": false, "notNull": false}, "Lagereinheit": {"autoincrement": false, "name": "Lagereinheit", "type": "text", "primaryKey": false, "notNull": false}, "aufnahmeDatum": {"autoincrement": false, "name": "aufnahmeDatum", "type": "text", "primaryKey": false, "notNull": false}, "aufnahmeZeit": {"autoincrement": false, "name": "aufnahmeZeit", "type": "text", "primaryKey": false, "notNull": false}, "maxPlaetze": {"autoincrement": false, "name": "max<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "auslastung": {"autoincrement": false, "name": "auslastung", "type": "text", "primaryKey": false, "notNull": false}, "maxA": {"autoincrement": false, "name": "maxA", "type": "text", "primaryKey": false, "notNull": false}, "maxB": {"autoincrement": false, "name": "maxB", "type": "text", "primaryKey": false, "notNull": false}, "maxC": {"autoincrement": false, "name": "maxC", "type": "text", "primaryKey": false, "notNull": false}, "auslastungA": {"autoincrement": false, "name": "auslastungA", "type": "text", "primaryKey": false, "notNull": false}, "auslastungB": {"autoincrement": false, "name": "auslastungB", "type": "text", "primaryKey": false, "notNull": false}, "auslastungC": {"autoincrement": false, "name": "auslastungC", "type": "text", "primaryKey": false, "notNull": false}, "import_timestamp": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "import_timestamp", "type": "numeric", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"ux_bestand240_Lagerplatz_Material_Charge_TA-Nummer_WE-Nummer_WE-Position_aufnahmeDatum_aufnahmeZeit": {"name": "ux_bestand240_Lagerplatz_Material_Charge_TA-Nummer_WE-Nummer_WE-Position_aufnahmeDatum_aufnahmeZeit", "columns": ["Lagerplatz", "Material", "Charge", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WE_Position", "aufnahmeDatum", "aufnahmeZeit"], "isUnique": true}, "ux_bestand240_Lagerplatz_Material_Charge_TA_Nummer_WE_Nummer_WE_Position_aufnahmeDatum_aufnahmeZeit": {"name": "ux_bestand240_Lagerplatz_Material_Charge_TA_Nummer_WE_Nummer_WE_Position_aufnahmeDatum_aufnahmeZeit", "columns": ["Lagerplatz", "Material", "Charge", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WE_Position", "aufnahmeDatum", "aufnahmeZeit"], "isUnique": true}, "bestand240_import_timestamp_idx": {"name": "bestand240_import_timestamp_idx", "columns": ["import_timestamp"], "isUnique": false}, "bestand240_aufnahmeDatum_idx": {"name": "bestand240_aufnahmeDatum_idx", "columns": ["aufnahmeDatum"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "materialdaten": {"name": "materialdaten", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "matnr": {"autoincrement": false, "name": "matnr", "type": "text", "primaryKey": false, "notNull": true}, "materialkurztext": {"autoincrement": false, "name": "materialkurztext", "type": "text", "primaryKey": false, "notNull": false}, "kabeldurchmesser": {"autoincrement": false, "name": "kabeldurchmesser", "type": "real", "primaryKey": false, "notNull": false}, "zuschlagKabeldurchmesser": {"autoincrement": false, "name": "zuschlagKabeldurchmesser", "type": "real", "primaryKey": false, "notNull": false}, "biegefaktor": {"autoincrement": false, "name": "biegefaktor", "type": "real", "primaryKey": false, "notNull": false}, "ringauslieferung": {"autoincrement": false, "name": "ringauslieferung", "type": "text", "primaryKey": false, "notNull": false}, "kleinsterErlauberFreiraum": {"autoincrement": false, "name": "kleinsterErlauberFreiraum", "type": "real", "primaryKey": false, "notNull": false}, "bruttogewicht": {"autoincrement": false, "name": "bruttog<PERSON><PERSON>t", "type": "real", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"materialdaten_matnr_idx": {"name": "materialdaten_matnr_idx", "columns": ["matnr"], "isUnique": false}, "materialdaten_matnr_key": {"name": "materialdaten_matnr_key", "columns": ["matnr"], "isUnique": true}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "trommeldaten": {"name": "trommeldaten", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "trommeldaten": {"autoincrement": false, "name": "trommeldaten", "type": "text", "primaryKey": false, "notNull": true}, "aussendurchmesser": {"autoincrement": false, "name": "aussendurchmesser", "type": "integer", "primaryKey": false, "notNull": false}, "kerndurchmesser": {"autoincrement": false, "name": "kerndurchmesser", "type": "integer", "primaryKey": false, "notNull": false}, "freiraum_mm": {"autoincrement": false, "name": "freiraum_mm", "type": "integer", "primaryKey": false, "notNull": false}, "wickelbreite_mm": {"autoincrement": false, "name": "wickelbreite_mm", "type": "integer", "primaryKey": false, "notNull": false}, "maxTragkraft_Kg": {"autoincrement": false, "name": "maxTragkraft_Kg", "type": "integer", "primaryKey": false, "notNull": false}, "max_Laenge": {"autoincrement": false, "name": "max_<PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "max_Gewicht": {"autoincrement": false, "name": "max_<PERSON><PERSON><PERSON>t", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"trommeldaten_trommeldaten_idx": {"name": "trommeldaten_trommeldaten_idx", "columns": ["trommeldaten"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "bereitschafts_ausnahmen": {"name": "bereitschafts_ausnahmen", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "person_id": {"autoincrement": false, "name": "person_id", "type": "integer", "primaryKey": false, "notNull": true}, "von": {"autoincrement": false, "name": "von", "type": "numeric", "primaryKey": false, "notNull": true}, "bis": {"autoincrement": false, "name": "bis", "type": "numeric", "primaryKey": false, "notNull": true}, "grund": {"autoincrement": false, "name": "grund", "type": "text", "primaryKey": false, "notNull": true}, "ersatz_person_id": {"autoincrement": false, "name": "ersatz_person_id", "type": "integer", "primaryKey": false, "notNull": false}, "aktiv": {"default": true, "autoincrement": false, "name": "aktiv", "type": "numeric", "primaryKey": false, "notNull": true}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_bereitschafts_ausnahmen_person_id": {"name": "idx_bereitschafts_ausnahmen_person_id", "columns": ["person_id"], "isUnique": false}, "idx_bereitschafts_ausnahmen_von_bis": {"name": "idx_bereitschafts_ausnahmen_von_bis", "columns": ["von", "bis"], "isUnique": false}, "idx_bereitschafts_ausnahmen_aktiv": {"name": "idx_bereitschafts_ausnahmen_aktiv", "columns": ["aktiv"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "bereitschafts_wochen": {"name": "bereitschafts_wochen", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "person_id": {"autoincrement": false, "name": "person_id", "type": "integer", "primaryKey": false, "notNull": true}, "wochen_start": {"autoincrement": false, "name": "wochen_start", "type": "numeric", "primaryKey": false, "notNull": true}, "wochen_ende": {"autoincrement": false, "name": "wochen_ende", "type": "numeric", "primaryKey": false, "notNull": true}, "von": {"autoincrement": false, "name": "von", "type": "numeric", "primaryKey": false, "notNull": true}, "bis": {"autoincrement": false, "name": "bis", "type": "numeric", "primaryKey": false, "notNull": true}, "aktiv": {"default": true, "autoincrement": false, "name": "aktiv", "type": "numeric", "primaryKey": false, "notNull": true}, "notiz": {"autoincrement": false, "name": "notiz", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_bereitschafts_wochen_person_id": {"name": "idx_bereitschafts_wochen_person_id", "columns": ["person_id"], "isUnique": false}, "idx_bereitschafts_wochen_wochen_start": {"name": "idx_bereitschafts_wochen_wochen_start", "columns": ["wochen_start"], "isUnique": false}, "idx_bereitschafts_wochen_aktiv": {"name": "idx_bereitschafts_wochen_aktiv", "columns": ["aktiv"], "isUnique": false}, "idx_bereitschafts_wochen_von_bis": {"name": "idx_bereitschafts_wochen_von_bis", "columns": ["von", "bis"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "StoerungsAttachment": {"name": "StoerungsAttachment", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "stoerung_id": {"autoincrement": false, "name": "stoerung_id", "type": "integer", "primaryKey": false, "notNull": true}, "filename": {"autoincrement": false, "name": "filename", "type": "text", "primaryKey": false, "notNull": true}, "stored_name": {"autoincrement": false, "name": "stored_name", "type": "text", "primaryKey": false, "notNull": true}, "file_path": {"autoincrement": false, "name": "file_path", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"autoincrement": false, "name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "mime_type": {"autoincrement": false, "name": "mime_type", "type": "text", "primaryKey": false, "notNull": true}, "file_type": {"autoincrement": false, "name": "file_type", "type": "text", "primaryKey": false, "notNull": true}, "uploaded_by": {"autoincrement": false, "name": "uploaded_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"StoerungsAttachment_created_at_idx": {"name": "StoerungsAttachment_created_at_idx", "columns": ["created_at"], "isUnique": false}, "StoerungsAttachment_file_type_idx": {"name": "StoerungsAttachment_file_type_idx", "columns": ["file_type"], "isUnique": false}, "StoerungsAttachment_stoerung_id_idx": {"name": "StoerungsAttachment_stoerung_id_idx", "columns": ["stoerung_id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "SystemStatusMessage": {"name": "SystemStatusMessage", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "category": {"autoincrement": false, "name": "category", "type": "text", "primaryKey": false, "notNull": true}, "status": {"autoincrement": false, "name": "status", "type": "text", "primaryKey": false, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"default": 1, "autoincrement": false, "name": "priority", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"default": 1, "autoincrement": false, "name": "is_active", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}, "system_status_id": {"autoincrement": false, "name": "system_status_id", "type": "integer", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"SystemStatusMessage_system_status_id_idx": {"name": "SystemStatusMessage_system_status_id_idx", "columns": ["system_status_id"], "isUnique": false}, "SystemStatusMessage_category_status_idx": {"name": "SystemStatusMessage_category_status_idx", "columns": ["category", "status"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "Runbook": {"name": "Runbook", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"autoincrement": false, "name": "content", "type": "text", "primaryKey": false, "notNull": true}, "affected_systems": {"autoincrement": false, "name": "affected_systems", "type": "text", "primaryKey": false, "notNull": false}, "tags": {"autoincrement": false, "name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"Runbook_updated_at_idx": {"name": "Runbook_updated_at_idx", "columns": ["updated_at"], "isUnique": false}, "Runbook_created_at_idx": {"name": "Runbook_created_at_idx", "columns": ["created_at"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}