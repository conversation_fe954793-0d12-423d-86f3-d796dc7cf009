import { ApiService } from './api.service';

export interface RegisterRequest {
  email: string;
  username: string;
  name?: string; // Optionales Feld für den Namen
  password: string;
}

export interface RegisterResponse {
  success: boolean;
  message: string;
  data?: {
    id: number;
    email: string;
    username: string;
    name?: string; // Optionales Feld für den Namen
    createdAt: string;
  };
  error?: string;
  code?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;
    user: {
      id: number;
      username: string;
      email: string;
      name?: string; // Optionales Feld für den Namen
      roles: string[];
    };
  };
  error?: string;
  code?: string;
}

export class AuthService {
  private apiService: ApiService;
  private refreshPromise: Promise<LoginResponse> | null = null;
  private isRefreshing = false;

  constructor() {
    this.apiService = new ApiService();
  }

  /**
   * Registers a new user with the provided credentials
   * @param data User registration data (email, username, password)
   * @returns Promise with registration response
   */
  async register(data: RegisterRequest): Promise<RegisterResponse> {
    try {
      // The API service returns the data field, but we need the full response
      // So we'll make a direct fetch call to get the complete response
      
      // Split the name into firstName and lastName for the backend
      let firstName = '';
      let lastName = '';
      
      if (data.name) {
        const nameParts = data.name.trim().split(' ');
        if (nameParts.length > 1) {
          firstName = nameParts[0];
          lastName = nameParts.slice(1).join(' ');
        } else {
          firstName = data.name;
          lastName = '';
        }
      }
      
      const requestData = {
        email: data.email,
        username: data.username,
        firstName,
        lastName,
        password: data.password
      };
      
      const response = await fetch(`${this.apiService.getBaseUrl()}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();
      
      // Handle successful API response
      if (result && result.success) {
        return {
          success: true,
          message: result.message || 'Registrierung erfolgreich',
          data: result.data
        };
      }
      
      // Handle API error response
      return {
        success: false,
        message: result?.error || result?.message || 'Registrierung fehlgeschlagen',
        error: result?.error || 'UNKNOWN_ERROR'
      };
    } catch (error: any) {
      // Handle network or other errors
      return {
        success: false,
        message: error.message || 'Registrierung fehlgeschlagen. Bitte versuchen Sie es später erneut.',
        error: 'NETWORK_ERROR',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Logs in a user with username and password
   * @param data User login data (username, password)
   * @returns Promise with login response including JWT token
   */
  async login(data: LoginRequest): Promise<LoginResponse> {
    try {
      // Make a direct fetch call to get the complete response
      const response = await fetch(`${this.apiService.getBaseUrl()}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      
      // Debug-Logging für die API-Antwort
      console.log('[AuthService] Login-Antwort:', result);
      
      // Handle successful API response
      if (result && result.success && result.data) {
        // Debug-Logging für Benutzerrollen
        console.log('[AuthService] Benutzerrollen:', result.data.user?.roles);
        
        // Store JWT token securely if login successful
        if (result.data.token) {
          localStorage.setItem('auth_token', result.data.token);
          localStorage.setItem('user_data', JSON.stringify(result.data.user));
        }
        
        return {
          success: true,
          message: result.message || 'Anmeldung erfolgreich',
          data: result.data
        };
      }
      
      // Handle API error response
      return {
        success: false,
        message: result?.error || result?.message || 'Anmeldung fehlgeschlagen',
        error: result?.error || 'UNKNOWN_ERROR'
      };
    } catch (error: any) {
      // Handle network or other errors
      return {
        success: false,
        message: error.message || 'Anmeldung fehlgeschlagen. Bitte versuchen Sie es später erneut.',
        error: 'NETWORK_ERROR',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Logs out the current user by removing stored tokens
   */
  logout(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
  }

  /**
   * Gets the stored JWT token
   * @returns JWT token or null if not found
   */
  getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  /**
   * Gets the stored user data
   * @returns User data or null if not found
   */
  getUser(): any | null {
    const userData = localStorage.getItem('user_data');
    return userData ? JSON.parse(userData) : null;
  }

  /**
   * Checks if user is currently logged in
   * @returns true if user has valid token
   */
  async isLoggedIn(): Promise<boolean> {
    const token = this.getToken();
    if (!token) return false;

    // Basic token validation
    try {
      const parts = token.split('.');
      if (parts.length !== 3) return false;

      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Math.floor(Date.now() / 1000);

      // Check if token is expired
      if (payload.exp && payload.exp < currentTime) {
        console.log('🕐 Token expired, attempting refresh...');

        // Prevent multiple simultaneous refresh attempts
        if (this.isRefreshing) {
          console.log('🔄 Refresh already in progress, waiting...');
          if (this.refreshPromise) {
            const result = await this.refreshPromise;
            return result.success;
          }
          return false;
        }

        try {
          this.isRefreshing = true;
          this.refreshPromise = this.refreshToken();
          const refreshResult = await this.refreshPromise;

          return refreshResult.success;
        } catch (refreshError) {
          console.log('🔄 Token refresh failed, clearing auth state');
          this.logout();
          return false;
        } finally {
          this.isRefreshing = false;
          this.refreshPromise = null;
        }
      }

      return true;
    } catch (error) {
      console.error('❌ Token validation error:', error);
      this.logout();
      return false;
    }
  }

  /**
   * Validates if the current token is still valid
   * @returns true if token is valid and not expired
   */
  isTokenValid(): boolean {
    const token = this.getToken();
    if (!token) return false;

    try {
      const parts = token.split('.');
      if (parts.length !== 3) return false;

      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      
      // Check if token is expired (with 1 minute buffer for refresh)
      return payload.exp && payload.exp > currentTime + 60;
    } catch (error) {
      console.error('❌ Token validation error:', error);
      return false;
    }
  }

  /**
   * Attempts to refresh the current token
   * @returns Promise with refresh response
   */
  async refreshToken(): Promise<LoginResponse> {
    try {
      const currentToken = this.getToken();
      if (!currentToken) {
        return {
          success: false,
          message: 'Kein Token zum Aktualisieren vorhanden',
          error: 'NO_TOKEN'
        };
      }

      const response = await fetch(`${this.apiService.getBaseUrl()}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentToken}`
        }
      });

      const result = await response.json();
      
      if (result && result.success && result.data) {
        // Update stored token and user data
        if (result.data.token) {
          localStorage.setItem('auth_token', result.data.token);
          localStorage.setItem('user_data', JSON.stringify(result.data.user));
        }
        
        return {
          success: true,
          message: result.message || 'Token erfolgreich aktualisiert',
          data: result.data
        };
      }
      
      return {
        success: false,
        message: result?.error || result?.message || 'Token-Aktualisierung fehlgeschlagen',
        error: result?.error || 'REFRESH_FAILED'
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Token-Aktualisierung fehlgeschlagen. Bitte melden Sie sich erneut an.',
        error: 'NETWORK_ERROR'
      };
    }
  }
}

export const authService = new AuthService();