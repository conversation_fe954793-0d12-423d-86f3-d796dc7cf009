import { useState, useRef, useEffect, useCallback } from 'react'
import { <PERSON><PERSON>, X, Maximize2, Minimize2, <PERSON>otate<PERSON>c<PERSON>, <PERSON>py, ThumbsUp, ThumbsDown, Database, FileText, ChevronDownIcon, BookIcon } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { apiService } from '@/services/api.service';
import lappLogo from '@/assets/lappLogo.png';
import type { ChatStatus } from 'ai';
import { cn } from '@/lib/utils';

// Import RAG Chat Service (frontend HTTP client only)
import { RAGChatService, ChatRequest, ChatResponse, Source } from '../../services/chat/RAGChatService';

// Import AI Elements components
import {
  Message,
  MessageContent,
  MessageAvatar,
  Response,
  PromptInput,
  PromptInputTextarea,
  PromptInputToolbar,
  PromptInputTools,
  PromptInputButton,
  PromptInputSubmit,
  Loader,
  Actions,
  Action,
  Sources,
  SourcesTrigger,
  SourcesContent,
  Source as SourceComponent,
  Task,
  TaskTrigger,
  TaskContent,
  TaskItem,
  TaskItemFile,
  Tool,
  ToolHeader,
  ToolContent,
  ToolInput,
  ToolOutput
} from '../ai-elements';
import BorderGlowButton from '../Buttons/BorderGlowButton';
import { ShimmerButton } from '../Buttons/shimmer-button';

// Interface für Chat-Nachrichten (erweitert für RAG)
interface ChatMessage {
  id: string
  text: string
  sender: 'user' | 'ai'
  timestamp: Date
  aiPlus?: boolean
  ragEnhanced?: boolean
  confidence?: number
  sources?: Source[]
  suggestions?: string[]
  tasks?: Array<{ title: string; items: string[]; files?: string[] }>
  tools?: Array<{ type: `tool-${string}`; state: 'input-streaming' | 'input-available' | 'output-available' | 'output-error'; input?: any; output?: any; errorText?: string }>
  usedContext?: string[]
  metadata?: {
    processingTime: number;
    dataEnrichmentUsed: boolean;
    detectedIntents: any[];
    performanceMetrics: any;
  }
}

// Haupt-ChatBot Komponente
interface ChatBotProps {
  isOpen?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
  initialMessage?: string;
  autoOpen?: boolean;
}

export default function ChatBot({ 
  isOpen: externalIsOpen, 
  onOpenChange, 
  initialMessage,
  autoOpen = false 
}: ChatBotProps = {}) {
  // State für Chat-Funktionalität
  const [internalIsOpen, setInternalIsOpen] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  
  // Use external control if provided, otherwise use internal state
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen
  const setIsOpen = onOpenChange || setInternalIsOpen
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [aiPlusMode, setAiPlusMode] = useState(false)
  const [ragMode, setRagMode] = useState(true) // RAG always enabled
  const [isLoading, setIsLoading] = useState(false)
  const [chatStatus, setChatStatus] = useState<ChatStatus | 'idle'>('idle')
  const [processingTime, setProcessingTime] = useState(0)

  // RAG Service instances (initialized lazily)
  const [ragChatService, setRagChatService] = useState<RAGChatService | null>(null)
  const [ragServiceError, setRagServiceError] = useState<string | null>(null)

  // Refs für DOM-Manipulation
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const hasProcessedInitialMessage = useRef(false)

  // Initialize RAG services (HTTP client only)
  useEffect(() => {
    const initializeRAGServices = async () => {
      try {
        // Create HTTP-based RAG chat service
        const ragChatService = new RAGChatService();

        // Test connection to backend using apiService for correct URL
        try {
          const response = await apiService.get('/rag/test') as any;
          if (response && response.success) {
            setRagChatService(ragChatService);
            setRagServiceError(null);
            console.log('✅ RAG services initialized successfully');
          } else {
            throw new Error('RAG backend service returned invalid response');
          }
        } catch (fetchError) {
          throw new Error('RAG backend service not available');
        }
      } catch (error) {
        console.error('❌ Failed to initialize RAG services:', error);
        setRagServiceError(error instanceof Error ? error.message : 'RAG backend not available');
        // Don't throw error - allow chat to work without RAG
        console.log('💡 Chat will continue to work without RAG functionality');
      }
    };

    initializeRAGServices();
  }, [])

  // Handle initial message and auto-open
  useEffect(() => {
    if (initialMessage && !hasProcessedInitialMessage.current) {
      hasProcessedInitialMessage.current = true;
      setInputMessage(initialMessage);
      
      if (autoOpen) {
        setIsOpen(true);
        // Auto-send the message after a short delay
        setTimeout(() => {
          handleSend(initialMessage);
        }, 500);
      }
    }
  }, [initialMessage, autoOpen])

  // Reset processed flag when chat is closed
  useEffect(() => {
    if (!isOpen) {
      hasProcessedInitialMessage.current = false;
    }
  }, [isOpen])

  // Automatisch nach unten scrollen bei neuen Nachrichten
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])

  // Nachricht senden (erweitert für RAG)
  const handleSend = useCallback(async (messageText?: string) => {
    const textToSend = messageText || inputMessage.trim()
    if (textToSend && !isLoading) {
      const newMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        text: textToSend,
        sender: 'user',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, newMessage])
      setInputMessage('')
      setIsLoading(true)
      setChatStatus('submitted')
      setProcessingTime(0)

      // Start processing time counter
      const startTime = Date.now()
      const timeInterval = setInterval(() => {
        setProcessingTime(Date.now() - startTime)
      }, 1000)

      try {
        setChatStatus('streaming')

        let aiResponse: ChatMessage;

        // Use RAG service if available and RAG mode is enabled
        if (ragMode && ragChatService && !ragServiceError) {
          try {
            const ragRequest: ChatRequest = {
              message: textToSend,
              useRAG: true,
              includeInsights: aiPlusMode,
              includeAnomalies: aiPlusMode,
              conversationHistory: messages.slice(-5) // Last 5 messages for context
            };

            const ragResponse: ChatResponse = await ragChatService.processChat(ragRequest);

            aiResponse = {
              id: `ai-${Date.now()}`,
              text: ragResponse.response,
              sender: 'ai',
              timestamp: new Date(),
              aiPlus: aiPlusMode,
              ragEnhanced: ragResponse.ragEnhanced,
              confidence: ragResponse.confidence,
              sources: ragResponse.sources,
              suggestions: ragResponse.suggestions,
              usedContext: ragResponse.usedContext,
              metadata: ragResponse.metadata,
              // Add tasks for RAG-enhanced responses
              tasks: ragResponse.ragEnhanced ? [
                {
                  title: "RAG-Analyse durchgeführt",
                  items: [
                    `${ragResponse.sources.length} relevante Quellen gefunden`,
                    `Konfidenz: ${Math.round(ragResponse.confidence * 100)}%`,
                    `${ragResponse.usedContext.length} Kontextfragmente verwendet`
                  ],
                  files: ragResponse.sources.map(s => s.title)
                }
              ] : undefined,
              // Add tools for vector search
              tools: ragResponse.ragEnhanced ? [
                {
                  type: "tool-vector_search",
                  state: "output-available",
                  input: { query: textToSend, threshold: 0.7 },
                  output: `${ragResponse.sources.length} ähnliche Dokumente gefunden`
                }
              ] : undefined
            };
          } catch (ragError) {
            console.error('RAG processing failed, falling back to basic chat:', ragError);
            // Fall back to basic chat
            aiResponse = await processBasicChat(textToSend);
          }
        } else {
          // Use basic chat (existing functionality)
          aiResponse = await processBasicChat(textToSend);
        }

        setMessages(prev => [...prev, aiResponse])
        setChatStatus('idle')
        clearInterval(timeInterval)
        setProcessingTime(0)
      } catch (error) {
        console.error('Chat Error:', error)
        setChatStatus('error')

        // Fallback-Antwort bei Fehler
        const aiResponse: ChatMessage = {
          id: `ai-error-${Date.now()}`,
          text: "Entschuldigung, bei der Verarbeitung Ihrer Nachricht ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.",
          sender: 'ai',
          timestamp: new Date(),
          aiPlus: aiPlusMode,
          ragEnhanced: false,
          suggestions: [
            "Nochmal versuchen",
            "Andere Formulierung",
            "Hilfe anzeigen"
          ]
        }
        setMessages(prev => [...prev, aiResponse])

        setTimeout(() => setChatStatus('idle'), 2000)
      } finally {
        setIsLoading(false)
        clearInterval(timeInterval)
        setProcessingTime(0)
      }
    }
  }, [inputMessage, aiPlusMode, ragMode, isLoading, ragChatService, ragServiceError, messages])

  // Basic chat processing (existing functionality)
  const processBasicChat = useCallback(async (textToSend: string): Promise<ChatMessage> => {
    const apiEndpoint = aiPlusMode ? '/chat/enhanced' : '/chat'
    const requestBody = aiPlusMode
      ? { message: textToSend, includeInsights: true, includeAnomalies: true }
      : { message: textToSend }

    const data = await apiService.post(apiEndpoint, requestBody) as any

    return {
      id: `ai-basic-${Date.now()}`,
      text: data.response || "Ich verstehe deine Nachricht. Lass mich dir dabei helfen!",
      sender: 'ai',
      timestamp: new Date(),
      aiPlus: aiPlusMode,
      ragEnhanced: false,
      sources: aiPlusMode ? [
        { id: 'db-1', title: "Leitstand Datenbank", content: "Aktuelle Datenbank-Abfrage", similarity: 0.9, metadata: { url: "#" } },
        { id: 'kpi-1', title: "KPI Dashboard", content: "Performance-Metriken", similarity: 0.8, metadata: { url: "#" } },
        { id: 'log-1', title: "Störungen Log", content: "Aktuelle Störungsmeldungen", similarity: 0.7, metadata: { url: "#" } }
      ] : undefined,
      suggestions: [
        "Weitere Details anzeigen",
        "Ähnliche Probleme finden",
        "Optimierungsvorschläge",
        "Historische Daten vergleichen"
      ],
      tasks: aiPlusMode ? [
        {
          title: "Datenbank-Analyse durchgeführt",
          items: [
            "Aktuelle KPIs abgerufen",
            "Störungen der letzten 24h analysiert",
            "Performance-Metriken berechnet"
          ],
          files: ["kpi_report.json", "stoerungen_log.csv"]
        }
      ] : undefined,
      tools: aiPlusMode ? [
        {
          type: "tool-database_query",
          state: "output-available",
          input: { query: "SELECT * FROM kpis WHERE date = TODAY()" },
          output: "42 Datensätze gefunden"
        }
      ] : undefined,
      metadata: data.metadata
    };
  }, [aiPlusMode])

  // Suggestion Handler
  const handleSuggestionClick = useCallback((suggestion: string) => {
    handleSend(suggestion)
  }, [handleSend])

  // Copy message handler
  const handleCopyMessage = useCallback((text: string) => {
    navigator.clipboard.writeText(text)
  }, [])

  // AI+ Modus umschalten
  const toggleAiPlusMode = useCallback(() => {
    setAiPlusMode(prev => !prev)
  }, [])

  // RAG Modus umschalten (disabled - RAG always enabled)
  const toggleRagMode = useCallback(() => {
    // RAG is always enabled, no toggle functionality
    console.log('RAG is always enabled');
  }, [])

  // Chat schließen
  const handleClose = useCallback(() => {
    setIsOpen(false)
    setIsExpanded(false)
  }, [])

  // Chat erweitern/minimieren
  const handleToggleExpand = useCallback(() => {
    setIsExpanded(prev => !prev)
  }, [])

  // Wenn Chat geschlossen ist, zeige nur den Floating Button
  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          className="rounded-full w-16 h-16 p-0 flex items-center justify-center shadow-lg bg-primary hover:bg-primary/90 overflow-hidden transition-all duration-200 hover:scale-105"
        >
          <img
            src={lappLogo}
            alt="Lapp Logo"
            className="w-12 h-12 object-contain"
          />
        </Button>
      </div>
    )
  }

  // Chat Interface mit Kibo-UI
  return (
    <div className="fixed bottom-6 right-6 z-50">
      <div className={`bg-background rounded-2xl shadow-2xl border-2 border-orange-200 transition-all duration-300 flex flex-col ${isExpanded
        ? 'fixed inset-4 w-auto h-auto'
        : 'w-96 h-[700px]'
        }`}>
        {/* Header */}
        <div className="bg-gradient-to-r from-orange-400 to-orange-500 text-white rounded-t-xl px-4 py-4 flex items-center justify-between flex-shrink-0">
          <div className="flex items-center space-x-2">
            <img src={lappLogo} alt="Lapp Logo" className="w-5 h-5" />
            <span className="text-base font-medium">JASZ AI</span>
            {aiPlusMode && (
              <div className="flex items-center bg-white/20 rounded-full px-2 py-1">
                <Database className="w-3 h-3 mr-1" />
                <span className="text-xs font-medium">AI+</span>
              </div>
            )}
            <div className="flex items-center bg-white/20 rounded-full px-2 py-1">
              <FileText className="w-3 h-3 mr-1" />
              <span className="text-xs font-medium">RAG</span>
            </div>
            {ragServiceError && (
              <div className="flex items-center bg-red-500/30 rounded-full px-2 py-1">
                <X className="w-3 h-3 mr-1" />
                <span className="text-xs font-medium">RAG Error</span>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20 h-8 w-8 p-0"
              onClick={handleToggleExpand}
            >
              {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20 h-8 w-8 p-0"
              onClick={() => {
                setMessages([])
              }}
              title="Chat zurücksetzen"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20 h-8 w-8 p-0"
              onClick={handleClose}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Messages mit AI Elements */}
        <div className="flex-1 bg-background overflow-y-auto">
          <div className="p-4">
            {messages.map((message) => (
              <div key={message.id} className="space-y-2">
                <Message from={message.sender === 'user' ? 'user' : 'assistant'}>
                  {message.sender === 'user' ? (
                    <>
                      <div className="flex flex-col items-end max-w-[80%]">
                        <MessageContent>
                          <Response>{message.text}</Response>
                        </MessageContent>
                        <div className="text-xs opacity-60 mt-1 mr-2">
                          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </div>
                      </div>
                      <MessageAvatar
                        src="/user-avatar.png"
                        name="User"
                      />
                    </>
                  ) : (
                    <>
                      <div className="size-8 rounded-full bg-orange-500 flex items-center justify-center flex-shrink-0">
                        <Bot className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex flex-col max-w-[80%]">
                        <MessageContent>
                          {/* AI+ Badge */}
                          {message.aiPlus && (
                            <div className="flex items-center mb-2 text-xs text-orange-600">
                              <Database className="w-3 h-3 mr-1" />
                              <span className="font-medium">Database Search Enhanced</span>
                            </div>
                          )}

                          {/* RAG Badge */}
                          {message.ragEnhanced && (
                            <div className="flex flex-col mb-2 text-xs">
                              <div className="flex items-center text-blue-600">
                                <FileText className="w-3 h-3 mr-1" />
                                <span className="font-medium">
                                  RAG Enhanced (Konfidenz: {message.confidence ? Math.round(message.confidence * 100) : 0}%)
                                </span>
                              </div>
                              {message.usedContext && message.usedContext.length > 0 && (
                                <div className="mt-1 text-gray-600">
                                  <span className="font-medium">Verwendeter Kontext:</span>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {message.usedContext.slice(0, 3).map((context, idx) => (
                                      <span key={idx} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                        {context.length > 30 ? `${context.substring(0, 30)}...` : context}
                                      </span>
                                    ))}
                                    {message.usedContext.length > 3 && (
                                      <span className="text-gray-500 text-xs">
                                        +{message.usedContext.length - 3} weitere
                                      </span>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          )}

                          {/* Sources with enhanced display */}
                          {message.sources && message.sources.length > 0 && (
                            <Sources>
                              <SourcesTrigger count={message.sources.length}>
                                <div className="flex items-center gap-2">
                                  <p className="font-medium">
                                    {message.sources.length} Quellen verwendet
                                    {message.confidence && (
                                      <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                        {Math.round(message.confidence * 100)}% Konfidenz
                                      </span>
                                    )}
                                  </p>
                                  <ChevronDownIcon className="h-4 w-4" />
                                </div>
                              </SourcesTrigger>
                              <SourcesContent>
                                <div className="space-y-3">
                                  {message.sources.map((source, idx) => (
                                    <div key={idx} className="border border-gray-200 rounded-lg p-3 bg-gray-50">
                                      <div className="flex items-start justify-between mb-2">
                                        <SourceComponent href={source.metadata?.url || '#'} title={source.title}>
                                          <div className="flex items-center gap-2">
                                            <BookIcon className="h-4 w-4 text-blue-600" />
                                            <span className="block font-medium text-blue-800">{source.title}</span>
                                          </div>
                                        </SourceComponent>
                                        {source.similarity && (
                                          <div className="flex items-center gap-1">
                                            <div className="text-xs text-gray-600">Ähnlichkeit:</div>
                                            <div className="text-xs font-medium text-green-700">
                                              {Math.round(source.similarity * 100)}%
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                      {source.content && (
                                        <div className="text-xs text-gray-700 bg-white p-2 rounded border-l-2 border-blue-200">
                                          {source.content.length > 150
                                            ? `${source.content.substring(0, 150)}...`
                                            : source.content
                                          }
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </SourcesContent>
                            </Sources>
                          )}

                          {/* Tasks */}
                          {message.tasks && message.tasks.map((task, idx) => (
                            <Task key={idx}>
                              <TaskTrigger title={task.title} />
                              <TaskContent>
                                {task.items.map((item, itemIdx) => (
                                  <TaskItem key={itemIdx}>{item}</TaskItem>
                                ))}
                                {task.files && (
                                  <div className="flex gap-2 mt-2">
                                    {task.files.map((file, fileIdx) => (
                                      <TaskItemFile key={fileIdx}>{file}</TaskItemFile>
                                    ))}
                                  </div>
                                )}
                              </TaskContent>
                            </Task>
                          ))}

                          {/* Tools */}
                          {message.tools && message.tools.map((tool, idx) => (
                            <Tool key={idx}>
                              <ToolHeader type={tool.type} state={tool.state} />
                              <ToolContent>
                                {tool.input && <ToolInput input={tool.input} />}
                                {(tool.output || tool.errorText) && (
                                  <ToolOutput output={tool.output} errorText={tool.errorText} />
                                )}
                              </ToolContent>
                            </Tool>
                          ))}

                          {/* Main Response */}
                          <Response>{message.text}</Response>
                        </MessageContent>



                        {/* Actions außerhalb von MessageContent */}
                        <Actions className="mt-2 ml-2">
                          <Action
                            onClick={() => handleCopyMessage(message.text)}
                          >
                            <Copy className="w-4 h-4" />
                          </Action>
                          <Action>
                            <ThumbsUp className="w-4 h-4" />
                          </Action>
                          <Action>
                            <ThumbsDown className="w-4 h-4" />
                          </Action>
                        </Actions>

                        {/* Uhrzeit außerhalb von MessageContent */}
                        <div className="text-xs opacity-60 mt-1 ml-2">
                          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </div>
                      </div>
                    </>
                  )}
                </Message>
              </div>
            ))}

            {isLoading && (
              <Message from="assistant">
                <div className="size-8 rounded-full bg-orange-500 flex items-center justify-center flex-shrink-0">
                  <Bot className="w-5 h-5 text-white" />
                </div>
                <MessageContent>
                  <div className="flex items-center space-x-2">
                    <Loader size={24} />
                    <div className="flex flex-col items-start">
                      <span className="text-sm text-muted-foreground">
                        {chatStatus === 'submitted' ? 'RAG-Analyse läuft - Durchsuche Wissensdatenbank...' :
                          chatStatus === 'streaming' ? 'KI-Antwort wird generiert mit Kontext...' :
                            'Denke nach...'}
                      </span>
                      {processingTime > 5000 && (
                        <span className="text-xs text-orange-600 mt-1">
                          Verarbeitung läuft seit {Math.round(processingTime / 1000)}s - Denke nach...
                        </span>
                      )}
                    </div>
                  </div>
                </MessageContent>
              </Message>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input mit AI Elements - kompakter */}
        <div className="flex-shrink-0 border-t border-orange-200 p-3">
          <PromptInput
            onSubmit={(e) => { e.preventDefault(); handleSend(); }}
            suggestions={(() => {
              const lastMessage = messages[messages.length - 1];
              return lastMessage && lastMessage.sender === 'ai' && lastMessage.suggestions ? lastMessage.suggestions : undefined;
            })()}
            onSuggestionClick={handleSuggestionClick}
          >
            <PromptInputTextarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder={
                aiPlusMode ? "Stelle JASZ-AI eine Frage..." :
                  "Stelle JASZ-AI eine Frage..."
              }
              disabled={isLoading}
              className={cn(
                'min-h-[40px] max-h-[80px]',
                ragMode ? 'bg-blue-50/50' : aiPlusMode ? 'bg-orange-50/50' : ''
              )}
            />
            <PromptInputToolbar>
              <PromptInputTools>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <BorderGlowButton
                      onClick={toggleAiPlusMode}
                      disabled={isLoading}
                      variant={aiPlusMode ? "default" : "ghost"}
                      className={aiPlusMode ? 'bg-gradient-to-r from-orange-400 to-orange-600 text-slate-500' : ''}
                    >
                      <Database className={`w-4 h-4 ${aiPlusMode ? 'animate-pulse' : 'text-slate-700'}`} />
                      <span className={`ml-1 text-xs ${aiPlusMode ? 'text-slate-700' : 'text-muted-foreground'}`}>
                        AI+ Datenbanksuche
                      </span>
                    </BorderGlowButton>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="bg-slate-900 text-white border-3 border-[#b59662]">
                    <div className="text-center">
                      <div className="font-extrabold text-[#b59662]">AI+ Enhance Database</div>
                      <div className="text-xs text-gray-300 mt-1">
                        {aiPlusMode ? 'Datenbanksuche aktiv' : 'Erweitere Datenbanksuche Aktivieren'}
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>

                <PromptInputButton
                  onClick={() => {
                    setMessages([])
                  }}
                  disabled={isLoading}
                  title="Chat zurücksetzen"
                >
                </PromptInputButton>
              </PromptInputTools>

              <PromptInputSubmit
                disabled={!inputMessage.trim() || isLoading}
                status={chatStatus === 'idle' ? undefined : chatStatus}
                className="bg-orange-500 hover:bg-orange-600"
              />
            </PromptInputToolbar>
          </PromptInput>
        </div>
      </div>
    </div>
  )
}