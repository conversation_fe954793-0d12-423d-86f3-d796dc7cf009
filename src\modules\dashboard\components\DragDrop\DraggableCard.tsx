"use client";

import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useDragDropContext } from './DragDropProvider';
import styles from './DragDrop.module.css';

// Interface für die Props der DraggableCard
interface DraggableCardProps {
  id: string;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  style?: React.CSSProperties; // Unterstützung für inline Styles (z.B. für Hintergrundbilder)
}

/**
 * DraggableCard - Wrapper-Komponente für ziehbare KPI-Karten
 * 
 * Diese Komponente macht jede beliebige Karte zu einem ziehbaren Element.
 * Sie nutzt die dnd Kit SortableContext und stellt visuelle Indikatoren bereit.
 * 
 * Features:
 * - Drag-Handle für bessere Benutzerführung
 * - Visuelle Rückmeldung während des Ziehens
 * - Unterstützung für Touch- und Mauseingaben
 * - Barrierefreie Tastaturnavigation
 * - Deaktivierungsmöglichkeit für bestimmte Karten
 */
export const DraggableCard: React.FC<DraggableCardProps> = ({
  id,
  children,
  className = '',
  disabled = false,
  style: customStyle = {} // Benutzerdefinierte Styles (z.B. für Hintergrundbilder)
}) => {
  // Hook für die Sortable-Funktionalität von dnd Kit
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
    isOver,
  } = useSortable({
    id,
    disabled: false // Immer aktivieren, da der Edit-Modus bereits im Provider gesteuert wird
  });

  // Kontext für aktuelle Drag-Operation
  const { activeId, isEditMode } = useDragDropContext();

  // CSS-Transformationen für das Ziehen - kombiniert mit benutzerdefinierten Styles
  const style = {
    ...customStyle, // Benutzerdefinierte Styles zuerst (können überschrieben werden)
    transform: CSS.Transform.toString(transform),
    transition,
    // Zusätzliche Styles während des Ziehens
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1000 : 'auto',
  };

  // Bestimme ob diese Karte gerade aktiv gezogen wird
  const isActiveCard = activeId === id;

  // Bestimme die CSS-Klassen basierend auf dem Drag-Status
  const cardClasses = [
    styles.draggableCard,
    className,
    isDragging && styles['draggableCard--dragging']
  ].filter(Boolean).join(' ');

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cardClasses}
      {...attributes}
    >
      {/* Drag-Fläche für die gesamte Karte im Edit-Modus */}
      {isEditMode && (
        <div
          className="absolute inset-0 cursor-grab"
          {...listeners}
          style={{ zIndex: 10 }}
          aria-label={`KPI-Karte ${id} verschieben`}
          title="Karte verschieben"
        />
      )}

      {/* Indikator für aktive Drag-Operation */}
      {isActiveCard && (
        <div className="absolute inset-0 border-2 border-dashed border-blue-400 rounded-lg pointer-events-none" />
      )}

      {/* Drop-Indikator */}
      {isOver && !isDragging && (
        <div className="absolute inset-0 bg-blue-100/20 border-2 border-blue-400 rounded-lg pointer-events-none" />
      )}

      {/* Inhalt der Karte */}
      <div className={`h-full ${isDragging ? 'pointer-events-none' : ''}`}>
        {children}
      </div>

      {/* Accessibility: Screen Reader Information */}
      <div className="sr-only">
        {isDragging ? 'Karte wird verschoben' : 'Karte kann verschoben werden'}
      </div>
    </div>
  );
};

export default DraggableCard;