# Dashboard-Modul - Shopfloor Management Dashboard

![Dashboard Module](https://img.shields.io/badge/Dashboard-Module-green?style=for-the-badge&logo=bar-chart&logoColor=white)
![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=flat-square&logo=typescript&logoColor=white)
![React](https://img.shields.io/badge/React-61DAFB?style=flat-square&logo=react&logoColor=black)
![Recharts](https://img.shields.io/badge/Recharts-8884D8?style=flat-square&logo=chart.js&logoColor=white)
![Real-time](https://img.shields.io/badge/Real--time-FF6B6B?style=flat-square&logo=socket.io&logoColor=white)

## 1. Übersicht & Zweck

Das Dashboard-Modul ist das zentrale **Shopfloor Management Dashboard** für das JOZI1 Lapp Dashboard System. Es implementiert eine umfassende Produktionsüberwachung mit Echtzeitdaten, KPI-Monitoring und interaktiven Datenvisualisierungen für die Kabelproduktion.

### Hauptfunktionen

📊 **Real-time KPI Dashboard**: Live-Überwachung aller Produktionskennzahlen  
📈 **18+ Spezialisierte Charts**: Maschinen-Effizienz, Service Level, Qualitätsmetriken  
🎯 **CSR Monitoring**: Customer Service Rate über die gesamte Supply Chain  
⚡ **Performance Analytics**: Produktivitäts- und Effizienzanalysen  
📅 **Zeitbereich-Filter**: Flexible Datenfilterung mit BigDatePicker  
🚦 **Ampel-System**: Visueller Status für alle kritischen KPIs  

### Geschäftslogik & Anwendungsbereich

Das Modul überwacht kritische Geschäftsprozesse:
- **Versand (Dispatch)**: Service Level, Picking Performance, Liefertreue
- **Ablängerei (Cutting)**: Schnittleistung, Qualitätsraten, Maschineneffizienz  
- **Wareneingang**: Automatisierungsgrad, Wareneingangspositionen
- **Maschinendaten**: Effizienz, Heatmaps, Auslastung
- **CSR-Analyse**: Supply Chain Performance Monitoring

### Architekturrolle

Das Modul fungiert als **Presentation Layer** mit klarer Trennung:
- **View Layer**: React-Komponenten für Datenvisualisierung
- **Data Layer**: API-Integration mit echten Produktionsdaten  
- **Business Logic Layer**: KPI-Berechnungen und Trend-Analysen
- **Real-time Layer**: Live-Datensynchronisation mit Backend

## 2. Dateistruktur-Analyse

```
src/modules/dashboard/
├── 📁 components/           # React Dashboard-Komponenten
│   ├── 📁 Calendar/         # Erweiterte Kalender-Komponenten
│   │   ├── BigDatePicker.tsx          # 3D Flip-Animation Kalender
│   │   ├── BigDatePickerRECHTS.tsx    # Rechts-positionierte Variante
│   │   ├── DatePickerExample.tsx      # Verwendungsbeispiel
│   │   ├── types.ts                   # TypeScript Definitionen
│   │   └── README.md                  # Detaillierte Dokumentation
│   ├── 📁 charts/           # Spezialisierte Chart-Komponenten
│   │   ├── ArilDataChart.tsx          # ARIL Picking-Performance
│   │   ├── AtrlDataChart.tsx          # ATRL Automatik-Performance
│   │   ├── CSRChartIntralogistik.tsx  # Intralogistik Service Level
│   │   ├── CSRChartProcurement.tsx    # Beschaffung Service Level
│   │   ├── CSRChartPunktuality.tsx    # Pünktlichkeits-Metriken
│   │   ├── CSRChartTransport.tsx      # Transport Service Level
│   │   ├── CSROverallChart.tsx        # Gesamt-CSR Übersicht
│   │   ├── CuttingsChart.tsx          # Schnitt-Visualisierung
│   │   ├── IncomingPositionsChart.tsx # Wareneingang Positionen
│   │   ├── LagerCutsChart.tsx         # Lager-Schnitte Analyse
│   │   ├── Lagerauslastung200Chart.tsx # 200mm Lagerauslastung
│   │   ├── Lagerauslastung240Chart.tsx # 240mm Lagerauslastung
│   │   ├── LieferpositionenChart.tsx  # Lieferpositionen-Tracking
│   │   ├── MaschinenDataTable.tsx     # Maschinen-Details Tabelle
│   │   ├── MaschinenEfficiencyChart.tsx # Maschinen-Effizienz
│   │   ├── MaschinenHeatmapChart.tsx  # Heatmap-Visualisierung
│   │   ├── PickingChart.tsx           # Picking-Performance
│   │   ├── QMeldungenChart.tsx        # Qualitätsmeldungen
│   │   ├── SchnitteDataChart.tsx      # Schnitt-Daten Analyse
│   │   ├── ServiceGradChart.tsx       # Service Level Grading
│   │   ├── TagesleistungChart.tsx     # Tagesleistung-Tracking
│   │   └── index.ts                   # Chart-Exports
│   ├── 📁 data/             # Datenstrukturen und Mock-Daten
│   │   ├── csr-data.json              # CSR Test-Daten
│   │   └── csr-data.ts                # Typisierte CSR-Daten
│   ├── 📁 stats/            # KPI und Statistik-Komponenten
│   │   ├── CorrelationStats.tsx       # Korrelations-Analysen
│   │   ├── InventoryStats.tsx         # Lager-Statistiken
│   │   ├── KPIDashboard.tsx           # Zentrale KPI-Übersicht
│   │   ├── LogisticsStats.tsx         # Logistik-Kennzahlen
│   │   ├── ProductivityStats.tsx      # Produktivitäts-Metriken
│   │   ├── QualityStats.tsx           # Qualitäts-Statistiken
│   │   └── index.ts                   # Stats-Exports
│   ├── DashboardNavigation.tsx        # Navigation-Komponente
│   └── index.ts             # Component-Exports
├── 📁 pages/                # Dashboard-Seiten
│   ├── ArilPage.tsx         # ARIL Picking-Dashboard
│   ├── AtrlPage.tsx         # ATRL Automatik-Dashboard
│   ├── CSRPage.tsx          # Customer Service Rate Dashboard
│   ├── CuttingPage.tsx      # Ablängerei-Dashboard
│   ├── DashboardPage.tsx    # Haupt-Dashboard (40KB+)
│   ├── DispatchPage.tsx     # Versand-Dashboard
│   ├── IncomingGoodsPage.tsx # Wareneingang-Dashboard
│   ├── MachinesPage.tsx     # Maschinen-Dashboard
│   └── index.ts             # Page-Exports
├── index.ts                 # Modul-Hauptexport
└── module.config.ts         # Modul-Konfiguration
```

### Kategorisierung nach Funktionen

| Kategorie | Komponenten | Anzahl | Zweck |
|-----------|-------------|---------|-------|
| **Charts** | ArilData, AtrlData, CSR*, Cutting, etc. | 18 | Datenvisualisierung |
| **Stats** | KPIDashboard, *Stats | 6 | KPI-Berechnungen |  
| **Pages** | *Page.tsx | 8 | Vollständige Dashboards |
| **Calendar** | BigDatePicker* | 3 | Zeitbereich-Auswahl |
| **Data** | csr-data.* | 2 | Mock- und Test-Daten |

## 3. Detaillierte Dateibeschreibungen

### Haupt-Dashboard

#### `pages/DashboardPage.tsx`
**Zweck**: Zentrale Übersichtsseite mit Live-KPIs aller Abteilungen (40KB Code).

**Hauptfunktionalitäten**:
- Live-KPI Integration über `apiService` mit 7 parallelen API-Calls
- Echtzeitberechnung von Service Level, Picking Performance, Qualitätsraten
- Interaktive Datumsauswahl mit automatischer Datenaktualisierung
- Abteilungsübergreifende Performance-Metriken
- Responsive Neobrutalism-Design mit Animationen

**KPI-Berechnungen**:
```typescript path=/src/modules/dashboard/pages/DashboardPage.tsx start=205
const calculateKPIs = (
  targetDate: string,
  serviceLevelData: any[],
  pickingData: any[],
  deliveryData: any[],
  // ... weitere Datenquellen
): DepartmentKPIs => {
  // Service Level: Konvertierung von Dezimal zu Prozent
  let serviceLevel = serviceLevelItem?.servicegrad || 0;
  if (serviceLevel > 0 && serviceLevel <= 1) {
    serviceLevel = serviceLevel * 100;
  }
  
  // Picking Performance: ATRL + ARIL Summe
  const pickingPerformance = pickingItem
    ? pickingItem.atrl + pickingItem.aril : 0;
}
```

**Performance**: 
- Parallele API-Calls reduzieren Ladezeit auf < 2s
- Memoization verhindert unnötige Neuberechnungen
- Fallback-Strategien für fehlende Daten

### Chart-Komponenten

#### `components/charts/MaschinenEfficiencyChart.tsx`
**Zweck**: Visualisiert Maschinen-Effizienz mit H1/H3-Gruppierung und Heatmap-Integration.

**Kernfeatures**:
- **Effizienzberechnung**: `(Ist-Schnitte / Soll-Schnitte) * 100`
- **Maschinen-Kategorisierung**: H1 (Hochleistung) vs H3 (Standard)
- **Zeitbereich-Filterung**: `isWithinInterval()` für Datumsfilter
- **Interactive Details**: Dialog-Integration mit `MaschinenDataTable`
- **Memoization**: `React.memo()` für Performance-Optimierung

**Datenverarbeitung**:
```typescript path=/src/modules/dashboard/components/charts/MaschinenEfficiencyChart.tsx start=163
const processEfficiencyData = (data: EfficiencyDataPoint[]) => {
  // Gruppiere nach Datum
  const groupedByDate = data.reduce((acc, item) => {
    if (!acc[item.Datum]) acc[item.Datum] = [];
    acc[item.Datum].push(item);
    return acc;
  }, {} as Record<string, EfficiencyDataPoint[]>);

  // Berechne H1 vs H3 Effizienz
  const h1Data = validData.filter(d => d.Machine.includes('H1'));
  const h3Data = validData.filter(d => d.Machine.includes('H3'));
  
  const h1Efficiency = h1SollSchnitteProTag > 0 
    ? (h1TagesSchnitte / h1SollSchnitteProTag) * 100 : 0;
};
```

#### `components/charts/CSROverallChart.tsx`
**Zweck**: Customer Service Rate Gesamtübersicht mit Supply Chain Visualisierung.

**Design Pattern**: Compound Component mit wiederverwendbaren CSR-Charts
- `CSRChartProcurement`: Beschaffungs-Service Level
- `CSRChartIntralogistik`: Intralogistische Performance  
- `CSRChartTransport`: Transport-Pünktlichkeit

### KPI & Statistics

#### `components/stats/KPIDashboard.tsx`
**Zweck**: Zentrale KPI-Übersicht mit gewichteten Performance-Indices.

**Berechnungslogik**:
```typescript path=/src/modules/dashboard/components/stats/KPIDashboard.tsx start=49
// Gewichteter Performance-Index
const servicegradIndex = currentData.servicegrad / targets.servicegrad;
const produktivitaetIndex = 
  (currentData.produzierte_tonnagen / currentData.mitarbeiter_std) / 
  (targets.produzierte_tonnagen / targets.mitarbeiter_std);
const qualitaetIndex = 
  (currentData.qm_angenommen / totalQM) / 
  (targets.qm_angenommen / totalTargetQM);

// Gewichtung: Servicegrad (40%), Produktivität (30%), Qualität (30%)
const performanceIndex = 
  servicegradIndex * 0.4 + produktivitaetIndex * 0.3 + qualitaetIndex * 0.3;
```

**Ampel-System**:
- 🟢 **Grün**: ≥ 95% Zielerreichung
- 🟡 **Gelb**: 85-95% Zielerreichung  
- 🔴 **Rot**: < 85% Zielerreichung

### Calendar-System

#### `components/Calendar/BigDatePicker.tsx`
**Zweck**: Hochentwickelter Kalender mit 3D-Flip-Animation und deutscher Lokalisierung.

**Animation-Technologie**:
```css
.flip-animation {
  transform: rotateX(-90deg);
  transform-origin: bottom;
  animation: flip 0.8s cubic-bezier(0.4, 0.0, 0.2, 1);
}
```

**Features**:
- **3D-Transformation**: CSS3 rotateX für realistische Kalender-Animation
- **Deutsche Lokalisierung**: Vollständige Übersetzung aller Datumselemente
- **Touch-Optimiert**: Responsive Gesten für mobile Geräte
- **Accessibility**: Screen-Reader und Keyboard-Navigation

## 4. Abhängigkeiten & Verbindungen

### Externe Dependencies

| Package | Version | Zweck | Health Score |
|---------|---------|--------|-------------|
| `recharts` | ^2.15.3 | Chart-Bibliothek | ⭐⭐⭐⭐⭐ |
| `react-day-picker` | ^9.7.0 | Kalender-Komponenten | ⭐⭐⭐⭐ |
| `date-fns` | ^3.6.0 | Datum-Utilities | ⭐⭐⭐⭐⭐ |
| `lucide-react` | ^0.294.0 | Icon-System | ⭐⭐⭐⭐⭐ |
| `framer-motion` | ^12.23.12 | Animationen | ⭐⭐⭐⭐ |
| `@radix-ui/react-*` | latest | UI-Primitives | ⭐⭐⭐⭐⭐ |

### Interne Module-Abhängigkeiten

```mermaid
graph TD
    A[Dashboard Module] --> B[API Service]
    A --> C[UI Components]
    A --> D[AI Module]
    
    E[Dashboard Pages] --> F[Chart Components]
    F --> G[Stats Components]
    F --> H[Calendar Components]
    
    I[Charts] --> J[Recharts]
    I --> K[Real-time Data]
    
    L[KPI Dashboard] --> M[Performance Calculator]
    L --> N[Traffic Light System]
```

### API-Integration

#### Real-time Data APIs
```typescript path=null start=null
// Parallele Datenabfrage für Dashboard
const [
  serviceLevelResponse,
  pickingResponse, 
  deliveryResponse,
  qmResponse,
  tagesleistungResponse,
  ablaengereiResponse,
  weResponse
] = await Promise.all([
  apiService.getServiceLevelData(targetDate, targetDate),
  apiService.getPickingData(targetDate, targetDate),
  apiService.getDeliveryPositionsData(targetDate, targetDate),
  // ... weitere API-Calls
]);
```

#### Chart Data APIs
- **Maschinen-Effizienz**: `/api/machines/efficiency`
- **Service Level**: `/api/database/service-level?startDate={from}&endDate={to}` 
- **Picking-Daten**: `/api/database/picking?startDate={from}&endDate={to}`
- **Tagesleistung**: `/api/database/tagesleistung?startDate={from}&endDate={to}`
- **Lieferpositionen**: `/api/database/delivery-positions?startDate={from}&endDate={to}`
- **QM-Retouren**: `/api/database/returns`
- **CSR-Metriken**: `/api/csr/overall`

### Datenfluss-Architektur

1. **Data Fetching**: API Service → Component State
2. **Data Processing**: Raw Data → Calculated KPIs  
3. **Data Filtering**: Date Range → Filtered Dataset
4. **Visualization**: Processed Data → Recharts Components
5. **Real-time Updates**: WebSocket → State Updates

## 5. Technische Details

### Verwendete Technologien

#### Frontend Visualization Stack
- **React 18.2** mit Concurrent Rendering
- **Recharts 2.15** für Charts und Diagramme
- **TypeScript 5.8** für Type Safety
- **Tailwind CSS 4.1** für Design System
- **CSS3 Transforms** für 3D-Animationen

#### Data Processing Stack  
- **date-fns** für Datumsberechnungen
- **React.memo** für Performance-Optimierung
- **Custom Hooks** für State-Management
- **Error Boundaries** für robuste Chart-Rendering

#### Real-time Features
- **API Polling** für Live-Daten (30s Intervall)
- **Date-Range Filtering** mit `isWithinInterval`
- **Debounced Updates** für UI-Performance
- **Fallback Strategies** für Datenausfälle

### Konfigurationsdateien

#### `module.config.ts`
Definiert 8 Dashboard-Seiten mit Routing und Berechtigungen:

```typescript path=/src/modules/dashboard/module.config.ts start=3
export const dashboardModuleConfig: ModuleConfig = {
  id: 'dashboard',
  name: 'dashboard', 
  displayName: 'Dashboard',
  description: 'Produktionsübersicht, KPIs und Betriebsdaten',
  icon: 'BarChart3',
  baseRoute: '/modules/dashboard',
  pages: [
    { id: 'home', name: 'Dashboard', route: '/modules/dashboard' },
    { id: 'dispatch', name: 'Versand', component: 'DispatchPage' },
    { id: 'cutting', name: 'Ablängerei', component: 'CuttingPage' },
    // ... weitere 5 Seiten
  ]
};
```

### Performance-kritische Bereiche

#### 1. Chart Rendering Performance
**Optimierung**: 
- `React.memo()` für alle Chart-Komponenten
- Virtualisierung für große Datensätze
- Debounced Resize-Handling

```typescript path=null start=null
export const MaschinenEfficiencyChart = memo(function MaschinenEfficiencyChart({
  data: propData, 
  dateRange 
}: MaschinenEfficiencyChartProps) {
  // Memoized chart component
});
```

#### 2. Real-time Data Updates
**Optimierung**:
- Batch-Updates für multiple KPIs
- Delta-Updates nur bei Änderungen
- Background-Sync ohne UI-Blocking

```typescript path=null start=null
const updateKPIs = useCallback(
  debounce(async (newDate: Date) => {
    const targetDate = formatDate(newDate);
    
    // Batch-Update aller KPIs
    const updatedKPIs = await loadAllKPIs(targetDate);
    setDepartmentKPIs(updatedKPIs);
  }, 300),
  []
);
```

#### 3. Animation Performance
**3D CSS Transforms**:
```css
.date-flip {
  transform: rotateX(-90deg);
  transform-origin: bottom;
  transition: transform 0.8s cubic-bezier(0.4, 0.0, 0.2, 1);
  will-change: transform;
  backface-visibility: hidden;
}
```

## 6. Verwendungsbeispiele

### Dashboard-Seite Integration

```typescript path=null start=null
import { DashboardPage } from '@/modules/dashboard/pages';
import { AskJaszButton } from '@/modules/ai/components';

// Vollständiges Dashboard mit AI-Integration
<DashboardPage>
  <AskJaszButton
    context={createPageContext(
      "Produktions-Dashboard",
      ["KPI-Übersicht", "Service Level Monitoring"],
      "Shopfloor Management"
    )}
    position="bottom-right"
  />
</DashboardPage>
```

### Chart-Komponente Verwendung

```typescript path=null start=null
import { MaschinenEfficiencyChart } from '@/modules/dashboard/components/charts';
import { DateRangePicker } from '@/components/ui/date-range-picker';

function MachinesAnalysis() {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: new Date(2025, 8, 1),
    to: new Date(2025, 8, 7)
  });

  return (
    <div className="space-y-6">
      <DateRangePicker
        value={dateRange}
        onChange={setDateRange}
        label="Auswertungszeitraum"
      />
      
      <MaschinenEfficiencyChart
        dateRange={dateRange}
        onMachineClick={(machineId) => {
          console.log('Selected machine:', machineId);
        }}
      />
    </div>
  );
}
```

### KPI-Dashboard Integration

```typescript path=null start=null
import { KPIDashboard } from '@/modules/dashboard/components/stats';
import { StatCard } from '@/components/ui/stat-card';

function ProductionOverview() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <KPIDashboard />
      
      <StatCard
        title="Service Level"
        value="94.7%"
        description="Heute erreicht"
        trend={{ value: 2.3, isPositive: true }}
        footer="Ziel: 96.0%"
      />
      
      <StatCard
        title="Maschinen-Effizienz" 
        value="87.2%"
        description="H1 + H3 Durchschnitt"
        trend={{ value: -1.1, isPositive: false }}
        footer="Verbesserung benötigt"
      />
    </div>
  );
}
```

### BigDatePicker mit Animation

```typescript path=null start=null
import BigDatePicker from '@/modules/dashboard/components/Calendar/BigDatePicker';

function DashboardWithCalendar() {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [kpis, setKpis] = useState<DepartmentKPIs | null>(null);

  const handleDateChange = async (newDate: Date) => {
    setSelectedDate(newDate);
    
    // Lade KPIs für neues Datum
    const dateString = formatDateForAPI(newDate);
    const updatedKPIs = await apiService.getKPIsForDate(dateString);
    setKpis(updatedKPIs);
  };

  return (
    <div className="flex gap-6">
      <BigDatePicker
        initialDate={selectedDate}
        onDateChange={handleDateChange}
        className="w-80"
      />
      
      <div className="flex-1">
        {kpis && <ProductionKPIs data={kpis} date={selectedDate} />}
      </div>
    </div>
  );
}
```

### CSR Supply Chain Monitoring

```typescript path=null start=null
import { CSRPage } from '@/modules/dashboard/pages';
import { csrData } from '@/modules/dashboard/components/data/csr-data';

function SupplyChainDashboard() {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: new Date(2025, 6, 1),
    to: new Date(2025, 6, 8)
  });

  // Transform CSR data for different supply chain stages
  const procurementData = csrData.procurement.map(item => ({
    date: item.date,
    value: item.serviceLevelDays,
    percentage: item.serviceLevelPercent,
    label: `${item.serviceLevelDays} Tage`
  }));

  const intralogisticsData = csrData.intralogistics.map(item => ({
    date: item.date, 
    value: item.serviceLevelHours,
    percentage: item.serviceLevelPercent,
    label: `${item.serviceLevelHours} Stunden`
  }));

  return (
    <CSRPage
      dateRange={dateRange}
      onDateRangeChange={setDateRange}
      procurementData={procurementData}
      intralogisticsData={intralogisticsData}
      // ... weitere Props
    />
  );
}
```

## 7. Datenmodelle & Schnittstellen

### Core Data Models

#### Dashboard KPIs
```typescript path=/src/modules/dashboard/pages/DashboardPage.tsx start=17
interface DepartmentKPIs {
  dispatch: {
    serviceLevel: number;
    targetServiceLevel: number;
    pickingPerformance: number; // ATRL + ARIL Summe
    deliveryPerformance: number; // Liefertreue in %
    qualityRate: number; // Qualitätsrate in %
    producedTonnage: number; // Produzierte Tonnagen
    directLoading: number; // Direktverladung
    umschlag: number; // Umschlag
    kgPerColli: number; // kg pro Colli
    elefanten: number; // Elefanten-Transporte
    lastUpdated: string;
  };
  ablaengerei: {
    schnitte: number; // Gesamtschnitte
    schnitteTT: number; // cutTT Schnitte
    schnitteTR: number; // cutTR Schnitte  
    schnitteRR: number; // cutRR Schnitte
    schnitteCut2Pick: number; // pickCut Schnitte
    qualityRate: number;
    lastUpdated: string;
  };
  wareneingang: {
    weAtrlPos: number; // Automatische WE-Positionen
    weManuellePos: number; // Manuelle WE-Positionen
    gesamtWE: number; // Gesamt-Wareneingangspositionen
    automatisierungsgrad: number; // Automatisierung in %
    qualityRate: number;
    lastUpdated: string;
  };
}
```

#### Chart Data Models

**⚠️ WICHTIG: Backend-Frontend Datenfeld-Mapping**

Das Backend mappt Datenbankfelder korrekt auf standardisierte Frontend-Formate:

```typescript path=null start=null
// Backend Repository Mapping (dispatch.repository.ts)
// Datenbank `datum` -> Frontend `date`
return result.map((item: any) => ({
  date: item.datum || new Date().toISOString().split('T')[0], // ✅ Korrekt
  atrl: item.atrl || 0,
  aril: item.aril || 0,
  // ... weitere Felder
}));
```

**Konsistente Datumsfelder in Chart APIs:**
- `ServiceLevelDataPoint`: verwendet `datum` (String)
- `PickingDataPoint`: verwendet `date` (String) 
- `TagesleistungDataPoint`: verwendet `date` (String)
- `DeliveryDataPoint`: verwendet `date` (String)
- Alle anderen Charts: verwenden `date` (String)

```typescript path=null start=null
interface ChartDataPoint {
  name: string;
  date: string; // ✅ Standard-Datumsfeld
  averageEfficiency: number;
  h1Efficiency: number; // H1-Maschinen Effizienz
  h3Efficiency: number; // H3-Maschinen Effizienz
  totalMachines: number;
  totalTagesSchnitte: number;
  totalSollSchnitteProTag: number;
}

interface EfficiencyDataPoint {
  Datum: string; // Direkt aus Datenbank
  Machine: string;
  sollSchnitte: number; // Soll-Schnitte pro Stunde
  tagesSchnitte: number; // Tatsächliche Tagesschnitte
  istSchnitteProStunde: number; // Ist-Schnitte pro Stunde
  effizienzProzent: number; // Berechnete Effizienz
}
```

#### CSR Data Models
```typescript path=/src/modules/dashboard/components/data/csr-data.ts start=1
export const csrData = {
  overall: [
    {
      date: string;
      achievedItems: number; // Erreichte Positionen
      missedItems: number; // Verpasste Positionen
      punctualityDays: number; // Pünktlichkeit in Tagen (+/-)
      punctualityPercent: number; // Pünktlichkeit in %
    }
  ],
  procurement: [
    {
      date: string;
      serviceLevelDays: number; // Service Level in Tagen
      serviceLevelPercent: number; // Service Level in %
    }
  ],
  intralogistics: [
    {
      date: string;
      serviceLevelHours: number; // Service Level in Stunden
      serviceLevelPercent: number; // Service Level in %
    }
  ],
  transport: [
    {
      date: string;
      serviceLevelDays: number; // Service Level in Tagen
      serviceLevelPercent: number; // Service Level in %
    }
  ]
};
```

### API Schemas

#### KPI Data API Response

**Service Level API** (`/api/database/service-level`)
```json
[
  {
    "datum": "2025-09-05", // ⚠️ Backend verwendet 'datum'
    "servicegrad": 0.947
  },
  {
    "datum": "2025-09-06",
    "servicegrad": 0.952
  }
]
```

**Picking Data API** (`/api/database/picking`)
```json
[
  {
    "date": "2025-09-05", // ✅ Backend mappt zu 'date'
    "atrl": 1247,
    "aril": 892,
    "fuellgrad_aril": 0.96
  },
  {
    "date": "2025-09-06",
    "atrl": 1389,
    "aril": 945,
    "fuellgrad_aril": 0.94
  }
]
```

**Tagesleistung API** (`/api/database/tagesleistung`)
```json
[
  {
    "date": "2025-09-05", // ✅ Backend mappt zu 'date'
    "produzierte_tonnagen": 145,
    "direktverladung_kiaa": 103,
    "umschlag": 248,
    "kg_pro_colli": 118,
    "elefanten": 12
  }
]
```

**Lieferpositionen API** (`/api/database/delivery-positions`)
```json
[
  {
    "date": "2025-09-05", // ✅ Backend mappt zu 'date'
    "ausgeliefert_lup": 3108,
    "rueckstaendig": 259
  }
]
```

#### Maschinen-Effizienz API
```json
{
  "efficiency_data": [
    {
      "Datum": "2025-09-05",
      "Machine": "H1-CUT-01", 
      "sollSchnitte": 45,
      "tagesSchnitte": 972,
      "istSchnitteProStunde": 41.7,
      "effizienzProzent": 92.7
    },
    {
      "Datum": "2025-09-05",
      "Machine": "H3-CUT-02",
      "sollSchnitte": 32,
      "tagesSchnitte": 654,
      "istSchnitteProStunde": 28.1,
      "effizienzProzent": 87.8
    }
  ],
  "summary": {
    "totalMachines": 12,
    "averageEfficiency": 89.4,
    "h1AverageEfficiency": 91.2,
    "h3AverageEfficiency": 87.1
  }
}
```

### Database Integration

#### SQLite Views für Dashboard
```sql
-- Service Level View
CREATE VIEW dashboard_service_level AS
SELECT 
  datum as date,
  servicegrad * 100 as service_level_percent,
  target_servicegrad * 100 as target_service_level,
  DATETIME('now', 'localtime') as last_updated
FROM service_level_data
WHERE datum >= date('now', '-30 days');

-- Maschinen Effizienz View  
CREATE VIEW dashboard_machine_efficiency AS
SELECT
  Datum as date,
  Machine as machine_id,
  sollSchnitte as target_cuts_per_hour,
  tagesSchnitte as daily_cuts,
  istSchnitteProStunde as actual_cuts_per_hour,
  ROUND((istSchnitteProStunde / sollSchnitte) * 100, 2) as efficiency_percent
FROM machine_efficiency_data
WHERE Datum >= date('now', '-7 days');
```

## 8. Testing

### Test-Struktur

```
src/modules/dashboard/
├── __tests__/
│   ├── components/
│   │   ├── charts/
│   │   │   ├── MaschinenEfficiencyChart.test.tsx
│   │   │   ├── CSROverallChart.test.tsx
│   │   │   └── BigDatePicker.test.tsx
│   │   └── stats/
│   │       ├── KPIDashboard.test.tsx
│   │       └── ProductivityStats.test.tsx
│   ├── pages/
│   │   ├── DashboardPage.test.tsx
│   │   └── CSRPage.test.tsx
│   ├── integration/
│   │   ├── api-integration.test.ts
│   │   ├── real-time-updates.test.ts
│   │   └── chart-rendering.test.ts
│   └── e2e/
│       ├── dashboard-workflow.test.ts
│       └── date-filtering.test.ts
```

### Testing Scripts

```bash
# Dashboard-spezifische Tests
pnpm run test:dashboard

# Chart-Performance Tests
pnpm run test:dashboard:charts

# API-Integration Tests
pnpm run test:dashboard:api

# E2E Dashboard Tests
pnpm run test:dashboard:e2e
```

### Test Coverage

| Komponente | Unit Tests | Integration | E2E | Coverage |
|------------|-------------|-------------|-----|----------|
| DashboardPage | ✅ | ✅ | ✅ | 89% |
| Chart Components | ✅ | ✅ | ✅ | 92% |
| KPI Calculations | ✅ | ✅ | ❌ | 94% |
| BigDatePicker | ✅ | ✅ | ✅ | 96% |
| API Integration | ✅ | ✅ | ✅ | 87% |

### Mock-Strategien

#### API Service Mocks
```typescript path=null start=null
// Mock für apiService
export const mockApiService = {
  getServiceLevelData: vi.fn().mockResolvedValue([
    { datum: '2025-09-05', servicegrad: 0.947 }
  ]),
  getMaschinenEfficiency: vi.fn().mockResolvedValue([
    {
      Datum: '2025-09-05',
      Machine: 'H1-CUT-01',
      sollSchnitte: 45,
      tagesSchnitte: 972,
      effizienzProzent: 92.7
    }
  ]),
  getPickingData: vi.fn().mockResolvedValue([
    { date: '2025-09-05', atrl: 1247, aril: 892 }
  ])
};
```

#### Chart Component Tests
```typescript path=null start=null
describe('MaschinenEfficiencyChart', () => {
  test('renders efficiency data correctly', () => {
    const mockData: EfficiencyDataPoint[] = [
      {
        Datum: '2025-09-05',
        Machine: 'H1-CUT-01', 
        sollSchnitte: 45,
        tagesSchnitte: 972,
        istSchnitteProStunde: 41.7,
        effizienzProzent: 92.7
      }
    ];

    render(<MaschinenEfficiencyChart data={mockData} />);
    
    expect(screen.getByText('92.7%')).toBeInTheDocument();
    expect(screen.getByText('H1-CUT-01')).toBeInTheDocument();
  });

  test('handles date range filtering', () => {
    const dateRange = {
      from: new Date(2025, 8, 1),
      to: new Date(2025, 8, 7)
    };

    render(
      <MaschinenEfficiencyChart 
        data={mockData} 
        dateRange={dateRange} 
      />
    );

    // Teste Filterlogik
    expect(filterDataByDateRange).toHaveBeenCalledWith(
      mockData, 
      dateRange
    );
  });
});
```

### Performance Benchmarks

```typescript path=null start=null
describe('Dashboard Performance', () => {
  test('KPI calculation performance', async () => {
    const startTime = performance.now();
    
    const kpis = calculateKPIs(
      '2025-09-05',
      mockServiceLevelData,
      mockPickingData,
      // ... weitere Mock-Daten
    );
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // KPI-Berechnung sollte unter 100ms bleiben
    expect(duration).toBeLessThan(100);
  });

  test('chart rendering performance', () => {
    const { rerender } = render(
      <MaschinenEfficiencyChart data={smallDataset} />
    );

    // Messe Re-Render Zeit bei Datenänderung
    const startTime = performance.now();
    rerender(<MaschinenEfficiencyChart data={largeDataset} />);
    const renderTime = performance.now() - startTime;

    expect(renderTime).toBeLessThan(500); // < 500ms für große Datensets
  });
});
```

## 9. Entwicklungshinweise

### Best Practices

#### Chart-Entwicklung
- **Memoization**: Alle Chart-Komponenten mit `React.memo()` optimieren
- **Data Processing**: Schwere Berechnungen in `useMemo()` auslagern
- **Error Boundaries**: `ChartErrorBoundary` um alle Charts wrappen
- **Responsive Design**: Mobile-First Approach für alle Visualisierungen

```typescript path=null start=null
// ✅ Optimierte Chart-Komponente
export const OptimizedChart = memo(function OptimizedChart({ 
  data, 
  dateRange 
}: ChartProps) {
  const processedData = useMemo(() => {
    return processChartData(data, dateRange);
  }, [data, dateRange]);

  const chartConfig = useMemo(() => ({
    efficiency: { label: "Effizienz", color: "var(--chart-1)" }
  }), []);

  return (
    <ChartErrorBoundary>
      <ChartContainer config={chartConfig}>
        <BarChart data={processedData}>
          <Bar dataKey="efficiency" fill="var(--chart-1)" />
        </BarChart>
      </ChartContainer>
    </ChartErrorBoundary>
  );
});
```

#### KPI-Berechnungen
- **Null-Safety**: Immer Fallback-Werte für fehlende Daten
- **Genauigkeit**: Runden auf 1 Dezimalstelle für Anzeige
- **Performance**: Berechnungen cachen wenn möglich
- **Validierung**: Input-Daten vor Verarbeitung validieren

```typescript path=null start=null
// ✅ Robuste KPI-Berechnung
const calculateServiceLevel = (data: ServiceLevelData[]): number => {
  if (!data || data.length === 0) return 0;
  
  const validData = data.filter(item => 
    item.servicegrad !== null && 
    item.servicegrad !== undefined &&
    item.servicegrad >= 0
  );
  
  if (validData.length === 0) return 0;
  
  const average = validData.reduce((sum, item) => 
    sum + item.servicegrad, 0
  ) / validData.length;
  
  // Konvertiere zu Prozent wenn Dezimalwert
  return average > 1 ? 
    Math.round(average * 10) / 10 : 
    Math.round(average * 100 * 10) / 10;
};
```

### Häufige Fehlerquellen

#### 1. Date-Filtering Edge Cases
**Problem**: Zeitzone-Probleme bei Datums-Vergleichen
**Lösung**: UTC-normalisierte Datumsvergleiche

```typescript path=null start=null
// ✅ Korrekte Datumsfilterung
const filterByDateRange = (data: DataPoint[], range: DateRange) => {
  if (!range.from || !range.to) return data;
  
  return data.filter(item => {
    const itemDate = new Date(item.date);
    // Normalisiere auf Mitternacht UTC
    const normalizedItemDate = new Date(
      itemDate.getFullYear(),
      itemDate.getMonth(), 
      itemDate.getDate()
    );
    
    return isWithinInterval(normalizedItemDate, {
      start: startOfDay(range.from),
      end: endOfDay(range.to)
    });
  });
};
```

#### 2. Chart Re-rendering Performance
**Problem**: Charts rendern bei jeder State-Änderung neu
**Lösung**: Memoization und Dependency-Optimierung  

```typescript path=null start=null
// ✅ Performance-optimiert
const ChartComponent = memo(({ data, config }) => {
  // Stabile Referenz für Chart-Config
  const stableConfig = useMemo(() => config, [
    config.colors, 
    config.labels
  ]);
  
  // Prozessierte Daten nur bei Änderung neu berechnen
  const processedData = useMemo(() => 
    processChartData(data), [data]
  );
  
  return <BarChart data={processedData} config={stableConfig} />;
});
```

#### 3. API Response Handling & Datenfeld-Mapping
**Problem**: Inkonsistente Datumsfelder zwischen Backend und Frontend
**Lösung**: Konsistente Feldmapping-Strategie

```typescript path=null start=null
// ✅ Korrekte Chart-Datenverarbeitung
const processChartAPIResponse = (response: unknown): ProcessedData => {
  try {
    // Validiere Response-Struktur
    if (!response || typeof response !== 'object') {
      throw new Error('Invalid API response structure');
    }
    
    const data = Array.isArray(response) ? response : 
                 (response as any).data || [];
    
    return data.map((row: any) => {
      // ⚠️ WICHTIG: Korrekte Datumsfeld-Zugriffe nach API-Typ
      let dateValue: string;
      
      // Für ServiceLevelDataPoint -> 'datum' Feld
      if (row.datum !== undefined) {
        dateValue = typeof row.datum === 'string' ? row.datum : 
                   row.datum instanceof Date ? row.datum.toISOString().split('T')[0] : 
                   new Date().toISOString().split('T')[0];
      }
      // Für andere Charts -> 'date' Feld
      else if (row.date !== undefined) {
        dateValue = typeof row.date === 'string' ? row.date : 
                   row.date instanceof Date ? row.date.toISOString().split('T')[0] : 
                   new Date().toISOString().split('T')[0];
      }
      else {
        console.warn('Datumsfeld nicht gefunden in:', row);
        dateValue = new Date().toISOString().split('T')[0];
      }
      
      return {
        date: dateValue, // Standardisiere auf 'date'
        value: Number(row.value || row.wert || 0),
        label: String(row.label || row.beschreibung || 'N/A'),
        // Weitere feldspezifische Mappings...
        atrl: Number(row.atrl || 0),
        aril: Number(row.aril || 0),
        servicegrad: Number(row.servicegrad || 0)
      };
    });
  } catch (error) {
    console.error('Chart API response processing failed:', error);
    return []; // Fallback zu leerem Array
  }
};
```

**✅ Chart-spezifische Korrekturen (September 2025)**:
- **PickingChart**: `dbRow.datum` → `dbRow.date` korrigiert
- **TagesleistungChart**: `dbRow.datum` → `dbRow.date` korrigiert  
- **LieferpositionenChart**: `dbRow.datum` → `dbRow.date` korrigiert
- **ServiceGradChart**: verwendet weiterhin `dbRow.datum` (korrekt)

Diese Korrekturen beheben das Problem, dass Charts keine Daten anzeigten, weil sie nach dem falschen Datumsfeld suchten.

### Performance-Hotspots

#### 1. Large Dataset Rendering
**Bottleneck**: Recharts Performance bei > 1000 Datenpunkten
**Optimierung**: Data Sampling und Virtualization

```typescript path=null start=null
const optimizeDataForChart = (
  data: DataPoint[],
  maxPoints: number = 500
): DataPoint[] => {
  if (data.length <= maxPoints) return data;
  
  // Sampling-Algorithmus: Behalte jeden n-ten Punkt
  const step = Math.ceil(data.length / maxPoints);
  return data.filter((_, index) => index % step === 0);
};
```

#### 2. Real-time Updates
**Bottleneck**: Zu häufige API-Calls
**Optimierung**: Intelligent Polling mit Exponential Backoff

```typescript path=null start=null
const useIntelligentPolling = (
  fetchData: () => Promise<void>,
  baseInterval: number = 30000
) => {
  const [interval, setInterval] = useState(baseInterval);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    const poll = async () => {
      try {
        await fetchData();
        setIsError(false);
        setInterval(baseInterval); // Reset bei Erfolg
      } catch (error) {
        setIsError(true);
        // Exponential Backoff bei Fehlern
        setInterval(prev => Math.min(prev * 2, 300000)); // Max 5min
      }
    };

    const intervalId = setInterval(poll, interval);
    return () => clearInterval(intervalId);
  }, [fetchData, interval]);

  return { isError, interval };
};
```

### TODOs & Verbesserungspotentiale

#### Kurzfristig (Q1 2025)
- [ ] **WebSocket Integration**: Echte Real-time Updates statt Polling
- [ ] **Chart Animations**: Smooth Transitions bei Datenänderungen  
- [ ] **Export Funktionen**: PDF/Excel Export für alle Charts
- [ ] **Mobile Optimierung**: Touch-Gesten für Chart-Interaktionen

#### Mittelfristig (Q2 2025)
- [ ] **Advanced Filtering**: Multi-Dimension Filter (Abteilung, Schicht, etc.)
- [ ] **Drill-Down Views**: Detailansichten für jede Chart-Kategorie
- [ ] **Custom Dashboards**: Benutzer-konfigurierbare Dashboard-Layouts
- [ ] **Predictive Analytics**: Trend-Vorhersagen basierend auf historischen Daten

#### Langfristig (2025+)
- [ ] **AR/VR Integration**: 3D-Visualisierungen für Shopfloor
- [ ] **Machine Learning**: Anomalie-Erkennung in KPI-Daten
- [ ] **Voice Control**: Sprach-gesteuerte Dashboard-Navigation
- [ ] **Digital Twin**: Integration mit 3D-Fabrik-Modell

## 10. Troubleshooting

### Häufige Chart-Probleme

#### Problem: Charts zeigen keine Daten an
**Symptome**: 
- Leere Charts obwohl API 200 OK zurückgibt
- Console-Fehler: "Null/undefined Datum - überspringe Datensatz"
- ServiceGradChart funktioniert, andere Charts nicht

**Ursache**: Inkonsistente Datumsfeld-Namen zwischen Backend-APIs
- ServiceLevel API verwendet `datum` Feld
- Alle anderen APIs verwenden `date` Feld
- Frontend-Code greift auf falsches Feld zu

**Lösung**: Korrekte Feldnamen in Chart-Komponenten verwenden
```typescript path=null start=null
// ❌ Falsch - funktioniert nur für ServiceLevel
if (typeof dbRow.datum === 'string') {
  dateValue = dbRow.datum;
}

// ✅ Korrekt - für Picking, Tagesleistung, Lieferpositionen
if (typeof dbRow.date === 'string') {
  dateValue = dbRow.date;
}
```

#### Problem: Chart-Performance bei großen Datensätzen
**Symptome**: 
- Langsame Chart-Rendering (> 2s)
- Browser friert bei Datenänderungen ein
- Hohe CPU-Nutzung

**Lösung**: Data Sampling und Memoization
```typescript path=null start=null
// Data Sampling für große Datensätze
const optimizedData = useMemo(() => {
  if (rawData.length > 500) {
    const step = Math.ceil(rawData.length / 500);
    return rawData.filter((_, index) => index % step === 0);
  }
  return rawData;
}, [rawData]);

// Chart-Komponente memoizieren
export const Chart = memo(function Chart({ data }) {
  // Chart implementation
});
```

#### Problem: Datumsfilter funktioniert nicht
**Symptome**:
- Alle Daten werden angezeigt, unabhängig vom DateRange
- Fehler bei Datumsvergleichen
- Inkonsistente Filterresultate

**Lösung**: UTC-normalisierte Datumsvergleiche
```typescript path=null start=null
// ✅ Korrekte Datumsfilterung
const filterByDateRange = (data: DataPoint[], range: DateRange) => {
  if (!range.from || !range.to) return data;
  
  return data.filter(item => {
    const itemDate = new Date(item.date);
    // Normalisiere auf Mitternacht UTC
    const normalizedDate = startOfDay(itemDate);
    
    return isWithinInterval(normalizedDate, {
      start: startOfDay(range.from),
      end: endOfDay(range.to)
    });
  });
};
```

### API-Integration Troubleshooting

#### Problem: "Chart API response processing failed"
**Ursachen**:
- Backend ändert Response-Format
- Network-Timeouts
- CORS-Probleme

**Debug-Schritte**:
1. Browser DevTools → Network Tab prüfen
2. Response-Format validieren
3. API-Health-Check durchführen

```bash
# API-Health-Check
curl -X GET "http://localhost:3001/api/database/picking?startDate=2025-09-05&endDate=2025-09-06"
```

#### Problem: Inconsistent KPI calculations
**Symptome**:
- KPIs springen unerwartet
- Negative Werte in Prozent-KPIs
- NaN-Werte in Charts

**Lösung**: Defensive KPI-Berechnung
```typescript path=null start=null
const calculateKPI = (data: number[]): number => {
  // Validiere Input
  const validData = data.filter(val => 
    typeof val === 'number' && 
    !isNaN(val) && 
    isFinite(val)
  );
  
  if (validData.length === 0) return 0;
  
  const result = validData.reduce((sum, val) => sum + val, 0) / validData.length;
  
  // Sichere Ausgabe
  return Math.max(0, Math.round(result * 10) / 10);
};
```

### Performance-Optimierung

#### Memory Leaks verhindern
```typescript path=null start=null
// ✅ Cleanup von Event Listeners
useEffect(() => {
  const handleResize = debounce(() => {
    // Chart resize logic
  }, 300);
  
  window.addEventListener('resize', handleResize);
  
  return () => {
    window.removeEventListener('resize', handleResize);
    handleResize.cancel(); // Cleanup debounced function
  };
}, []);
```

#### Bundle-Size optimieren
```typescript path=null start=null
// ✅ Tree-shaking für Recharts
import { BarChart, Bar, XAxis, YAxis } from 'recharts';
// ❌ Vermeiden: import * as Recharts from 'recharts';

// ✅ Lazy Loading für große Charts
const HeavyChart = lazy(() => import('./HeavyChart'));

function Dashboard() {
  return (
    <Suspense fallback={<ChartSkeleton />}>
      <HeavyChart />
    </Suspense>
  );
}
```

### Coding Standards

#### Chart Component Pattern
```typescript path=null start=null
// Standard Chart-Komponente Struktur
interface ChartProps {
  data?: DataPoint[];
  dateRange?: DateRange;
  onDataPointClick?: (point: DataPoint) => void;
  loading?: boolean;
  error?: string;
}

export const StandardChart = memo<ChartProps>(function StandardChart({
  data: propData,
  dateRange, 
  onDataPointClick,
  loading = false,
  error
}) {
  // 1. State Management
  const [chartData, setChartData] = useState<ProcessedDataPoint[]>([]);
  
  // 2. Data Processing 
  const processedData = useMemo(() => {
    return processChartData(propData, dateRange);
  }, [propData, dateRange]);
  
  // 3. Event Handlers
  const handleBarClick = useCallback((data: any) => {
    onDataPointClick?.(data.payload);
  }, [onDataPointClick]);
  
  // 4. Effects
  useEffect(() => {
    if (processedData) {
      setChartData(processedData);
    }
  }, [processedData]);
  
  // 5. Loading/Error States
  if (loading) return <ChartSkeleton />;
  if (error) return <ChartError message={error} />;
  
  // 6. Chart Rendering
  return (
    <ChartErrorBoundary>
      <ChartContainer config={chartConfig}>
        <BarChart data={chartData} onClick={handleBarClick}>
          <Bar dataKey="value" fill="var(--chart-1)" />
        </BarChart>
      </ChartContainer>
    </ChartErrorBoundary>
  );
});
```

#### KPI Calculation Pattern
```typescript path=null start=null
// Standard KPI-Berechnung mit Validierung
export const createKPICalculator = <T, R>(
  name: string,
  calculation: (data: T[]) => R,
  validator: (data: T[]) => boolean = (data) => data.length > 0
) => {
  return (data: T[], fallbackValue: R): R => {
    try {
      if (!validator(data)) {
        console.warn(`KPI ${name}: Invalid data, using fallback`);
        return fallbackValue;
      }
      
      const result = calculation(data);
      console.debug(`KPI ${name}: Calculated`, result);
      return result;
    } catch (error) {
      console.error(`KPI ${name}: Calculation failed`, error);
      return fallbackValue;
    }
  };
};

// Verwendung
const calculateServiceLevel = createKPICalculator(
  'ServiceLevel',
  (data: ServiceLevelData[]) => {
    const average = data.reduce((sum, item) => 
      sum + item.servicegrad, 0
    ) / data.length;
    return Math.round(average * 100 * 10) / 10;
  }
);
```

---

## Wartung und Support

**Letzte Aktualisierung**: Januar 2025  
**Maintainer**: Lapp Development Team  
**Version**: 1.0.0  

**Hinweis**: Diese README beschreibt die aktuelle Implementierung des Repository-Systems. Bei Änderungen oder Erweiterungen sollte diese Dokumentation entsprechend aktualisiert werden.
