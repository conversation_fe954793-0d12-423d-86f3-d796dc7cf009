/**
 * Unified Error Handling Service
 * 
 * Standardisiert Error-Handling, Logging und Response-Formate
 * für alle Repository und Service-Klassen.
 */

// TODO: Update for Drizzle ORM error types
// Drizzle ORM Error-Handling - nutzt Standard-SQLite-Errors

/**
 * Standard-Error-Typen für die Anwendung
 */
export enum ErrorType {
  DATABASE_ERROR = 'DATABASE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  CACHE_ERROR = 'CACHE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  BUSINESS_LOGIC_ERROR = 'BUSINESS_LOGIC_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * Standard-Error-Severity für Logging und Monitoring
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * Standardisiertes Error-Interface
 */
export interface StandardError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage: string;
  code: string;
  details?: Record<string, any>;
  originalError?: Error;
  context?: {
    service?: string;
    method?: string;
    userId?: string;
    requestId?: string;
    timestamp: number;
  };
  stack?: string;
  suggestions?: string[];
}

/**
 * Error-Handler-Konfiguration
 */
interface ErrorHandlerConfig {
  enableDetailedLogging: boolean;
  enableStackTraces: boolean;
  enableUserSuggestions: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  maxErrorHistorySize: number;
}

/**
 * Error-Statistiken
 */
interface ErrorStats {
  totalErrors: number;
  errorsByType: Record<ErrorType, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  recentErrors: StandardError[];
  errorRate: number; // Errors per minute
  lastErrorTime: number;
}

/**
 * Unified Error Handler Service
 */
export class ErrorHandlerService {
  private static instance: ErrorHandlerService;
  private config: ErrorHandlerConfig;
  private errorHistory: StandardError[] = [];
  private stats: ErrorStats = {
    totalErrors: 0,
    errorsByType: {} as Record<ErrorType, number>,
    errorsBySeverity: {} as Record<ErrorSeverity, number>,
    recentErrors: [],
    errorRate: 0,
    lastErrorTime: 0
  };

  private constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enableDetailedLogging: process.env.NODE_ENV === 'development',
      enableStackTraces: process.env.NODE_ENV === 'development',
      enableUserSuggestions: true,
      logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'error',
      maxErrorHistorySize: 100,
      ...config
    };

    // Initialisiere Error-Statistiken
    this.initializeStats();
  }

  static getInstance(config?: Partial<ErrorHandlerConfig>): ErrorHandlerService {
    if (!ErrorHandlerService.instance) {
      ErrorHandlerService.instance = new ErrorHandlerService(config);
    }
    return ErrorHandlerService.instance;
  }

  /**
   * Hauptmethode zum Behandeln von Fehlern
   */
  handleError(
    error: Error | any,
    context?: {
      service?: string;
      method?: string;
      userId?: string;
      requestId?: string;
    }
  ): StandardError {
    const standardError = this.standardizeError(error, context);
    
    // Error in History und Stats speichern
    this.recordError(standardError);
    
    // Error loggen
    this.logError(standardError);
    
    // Performance Monitoring benachrichtigen falls verfügbar
    this.notifyPerformanceMonitor(standardError);
    
    return standardError;
  }

  /**
   * Error zu StandardError umwandeln
   */
  private standardizeError(
    error: Error | any,
    context?: {
      service?: string;
      method?: string;
      userId?: string;
      requestId?: string;
    }
  ): StandardError {
    const errorId = this.generateErrorId();
    const timestamp = Date.now();
    
    // Error-Typ und Details bestimmen
    const { type, severity, code, userMessage, details, suggestions } = this.categorizeError(error);
    
    const standardError: StandardError = {
      id: errorId,
      type,
      severity,
      message: error?.message || 'Unbekannter Fehler',
      userMessage,
      code,
      details,
      originalError: error instanceof Error ? error : undefined,
      context: {
        ...context,
        timestamp
      },
      stack: this.config.enableStackTraces ? error?.stack : undefined,
      suggestions: this.config.enableUserSuggestions ? suggestions : undefined
    };

    return standardError;
  }

  /**
   * Error kategorisieren und Details extrahieren
   */
  private categorizeError(error: any): {
    type: ErrorType;
    severity: ErrorSeverity;
    code: string;
    userMessage: string;
    details?: Record<string, any>;
    suggestions?: string[];
  } {
    // TODO: Update for Drizzle ORM error types
    // Drizzle ORM nutzt Standard-SQLite-Errors - keine spezielle ORM-Behandlung erforderlich
    // }

    // Standard-Fehler-Kategorisierung
    const errorMessage = (error?.message || '').toLowerCase();
    
    // Timeout-Fehler
    if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
      return {
        type: ErrorType.TIMEOUT_ERROR,
        severity: ErrorSeverity.MEDIUM,
        code: 'TIMEOUT',
        userMessage: 'Die Operation hat zu lange gedauert. Bitte versuchen Sie es erneut.',
        suggestions: ['Überprüfen Sie Ihre Internetverbindung', 'Versuchen Sie es in wenigen Minuten erneut']
      };
    }

    // Netzwerk-Fehler
    if (errorMessage.includes('network') || errorMessage.includes('connection') || errorMessage.includes('fetch')) {
      return {
        type: ErrorType.NETWORK_ERROR,
        severity: ErrorSeverity.HIGH,
        code: 'NETWORK_ERROR',
        userMessage: 'Verbindungsfehler aufgetreten. Bitte überprüfen Sie Ihre Internetverbindung.',
        suggestions: ['Internetverbindung prüfen', 'Server-Status überprüfen']
      };
    }

    // Validierungs-Fehler
    if (errorMessage.includes('validation') || errorMessage.includes('invalid') || errorMessage.includes('required')) {
      return {
        type: ErrorType.VALIDATION_ERROR,
        severity: ErrorSeverity.LOW,
        code: 'VALIDATION_ERROR',
        userMessage: 'Ungültige Eingabedaten. Bitte überprüfen Sie Ihre Eingaben.',
        suggestions: ['Eingabedaten überprüfen', 'Pflichtfelder ausfüllen']
      };
    }

    // Not Found-Fehler
    if (errorMessage.includes('not found') || errorMessage.includes('404')) {
      return {
        type: ErrorType.NOT_FOUND_ERROR,
        severity: ErrorSeverity.LOW,
        code: 'NOT_FOUND',
        userMessage: 'Die angeforderten Daten wurden nicht gefunden.',
        suggestions: ['Überprüfen Sie die angeforderten Parameter', 'Aktualisieren Sie die Seite']
      };
    }

    // Cache-Fehler
    if (errorMessage.includes('cache') || errorMessage.includes('redis') || errorMessage.includes('memory')) {
      return {
        type: ErrorType.CACHE_ERROR,
        severity: ErrorSeverity.MEDIUM,
        code: 'CACHE_ERROR',
        userMessage: 'Cache-Fehler aufgetreten. Die Daten werden direkt aus der Datenbank geladen.',
        suggestions: ['Cache-Service neu starten', 'Memory-Verbrauch prüfen']
      };
    }

    // Permission-Fehler
    if (errorMessage.includes('permission') || errorMessage.includes('unauthorized') || errorMessage.includes('forbidden')) {
      return {
        type: ErrorType.PERMISSION_ERROR,
        severity: ErrorSeverity.HIGH,
        code: 'PERMISSION_DENIED',
        userMessage: 'Sie haben nicht die erforderlichen Berechtigungen für diese Operation.',
        suggestions: ['Anmeldedaten überprüfen', 'Administrator kontaktieren']
      };
    }

    // Standard-Unbekannter-Fehler
    return {
      type: ErrorType.UNKNOWN_ERROR,
      severity: ErrorSeverity.MEDIUM,
      code: 'UNKNOWN_ERROR',
      userMessage: 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.',
      details: { originalMessage: error?.message },
      suggestions: ['Seite neu laden', 'In wenigen Minuten erneut versuchen']
    };
  }

  /**
   * TODO: Update for Drizzle ORM error types
   * ORM-spezifische Fehler kategorisieren (Legacy-Methode)
   */
  /*
  private categorizeDatabaseError(error: Error): {
    type: ErrorType;
    severity: ErrorSeverity;
    code: string;
    userMessage: string;
    details?: Record<string, any>;
    suggestions?: string[];
  } {
    const code = error.code;
    const meta = error.meta;

    switch (code) {
      case 'P2002': // Unique constraint violation
        return {
          type: ErrorType.VALIDATION_ERROR,
          severity: ErrorSeverity.LOW,
          code: 'DUPLICATE_ENTRY',
          userMessage: 'Ein Eintrag mit diesen Daten existiert bereits.',
          details: { fields: meta?.target, constraint: meta?.constraint },
          suggestions: ['Eindeutige Werte verwenden', 'Bestehenden Eintrag aktualisieren']
        };

      case 'P2025': // Record not found
        return {
          type: ErrorType.NOT_FOUND_ERROR,
          severity: ErrorSeverity.LOW,
          code: 'RECORD_NOT_FOUND',
          userMessage: 'Der gesuchte Datensatz wurde nicht gefunden.',
          details: { cause: meta?.cause },
          suggestions: ['ID überprüfen', 'Datensatz-Existenz prüfen']
        };

      case 'P2003': // Foreign key constraint violation
        return {
          type: ErrorType.VALIDATION_ERROR,
          severity: ErrorSeverity.MEDIUM,
          code: 'FOREIGN_KEY_VIOLATION',
          userMessage: 'Die Operation verletzt referentielle Integrität.',
          details: { field: meta?.field_name },
          suggestions: ['Abhängigkeiten prüfen', 'Referenzierte Datensätze erstellen']
        };

      case 'P2021': // Table does not exist
        return {
          type: ErrorType.CONFIGURATION_ERROR,
          severity: ErrorSeverity.CRITICAL,
          code: 'TABLE_NOT_EXISTS',
          userMessage: 'Datenbankstruktur ist nicht korrekt konfiguriert.',
          details: { table: meta?.table },
          suggestions: ['Datenbank-Migration ausführen', 'Administrator kontaktieren']
        };

      case 'P2024': // Timeout
        return {
          type: ErrorType.TIMEOUT_ERROR,
          severity: ErrorSeverity.HIGH,
          code: 'DATABASE_TIMEOUT',
          userMessage: 'Datenbank-Operation hat zu lange gedauert.',
          details: { timeout: meta?.timeout },
          suggestions: ['Query-Performance optimieren', 'Datenbank-Load prüfen']
        };

      default:
        return {
          type: ErrorType.DATABASE_ERROR,
          severity: ErrorSeverity.HIGH,
          code: `ORM_${code}`,
          userMessage: 'Datenbankfehler aufgetreten. Bitte versuchen Sie es erneut.',
          details: { ormCode: code, meta },
          suggestions: ['Erneut versuchen', 'Administrator benachrichtigen']
        };
    }
  }
  */

  /**
   * Error in History und Stats erfassen
   */
  private recordError(error: StandardError): void {
    // Error zu History hinzufügen
    this.errorHistory.push(error);
    
    // History-Größe begrenzen
    if (this.errorHistory.length > this.config.maxErrorHistorySize) {
      this.errorHistory = this.errorHistory.slice(-this.config.maxErrorHistorySize);
    }

    // Statistiken aktualisieren
    this.stats.totalErrors++;
    this.stats.errorsByType[error.type] = (this.stats.errorsByType[error.type] || 0) + 1;
    this.stats.errorsBySeverity[error.severity] = (this.stats.errorsBySeverity[error.severity] || 0) + 1;
    this.stats.lastErrorTime = error.context!.timestamp;

    // Recent errors aktualisieren (letzte 10)
    this.stats.recentErrors = this.errorHistory.slice(-10);

    // Error-Rate berechnen (Errors pro Minute in den letzten 5 Minuten)
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    const recentErrorCount = this.errorHistory.filter(e => e.context!.timestamp >= fiveMinutesAgo).length;
    this.stats.errorRate = recentErrorCount / 5; // Errors per minute
  }

  /**
   * Error loggen
   */
  private logError(error: StandardError): void {
    const logLevel = this.getLogLevel(error.severity);
    const logMessage = this.formatLogMessage(error);

    switch (logLevel) {
      case 'debug':
        console.debug(logMessage);
        break;
      case 'info':
        console.info(logMessage);
        break;
      case 'warn':
        console.warn(logMessage);
        break;
      case 'error':
        console.error(logMessage);
        break;
    }

    // Detailliertes Logging in Development
    if (this.config.enableDetailedLogging) {
      console.log('[ERROR-DETAILS]', {
        id: error.id,
        type: error.type,
        context: error.context,
        details: error.details,
        stack: error.stack
      });
    }
  }

  /**
   * Performance Monitor benachrichtigen
   */
  private notifyPerformanceMonitor(error: StandardError): void {
    try {
      // Lazy-load um zirkuläre Abhängigkeiten zu vermeiden
      const { performanceMonitor } = require('./performance-monitor.service');
      performanceMonitor.recordQueryError(error.id, error.type);
    } catch (err) {
      // Ignore performance monitoring errors
      if (this.config.enableDetailedLogging) {
        console.debug('[ERROR-HANDLER] Performance monitor notification failed:', err);
      }
    }
  }

  /**
   * Error-Statistiken abrufen
   */
  getErrorStats(): ErrorStats {
    return { ...this.stats };
  }

  /**
   * Error-History abrufen
   */
  getErrorHistory(limit?: number): StandardError[] {
    return limit ? this.errorHistory.slice(-limit) : [...this.errorHistory];
  }

  /**
   * Repository-freundliche Error-Handler-Methoden
   */

  /**
   * Database-Error-Handler
   */
  handleDatabaseError(error: any, context?: { service?: string; method?: string }): StandardError {
    return this.handleError(error, context);
  }

  /**
   * Cache-Error-Handler
   */
  handleCacheError(error: any, context?: { service?: string; method?: string }): StandardError {
    return this.handleError(error, context);
  }

  /**
   * Validation-Error-Handler
   */
  handleValidationError(message: string, details?: Record<string, any>, context?: { service?: string; method?: string }): StandardError {
    const error = new Error(message);
    const standardError = this.handleError(error, context);
    standardError.type = ErrorType.VALIDATION_ERROR;
    standardError.details = details;
    return standardError;
  }

  /**
   * Not-Found-Error-Handler
   */
  handleNotFoundError(resource: string, id?: string | number, context?: { service?: string; method?: string }): StandardError {
    const message = `${resource} ${id ? `mit ID ${id}` : ''} nicht gefunden`;
    const error = new Error(message);
    const standardError = this.handleError(error, context);
    standardError.type = ErrorType.NOT_FOUND_ERROR;
    standardError.details = { resource, id };
    return standardError;
  }

  /**
   * Helper-Methoden
   */
  
  private initializeStats(): void {
    // Initialisiere Error-Counters
    Object.values(ErrorType).forEach(type => {
      this.stats.errorsByType[type] = 0;
    });
    
    Object.values(ErrorSeverity).forEach(severity => {
      this.stats.errorsBySeverity[severity] = 0;
    });
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getLogLevel(severity: ErrorSeverity): 'debug' | 'info' | 'warn' | 'error' {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 'info';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return 'error';
      default:
        return 'error';
    }
  }

  private formatLogMessage(error: StandardError): string {
    return [
      `[${error.severity.toUpperCase()}]`,
      `[${error.type}]`,
      `[${error.code}]`,
      `${error.message}`,
      error.context?.service && `Service: ${error.context.service}`,
      error.context?.method && `Method: ${error.context.method}`,
      error.context?.requestId && `RequestID: ${error.context.requestId}`
    ].filter(Boolean).join(' | ');
  }

  /**
   * Service beenden und Cleanup
   */
  destroy(): void {
    this.errorHistory = [];
    this.initializeStats();
  }
}

/**
 * Error-Handler Decorator für Repository-Methoden
 */
export function withErrorHandling(service: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      try {
        return await method.apply(this, args);
      } catch (error) {
        const errorHandler = ErrorHandlerService.getInstance();
        const standardError = errorHandler.handleError(error, {
          service,
          method: propertyName
        });
        
        throw standardError;
      }
    };

    return descriptor;
  };
}

/**
 * Typen für API-Response-Error-Handling
 */
export interface ApiErrorResponse {
  success: false;
  error: string;
  code: string;
  details?: Record<string, any>;
  suggestions?: string[];
  errorId?: string;
}

/**
 * Konvertiert StandardError zu API-Response-Format
 */
export function toApiErrorResponse(error: StandardError): ApiErrorResponse {
  return {
    success: false,
    error: error.userMessage,
    code: error.code,
    details: error.details,
    suggestions: error.suggestions,
    errorId: error.id
  };
}

// Singleton instance
export const errorHandler = ErrorHandlerService.getInstance();