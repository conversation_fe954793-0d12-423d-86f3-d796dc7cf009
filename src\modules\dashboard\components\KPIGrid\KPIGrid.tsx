import React from 'react';
import { cn } from '@/lib/utils';
import { useDragDropContext } from '@/modules/dashboard/components/DragDrop/DragDropProvider';
import { DraggableCard } from '@/modules/dashboard/components/DragDrop/DraggableCard';
import { DroppableContainer } from '@/modules/dashboard/components/DragDrop/DroppableContainer';
import {
  WareneingangCard,
  TonnageCard,
  PerformanceCard,
  EfficiencyCard,
  QualityCard,
  DeliveryCard,
  PickingCard,
  AutomatisierungCard,
  ProduktionCard,
  LagerCard,
  VersandCard,
  ServiceCard,
  type DepartmentKPIs
} from '@/modules/dashboard/components/stats';
import dashboardKommiImage from '@/assets/DashboardKommi.png';
import dashboardSG5Image from '@/assets/DashboardSG5.png';
import dashboardTonnageImage from '@/assets/DashboardTon.png';
import dashboardQMImage from '@/assets/DashboardQM.png';

/**
 * Props für die KPIGrid-Komponente
 */
interface KPIGridProps {
  /** KPI-Daten für alle Abteilungen */
  departmentKPIs: DepartmentKPIs | null;
  /** Ob der Edit-Modus aktiv ist */
  isEditMode: boolean;
}

/**
 * KPIGrid-Komponente für die Darstellung der Drag-and-Drop KPI-Karten
 * 
 * Diese Komponente wurde aus der DashboardPage extrahiert, um die Wartbarkeit
 * zu verbessern und die Verantwortlichkeiten zu trennen.
 * 
 * Features:
 * - Drag-and-Drop-Funktionalität für KPI-Karten
 * - Edit-Modus mit Entfernen-Buttons
 * - Responsive Grid-Layout (1/2/4 Spalten)
 * - Spezielle Hintergrundbilder für bestimmte Karten
 * - Automatische Sortierung basierend auf Positionen
 */
export const KPIGrid: React.FC<KPIGridProps> = ({
  departmentKPIs,
  isEditMode
}) => {
  // Drag-and-Drop-Kontext verwenden
  const { cardPositions, removeCard } = useDragDropContext();

  /**
   * Funktion zum Rendern einer KPI-Karte basierend auf der ID
   * 
   * Diese Funktion wurde refaktorisiert, um separate Komponenten zu verwenden
   * anstatt inline JSX. Dies verbessert die Wartbarkeit und Wiederverwendbarkeit.
   */
  const renderKPICard = (cardId: string, departmentKPIs: DepartmentKPIs | null) => {
    // Gemeinsame Props für alle KPI-Karten
    const cardProps = { departmentKPIs };

    switch (cardId) {
      case 'wareneingang':
        return <WareneingangCard {...cardProps} />;
      case 'tonnage':
        return <TonnageCard {...cardProps} />;
      case 'performance':
        return <PerformanceCard {...cardProps} />;
      case 'efficiency':
        return <EfficiencyCard {...cardProps} />;
      case 'quality':
        return <QualityCard {...cardProps} />;
      case 'delivery':
        return <DeliveryCard {...cardProps} />;
      case 'picking':
        return <PickingCard {...cardProps} />;
      case 'automatisierung':
        return <AutomatisierungCard {...cardProps} />;
      case 'produktion':
        return <ProduktionCard {...cardProps} />;
      case 'lager':
        return <LagerCard {...cardProps} />;
      case 'versand':
        return <VersandCard {...cardProps} />;
      case 'service':
        return <ServiceCard {...cardProps} />;
      default:
        return null;
    }
  };

  // Sortiere die Karten-IDs basierend auf ihren Positionen
  const sortedCardIds = cardPositions
    .sort((a, b) => a.position - b.position)
    .map((pos) => pos.id);

  /**
   * Bestimmt die CSS-Klassen für eine KPI-Karte basierend auf ihrer ID
   */
  const getCardClassName = (cardId: string): string => {
    return cn(
      "rounded-lg shadow-md p-6 border border-slate-800 relative min-h-[200px]",
      cardId === 'picking' ? "bg-[length:225px] bg-right bg-no-repeat bg-green-100" : "bg-white",
      cardId === 'performance' ? "bg-[length:175px] bg-right bg-no-repeat bg-green-100" : "",
      cardId === 'tonnage' ? "bg-[length:200px] bg-right bg-no-repeat bg-green-100" : "",
      cardId === 'quality' ? "bg-[length:225px] bg-right bg-no-repeat bg-red-100" : ""
    );
  };

  /**
   * Bestimmt das Hintergrundbild für eine KPI-Karte basierend auf ihrer ID
   */
  const getCardStyle = (cardId: string): React.CSSProperties | undefined => {
    if (cardId === 'picking') {
      return { backgroundImage: `url(${dashboardKommiImage})` };
    }
    if (cardId === 'performance') {
      return { backgroundImage: `url(${dashboardSG5Image})` };
    }
    if (cardId === 'tonnage') {
      return { backgroundImage: `url(${dashboardTonnageImage})` };
    }
    if (cardId === 'quality') {
      return { backgroundImage: `url(${dashboardQMImage})` };
    }
    return undefined;
  };

  /**
   * Prüft, ob eine Karte ein Overlay benötigt (für bessere Lesbarkeit)
   */
  const needsOverlay = (cardId: string): boolean => {
    return cardId === 'picking' || cardId === 'performance' || cardId === 'tonnage' || cardId === 'quality';
  };

  /**
   * Rendert den Entfernen-Button für eine Karte (nur im Edit-Modus)
   */
  const renderRemoveButton = (cardId: string) => {
    if (!isEditMode) return null;

    return (
      <button
        onClick={(e) => {
          e.stopPropagation();
          removeCard(cardId);
        }}
        className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity bg-red-500 text-white rounded-full p-1"
        aria-label={`${cardId} Karte entfernen`}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>
    );
  };

  /**
   * Rendert eine einzelne KPI-Karte mit allen notwendigen Wrappern
   */
  const renderCard = (cardId: string, index: number) => {
    return (
      <DraggableCard 
        id={cardId} 
        className={getCardClassName(cardId)}
        style={getCardStyle(cardId)}
      >
        {/* Overlay für bessere Lesbarkeit bei Hintergrundbildern */}
        {needsOverlay(cardId) && (
          <div className="absolute inset-0 bg-white/70 rounded-lg"></div>
        )}
        
        {/* Entfernen-Button (nur im Edit-Modus) */}
        {renderRemoveButton(cardId)}
        
        {/* KPI-Karten-Inhalt */}
        <div className={needsOverlay(cardId) ? 'relative z-10' : ''}>
          {renderKPICard(cardId, departmentKPIs)}
        </div>
      </DraggableCard>
    );
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      {Array.from({ length: 12 }).map((_, index) => {
        const cardId = sortedCardIds[index];
        
        // Im Edit-Modus alle Positionen anzeigen, auch die leeren
        if (isEditMode) {
          return (
            <DroppableContainer
              key={index}
              id={`position-${index}`}
              className="relative group min-h-[180px]"
              isEmpty={!cardId}
            >
              {cardId ? renderCard(cardId, index) : null}
            </DroppableContainer>
          );
        }
        
        // Normaler Modus: Nur belegte Positionen anzeigen
        return cardId ? (
          <DroppableContainer 
            key={index} 
            id={cardId} 
            className="relative group"
          >
            {renderCard(cardId, index)}
          </DroppableContainer>
        ) : null;
      })}
    </div>
  );
};

export default KPIGrid;