# Backend - SFM Leitstand Anwendung

![Node.js](https://img.shields.io/badge/Node.js-20.x-brightgreen?style=flat-square&logo=node.js)
![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue?style=flat-square&logo=typescript)
![Express.js](https://img.shields.io/badge/Express.js-4.x-lightgrey?style=flat-square&logo=express)
![Drizzle ORM](https://img.shields.io/badge/Drizzle-ORM-orange?style=flat-square)
![SQLite](https://img.shields.io/badge/SQLite-Database-blue?style=flat-square&logo=sqlite)

## 1. Übersicht & Zweck

### 🎯 Hauptfunktion
Das Backend bildet das **zentrale API-Layer** der SFM (Stifte Federn Metall) Leitstand-Anwendung. Es implementiert eine **umfassende REST-API** für die Verwaltung und Überwachung industrieller Produktionsprozesse in einer Fertigungsumgebung.

### 🏭 Geschäftslogik & Anwendungsbereich
- **Störungsmanagement**: Vollständiges CRUD-System für Incident-Management mit ITIL-konformem Workflow
- **Performance-Monitoring**: Real-time Überwachung von Produktionsleistung, Servicegrad und Maschineneffizienz  
- **Lager- & Logistiksteuerung**: Intelligente Verwaltung von Warenbeständen (200er/240er Lager, ARiL/ATrL)
- **AI-Integration**: OpenRouter-basierte Chatbot-Funktionalität mit kontextbewusster Datenanalyse
- **Bereitschaftsplanung**: Automatisierte Dienstplanung mit Ausnahme- und Urlaubsmanagement

### 🏛️ Architekturmuster
Das Backend folgt einer **modernen Hexagonal Architecture** (Clean Architecture) mit klarer Trennung der Verantwortlichkeiten:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Layer     │    │  Business Layer │    │ Data Access     │
│                 │    │                 │    │                 │
│ • Routes        │───▶│ • Services      │───▶│ • Repositories  │
│ • Middleware    │    │ • AI Logic      │    │ • Cache Layer   │
│ • Validation    │    │ • Workflows     │    │ • Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Rolle im Gesamtsystem:**
- **API Gateway**: Zentrale Schnittstelle zwischen Frontend (Electron) und Datenschicht
- **Service Layer**: Implementierung komplexer Geschäftsregeln und Datenverarbeitungslogik
- **Integration Hub**: Verbindung zu externen Systemen (SAP, OpenRouter AI, E-Mail-Services)

---

## 2. Dateistruktur-Analyse

### 🌳 Hierarchische Projektstruktur

```
backend/
├── 📁 config/                    # Workflow-Konfigurationen
│   └── workflows/                # Automatisierte SAP-Integration
├── 📁 database/                  # Datenbankdateien & Migrationen
│   └── sfm_dashboard.db         # SQLite-Produktionsdatenbank
├── 📁 dist/                     # Kompilierte TypeScript-Ausgabe
│   ├── 📁 controllers/          # Request-Handler für HTTP-Endpunkte
│   ├── 📁 middleware/           # Express.js-Middleware-Stack
│   ├── 📁 repositories/         # Repository-Pattern Implementation
│   ├── 📁 routes/               # REST-API Routing-Definitionen
│   ├── 📁 services/             # Kerngeschäftslogik & externe Integrations
│   ├── 📁 types/                # TypeScript-Typdefinitionen
│   ├── 📁 tests/                # Umfassende Test-Suite
│   └── server.js                # Haupt-Express-Server
├── 📄 package.json              # NPM-Dependencies & Scripts
├── 📄 .env.example              # Umgebungsvariablen-Template
└── 📄 CLAUDE.md                 # Entwicklerdokumentation
```

### 🔧 Logische Gruppierung der Komponenten

| **Gruppe** | **Ordner** | **Zweck** |
|------------|------------|-----------|
| **🌐 Präsentationsschicht** | `routes/`, `controllers/`, `middleware/` | HTTP-Anfragen verarbeiten, Authentifizierung, Validierung |
| **⚙️ Anwendungsschicht** | `services/`, `workflows/` | Geschäftslogik, AI-Integration, externe Service-Calls |
| **💾 Datenschicht** | `repositories/`, `db/`, `database/` | Datenbank-Abstraktion, Caching, Schema-Management |
| **🔧 Infrastruktur** | `types/`, `utils/`, `scripts/` | Hilfsfunktionen, Typisierung, Automatisierung |
| **🧪 Qualitätssicherung** | `tests/` | Unit-, Integration- und E2E-Tests |

---

## 3. Detaillierte Dateibeschreibungen

### 🌐 Controllers Layer

| **Controller** | **Verantwortlichkeit** | **Hauptfunktionen** | **Design Pattern** |
|----------------|------------------------|---------------------|-------------------|
| `bereitschaftsController.js` | Bereitschaftsplanung verwalten | `createPerson()`, `updateSchedule()`, `getExceptions()` | Command Pattern |
| `stoerungen.controller.js` | Störungsmanagement orchestrieren | `createIncident()`, `updateStatus()`, `getStatistics()` | Repository Pattern |
| `ragController.js` | RAG-basierte AI-Suche | `searchKnowledge()`, `indexDocument()`, `getEmbeddings()` | Strategy Pattern |
| `warehouse.controller.js` | Lagerbestand-APIs bereitstellen | `getInventory()`, `trackMovements()`, `calculateUtilization()` | Observer Pattern |

### ⚙️ Services Layer (Geschäftslogik)

| **Service** | **SRP-Verantwortung** | **Externe Dependencies** | **Performance-Charakteristika** |
|-------------|----------------------|---------------------------|--------------------------------|
| `database.service.js` | Zentrale Datenbank-Operationen | Drizzle ORM, SQLite | **Cache-optimiert**: 40-70% Leistungssteigerung |
| `cache.service.js` | In-Memory-Caching (LRU) | Keine | **Memory-Management**: 100MB Limit, 2000 Entries |
| `ai.service.js` | Anomalie-Erkennung & Insights | OpenRouter API | **ML-Pipeline**: Threshold-basierte Algorithmen |
| `openrouter.service.js` | AI-Chatbot Integration | OpenRouter, OpenAI | **Rate-Limited**: 30 req/15min Produktion |
| `email.service.js` | Benachrichtigungssystem | Nodemailer, SMTP | **Template-Engine**: Handlebars-basiert |

### 💾 Repository Layer (Datenzugriff)

```typescript
interface Repository<T> {
  findById(id: string): Promise<T | null>
  save(entity: T): Promise<T>
  delete(id: string): Promise<void>
  getStats(): Promise<RepositoryStats>
  invalidateCache(): Promise<void>
}
```

| **Repository** | **Datenquelle** | **Cache-TTL** | **Besonderheiten** |
|----------------|-----------------|---------------|-------------------|
| `DispatchRepository` | `dispatch_data` Tabelle | 2 Minuten | Service-Level-Agreement Tracking |
| `WarehouseRepository` | `bestand200`, `bestand240` | 5 Minuten | Multi-Lager Auslastungsberechnung |
| `CuttingRepository` | `schnitte`, `maschinen` | 15 Minuten | Maschineneffizienz-Algorithmen |
| `StoerungenRepository` | `Stoerungen`, `SystemStatus` | 5-15 Minuten | MTTR/MTBF-Berechnung |

### 🛡️ Middleware Stack

| **Middleware** | **Zweck** | **Konfiguration** | **Security-Level** |
|----------------|-----------|-------------------|------------------|
| `auth.middleware.js` | API-Schlüssel Validierung | Bearer Token + X-API-Key | **Hoch** |
| `rate-limiting.middleware.js` | DoS-Schutz | 30-500 req/15min (umgebungsabhängig) | **Kritisch** |
| `validation.middleware.js` | Input-Sanitization | Zod-Schema Validierung | **Hoch** |
| `performance-tracking.middleware.js` | Request-Monitoring | Response-Time + Memory | **Medium** |
| `ai-security.middleware.js` | AI-Request Filtering | Prompt-Injection-Schutz | **Hoch** |

---

## 4. Abhängigkeiten & Verbindungen

### 📦 Externe NPM-Pakete (Produktionsabhängigkeiten)

| **Kategorie** | **Pakete** | **Versionen** | **Health-Score** | **Zweck** |
|---------------|------------|---------------|-----------------|-----------|
| **🌐 Web Framework** | `express@4.18.2`, `cors@2.8.5` | Latest Stable | 98% | HTTP-Server & CORS |
| **💾 Datenbank** | `drizzle-orm@0.44.5`, `@libsql/client@0.15.14` | Latest | 95% | ORM & SQLite-Client |
| **🔐 Sicherheit** | `helmet@8.1.0`, `bcryptjs@3.0.2`, `jsonwebtoken@9.0.2` | Current | 97% | Security Headers & Auth |
| **🤖 AI-Integration** | `openai@4.43.0`, `axios@1.10.0` | Latest | 94% | OpenRouter & HTTP-Client |
| **✉️ Kommunikation** | `nodemailer@7.0.5`, `handlebars@4.7.8` | Stable | 92% | E-Mail & Templates |
| **⚡ Performance** | `express-rate-limit@6.10.0`, `uuid@11.0.4` | Latest | 96% | Rate Limiting & IDs |

### 🔗 Interne Abhängigkeiten (Schichtenmodell)

```
┌──────────────────────────────────────────┐
│                Routes                    │ ←─ HTTP-Endpunkte
├──────────────────────────────────────────┤
│              Controllers                 │ ←─ Request-Orchestrierung
├──────────────────────────────────────────┤
│               Services                   │ ←─ Geschäftslogik
├──────────────────────────────────────────┤
│             Repositories                 │ ←─ Daten-Abstraktion
├──────────────────────────────────────────┤
│          Database + Cache                │ ←─ Persistierung
└──────────────────────────────────────────┘
```

**Abhängigkeitsregeln:**
- ✅ **Erlaubt**: Routes → Controllers → Services → Repositories
- ❌ **Verboten**: Repositories → Services, Controllers → Database direkt
- 🔄 **Zirkular**: Automatische Erkennung durch TypeScript Compiler

### 🌉 Frontend-Integration

| **Schnittstelle** | **Protokoll** | **Port** | **Authentifizierung** |
|-------------------|---------------|----------|----------------------|
| **REST API** | HTTP/HTTPS | 3001 | API-Key (Bearer Token) |
| **WebSocket** | WSS | 3001 | Token-basiert |
| **Health Check** | HTTP | 3001/api/health | Öffentlich |

### 🔌 Externe Service-Integration

```json
{
  "sap_integration": {
    "protocol": "COM",
    "executable": "sapshcut.exe",
    "system": "PS4",
    "client": "009"
  },
  "openrouter_ai": {
    "base_url": "https://openrouter.ai/api/v1",
    "rate_limit": "30_requests_per_15_minutes",
    "models": ["@preset/lapp"]
  },
  "email_smtp": {
    "provider": "configurable",
    "tls": true,
    "auth_method": "oauth2_or_basic"
  }
}
```

---

## 5. Technische Details

### 🚀 Laufzeitumgebung

| **Komponente** | **Version/Spezifikation** | **Begründung** |
|----------------|---------------------------|----------------|
| **Node.js** | ≥14.0.0 (Empfohlen: 20.x LTS) | ES2022 Features, Performance |
| **TypeScript** | 5.9.2 | Moderne Type-Features, bessere IDE-Unterstützung |
| **Express.js** | 4.18.2 | Stabile API, umfangreiches Middleware-Ökosystem |
| **SQLite** | Latest via libsql | Eingebettete DB, Zero-Config, ACID-Transaktionen |

### 🛠️ Build & Deployment

```bash
# Entwicklung starten
pnpm install              # Dependencies installieren (nur pnpm!)
pnpm run dev             # Hot-reload Development Server (Port 3001)

# Produktion
pnpm run build           # TypeScript → JavaScript kompilieren
pnpm run serve           # Kompilierte Version starten

# Datenbank-Management
pnpm run rag:init        # RAG-Wissensdatenbank initialisieren
pnpm run rag:reset       # Datenbank zurücksetzen und neu aufbauen

# Testing
pnpm run test            # Jest Unit- und Integrationstests
pnpm run test:coverage   # Code-Coverage-Bericht generieren
pnpm run test:chat-all   # Vollständige Chat-Integration-Tests
```

### 🏗️ Architekturprinzipien

#### Clean Architecture Implementierung
- **Domain Layer**: `types/`, `schemas/` - Geschäftsobjekte und -regeln
- **Application Layer**: `services/` - Use Cases und Workflows  
- **Infrastructure Layer**: `repositories/`, `db/` - Datenzugriff und externe Services
- **Presentation Layer**: `routes/`, `controllers/` - HTTP-Interface

#### Repository Pattern mit Factory
```typescript
// Singleton-basierte Repository-Verwaltung
const repositoryFactory = RepositoryFactoryImpl.getInstance()
const dispatchRepo = repositoryFactory.dispatch()
const warehouseRepo = repositoryFactory.warehouse()

// Automatische Dependency Injection
await repositoryFactory.invalidateAllCaches()
```

### 🔧 Konfiguration (12-Factor App konform)

```bash
# Kritische Umgebungsvariablen (.env)
DATABASE_URL="file:../database/sfm_dashboard.db"
API_SECRET_KEY="sfm_api_[production-key]"  
NODE_ENV="development|production"
API_PORT=3001

# AI-Integration
OPENROUTER_API_KEY="sk-or-v1-[key]"
OPENROUTER_PRESET_MODEL="@preset/lapp"
OPENAI_API_KEY="sk-[optional-for-embeddings]"

# Performance-Tuning
CACHE_MAX_MEMORY_MB=100
CACHE_MAX_ENTRIES=2000
PERFORMANCE_MONITORING_ENABLED=true
```

### 📊 Observability & Monitoring

#### Performance-Tracking
```typescript
// Automatisches Request-Monitoring
app.use(performanceTrackingMiddleware)

// Cache-Hit-Rate Monitoring (Ziel: >80%)
const cacheStats = cache.getStats()
console.log(`Cache Hit Rate: ${cacheStats.hitRate}%`)
```

#### Security Event Logging
```typescript
// Sicherheitsereignisse werden automatisch geloggt
// 🚨 401/403 Status Codes → Security-Log
// ⚠️  4xx Fehler → Error-Log  
// ✅ 200 API-Aufrufe → Access-Log
```

#### System-Metriken-Endpunkt
```bash
curl http://localhost:3001/api/metrics
# Returns: Memory usage, Uptime, Node.js version, Cache statistics
```

### 🛡️ Security-Implementierung (OWASP-konform)

| **Schutzmaßnahme** | **Implementierung** | **OWASP-Kategorie** |
|-------------------|---------------------|---------------------|
| **Input-Validierung** | Zod-Schemas mit strikten Limits | A03: Injection |
| **Authentication** | JWT + API-Key Multi-Factor | A07: Auth Failures |
| **Rate Limiting** | Express-Rate-Limit (IP-based) | A10: SSRF |
| **SQL-Injection-Schutz** | Drizzle ORM (Prepared Statements) | A03: Injection |
| **CORS-Policy** | Restrictive Origins (Electron-specific) | A05: Security Misconfiguration |
| **Security Headers** | Helmet.js (CSP, HSTS, X-Frame-Options) | A05: Security Misconfiguration |

---

## 6. Datenmodelle & Schnittstellen

### 🗄️ Datenbankschema (Drizzle ORM)

#### Haupttabellen für Produktionsdaten

| **Tabelle** | **Schlüsselfelder** | **Beziehungen** | **Indizierung** |
|-------------|---------------------|-----------------|-----------------|
| `dispatch_data` | `datum`, `servicegrad`, `ausgeliefert_lup` | 1:n → System | `datum_idx` |
| `schnitte` | `datum`, `m5RH1...m15RH1` | n:1 ← Maschinen | `datum_idx` |
| `bestand200/240` | `auslastungA/B/C`, `aufnahmeDatum` | 1:n → Auslastung | `aufnahmeDatum_idx` |
| `ARiL/ATrL` | `datum`, `auslastung`, `belegtePlaetze` | - | `datum_idx` |
| `System` | `datum`, `verfuegbarkeitFTS` | 1:n → Dispatch | `datum_idx` |

#### Störungsmanagement-Tabellen

```typescript
interface StoerungsModel {
  id: number                    // Auto-increment PK
  title: string                 // Kurzbeschreibung (max 200 Zeichen)
  description?: string          // Detailbeschreibung
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  status: 'NEW' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED'
  category?: string             // Fachbereich (Hardware, Software, Network)
  assignedTo?: string           // Bearbeiter-ID
  createdAt: string            // ISO 8601 Timestamp  
  updatedAt: string            // Last Modified
  resolvedAt?: string          // Resolution Timestamp (für MTTR)
  estimatedResolution?: string  // ETA
  actualResolution?: string     // Actual Resolution Time
  // ITIL-konforme Felder
  impact: 'LOW' | 'MEDIUM' | 'HIGH'
  urgency: 'LOW' | 'MEDIUM' | 'HIGH'  
  priority: string             // Calculated from Impact × Urgency
  rootCause?: string           // Root Cause Analysis
  preventiveMeasures?: string  // Future Prevention
  // Compliance & Tracking
  customerImpact?: string      // Business Impact Description
  escalationLevel: number      // 0=Normal, 1=L2, 2=L3, 3=Management
  attachments?: string         // JSON Array von File-URLs
}
```

#### Performance & Monitoring-Tabellen

```typescript
interface QueryPerformanceMetric {
  id: string
  timestamp: string
  query_type: string           // 'database' | 'cache' | 'api' | 'ai'
  execution_time_ms: number
  cache_hit: boolean
  data_size_bytes?: number
  error_occurred: boolean
  error_message?: string
  // Optimierungshinweise
  optimization_suggestion?: string
  query_complexity_score: number  // 1-10 Skala
}
```

### 🌐 REST-API-Schnittstellen

#### Core Data Endpoints

```typescript
// Dispatch & Performance Monitoring
GET  /api/database/system-fts-data
     ?startDate=2024-01-01&endDate=2024-12-31
Response: {
  success: boolean
  data: SystemFTSDataPoint[]
  count: number
  cached: boolean
}

interface SystemFTSDataPoint {
  datum: string              // YYYY-MM-DD Format
  verfuegbarkeitFTS: number  // 0.0 - 1.0 (Verfügbarkeit in Prozent)
  servicegrad?: number       // Optional: Service Level
}
```

#### Störungsmanagement-API

```typescript
// CRUD-Operationen für Störungen
POST   /api/stoerungen
PUT    /api/stoerungen/:id  
DELETE /api/stoerungen/:id
GET    /api/stoerungen?status=ACTIVE&severity=CRITICAL&limit=50

// Statistiken und KPIs
GET    /api/stoerungen/stats
Response: {
  success: boolean
  data: {
    total: number
    active: number
    resolved: number
    mttr_hours: number        // Mean Time To Repair
    mtbf_hours: number        // Mean Time Between Failures
    severity_distribution: {
      critical: number
      high: number
      medium: number
      low: number
    }
    trends: {
      weekly_resolution_rate: number
      monthly_incident_count: number
      top_categories: string[]
    }
  }
}

// Live-Systemstatus
GET    /api/stoerungen/system/status
POST   /api/stoerungen/system/status
Body: {
  system: string             // System-Identifier
  status: 'HEALTHY' | 'WARNING' | 'CRITICAL' | 'MAINTENANCE'
  last_check: string         // ISO 8601 Timestamp
  details?: string           // Zusätzliche Informationen
}
```

#### AI & Analytics-API

```typescript
// Chat-Integration mit Kontext-Anreicherung
POST /api/ai/chat
Body: {
  message: string            // User-Query (max 1000 Zeichen)
  includeInsights?: boolean  // KI-Insights einbeziehen
  includeAnomalies?: boolean // Anomalie-Erkennung aktivieren
}
Response: {
  response: string           // AI-Antwort
  timestamp: string
  model: string              // Verwendetes AI-Modell
  hasEnrichedContext: boolean
  dataTypes: string[]        // Verwendete Datenquellen
  detectedIntents?: Intent[] // Erkannte Absichten
}

// Anomalie-Erkennung
GET /api/ai/anomalies?timeframe=7
Response: {
  success: boolean
  data: Anomaly[]
  timeframe: number
}

interface Anomaly {
  type: 'warehouse' | 'cutting' | 'dispatch' | 'system'
  severity: 'critical' | 'high' | 'medium' | 'low'
  description: string
  value: number
  threshold: number
  timestamp: string
  recommendation: string
}
```

#### Performance-Monitoring-API

```typescript
// Cache-Performance
GET /api/performance/cache
Response: {
  hit_rate: number           // 0.0 - 1.0
  memory_usage_mb: number
  entry_count: number
  evictions: number
  total_requests: number
}

// Database-Performance  
GET /api/performance/database
Response: {
  avg_query_time_ms: number
  slow_queries: number       // >1000ms
  connection_pool_usage: number
  cache_effectiveness: number
}
```

### 🔒 DSGVO-relevante Felder

| **Tabelle** | **Personenbezogene Daten** | **Rechtsgrundlage** | **Retention** |
|-------------|----------------------------|---------------------|---------------|
| `bereitschaftsPersonen` | `name`, `email`, `telefon` | Berechtigtes Interesse (Art. 6 Abs. 1 lit. f DSGVO) | 2 Jahre nach Ausscheiden |
| `stoerungen` | `assignedTo` (Mitarbeiter-ID) | Arbeitsvertrag (Art. 6 Abs. 1 lit. b DSGVO) | 7 Jahre (Aufbewahrungspflicht) |
| `user` | `email`, `name`, `lastLogin` | Einwilligung (Art. 6 Abs. 1 lit. a DSGVO) | Bis Widerruf |

**Anonymisierung:** Produktionsdaten (Dispatch, Schnitte, Lager) enthalten keine personenbezogenen Daten.

---

## 7. Testing

### 🧪 Test-Architektur & Coverage

| **Test-Typ** | **Framework** | **Abdeckung** | **Ausführungszeit** | **Zweck** |
|--------------|---------------|---------------|---------------------|-----------|
| **Unit Tests** | Jest 29.7.0 | 85%+ | <30s | Einzelfunktionen isoliert testen |
| **Integration Tests** | Jest + SuperTest | 75%+ | 1-2min | API-Endpunkte mit echter DB |
| **E2E Tests** | Jest (Custom Scenarios) | 60%+ | 3-5min | Komplette User-Workflows |
| **Performance Tests** | Jest (Custom Timing) | - | 30s-1min | Response-Zeit & Memory-Leaks |

### 📁 Test-Organisation

```
tests/
├── 🧪 unit/
│   ├── services/                 # Service-Layer Tests
│   │   ├── cache.service.test.ts       # LRU-Cache Funktionalität
│   │   ├── database.service.test.ts    # Database-Queries & Drizzle
│   │   └── ai.service.test.ts          # Anomalie-Erkennung Algorithmen
│   └── repositories/             # Repository-Pattern Tests  
│       ├── dispatch.repository.test.ts
│       ├── warehouse.repository.test.ts
│       └── repository.factory.test.ts
├── 🔗 integration/
│   ├── api/                      # API-Endpunkt Tests
│   │   ├── stoerungen.routes.test.ts   # CRUD + Statistiken
│   │   ├── database.routes.test.ts     # System-FTS-Data
│   │   └── performance.routes.test.ts  # Monitoring-Endpunkte
│   └── database/                 # Database-Integration
│       └── drizzle-orm.test.ts         # Schema-Konsistenz
├── 🎭 e2e/
│   ├── chat-integration-complete.test.ts  # AI-Chat End-to-End
│   ├── chat-performance.test.ts           # Response-Zeit Benchmarks
│   └── error-handling-integration.test.ts # Fehlerbehandlung
└── 📊 fixtures/                  # Test-Daten (NUR für Tests!)
    ├── mock-dispatch-data.json          # Realistische Produktionsdaten
    ├── mock-stoerungen.json             # ITIL-konforme Incident-Daten
    └── mock-warehouse-data.json         # Lagerbestände für Testszenarien
```

### ⚙️ Test-Konfiguration

```json
{
  "jest": {
    "preset": "ts-jest",
    "testEnvironment": "node",
    "setupFilesAfterEnv": ["<rootDir>/tests/helpers/test-setup.ts"],
    "coverageThreshold": {
      "global": {
        "branches": 80,
        "functions": 85,
        "lines": 85,
        "statements": 85
      }
    },
    "testMatch": ["**/*.test.ts"],
    "collectCoverageFrom": [
      "src/**/*.ts",
      "!src/types/**",
      "!src/tests/**"
    ]
  }
}
```

### 🚀 Test-Ausführung

```bash
# Komplette Test-Suite
pnpm run test                    # Alle Unit- und Integration-Tests

# Spezifische Test-Kategorien  
pnpm run test:unit               # Nur Unit-Tests
pnpm run test:integration        # Nur Integration-Tests
pnpm run test:chat-integration   # AI-Chat-Funktionalität
pnpm run test:chat-performance   # Performance-Benchmarks
pnpm run test:chat-e2e           # End-to-End Chat-Szenarien

# Development-Workflow
pnpm run test:watch              # Watch-Mode für aktive Entwicklung
pnpm run test:coverage           # Coverage-Report (HTML + Terminal)

# Chat-spezifische Tests (vollständig)
pnpm run test:chat-all           # Alle Chat-Tests in der korrekten Reihenfolge
```

### 🎯 Qualitäts-Gates & CI

```yaml
# GitHub Actions / GitLab CI Pipeline
quality_gates:
  - name: "Code Coverage"
    threshold: 85%
    blocking: true
  - name: "No High-Severity Security Issues"  
    tool: "npm audit"
    max_high: 0
  - name: "TypeScript Compilation"
    command: "pnpm run build"
    success_required: true
  - name: "Linting (ESLint)"
    command: "pnpm run lint"
    max_errors: 0
```

### 🧩 Mock-Strategien

```typescript
// Service-Layer Mocks
jest.mock('../services/openrouter.service');
const mockOpenRouterService = OpenRouterService as jest.Mocked<typeof OpenRouterService>;

// Database-Mocks für isolierte Tests
jest.mock('../db', () => ({
  db: {
    select: jest.fn().mockReturnValue({
      from: jest.fn().mockReturnValue({
        where: jest.fn().mockResolvedValue([])
      })
    })
  }
}));

// Realistische Test-Fixtures (keine Produktionsdaten!)
const mockDispatchData: DispatchDataPoint[] = [
  {
    datum: '2024-01-15',
    servicegrad: 0.952,           // 95.2% Service Level
    ausgeliefert_lup: 1250,       // 1250 LUP ausgeliefert
    produzierte_tonnagen: 85.7    // 85.7t Tonnage
  }
];
```

---

## 8. Entwicklungshinweise

### 🔄 Git-Workflow & Zusammenarbeit

#### Branch-Naming-Konvention
```bash
feature/add-anomaly-detection     # Neue Features
fix/cache-memory-leak             # Bug-Fixes  
refactor/repository-pattern       # Code-Verbesserungen
docs/api-documentation           # Dokumentation
security/fix-sql-injection       # Sicherheitsupdates
performance/optimize-queries     # Performance-Optimierungen
```

#### Commit-Konvention (Conventional Commits)
```bash
feat: implement MTTR calculation for störungsmanagement
fix: resolve cache memory leak in warehouse repository  
refactor: extract AI service interface for better testability
docs: add API documentation for system-fts-data endpoint
security: implement rate limiting for AI chat endpoints
perf: optimize database queries with proper indexing

# Breaking Changes
feat!: migrate from Prisma to Drizzle ORM
BREAKING CHANGE: Database schema migration required
```

#### Pull-Request-Workflow
1. **Atomic Commits**: Ein logischer Change pro Commit
2. **Senior Review**: Mindestens ein Review von erfahrenem Entwickler  
3. **CI-Pipeline**: Alle Tests müssen grün sein
4. **Documentation**: README und Code-Comments aktualisieren

### 🛠️ Entwicklungsumgebung

#### Lint/Format-Tools
```bash
# Code-Qualität automatisch sicherstellen
pnpm run lint                    # ESLint + TypeScript-Checks
pnpm run format                  # Prettier Auto-Formatting
pnpm run type-check              # TypeScript Compilation Check

# Pre-Commit-Hooks (automatisch)
npx husky add .husky/pre-commit "pnpm run lint && pnpm run test:unit"
```

#### Hot-Reload & Debugging
```bash
# Development Server mit Hot-Reload
pnpm run dev                     # Nodemon + TypeScript-Compiler

# Debugging mit VS Code
# .vscode/launch.json:
{
  "type": "node",
  "request": "launch", 
  "name": "Debug Backend",
  "program": "${workspaceFolder}/backend/dist/server.js",
  "preLaunchTask": "npm: build",
  "env": {
    "NODE_ENV": "development"
  }
}
```

### ⚠️ Häufige Stolperfallen & Lösungen

| **Problem** | **Symptom** | **Lösung** | **Prävention** |
|-------------|-------------|------------|----------------|
| **Memory Leaks** | Server wird langsam, RAM-Verbrauch steigt | `cache.destroy()` aufrufen, Event-Listener entfernen | Regelmäßige Memory-Profiling |
| **Cache-Invalidierung** | Veraltete Daten im Frontend | `invalidateByDataTypes(['dispatch'])` verwenden | TTL-Werte korrekt setzen |
| **Database-Lock** | SQLite-Timeout-Fehler | Transaction-Batching implementieren | Connection-Pooling nutzen |
| **Rate-Limit-Overflow** | 429 Fehler in Produktion | Rate-Limit-Konfiguration anpassen | Environment-spezifische Limits |
| **TypeScript-Compilation** | Build-Fehler nach Dependency-Update | `pnpm run type-check` vor Commit | Automatische CI-Checks |

### 🚀 Performance-Optimierung

#### Database-Query-Optimierung
```typescript
// ❌ Schlecht: N+1 Query Problem
for (const incident of incidents) {
  const comments = await getCommentsForIncident(incident.id);
}

// ✅ Besser: Batch-Query mit JOIN
const incidentsWithComments = await db
  .select()
  .from(stoerungen)
  .leftJoin(stoerungsComments, eq(stoerungen.id, stoerungsComments.stoerungId));
```

#### Cache-Strategien  
```typescript
// Cache-Keys strukturiert aufbauen für effiziente Invalidierung
const cacheKey = `dispatch:${year}-${month}:servicegrad`;
const data = await cache.cachedQuery(cacheKey, async () => {
  return database.getServiceLevelData(year, month);
}, CACHE_TTL.DISPATCH_DATA);
```

### 📚 Architektur-Entscheidungen (ADRs)

#### ADR-001: Migration von Prisma zu Drizzle ORM
```markdown
# Status: Akzeptiert (2024-08-28)

## Kontext
Prisma ORM zeigte Performance-Probleme bei großen Datasets (>100k Records)
und hatte Kompatibilitätsprobleme mit Node.js v20+.

## Entscheidung  
Migration zu Drizzle ORM für bessere Performance und TypeScript-Integration.

## Konsequenzen
+ 40-60% bessere Query-Performance
+ Vollständige Type-Safety zur Compile-Zeit
+ Kleinere Bundle-Size
- Einmaliger Migrations-Aufwand
- Team-Schulung erforderlich
```

#### ADR-002: Repository Pattern mit Factory
```markdown
# Status: Akzeptiert (2024-09-01)

## Kontext
Direkte Drizzle-DB-Zugriffe in Services führten zu Code-Duplikation
und erschwerten das Unit-Testing.

## Entscheidung
Implementierung des Repository-Patterns mit Singleton-Factory.

## Konsequenzen  
+ Bessere Testbarkeit durch Dependency Injection
+ Einheitliche Cache-Strategien pro Datentyp  
+ Klare Trennung zwischen Business- und Data-Logic
- Initiale Komplexität höher
- Mehr Abstraktions-Layer
```

### 📞 Team-Kommunikation & Support

#### Kontaktpunkte
- **📧 Tech-Lead**: `<EMAIL>`
- **💬 Slack-Channel**: `#leitstand-entwicklung` 
- **📖 Confluence**: `SFM Leitstand - Entwicklerdokumentation`
- **🐛 Issue-Tracking**: GitHub Issues mit Labels (`bug`, `enhancement`, `question`)

#### Eskalationspfad
1. **L1**: Peer-Developer (Code-Review, Pair-Programming)
2. **L2**: Senior-Developer / Tech-Lead 
3. **L3**: Architektur-Team (Breaking Changes, Security)
4. **L4**: Management (Budget, Ressourcen, Deadlines)

### 🎯 Module-Entwicklung Best Practices

#### Neue Service-Implementierung
```typescript
// 1. Interface definieren (Domain-Layer)
interface NewFeatureService {
  processData(input: InputType): Promise<OutputType>;
}

// 2. Implementation (Application-Layer)
class NewFeatureServiceImpl implements NewFeatureService {
  constructor(
    private repository: Repository<EntityType>,
    private cache: CacheService
  ) {}
  
  async processData(input: InputType): Promise<OutputType> {
    // Geschäftslogik implementieren
  }
}

// 3. Tests schreiben (Test-First Approach)
describe('NewFeatureService', () => {
  it('should process data correctly', () => {
    // Arrange, Act, Assert
  });
});

// 4. Integration in Factory-Pattern
export function createNewFeatureService(): NewFeatureService {
  return new NewFeatureServiceImpl(
    getRepository(),
    getBackendCache()
  );
}
```

---

## 🏆 Fazit

Das Backend der SFM Leitstand-Anwendung implementiert eine **moderne, skalierbare und sichere API-Architektur** nach bewährten Enterprise-Standards. Mit seiner **Hexagonal Architecture**, dem **Repository Pattern** und **umfassenden Caching-Strategien** bietet es eine solide Grundlage für die industrielle Produktionsüberwachung.

Die Integration von **AI-basierter Anomalie-Erkennung**, **real-time Performance-Monitoring** und **ITIL-konformem Störungsmanagement** macht es zu einer zukunftsfähigen Lösung für moderne Fertigungsumgebungen.

### 🎯 Nächste Entwicklungsschritte
1. **Mikroservice-Migration**: Aufspaltung in Domain-spezifische Services  
2. **Event-Streaming**: Implementation von Apache Kafka für Real-time Analytics
3. **Machine Learning**: Erweiterte Predictive Analytics mit TensorFlow.js
4. **API-Versionierung**: Einführung von v2 API mit GraphQL-Support

---

**📝 Letzte Aktualisierung**: 06. September 2024  
**👨‍💻 Maintainer**: Leitstand Entwicklungsteam  
**📄 Version**: 1.0.0

**Hinweis**: Diese README beschreibt die aktuelle Implementierung des Repository-Systems. Bei Änderungen oder Erweiterungen sollte diese Dokumentation entsprechend aktualisiert werden.
