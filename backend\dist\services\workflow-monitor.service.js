"use strict";
/**
 * Workflow Monitor Service
 *
 * Überwacht automatisierte Python-Skripte durch:
 * - Status-File Parsing
 * - Log-File Monitoring
 * - Process Detection
 * - Performance Metriken
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowMonitorService = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const child_process_1 = require("child_process");
const util_1 = require("util");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class WorkflowMonitorService {
    constructor() {
        this.workflowsDir = path_1.default.join(__dirname, '../../workflows');
        this.statusDir = path_1.default.join(this.workflowsDir, 'status');
        this.logsDir = path_1.default.join(this.workflowsDir, 'logs');
        this.configDir = path_1.default.join(this.workflowsDir, 'config');
        this.ensureDirectories();
    }
    async ensureDirectories() {
        try {
            await promises_1.default.mkdir(this.workflowsDir, { recursive: true });
            await promises_1.default.mkdir(this.statusDir, { recursive: true });
            await promises_1.default.mkdir(this.logsDir, { recursive: true });
            await promises_1.default.mkdir(this.configDir, { recursive: true });
        }
        catch (error) {
            console.error('[WorkflowMonitor] Fehler beim Erstellen der Verzeichnisse:', error);
        }
    }
    /**
     * Lädt alle Workflow-Konfigurationen und aktuellen Status
     */
    async getAllWorkflows() {
        try {
            const configs = await this.getWorkflowConfigs();
            const workflows = [];
            for (const config of configs) {
                const status = await this.getWorkflowStatus(config.id);
                const metrics = await this.getWorkflowMetrics(config.id);
                const workflow = {
                    id: config.id,
                    name: config.name,
                    description: config.description,
                    sourceType: config.sourceType,
                    frequency: config.frequency,
                    targetTables: config.targetTables,
                    scriptPath: config.scriptPath,
                    isActive: config.isActive,
                    status: status.status,
                    lastExecution: status.lastExecution,
                    nextExecution: status.nextExecution,
                    duration: status.duration,
                    successRate: metrics.successRate,
                    errorCount: metrics.errorCount,
                    totalRuns: metrics.totalExecutions
                };
                workflows.push(workflow);
            }
            return workflows;
        }
        catch (error) {
            console.error('[WorkflowMonitor] Fehler beim Laden der Workflows:', error);
            return [];
        }
    }
    /**
     * Lädt Workflow-Konfigurationen aus JSON-Files
     */
    async getWorkflowConfigs() {
        try {
            // Default-Konfigurationen erstellen falls nicht vorhanden
            await this.createDefaultConfigs();
            const configFiles = await promises_1.default.readdir(this.configDir);
            const configs = [];
            for (const file of configFiles) {
                if (file.endsWith('.json')) {
                    const configPath = path_1.default.join(this.configDir, file);
                    const configContent = await promises_1.default.readFile(configPath, 'utf-8');
                    const config = JSON.parse(configContent);
                    configs.push(config);
                }
            }
            return configs;
        }
        catch (error) {
            console.error('[WorkflowMonitor] Fehler beim Laden der Konfigurationen:', error);
            return [];
        }
    }
    /**
     * Erstellt Standard-Workflow-Konfigurationen
     */
    async createDefaultConfigs() {
        const defaultConfigs = [
            {
                id: 'sap-ablaengerei-auto',
                name: 'SAP Ablängerei Import',
                description: 'Automatischer Import von Schnittdaten aus SAP',
                sourceType: 'SAP',
                frequency: 'hourly',
                targetTables: ['Ablaengerei', 'Schnitte', 'alle_daten'],
                scriptPath: 'scripts/sap_ablaengerei.py',
                isActive: true,
                timeout: 300,
                retryCount: 3,
                notificationEmails: [],
                environmentVariables: {}
            },
            {
                id: 'bi-lager-auto',
                name: 'BI Lagerauslastung Sync',
                description: 'Automatische Aktualisierung Lager 200/240 Auslastung',
                sourceType: 'BI_Excel',
                frequency: 'daily',
                targetTables: ['auslastung200', 'auslastung240', 'Bestand200'],
                scriptPath: 'scripts/bi_lagerauslastung.py',
                isActive: true,
                timeout: 600,
                retryCount: 2,
                notificationEmails: [],
                environmentVariables: {}
            },
            {
                id: 'sap-warehouse-auto',
                name: 'SAP Warehouse Data Sync',
                description: 'Automatische Synchronisation der Lagerdaten',
                sourceType: 'SAP',
                frequency: 'daily',
                targetTables: ['ARiL', 'ATrL', 'ManL'],
                scriptPath: 'scripts/sap_warehouse.py',
                isActive: true,
                timeout: 450,
                retryCount: 3,
                notificationEmails: [],
                environmentVariables: {}
            }
        ];
        for (const config of defaultConfigs) {
            const configPath = path_1.default.join(this.configDir, `${config.id}.json`);
            try {
                await promises_1.default.access(configPath);
                // File exists, skip creation
            }
            catch (_a) {
                // File doesn't exist, create it
                await promises_1.default.writeFile(configPath, JSON.stringify(config, null, 2));
            }
        }
    }
    /**
     * Lädt aktuellen Status eines Workflows
     */
    async getWorkflowStatus(workflowId) {
        try {
            const statusPath = path_1.default.join(this.statusDir, `${workflowId}.json`);
            try {
                const statusContent = await promises_1.default.readFile(statusPath, 'utf-8');
                const statusData = JSON.parse(statusContent);
                return {
                    status: statusData.status,
                    lastExecution: statusData.lastExecution ? new Date(statusData.lastExecution) : null,
                    nextExecution: statusData.nextExecution ? new Date(statusData.nextExecution) : null,
                    duration: statusData.duration
                };
            }
            catch (_a) {
                // Status-File nicht gefunden, Standard-Status zurückgeben
                return {
                    status: 'scheduled',
                    lastExecution: null,
                    nextExecution: null,
                    duration: null
                };
            }
        }
        catch (error) {
            console.error(`[WorkflowMonitor] Fehler beim Laden des Status für ${workflowId}:`, error);
            return {
                status: 'error',
                lastExecution: null,
                nextExecution: null,
                duration: null
            };
        }
    }
    /**
     * Berechnet Workflow-Metriken
     */
    async getWorkflowMetrics(workflowId) {
        try {
            // Simulation der Metriken - in realer Implementierung würden diese aus Logs berechnet
            const executions = await this.getRecentExecutions(workflowId);
            const totalExecutions = executions.length;
            const successfulExecutions = executions.filter(e => e.status === 'completed').length;
            const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;
            const errorCount = executions.filter(e => e.status === 'error').length;
            const lastSuccessful = executions
                .filter(e => e.status === 'completed' && e.startTime !== null)
                .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())[0];
            const avgDuration = executions.length > 0
                ? executions.reduce((sum, e) => sum + (e.duration || 0), 0) / executions.length
                : 0;
            return {
                workflowId,
                averageDuration: avgDuration,
                successRate,
                lastSuccessfulRun: (lastSuccessful === null || lastSuccessful === void 0 ? void 0 : lastSuccessful.startTime) || null,
                totalExecutions,
                errorCount,
                dataFreshness: this.calculateDataFreshness(lastSuccessful === null || lastSuccessful === void 0 ? void 0 : lastSuccessful.startTime)
            };
        }
        catch (error) {
            console.error(`[WorkflowMonitor] Fehler beim Berechnen der Metriken für ${workflowId}:`, error);
            return {
                workflowId,
                averageDuration: 0,
                successRate: 0,
                lastSuccessfulRun: null,
                totalExecutions: 0,
                errorCount: 0,
                dataFreshness: 0
            };
        }
    }
    /**
     * Lädt letzte Ausführungen eines Workflows
     */
    async getRecentExecutions(workflowId, limit = 10) {
        // Simulation - in realer Implementierung aus Log-Files oder Status-History
        const now = new Date();
        const executions = [];
        for (let i = 0; i < limit; i++) {
            const startTime = new Date(now.getTime() - (i * 60 * 60 * 1000)); // Stündlich
            const duration = Math.floor(Math.random() * 300) + 30; // 30-330 Sekunden
            const success = Math.random() > 0.1; // 90% Erfolgsrate
            executions.push({
                id: `${workflowId}-${i}`,
                workflowId,
                startTime,
                endTime: new Date(startTime.getTime() + duration * 1000),
                duration,
                status: success ? 'completed' : 'error',
                errorMessage: success ? null : 'Beispiel-Fehler',
                recordsProcessed: success ? Math.floor(Math.random() * 1000) + 100 : null,
                dataSize: success ? Math.floor(Math.random() * 1024 * 1024) + 1024 : null
            });
        }
        return executions;
    }
    /**
     * Berechnet Datenaktualität in Minuten
     */
    calculateDataFreshness(lastSuccessfulRun) {
        if (!lastSuccessfulRun)
            return 999; // Very old data
        const now = new Date();
        const diffMs = now.getTime() - lastSuccessfulRun.getTime();
        return Math.floor(diffMs / (1000 * 60)); // in minutes
    }
    /**
     * Lädt Log-Einträge für einen Workflow
     */
    async getWorkflowLogs(workflowId, limit = 50) {
        try {
            const logPath = path_1.default.join(this.logsDir, `${workflowId}.log`);
            try {
                const logContent = await promises_1.default.readFile(logPath, 'utf-8');
                const lines = logContent.split('\n').filter(line => line.trim());
                // Parse die letzten N Zeilen als JSON Log-Einträge
                const logs = [];
                const recentLines = lines.slice(-limit);
                for (const line of recentLines) {
                    try {
                        const logEntry = JSON.parse(line);
                        logs.push({
                            timestamp: new Date(logEntry.timestamp),
                            level: logEntry.level || 'info',
                            message: logEntry.message || line,
                            workflowId,
                            executionId: logEntry.executionId,
                            details: logEntry.details
                        });
                    }
                    catch (_a) {
                        // Falls Line nicht JSON ist, als einfache Nachricht behandeln
                        logs.push({
                            timestamp: new Date(),
                            level: 'info',
                            message: line,
                            workflowId
                        });
                    }
                }
                return logs.reverse(); // Neueste zuerst
            }
            catch (_b) {
                // Log-File nicht gefunden
                return [];
            }
        }
        catch (error) {
            console.error(`[WorkflowMonitor] Fehler beim Laden der Logs für ${workflowId}:`, error);
            return [];
        }
    }
    /**
     * Aktiviert/deaktiviert einen Workflow
     */
    async toggleWorkflow(workflowId) {
        try {
            const configPath = path_1.default.join(this.configDir, `${workflowId}.json`);
            const configContent = await promises_1.default.readFile(configPath, 'utf-8');
            const config = JSON.parse(configContent);
            config.isActive = !config.isActive;
            await promises_1.default.writeFile(configPath, JSON.stringify(config, null, 2));
            console.log(`[WorkflowMonitor] Workflow ${workflowId} ${config.isActive ? 'aktiviert' : 'deaktiviert'}`);
            return config.isActive;
        }
        catch (error) {
            console.error(`[WorkflowMonitor] Fehler beim Toggle von Workflow ${workflowId}:`, error);
            return false;
        }
    }
    /**
     * Prüft ob ein Workflow gerade läuft (Process Detection)
     */
    async isWorkflowRunning(workflowId) {
        try {
            // Vereinfachte Process-Erkennung über Python-Skript-Namen
            const config = await this.getWorkflowConfigs();
            const workflow = config.find(c => c.id === workflowId);
            if (!workflow)
                return false;
            const scriptName = path_1.default.basename(workflow.scriptPath);
            const { stdout } = await execAsync(`pgrep -f "${scriptName}"`);
            return stdout.trim().length > 0;
        }
        catch (_a) {
            // pgrep Fehler bedeutet normalerweise "kein Prozess gefunden"
            return false;
        }
    }
}
exports.WorkflowMonitorService = WorkflowMonitorService;
