"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoerungenRepository = void 0;
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
// Simple in-memory cache for störungen
const simpleCache = new Map();
const getCached = (key) => {
    const entry = simpleCache.get(key);
    if (entry && entry.expires > Date.now()) {
        return entry.data;
    }
    simpleCache.delete(key);
    return null;
};
const setCache = (key, data, ttlMs = 300000) => {
    simpleCache.set(key, { data, expires: Date.now() + ttlMs });
};
const clearCachePattern = (pattern) => {
    for (const key of simpleCache.keys()) {
        if (key.includes(pattern)) {
            simpleCache.delete(key);
        }
    }
};
class StoerungenRepository {
    constructor() {
        this.db = db_1.db;
    }
    static getInstance(database) {
        if (!StoerungenRepository.instance) {
            StoerungenRepository.instance = new StoerungenRepository();
        }
        return StoerungenRepository.instance;
    }
    async createStoerung(data) {
        const tagsJson = data.tags ? JSON.stringify(data.tags) : null;
        const now = new Date().toISOString();
        const result = await this.db
            .insert(schema_1.stoerungen)
            .values({
            ...data,
            tags: tagsJson,
            created_at: now,
            updated_at: now,
        })
            .returning();
        const stoerung = result[0];
        // Fetch comments separately
        const comments = await this.db
            .select()
            .from(schema_1.stoerungsComments)
            .where((0, drizzle_orm_1.eq)(schema_1.stoerungsComments.stoerungId, stoerung.id))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.stoerungsComments.createdAt));
        clearCachePattern("stoerungen");
        return this.formatStoerung({ ...stoerung, comments });
    }
    async getStoerungen(options) {
        const cacheKey = `stoerungen:list:${JSON.stringify(options || {})}`;
        const cached = getCached(cacheKey);
        if (cached)
            return cached;
        const conditions = [];
        if (options === null || options === void 0 ? void 0 : options.status)
            conditions.push((0, drizzle_orm_1.eq)(schema_1.stoerungen.status, options.status));
        if (options === null || options === void 0 ? void 0 : options.severity)
            conditions.push((0, drizzle_orm_1.eq)(schema_1.stoerungen.severity, options.severity));
        if (options === null || options === void 0 ? void 0 : options.category)
            conditions.push((0, drizzle_orm_1.eq)(schema_1.stoerungen.category, options.category));
        if (options === null || options === void 0 ? void 0 : options.affected_system)
            conditions.push((0, drizzle_orm_1.eq)(schema_1.stoerungen.affected_system, options.affected_system));
        // Build query step by step to avoid Drizzle ORM type issues
        const baseQuery = this.db.select().from(schema_1.stoerungen);
        let finalQuery;
        if (conditions.length > 0) {
            finalQuery = baseQuery.where((0, drizzle_orm_1.and)(...conditions));
        }
        else {
            finalQuery = baseQuery;
        }
        // Apply ordering, limit, and offset
        finalQuery = finalQuery.orderBy((0, drizzle_orm_1.desc)(schema_1.stoerungen.created_at));
        if (options === null || options === void 0 ? void 0 : options.limit) {
            finalQuery = finalQuery.limit(options.limit);
        }
        if (options === null || options === void 0 ? void 0 : options.offset) {
            finalQuery = finalQuery.offset(options.offset);
        }
        const stoerungsResult = await finalQuery;
        // Fetch comments for each störung
        const stoerungsWithComments = await Promise.all(stoerungsResult.map(async (stoerung) => {
            const comments = await this.db
                .select()
                .from(schema_1.stoerungsComments)
                .where((0, drizzle_orm_1.eq)(schema_1.stoerungsComments.stoerungId, stoerung.id))
                .orderBy((0, drizzle_orm_1.desc)(schema_1.stoerungsComments.createdAt));
            return { ...stoerung, comments };
        }));
        const formatted = stoerungsWithComments.map(this.formatStoerung);
        setCache(cacheKey, formatted, 300000); // 5 minutes TTL
        return formatted;
    }
    async getStoerungById(id) {
        const cacheKey = `stoerungen:detail:${id}`;
        const cached = getCached(cacheKey);
        if (cached)
            return cached;
        const result = await this.db
            .select()
            .from(schema_1.stoerungen)
            .where((0, drizzle_orm_1.eq)(schema_1.stoerungen.id, id))
            .limit(1);
        const stoerung = result[0];
        if (!stoerung)
            return null;
        // Fetch comments separately
        const comments = await this.db
            .select()
            .from(schema_1.stoerungsComments)
            .where((0, drizzle_orm_1.eq)(schema_1.stoerungsComments.stoerungId, stoerung.id))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.stoerungsComments.createdAt));
        const formatted = this.formatStoerung({ ...stoerung, comments });
        setCache(cacheKey, formatted, 300000);
        return formatted;
    }
    async updateStoerung(id, data) {
        console.log("[DEBUG] updateStoerung aufgerufen mit ID:", id);
        console.log("[DEBUG] Empfangene Daten:", JSON.stringify(data, null, 2));
        // Map frontend data to database column names
        const updateData = {
            title: data.title,
            description: data.description,
            severity: data.severity,
            status: data.status,
            category: data.category,
            assigned_to: data.assigned_to,
            affected_system: data.affected_system,
            location: data.location,
            reported_by: data.reported_by,
            resolution_steps: data.resolution_steps,
            root_cause: data.root_cause,
            lessons_learned: data.lessons_learned,
            updated_at: new Date().toISOString(),
        };
        // Handle tags
        if (data.tags) {
            updateData.tags = JSON.stringify(data.tags);
        }
        // Handle resolved_at
        if (data.status === "RESOLVED" && !data.resolved_at) {
            updateData.resolved_at = new Date().toISOString();
        }
        console.log("[DEBUG] Update-Daten für Datenbank:", JSON.stringify(updateData, null, 2));
        try {
            const result = await this.db
                .update(schema_1.stoerungen)
                .set(updateData)
                .where((0, drizzle_orm_1.eq)(schema_1.stoerungen.id, id))
                .returning();
            console.log("[DEBUG] Datenbank-Update erfolgreich, Ergebnis:", JSON.stringify(result, null, 2));
            const stoerung = result[0];
            // Fetch comments separately
            const comments = await this.db
                .select()
                .from(schema_1.stoerungsComments)
                .where((0, drizzle_orm_1.eq)(schema_1.stoerungsComments.stoerungId, stoerung.id))
                .orderBy((0, drizzle_orm_1.desc)(schema_1.stoerungsComments.createdAt));
            clearCachePattern("stoerungen");
            const formattedResult = this.formatStoerung({ ...stoerung, comments });
            console.log("[DEBUG] Formatierte Antwort:", JSON.stringify(formattedResult, null, 2));
            return formattedResult;
        }
        catch (error) {
            console.error("[DEBUG] Fehler beim Datenbank-Update:", error);
            throw error;
        }
    }
    async deleteStoerung(id) {
        try {
            await this.db.delete(schema_1.stoerungen).where((0, drizzle_orm_1.eq)(schema_1.stoerungen.id, id));
            clearCachePattern("stoerungen");
            return true;
        }
        catch (_a) {
            return false;
        }
    }
    async addComment(data) {
        // Add createdAt to the comment data as required by the schema
        const commentWithTimestamp = {
            stoerungId: data.stoerungId,
            userId: data.userId,
            comment: data.comment,
            createdAt: new Date().toISOString(),
            caretakerId: data.caretakerId,
        };
        const result = await this.db
            .insert(schema_1.stoerungsComments)
            .values(commentWithTimestamp)
            .returning();
        clearCachePattern("stoerungen");
        return result[0];
    }
    async getStoerungsStats() {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        const cacheKey = "stoerungen:stats";
        const cached = getCached(cacheKey);
        if (cached)
            return cached;
        // Execute queries separately to avoid Drizzle ORM type issues
        const totalResult = await this.db
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.stoerungen);
        const activeResult = await this.db
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.stoerungen)
            .where((0, drizzle_orm_1.inArray)(schema_1.stoerungen.status, ["NEW", "IN_PROGRESS"]));
        const resolvedResult = await this.db
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.stoerungen)
            .where((0, drizzle_orm_1.eq)(schema_1.stoerungen.status, "RESOLVED"));
        const avgMttrResult = await this.db
            .select({ avg: (0, drizzle_orm_1.avg)(schema_1.stoerungen.mttr_minutes) })
            .from(schema_1.stoerungen)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.stoerungen.status, "RESOLVED"), (0, drizzle_orm_1.isNotNull)(schema_1.stoerungen.mttr_minutes)));
        // Calculate 24 hours ago as ISO string for proper date comparison
        const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
        const recentResult = await this.db
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.stoerungen)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.stoerungen.status, "RESOLVED"), (0, drizzle_orm_1.gte)(schema_1.stoerungen.resolved_at, twentyFourHoursAgo)));
        // Get severity counts separately to avoid type issues
        const criticalResult = await this.db
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.stoerungen)
            .where((0, drizzle_orm_1.eq)(schema_1.stoerungen.severity, "CRITICAL"));
        const highResult = await this.db
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.stoerungen)
            .where((0, drizzle_orm_1.eq)(schema_1.stoerungen.severity, "HIGH"));
        const mediumResult = await this.db
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.stoerungen)
            .where((0, drizzle_orm_1.eq)(schema_1.stoerungen.severity, "MEDIUM"));
        const lowResult = await this.db
            .select({ count: (0, drizzle_orm_1.count)() })
            .from(schema_1.stoerungen)
            .where((0, drizzle_orm_1.eq)(schema_1.stoerungen.severity, "LOW"));
        const stats = {
            total: ((_a = totalResult[0]) === null || _a === void 0 ? void 0 : _a.count) || 0,
            active: ((_b = activeResult[0]) === null || _b === void 0 ? void 0 : _b.count) || 0,
            resolved: ((_c = resolvedResult[0]) === null || _c === void 0 ? void 0 : _c.count) || 0,
            critical: ((_d = criticalResult[0]) === null || _d === void 0 ? void 0 : _d.count) || 0,
            high: ((_e = highResult[0]) === null || _e === void 0 ? void 0 : _e.count) || 0,
            medium: ((_f = mediumResult[0]) === null || _f === void 0 ? void 0 : _f.count) || 0,
            low: ((_g = lowResult[0]) === null || _g === void 0 ? void 0 : _g.count) || 0,
            avg_mttr_minutes: Math.round(Number((_h = avgMttrResult[0]) === null || _h === void 0 ? void 0 : _h.avg) || 0),
            resolution_rate_24h: ((_j = recentResult[0]) === null || _j === void 0 ? void 0 : _j.count) || 0,
        };
        setCache(cacheKey, stats, 900000); // 15 minutes TTL
        return stats;
    }
    async getSystemStatus() {
        const cacheKey = "system:status:all";
        const cached = getCached(cacheKey);
        if (cached)
            return cached;
        const statuses = await this.db
            .select()
            .from(schema_1.systemStatus)
            .orderBy((0, drizzle_orm_1.asc)(schema_1.systemStatus.systemName));
        // Load status messages for each system status with category-based selection
        const statusesWithMessages = await Promise.all(statuses.map(async (status) => {
            // Get detailed status messages for this system
            const statusMessages = await this.db
                .select()
                .from(schema_1.systemStatusMessage)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.systemStatusMessage.systemStatusId, status.id), (0, drizzle_orm_1.eq)(schema_1.systemStatusMessage.isActive, true)))
                .orderBy((0, drizzle_orm_1.asc)(schema_1.systemStatusMessage.createdAt))
                .limit(3);
            return {
                id: status.id,
                system_name: status.systemName, // Map camelCase to snake_case for frontend
                status: status.status,
                last_check: status.lastUpdated, // Map camelCase to snake_case for frontend
                metadata: status.metadata || undefined,
                created_at: status.createdAt, // Map camelCase to snake_case for frontend
                updated_at: status.updatedAt, // Map camelCase to snake_case for frontend
                statusMessages: statusMessages.map((msg) => ({
                    id: msg.id,
                    title: msg.title || "Status Update",
                    description: msg.description || "",
                    priority: msg.priority || 1,
                    category: msg.category || "general",
                })),
            };
        }));
        setCache(cacheKey, statusesWithMessages, 30000); // 30 seconds TTL for live data
        return statusesWithMessages;
    }
    async updateSystemStatus(data) {
        // First try to find existing record
        const existingStatusResult = await this.db
            .select()
            .from(schema_1.systemStatus)
            .where((0, drizzle_orm_1.eq)(schema_1.systemStatus.systemName, data.system_name)) // Use snake_case from interface
            .limit(1);
        const existingStatus = existingStatusResult[0];
        let status;
        if (existingStatus) {
            // Update existing record
            const updateResult = await this.db
                .update(schema_1.systemStatus)
                .set({
                status: data.status,
                lastUpdated: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                metadata: data.metadata,
            })
                .where((0, drizzle_orm_1.eq)(schema_1.systemStatus.id, existingStatus.id))
                .returning();
            status = updateResult[0];
        }
        else {
            // Create new record
            const insertResult = await this.db
                .insert(schema_1.systemStatus)
                .values({
                systemName: data.system_name, // Use snake_case from interface
                status: data.status,
                lastUpdated: new Date().toISOString(),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                metadata: data.metadata || null,
            })
                .returning();
            status = insertResult[0];
        }
        clearCachePattern("system:status");
        return {
            id: status.id,
            system_name: status.systemName, // Map camelCase to snake_case for frontend
            status: status.status,
            last_check: status.lastUpdated, // Map camelCase to snake_case for frontend
            metadata: status.metadata || undefined,
            created_at: status.createdAt, // Map camelCase to snake_case for frontend
            updated_at: status.updatedAt, // Map camelCase to snake_case for frontend
            statusMessages: [],
        };
    }
    formatStoerung(stoerung) {
        let tags = [];
        try {
            if (stoerung.tags) {
                if (typeof stoerung.tags === "string") {
                    // Try to parse as JSON first
                    try {
                        tags = JSON.parse(stoerung.tags);
                    }
                    catch (_a) {
                        // If JSON parsing fails, split by comma
                        tags = stoerung.tags
                            .split(",")
                            .map((tag) => tag.trim())
                            .filter(Boolean);
                    }
                }
                else if (Array.isArray(stoerung.tags)) {
                    tags = stoerung.tags;
                }
            }
        }
        catch (error) {
            console.error("Error parsing tags for störung:", stoerung.id, error);
            tags = [];
        }
        // Convert string dates to ISO strings as expected by the frontend
        const formatDate = (dateStr, fallback = false) => {
            if (!dateStr) {
                if (fallback) {
                    // For required fields, provide current date as fallback
                    return new Date().toISOString();
                }
                return undefined;
            }
            try {
                const date = new Date(dateStr);
                // Check if the date is valid
                if (isNaN(date.getTime())) {
                    console.error(`Invalid date value for störung ${stoerung.id}:`, dateStr);
                    if (fallback) {
                        return new Date().toISOString();
                    }
                    return undefined;
                }
                return date.toISOString();
            }
            catch (error) {
                console.error(`Error converting date for störung ${stoerung.id}:`, dateStr, error);
                if (fallback) {
                    return new Date().toISOString();
                }
                return undefined;
            }
        };
        // Format data to match frontend expectations (snake_case properties)
        return {
            id: stoerung.id,
            title: stoerung.title,
            description: stoerung.description,
            severity: stoerung.severity,
            status: stoerung.status,
            category: stoerung.category,
            affected_system: stoerung.affected_system, // Use snake_case from database
            location: stoerung.location,
            reported_by: stoerung.reported_by, // Use snake_case from database
            assigned_to: stoerung.assigned_to, // Use snake_case from database
            created_at: (formatDate(stoerung.created_at, true) ||
                new Date().toISOString()), // Ensure string
            updated_at: (formatDate(stoerung.updated_at, true) ||
                new Date().toISOString()), // Ensure string
            resolved_at: formatDate(stoerung.resolved_at),
            acknowledged_at: formatDate(stoerung.acknowledged_at),
            acknowledged_by: stoerung.acknowledged_by,
            escalation_level: stoerung.escalation_level,
            resolution_steps: stoerung.resolution_steps,
            root_cause: stoerung.root_cause,
            lessons_learned: stoerung.lessons_learned,
            mttr_minutes: stoerung.mttr_minutes,
            mtta_minutes: stoerung.mtta_minutes,
            tags,
            // Ensure comments array exists
            comments: stoerung.comments || [],
        };
    }
    /**
     * Determine system category based on system name
     * This matches the categorization logic from SystemStatusHeatmap component
     */
    determineSystemCategory(systemName) {
        if (systemName.includes("Datenbank"))
            return "Datenbanken";
        if (systemName.includes("Terminal"))
            return "Terminals";
        if (systemName.includes("Fördertechnik"))
            return "Fördertechnik";
        if (systemName.includes("Schrumpfanlage"))
            return "Anlagen";
        if (systemName.includes("Wlan"))
            return "Netzwerk";
        if (systemName.includes("Automatisches Trommellager") ||
            systemName.includes("Automatisches Ringlager"))
            return "Läger";
        if (systemName.includes("Stapler") || systemName.includes("FTS"))
            return "Flurförderzeuge";
        if (systemName.includes("SAP"))
            return "SAP";
        if (systemName.includes("ITM"))
            return "ITM";
        // Check for machine identifiers (M1-T-H3, M2-T-H3, etc.)
        const machineIdentifiers = [
            "M1-T-H3",
            "M2-T-H3",
            "M3-R-H3",
            "M4-T-H3",
            "M5-R-H3",
            "M6-T-H1",
            "M7-R-H1",
            "M8-T-H1",
            "M9-R-H1",
            "M10-T-H1",
            "M11-R-H1",
            "M12-T-H1",
            "M13-R-H1",
            "M14-T-H1",
            "M15-R-H1",
            "M16-T-H1",
            "M17-R-H1",
            "M18-T-H1",
            "M19-T-H1",
            "M20-T-H1",
            "M21-R-H1",
            "M22-T-H3",
            "M23-T-H1",
            "M24-T-H3",
            "M25-RR-H1",
            "M26-T-H1",
            "M27-R-H3",
            "M28-T-H1",
        ];
        if (machineIdentifiers.some((id) => systemName.includes(id))) {
            return "Maschinen";
        }
        // Default fallback
        return "System";
    }
    // ===== ATTACHMENT METHODS =====
    /**
     * Get all attachments for a störung
     */
    async getAttachments(stoerungId) {
        const cacheKey = `attachments:${stoerungId}`;
        const cached = getCached(cacheKey);
        if (cached)
            return cached;
        const attachments = await this.db
            .select()
            .from(schema_1.stoerungsAttachment)
            .where((0, drizzle_orm_1.eq)(schema_1.stoerungsAttachment.stoerungId, stoerungId))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.stoerungsAttachment.createdAt));
        setCache(cacheKey, attachments, 300000); // 5 minutes TTL
        return attachments;
    }
    /**
     * Get a specific attachment by ID
     */
    async getAttachmentById(id) {
        const cacheKey = `attachment:${id}`;
        const cached = getCached(cacheKey);
        if (cached)
            return cached;
        const attachment = await this.db
            .select()
            .from(schema_1.stoerungsAttachment)
            .where((0, drizzle_orm_1.eq)(schema_1.stoerungsAttachment.id, id))
            .limit(1);
        const result = attachment[0] || null;
        if (result) {
            setCache(cacheKey, result, 300000); // 5 minutes TTL
        }
        return result;
    }
    /**
     * Create a new attachment
     */
    async createAttachment(data) {
        const attachmentData = {
            ...data,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
        };
        const result = await this.db
            .insert(schema_1.stoerungsAttachment)
            .values(attachmentData)
            .returning();
        const created = result[0];
        // Clear cache
        clearCachePattern(`attachments:${data.stoerungId}`);
        clearCachePattern("stoerungen");
        return created;
    }
    /**
     * Delete an attachment
     */
    async deleteAttachment(id) {
        try {
            // Get the attachment first to clear the right cache
            const attachment = await this.getAttachmentById(id);
            await this.db
                .delete(schema_1.stoerungsAttachment)
                .where((0, drizzle_orm_1.eq)(schema_1.stoerungsAttachment.id, id));
            // Clear cache
            if (attachment) {
                clearCachePattern(`attachments:${attachment.stoerungId}`);
            }
            clearCachePattern(`attachment:${id}`);
            clearCachePattern("stoerungen");
            return true;
        }
        catch (_a) {
            return false;
        }
    }
    /**
     * Update an attachment
     */
    async updateAttachment(id, data) {
        const updateData = {
            ...data,
            updatedAt: new Date().toISOString(),
        };
        const result = await this.db
            .update(schema_1.stoerungsAttachment)
            .set(updateData)
            .where((0, drizzle_orm_1.eq)(schema_1.stoerungsAttachment.id, id))
            .returning();
        const updated = result[0] || null;
        if (updated) {
            // Clear cache
            clearCachePattern(`attachments:${updated.stoerungId}`);
            clearCachePattern(`attachment:${id}`);
            clearCachePattern("stoerungen");
        }
        return updated;
    }
}
exports.StoerungenRepository = StoerungenRepository;
