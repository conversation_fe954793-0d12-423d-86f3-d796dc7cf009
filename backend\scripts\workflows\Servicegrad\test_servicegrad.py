#!/usr/bin/env python3
"""
Test-Skript für Servicegrad Workflow Integration
"""

import sys
import os
import json
from pathlib import Path

# Füge das aktuelle Verzeichnis zum Python-Pfad hinzu
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from workflow_logger import WorkflowLogger

def test_workflow_logger():
    """Testet den WorkflowLogger für Servicegrad"""
    print("=== TESTE SERVICEGRAD WORKFLOW LOGGER ===")
    
    try:
        # <PERSON><PERSON><PERSON>gger mit PostgreSQL-Konfiguration
        db_config = {
            'host': os.getenv("DB_HOST", "localhost"),
            'database': os.getenv("DB_NAME", "lapp_dashboard"),
            'user': os.getenv("DB_USER", "postgres"),
            'password': os.getenv("DB_PASSWORD", ""),
            'port': os.getenv("DB_PORT", "5432")
        }
        
        logger = WorkflowLogger("servicegrad", db_config)
        print("✅ WorkflowLogger erfolgreich erstellt")
        
        # Teste verschiedene Log-Methoden
        logger.info("Test Info-Nachricht")
        logger.warning("Test Warning-Nachricht")
        logger.debug("Test Debug-Nachricht")
        
        # Teste Prozess-Logging
        logger.log_process_start("Test Servicegrad Prozess")
        logger.log_sap_action("Test SAP-Aktion", "wnd[0]", True)
        logger.log_file_operation("Test Datei-Operation", "test.xlsx", True)
        logger.log_process_complete("Test Servicegrad Prozess", "test_export.xlsx")
        
        print("✅ Alle Log-Methoden erfolgreich getestet")
        
        # Teste Log-Abfrage
        logs = WorkflowLogger.get_logs_for_workflow("servicegrad", db_config, limit=10)
        print(f"✅ {len(logs)} Logs für Servicegrad gefunden")
        
        return True
        
    except Exception as e:
        print(f"❌ Fehler beim Testen des WorkflowLoggers: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """Testet die PostgreSQL-Datenbankverbindung"""
    print("\n=== TESTE POSTGRESQL DATENBANKVERBINDUNG ===")
    
    try:
        import psycopg2
        
        # Lade DB-Credentials aus Umgebungsvariablen
        db_host = os.getenv("DB_HOST", "localhost")
        db_name = os.getenv("DB_NAME", "lapp_dashboard")
        db_user = os.getenv("DB_USER", "postgres")
        db_password = os.getenv("DB_PASSWORD")
        db_port = os.getenv("DB_PORT", "5432")
        
        if not db_password:
            print("❌ DB_PASSWORD-Umgebungsvariable nicht gesetzt.")
            return False
        
        print(f"DB-Host: {db_host}")
        print(f"DB-Name: {db_name}")
        print(f"DB-User: {db_user}")
        print(f"DB-Port: {db_port}")
        
        # Teste Verbindung
        conn_string = f"host='{db_host}' dbname='{db_name}' user='{db_user}' password='{db_password}' port='{db_port}'"
        conn = psycopg2.connect(conn_string)
        cursor = conn.cursor()
        
        print("✅ PostgreSQL-Verbindung erfolgreich hergestellt")
        
        # Prüfe ob workflow_logs Tabelle existiert
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'workflow_logs'
        """)
        
        result = cursor.fetchone()
        if result:
            print("✅ workflow_logs Tabelle existiert")
            
            # Zähle Einträge
            cursor.execute("SELECT COUNT(*) FROM workflow_logs WHERE workflow_id = 'servicegrad'")
            count = cursor.fetchone()[0]
            print(f"✅ {count} Servicegrad-Logs in der Datenbank")
        else:
            print("⚠️ workflow_logs Tabelle existiert nicht (wird beim ersten Lauf erstellt)")
        
        # Prüfe ob dispatch_data Tabelle existiert
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'dispatch_data'
        """)
        
        result = cursor.fetchone()
        if result:
            print("✅ dispatch_data Tabelle existiert")
            
            # Zähle Einträge
            cursor.execute("SELECT COUNT(*) FROM dispatch_data")
            count = cursor.fetchone()[0]
            print(f"✅ {count} Servicegrad-Datensätze in der Datenbank")
        else:
            print("⚠️ dispatch_data Tabelle existiert nicht (wird beim ersten Lauf erstellt)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Fehler bei PostgreSQL-Datenbankverbindung: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """Testet das Laden der Konfiguration"""
    print("\n=== TESTE KONFIGURATIONSLADEN ===")
    
    try:
        # Pfad zur Konfigurationsdatei
        config_path = os.path.join(current_dir, 'config.json')
        
        # Lade die Konfiguration
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ Konfigurationsdatei erfolgreich geladen")
        
        # Prüfe wichtige Konfigurationsabschnitte
        required_sections = ['sap', 'paths', 'email']
        for section in required_sections:
            if section in config:
                print(f"✅ Konfigurationsabschnitt '{section}' vorhanden")
            else:
                print(f"❌ Konfigurationsabschnitt '{section}' fehlt")
                return False
        
        # Prüfe wichtige Pfade
        required_paths = ['export_dir', 'sap_excel_path', 'main_excel_path', 'target_excel_path']
        for path in required_paths:
            if path in config['paths']:
                print(f"✅ Pfad '{path}' konfiguriert: {config['paths'][path]}")
            else:
                print(f"❌ Pfad '{path}' fehlt")
                return False
        
        # Prüfe Umgebungsvariablen
        env_vars_to_check = [
            'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD', 'DB_PORT',
            'SERVICEGRAD_EXPORT_DIR', 'SERVICEGRAD_SAP_EXCEL_PATH', 
            'SERVICEGRAD_MAIN_EXCEL_PATH', 'SERVICEGRAD_TARGET_EXCEL_PATH'
        ]
        
        for var in env_vars_to_check:
            value = os.getenv(var)
            if value:
                print(f"✅ Umgebungsvariable '{var}' gesetzt: {value}")
            else:
                print(f"⚠️ Umgebungsvariable '{var}' nicht gesetzt")
        
        return True
        
    except Exception as e:
        print(f"❌ Fehler beim Laden der Konfiguration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Hauptfunktion für Tests"""
    print("SERVICEGRAD WORKFLOW INTEGRATION TEST")
    print("=" * 50)
    
    success = True
    
    # Test 1: WorkflowLogger
    if not test_workflow_logger():
        success = False
    
    # Test 2: Datenbankverbindung
    if not test_database_connection():
        success = False
    
    # Test 3: Konfigurationsladen
    if not test_config_loading():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ ALLE TESTS ERFOLGREICH")
    else:
        print("❌ EINIGE TESTS FEHLGESCHLAGEN")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)