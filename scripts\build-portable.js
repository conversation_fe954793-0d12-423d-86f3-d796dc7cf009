#!/usr/bin/env node

/**
 * Optimiertes Build-Skript für portable Electron-App
 * 
 * Dieses Skript erstellt eine vollständig portable Electron-Anwendung,
 * die ohne Installation auf Windows-Systemen und in Citrix-Umgebungen läuft.
 * 
 * Features:
 * - Keine Administratorrechte erforderlich
 * - Alle Dependencies sind eingebettet
 * - Funktioniert in eingeschränkten Umgebungen wie Citrix
 * - Startet direkt ohne Installationsprozess
 * - Verwendet externe Datenbankverbindung (keine lokale SQLite)
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Konfiguration
const config = {
  appName: 'LappDashboard',
  platform: 'win32',
  arch: 'x64',
  outputDir: 'portable-dist',
  electronVersion: require('../package.json').devDependencies.electron.replace('^', ''),
  // Citrix-spezifische Optimierungen
  citrixOptimized: {
    disableGpu: false,  // GPU-Acceleration für bessere Performance
    enableSandbox: false,  // Sandbox für Citrix-Kompatibilität deaktivieren
    memoryLimit: '2048',  // Memory-Limit für Citrix-Umgebungen
    networkTimeout: 30000  // Längere Timeouts für Netzwerk-Delays
  },
  // Externe Datenbankkonfiguration
  databaseConfig: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || '5432',
    database: process.env.DB_NAME || 'lapp_dashboard',
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    ssl: process.env.DB_SSL === 'true' || false,
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 30000
  },
  // Native Module die speziell behandelt werden müssen
  nativeModules: [
    'better-sqlite3',
    'sqlite3', 
    'pg-native',
    '@prisma/engines'
  ]
};

// Hilfsfunktionen
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '[ERROR]' : type === 'warn' ? '[WARN]' : '[INFO]';
  console.log(`${timestamp} ${prefix} ${message}`);
}

function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    log(`Verzeichnis erstellt: ${dirPath}`);
  }
}

function cleanDist() {
  if (fs.existsSync(config.outputDir)) {
    log('Bereinige vorheriges Build-Verzeichnis...');
    fs.rmSync(config.outputDir, { recursive: true, force: true });
  }
  ensureDir(config.outputDir);
}

// Build-Prozess
async function buildPortableApp() {
  try {
    log('Starte Build-Prozess für portable Anwendung...');
    
    // 1. Bereinige vorherige Builds
    cleanDist();
    
    // 2. Installiere Abhängigkeiten
    log('Installiere Abhängigkeiten...');
    execSync('pnpm install', { stdio: 'inherit' });
    
    // 3. Stelle sicher, dass electron-forge korrekt installiert ist
    log('Installiere electron-forge...');
    execSync('pnpm install @electron-forge/cli --save-dev', { stdio: 'inherit' });
    
    // 4. Backend-Abhängigkeiten installieren
    log('Installiere Backend-Abhängigkeiten...');
    execSync('pnpm --prefix backend install --prod', { stdio: 'inherit' });
    
    // 5. TypeScript kompilieren
    log('Kompiliere TypeScript...');
    try {
      execSync('pnpm run make', { stdio: 'inherit' });
    } catch (error) {
      log('Make-Befehl fehlgeschlagen, versuche package-Befehl...', 'warn');
      execSync('pnpm run package', { stdio: 'inherit' });
    }
    execSync('pnpm --prefix backend run build', { stdio: 'inherit' });
    
    // 5. Native Module für Electron neu bauen (erweitert für portable Build)
    log('Baue native Module für Electron neu...');
    await rebuildNativeModules();
    
    // 6. Erstelle portable Anwendung mit electron-packager (Citrix-optimiert)
    log('Erstelle portable Anwendung mit electron-packager...');
    const packagerCmd = [
      'pnpm dlx electron-packager .',
      config.appName,
      `--platform=${config.platform}`,
      `--arch=${config.arch}`,
      `--out=${config.outputDir}`,
      '--overwrite',
      '--asar',
      // Citrix-spezifische Ignores
      '--ignore=node_modules/electron-packager',
      '--ignore=node_modules/.bin',
      '--ignore=.git',
      '--ignore=.vscode',
      '--ignore=portable-dist',
      '--ignore=dist',
      '--ignore="backend/node_modules/.cache"',
      '--ignore="**/*.map"',
      '--prune=true',
      '--temp=build-temp',
      // Zusätzliche Electron-Flags für Citrix
      '--app-copyright="JOZI1 Lapp Dashboard"',
      '--app-version=' + require('../package.json').version
    ].join(' ');
    
    execSync(packagerCmd, { stdio: 'inherit' });
    
    // 7. Finde das erstellte App-Verzeichnis
    const appDir = path.join(config.outputDir, `${config.appName}-${config.platform}-${config.arch}`);
    if (!fs.existsSync(appDir)) {
      throw new Error(`App-Verzeichnis nicht gefunden: ${appDir}`);
    }
    
    // 8. Kopiere Backend-Dateien (erweitert für portable Build)
    log('Kopiere Backend-Dateien...');
    const backendDir = path.join(appDir, 'resources', 'app', 'backend');
    ensureDir(backendDir);
    
    // Backend dist kopieren
    const backendDist = path.join('backend', 'dist');
    if (fs.existsSync(backendDist)) {
      log('Kopiere Backend dist...');
      copyRecursiveSync(backendDist, path.join(backendDir, 'dist'));
    }
    
    // Backend package.json kopieren
    const backendPackageJson = path.join('backend', 'package.json');
    if (fs.existsSync(backendPackageJson)) {
      fs.copyFileSync(backendPackageJson, path.join(backendDir, 'package.json'));
    }
    
    // Backend node_modules (nur production dependencies)
    const backendNodeModules = path.join('backend', 'node_modules');
    if (fs.existsSync(backendNodeModules)) {
      log('Kopiere Backend node_modules (production only)...');
      copyRecursiveSync(backendNodeModules, path.join(backendDir, 'node_modules'));
    }
    
    // Backend Konfigurationsdateien
    const backendConfigFiles = ['drizzle.config.ts', '.env.example'];
    for (const configFile of backendConfigFiles) {
      const srcPath = path.join('backend', configFile);
      if (fs.existsSync(srcPath)) {
        fs.copyFileSync(srcPath, path.join(backendDir, configFile));
      }
    }
    
    // Logs-Verzeichnis erstellen
    ensureDir(path.join(appDir, 'logs'));
    
    log('✅ Backend-Dateien erfolgreich kopiert');
    
    // 9. Erstelle erweiterte portable Konfigurationsdatei
    log('Erstelle portable Konfigurationsdatei...');
    const portableConfig = {
      portable: true,
      version: require('../package.json').version,
      buildDate: new Date().toISOString(),
      citrix: config.citrixOptimized,
      database: config.databaseConfig,
      system: {
        platform: config.platform,
        arch: config.arch,
        nodeVersion: process.version,
        electronVersion: config.electronVersion
      },
      paths: {
        appDir: './',  // Relativ für Portabilität
        backendDir: './resources/app/backend',
        configDir: './',
        logsDir: './logs'
      }
    };
    
    fs.writeFileSync(
      path.join(appDir, 'portable-config.json'),
      JSON.stringify(portableConfig, null, 2)
    );
    
    // 10. Erstelle erweiterte portable Startskripte
    log('Erstelle portable Startskripte...');
    
    // Standard Startskript
    const startScript = `@echo off
echo Starte ${config.appName}...
cd /d "%~dp0"
start "" "${config.appName}.exe"
`;
    fs.writeFileSync(path.join(appDir, 'start-app.bat'), startScript);
    
    // Citrix-optimiertes Startskript
    const citrixStartScript = `@echo off
REM Citrix-optimiertes Startskript für ${config.appName}
echo ====================================
echo ${config.appName} - Citrix-optimiert
echo ====================================
echo.

REM Setze Citrix-spezifische Umgebungsvariablen
set ELECTRON_DISABLE_SANDBOX=1
set ELECTRON_NO_ATTACH_CONSOLE=1
set NODE_OPTIONS="--max-old-space-size=${config.citrixOptimized.memoryLimit}"

REM Wechsel zum App-Verzeichnis
cd /d "%~dp0"

REM Prüfe verfügbaren Arbeitsspeicher
echo Prüfe Systemressourcen...
for /f "skip=1" %%p in ('wmic computersystem get TotalPhysicalMemory') do set RAM=%%p & goto :continue
:continue
if defined RAM echo Verfügbarer Arbeitsspeicher: %RAM% Bytes

echo Starte Anwendung mit Citrix-Optimierungen...
start "${config.appName}" "${config.appName}.exe" --no-sandbox --disable-web-security --disable-features=VizDisplayCompositor

echo Anwendung gestartet. Dieses Fenster kann geschlossen werden.
timeout /t 3 /nobreak >nul
`;
    fs.writeFileSync(path.join(appDir, 'start-citrix.bat'), citrixStartScript);
    
    // 11. Erstelle erweiterte README für portable Version
    log('Erstelle README für portable Version...');
    const readme = `# ${config.appName} - Portable Version

## Starten der Anwendung

### Standard-Umgebungen:
1. Doppelklick auf \`start-app.bat\`
2. Oder direkt auf \`${config.appName}.exe\`

### Citrix-Umgebungen (empfohlen):
1. Doppelklick auf \`start-citrix.bat\`
2. Automatische Optimierung für Citrix-Performance

## Systemanforderungen
- Windows 10 oder höher
- Mindestens 2 GB RAM (4 GB für Citrix empfohlen)
- 500 MB freier Speicherplatz
- Netzwerkverbindung für Datenbankzugriff
- Keine Administratorrechte erforderlich
- Voll kompatibel mit Citrix-Umgebungen

## Datenbankkonfiguration

### Externe PostgreSQL-Datenbank:
- Host: ${config.databaseConfig.host}
- Port: ${config.databaseConfig.port}
- Datenbank: ${config.databaseConfig.database}
- SSL: ${config.databaseConfig.ssl ? 'Aktiviert' : 'Deaktiviert'}

### Konfiguration ändern:
1. Bearbeiten Sie \`portable-config.json\`
2. Oder setzen Sie Umgebungsvariablen (DB_HOST, DB_PORT, etc.)
3. Starten Sie die Anwendung neu

## Citrix-Optimierungen

- Sandbox deaktiviert für bessere Kompatibilität
- Memory-Limit: ${config.citrixOptimized.memoryLimit}MB
- Erweiterte Netzwerk-Timeouts: ${config.citrixOptimized.networkTimeout}ms
- GPU-Acceleration: ${config.citrixOptimized.disableGpu ? 'Deaktiviert' : 'Aktiviert'}

## Fehlersuche

### Anwendung startet nicht:
1. Verwenden Sie \`start-citrix.bat\` in Citrix-Umgebungen
2. Prüfen Sie Log-Dateien im \`logs\` Verzeichnis
3. Stellen Sie sicher, dass .NET Framework verfügbar ist

### Datenbankverbindung fehlgeschlagen:
1. Prüfen Sie \`portable-config.json\`
2. Testen Sie die Netzwerkverbindung
3. Kontrollieren Sie Firewall-Einstellungen

### Performance-Probleme:
1. Nutzen Sie \`start-citrix.bat\` für automatische Optimierung
2. Prüfen Sie verfügbaren Arbeitsspeicher
3. Kontrollieren Sie Citrix-Ressourcen-Limits

## Portabilität

✅ Alle Dependencies eingebettet
✅ Keine Installation erforderlich
✅ Lauffähig von USB-Stick
✅ Netzwerklaufwerk-kompatibel
✅ Citrix-optimiert
✅ Relative Pfade für maximale Portabilität

## Build-Informationen

- Version: ${require('../package.json').version}
- Build-Datum: ${new Date().toLocaleDateString('de-DE')}
- Electron: ${config.electronVersion}
- Node.js: ${process.version}
- Plattform: ${config.platform}-${config.arch}

## Unterstützung

Detaillierte Anweisungen finden Sie in \`README-DEPLOYMENT.md\`
`;
    
    fs.writeFileSync(path.join(appDir, 'README-PORTABLE.md'), readme);
    
    // 12. Erstelle optimiertes ZIP-Archiv für Verteilung
    log('Erstelle ZIP-Archiv für Verteilung...');
    const zipName = `${config.appName}-portable-${require('../package.json').version}.zip`;
    
    await createDistributionZip(appDir, path.join(config.outputDir, zipName));
    
    // 13. Erstelle Deployment-Dokumentation
    log('Erstelle Deployment-Dokumentation...');
    await createDeploymentDocumentation(appDir);
    
    log('='.repeat(50));
    log('Build-Prozess erfolgreich abgeschlossen! ✅');
    log('='.repeat(50));
    log(`Portable Anwendung: ${appDir}`);
    log(`Standard-Start: ${path.join(appDir, 'start-app.bat')}`);
    log(`Citrix-optimiert: ${path.join(appDir, 'start-citrix.bat')}`);
    log(`Konfiguration: ${path.join(appDir, 'portable-config.json')}`);
    
    // Überprüfe kritische Dateien
    const criticalFiles = [
      path.join(appDir, config.appName + '.exe'),
      path.join(appDir, 'resources', 'app', 'backend', 'dist', 'server.js'),
      path.join(appDir, 'portable-config.json')
    ];
    
    let allFilesPresent = true;
    for (const file of criticalFiles) {
      if (!fs.existsSync(file)) {
        log(`❌ Kritische Datei fehlt: ${file}`, 'error');
        allFilesPresent = false;
      }
    }
    
    if (allFilesPresent) {
      log('✅ Alle kritischen Dateien sind vorhanden');
    } else {
      log('⚠️  Einige kritische Dateien fehlen - Build möglicherweise unvollständig', 'warn');
    }
    
    return appDir;
    
  } catch (error) {
    log(`=`.repeat(50), 'error');
    log(`❌ Build-Prozess fehlgeschlagen!`, 'error');
    log(`=`.repeat(50), 'error');
    log(`Fehler: ${error.message}`, 'error');
    log(`Stack: ${error.stack}`, 'error');
    
    // Cleanup bei Fehlern
    if (fs.existsSync(config.outputDir)) {
      try {
        log('Bereinige unvollständige Build-Artefakte...', 'warn');
        fs.rmSync(config.outputDir, { recursive: true, force: true });
      } catch (cleanupError) {
        log(`Cleanup-Fehler: ${cleanupError.message}`, 'warn');
      }
    }
    
    throw error;
  }
}

// Hilfsfunktion zum rekursiven Kopieren
function copyRecursiveSync(src, dest) {
  if (!fs.existsSync(src)) return;
  
  const stat = fs.statSync(src);
  if (stat.isDirectory()) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    for (const item of fs.readdirSync(src)) {
      copyRecursiveSync(path.join(src, item), path.join(dest, item));
    }
  } else {
    fs.copyFileSync(src, dest);
  }
}

// Hauptfunktion
async function main() {
  try {
    log('=== Build-Prozess für portable Electron-App ===');
    log(`Plattform: ${config.platform} (${config.arch})`);
    log(`App-Name: ${config.appName}`);
    log(`Ausgabeverzeichnis: ${config.outputDir}`);
    
    const appDir = await buildPortableApp();
    
    log('\n=== Build erfolgreich abgeschlossen ===');
    log(`Portale Anwendung: ${appDir}`);
    log(`Startskript: ${path.join(appDir, 'start-app.bat')}`);
    log(`Konfiguration: ${path.join(appDir, 'portable-config.json')}`);
    
  } catch (error) {
    log(`\n=== Build fehlgeschlagen ===`, 'error');
    log(`Fehler: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Starte den Build-Prozess
if (require.main === module) {
  main();
}

module.exports = { buildPortableApp, config };