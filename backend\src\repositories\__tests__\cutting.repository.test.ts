import { describe, it, expect, vi, beforeEach } from 'vitest';
import { CuttingRepositoryImpl } from '../cutting.repository';

// Mock für die Datenbank und Cache
vi.mock('../../db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis()
  }
}));

vi.mock('../../utils/cache/backend-cache', () => ({
  getBackendCache: () => ({
    cachedQuery: vi.fn()
  })
}));

describe('CuttingRepository', () => {
  let repository: CuttingRepositoryImpl;
  let mockCachedQuery: any;

  beforeEach(() => {
    repository = new CuttingRepositoryImpl();
    // Zugriff auf die gemockte cachedQuery Funktion
    mockCachedQuery = (repository as any).cache.cachedQuery;
  });

  describe('getWEData', () => {
    it('sollte datum-Feld korrekt aus dem Query-Ergebnis übernehmen (ARRANGE-ACT-ASSERT)', async () => {
      // ARRANGE: Bereite Mock-Daten vor mit korrektem Feldnamen 'datum' (kleingeschrieben)
      const mockWEData = [
        {
          id: 1,
          datum: '2025-04-15',
          weAtrl: 409,
          weManl: 56
        },
        {
          id: 2,
          datum: '2025-04-16',
          weAtrl: 713,
          weManl: 74
        },
        {
          id: 3,
          datum: '2025-04-17',
          weAtrl: 550,
          weManl: 20
        }
      ];

      // Mock die cachedQuery Funktion, um unsere Testdaten zurückzugeben
      mockCachedQuery.mockImplementation(async (key: string, queryFn: Function) => {
        return mockWEData;
      });

      // ACT: Rufe die getWEData Methode auf
      const result = await repository.getWEData({
        startDate: '2025-04-15',
        endDate: '2025-04-17'
      });

      // ASSERT: Verifiziere, dass die Daten korrekt gemappt wurden
      expect(result).toHaveLength(3);
      
      // Prüfe das erste Element im Detail
      expect(result[0]).toEqual({
        id: 1,
        datum: '2025-04-15',
        weAtrl: 409,
        weManl: 56
      });

      // Verifiziere, dass alle datum-Felder korrekt übernommen wurden
      result.forEach((item, index) => {
        expect(item.datum).toBe(mockWEData[index].datum);
        expect(item.datum).not.toBe(''); // Stelle sicher, dass das Datum nicht leer ist
      });
    });

    it('sollte leeren String als Fallback verwenden, wenn datum fehlt', async () => {
      // ARRANGE: Mock-Daten mit fehlendem datum-Feld
      const mockWEDataWithMissingDatum = [
        {
          id: 1,
          // datum fehlt hier absichtlich
          weAtrl: 409,
          weManl: 56
        },
        {
          id: 2,
          datum: null, // explizit null
          weAtrl: 713,
          weManl: 74
        },
        {
          id: 3,
          datum: undefined, // explizit undefined
          weAtrl: 550,
          weManl: 20
        }
      ];

      mockCachedQuery.mockImplementation(async (key: string, queryFn: Function) => {
        return mockWEDataWithMissingDatum;
      });

      // ACT: Rufe die getWEData Methode auf
      const result = await repository.getWEData();

      // ASSERT: Verifiziere, dass leere Strings als Fallback verwendet werden
      expect(result[0].datum).toBe('');
      expect(result[1].datum).toBe('');
      expect(result[2].datum).toBe('');
    });

    it('sollte die Daten nach Datum in absteigender Reihenfolge zurückgeben', async () => {
      // ARRANGE: Mock-Daten in zufälliger Reihenfolge
      const mockWEData = [
        { id: 2, datum: '2025-04-16', weAtrl: 713, weManl: 74 },
        { id: 3, datum: '2025-04-17', weAtrl: 550, weManl: 20 },
        { id: 1, datum: '2025-04-15', weAtrl: 409, weManl: 56 }
      ];

      mockCachedQuery.mockImplementation(async (key: string, queryFn: Function) => {
        // Simuliere absteigende Sortierung
        return mockWEData.sort((a, b) => b.datum.localeCompare(a.datum));
      });

      // ACT: Rufe die getWEData Methode auf
      const result = await repository.getWEData();

      // ASSERT: Verifiziere die absteigende Sortierung
      expect(result[0].datum).toBe('2025-04-17');
      expect(result[1].datum).toBe('2025-04-16');
      expect(result[2].datum).toBe('2025-04-15');
    });
  });
});
