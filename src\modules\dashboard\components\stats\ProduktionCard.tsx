import React from 'react';
import { TrendingUp } from 'lucide-react';
import { BaseKPICardProps } from './types';

/**
 * KPI-Karte für Produktion
 * 
 * Zeigt die Gesamtzahl der Schnitte aus der Ablängerei an.
 * Verwendet oranges Farbschema mit TrendingUp-Icon aus Lucide.
 */
export const ProduktionCard: React.FC<BaseKPICardProps> = ({ 
  departmentKPIs, 
  className = '' 
}) => {
  return (
    <>
      <div className="flex items-center mb-2">
        <div className="p-2 bg-orange-100 rounded-lg mr-3">
          <TrendingUp className="h-6 w-6 text-orange-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Produktion</h3>
      </div>
      
      <div className="text-2xl font-bold text-gray-900">
        {departmentKPIs?.ablaengerei.schnitte.toLocaleString() || '0'}
        <span className="text-sm font-normal text-gray-500 ml-1">Schnitte</span>
      </div>
      
      <div className="text-sm text-gray-600 mt-1">
        Ziel: 3.200 Schnitte
      </div>
    </>
  );
};

export default ProduktionCard;