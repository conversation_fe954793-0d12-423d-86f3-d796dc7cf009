import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User, Bell, Palette, Shield, Save, Camera, Upload, X } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import SmoothTab from "@/components/Animation/kokonutui/smooth-tab";
import { userService } from "@/services/user.service";
import { cn } from "@/lib/utils";

// Wrapper für API-Calls mit automatischer 403-Behandlung
const withAuthCheck = async (apiCall: () => Promise<any>) => {
  try {
    const result = await apiCall();
    if (result && (result.error === 'UNAUTHORIZED' || result.message?.includes('403'))) {
      console.log("🚪 403-Fehler erkannt, leite zur Login-Seite um");
      // Hier könnte eine Redirect-Logik implementiert werden
      alert("Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an.");
      // Für jetzt einfach die Seite neu laden
      window.location.reload();
    }
    return result;
  } catch (error) {
    console.error("API-Call fehlgeschlagen:", error);
    throw error;
  }
};

// Typdefinitionen
interface TableInfo {
    name: string;
}
  
interface TableData {
    columns: string[];
    rows: any[];
}

export default function UserSettingsPage() {
  // Keine Animation-Varianten mehr, wir nutzen nur CSS-Transitionen
  // Authentication hook
  const { user, isLoading, isAuthenticated, updateUser, validateCurrentAuth } = useAuth();

  // State to track if auth has been validated
  const [authValidated, setAuthValidated] = useState(false);
  const [authValid, setAuthValid] = useState(false);

  // Debug logging for authentication state
  useEffect(() => {
    console.log("🔐 UserSettingsPage - Auth State:", {
      user: user ? "Present" : "Null",
      isAuthenticated,
      isLoading
    });
  }, [user, isAuthenticated, isLoading]);

  // Aktiver Tab
  const [activeTab, setActiveTab] = useState("profile");
  
  // Profildaten basierend auf dem eingeloggten Benutzer
  const [profile, setProfile] = useState({
    username: "",
    email: "",
    name: "",
    role: "",
    bio: "",
    avatar: ""
  });

  // Avatar-Upload-States
  const [selectedAvatar, setSelectedAvatar] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [showAvatarModal, setShowAvatarModal] = useState(false);

  // Loading states for data loading
  const [isLoadingProfile, setIsLoadingProfile] = useState(false);
  const [isLoadingSettings, setIsLoadingSettings] = useState(false);

  // Benachrichtigungseinstellungen
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    pushNotifications: false,
    desktopNotifications: true,
    soundNotifications: true,
    newMessages: true,
    newMentions: true,
    newOrders: true,
    newAlerts: true
  });

  // Erscheinungsbild-Einstellungen
  const [appearance, setAppearance] = useState({
    theme: "system",
    language: "de",
    fontSize: "medium",
    highContrast: false,
    animations: true,
    compactMode: false
  });


  // Auth validation effect - runs BEFORE any API calls
  useEffect(() => {
    const validateAuth = async () => {
      console.log("🔐 Starting auth validation...");

      if (!user) {
        console.log("❌ No user found, skipping validation");
        setAuthValidated(true);
        setAuthValid(false);
        return;
      }

      try {
        const isValid = await validateCurrentAuth();
        console.log("🔐 Auth validation result:", isValid);
        setAuthValid(isValid);
        setAuthValidated(true);

        if (isValid) {
          console.log("✅ Auth is valid, setting up profile data");
          setProfile({
            username: user.username || "",
            email: user.email || "",
            name: user.name || "",
            role: user.roles?.[0] || "user",
            bio: "",
            avatar: user.avatar || ""
          });
        } else {
          console.log("❌ Auth is invalid, clearing profile data");
          setProfile({
            username: "",
            email: "",
            name: "",
            role: "",
            bio: "",
            avatar: ""
          });
        }
      } catch (error) {
        console.error("❌ Auth validation error:", error);
        setAuthValid(false);
        setAuthValidated(true);
      }
    };

    validateAuth();
  }, [user]); // Removed validateCurrentAuth from dependencies to prevent infinite loop

  // Load data effect - only runs after auth validation is complete
  useEffect(() => {
    if (!authValidated) {
      console.log("⏳ Auth validation not complete yet, waiting...");
      return;
    }

    if (authValid && user) {
      console.log("✅ Loading profile and settings data...");
      // Load data immediately without setTimeout to avoid race conditions
      loadUserProfile();
      loadUserSettings();
    } else {
      console.log("❌ Auth not valid or no user, skipping data load");
    }
  }, [authValidated, authValid, user]); // Removed unnecessary dependencies

  // Lade vollständige Profildaten vom Backend
  const loadUserProfile = async (retryCount = 0) => {
    console.log("🔄 loadUserProfile called");

    // Auth validation has already been done in useEffect
    if (!authValid || !user) {
      console.log("❌ loadUserProfile: Auth not valid or no user, aborting");
      return;
    }

    setIsLoadingProfile(true);
    try {
      console.log("📡 loadUserProfile: Making API call...");
      const response = await userService.getProfile();
      console.log("📡 loadUserProfile: API response:", response);

      if (response.success && response.data) {
        setProfile(prev => ({
          ...prev,
          bio: response.data?.bio || "",
          avatar: response.data?.avatar || prev.avatar // Avatar aktualisieren
        }));
        console.log("✅ loadUserProfile: Profile updated successfully");
      } else {
        console.error("⚠️ loadUserProfile: API-Fehler:", response.message);

        // Verbesserte Fehlerbehandlung
        if (response.error === 'UNAUTHORIZED' || response.message?.includes('403') || response.message?.includes('Nicht authentifiziert')) {
          console.log("🚪 loadUserProfile: Authentifizierungsfehler erkannt");

          // Erhöhe Retry-Counter und versuche maximal 1 Mal erneut
          if (retryCount < 1) {
            console.log("🔄 Versuche API-Call erneut...");
            await new Promise(resolve => setTimeout(resolve, 1000)); // Kurze Pause
            await loadUserProfile(retryCount + 1);
            return;
          } else {
            console.log("🚪 Maximale Retry-Anzahl erreicht, leite zur Login-Seite um");
            alert("Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an.");
            window.location.reload();
          }
        } else {
          console.error("⚠️ loadUserProfile: Anderer API-Fehler:", response.message);
          alert(`Fehler beim Laden des Profils: ${response.message}`);
        }
      }
    } catch (error) {
      console.error("❌ loadUserProfile: Unerwarteter Fehler:", error);
      alert("Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.");
    } finally {
      setIsLoadingProfile(false);
    }
  };

  // Kombiniere firstName und lastName zu einem vollständigen Namen
  const combineName = (firstName?: string, lastName?: string): string => {
    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    }
    return firstName || lastName || "";
  };

  // Lade Benutzereinstellungen vom Backend
  const loadUserSettings = async (retryCount = 0) => {
    console.log("🔄 loadUserSettings called");

    // Auth validation has already been done in useEffect
    if (!authValid || !user) {
      console.log("❌ loadUserSettings: Auth not valid or no user, aborting");
      return;
    }

    setIsLoadingSettings(true);
    try {
      console.log("📡 loadUserSettings: Making API call...");
      const response = await userService.getSettings();
      console.log("📡 loadUserSettings: API response:", response);

      if (response.success && response.data) {
        const settings = response.data;

        // Aktualisiere Benachrichtigungseinstellungen
        if (settings.notifications) {
          setNotifications(prev => ({
            ...prev,
            ...settings.notifications
          }));
        }

        // Aktualisiere Erscheinungsbild-Einstellungen
        if (settings.theme) setAppearance(prev => ({ ...prev, theme: settings.theme! }));
        if (settings.language) setAppearance(prev => ({ ...prev, language: settings.language! }));
        if (settings.fontSize) setAppearance(prev => ({ ...prev, fontSize: settings.fontSize! }));
        if (settings.highContrast !== undefined) setAppearance(prev => ({ ...prev, highContrast: settings.highContrast! }));
        if (settings.animations !== undefined) setAppearance(prev => ({ ...prev, animations: settings.animations! }));
        if (settings.compactMode !== undefined) setAppearance(prev => ({ ...prev, compactMode: settings.compactMode! }));
        console.log("✅ loadUserSettings: Settings updated successfully");
      } else {
        console.error("⚠️ loadUserSettings: API-Fehler:", response.message);

        // Verbesserte Fehlerbehandlung
        if (response.error === 'UNAUTHORIZED' || response.message?.includes('403') || response.message?.includes('Nicht authentifiziert')) {
          console.log("🚪 loadUserSettings: Authentifizierungsfehler erkannt");

          // Erhöhe Retry-Counter und versuche maximal 1 Mal erneut
          if (retryCount < 1) {
            console.log("🔄 Versuche API-Call erneut...");
            await new Promise(resolve => setTimeout(resolve, 1000)); // Kurze Pause
            await loadUserSettings(retryCount + 1);
            return;
          } else {
            console.log("🚪 Maximale Retry-Anzahl erreicht, leite zur Login-Seite um");
            alert("Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an.");
            window.location.reload();
          }
        } else {
          console.error("⚠️ loadUserSettings: Anderer API-Fehler:", response.message);
          alert(`Fehler beim Laden der Einstellungen: ${response.message}`);
        }
      }
    } catch (error) {
      console.error("❌ loadUserSettings: Unerwarteter Fehler:", error);
      alert("Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.");
    } finally {
      setIsLoadingSettings(false);
    }
  };

  // Handler für Tab-Wechsel
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  // Handler für Profiländerungen
  const handleProfileChange = (field: string, value: string) => {
    setProfile(prev => ({ ...prev, [field]: value }));
  };

  // Handler für Benachrichtigungsänderungen
  const handleNotificationChange = (field: string, value: boolean) => {
    setNotifications(prev => ({ ...prev, [field]: value }));
  };

  // Handler für Erscheinungsbildänderungen
  const handleAppearanceChange = (field: string, value: string | boolean) => {
    setAppearance(prev => ({ ...prev, [field]: value }));
  };

  // Handler für Avatar-Auswahl
  const handleAvatarSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedAvatar(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarPreview(e.target?.result as string);
        setShowAvatarModal(true);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handler für Avatar-Upload
  const handleAvatarUpload = async (retryCount = 0) => {
    if (!selectedAvatar) return;

    // Use the validated auth state
    if (!authValid || !user) {
      alert("Sie müssen angemeldet sein, um Ihren Avatar hochzuladen.");
      return;
    }

    setIsUploadingAvatar(true);
    try {
      console.log("📡 handleAvatarUpload: Making API call...");
      const response = await userService.uploadAvatar(selectedAvatar);
      console.log("📡 handleAvatarUpload: API response:", response);

      if (response.success && response.data?.avatar) {
        alert("Avatar erfolgreich hochgeladen!");
        setSelectedAvatar(null);
        setAvatarPreview(null);
        // Profil mit neuem Avatar aktualisieren
        setProfile(prev => ({
          ...prev,
          avatar: response.data!.avatar
        }));
        // useAuth Hook aktualisieren, damit Avatar auch in Navbar angezeigt wird
        updateUser({ avatar: response.data!.avatar });
        // Aktualisiere lokalen State statt Backend-Call
        setProfile(prev => ({
          ...prev,
          avatar: response.data!.avatar
        }));
      } else {
        console.error("⚠️ handleAvatarUpload: API-Fehler:", response.message);

        // Verbesserte Fehlerbehandlung
        if (response.error === 'UNAUTHORIZED' || response.message?.includes('403') || response.message?.includes('Nicht authentifiziert')) {
          console.log("🚪 handleAvatarUpload: Authentifizierungsfehler erkannt");

          // Erhöhe Retry-Counter und versuche maximal 1 Mal erneut
          if (retryCount < 1) {
            console.log("🔄 Versuche Avatar-Upload erneut...");
            await new Promise(resolve => setTimeout(resolve, 1000)); // Kurze Pause
            await handleAvatarUpload(retryCount + 1);
            return;
          } else {
            console.log("🚪 Maximale Retry-Anzahl erreicht");
            alert("Sie sind nicht berechtigt, Ihren Avatar hochzuladen. Bitte melden Sie sich erneut an.");
            window.location.reload();
          }
        } else {
          alert(`Fehler beim Avatar-Upload: ${response.message}`);
        }
      }
    } catch (error) {
      console.error("Fehler beim Avatar-Upload:", error);
      alert("Fehler beim Avatar-Upload. Bitte versuchen Sie es erneut.");
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  // Handler für Avatar-Löschung
  const handleDeleteAvatar = async () => {
    // Use the validated auth state
    if (!authValid || !user) {
      alert("Sie müssen angemeldet sein, um Ihren Avatar zu entfernen.");
      return;
    }

    if (!confirm("Möchten Sie Ihren Avatar wirklich entfernen?")) return;

    try {
      console.log("📡 handleDeleteAvatar: Making API call...");
      const response = await userService.deleteAvatar();
      console.log("📡 handleDeleteAvatar: API response:", response);

      if (response.success) {
        alert("Avatar erfolgreich entfernt!");
        // Profil mit entferntem Avatar aktualisieren
        setProfile(prev => ({
          ...prev,
          avatar: ""
        }));
        // useAuth Hook aktualisieren, damit Avatar auch in Navbar entfernt wird
        updateUser({ avatar: "" });
        // Aktualisiere lokalen State statt Backend-Call
        setProfile(prev => ({
          ...prev,
          avatar: ""
        }));
      } else {
        alert(`Fehler beim Entfernen des Avatars: ${response.message}`);
        // Bei 403-Fehler spezielle Behandlung
        if (response.error === 'UNAUTHORIZED' || response.message.includes('403')) {
          alert("Sie sind nicht berechtigt, Ihren Avatar zu entfernen. Bitte melden Sie sich erneut an.");
          window.location.reload();
        }
      }
    } catch (error) {
      console.error("Fehler beim Avatar-Löschen:", error);
      alert("Fehler beim Entfernen des Avatars. Bitte versuchen Sie es erneut.");
    }
  };

  // Handler für Avatar-Modal schließen
  const handleCloseAvatarModal = () => {
    setShowAvatarModal(false);
    setSelectedAvatar(null);
    setAvatarPreview(null);
  };

  // Handler für Avatar-Upload bestätigen
  const handleConfirmAvatarUpload = async () => {
    if (!selectedAvatar) return;

    // Use the validated auth state
    if (!authValid || !user) {
      alert("Sie müssen angemeldet sein, um Ihren Avatar hochzuladen.");
      setShowAvatarModal(false);
      return;
    }

    setIsUploadingAvatar(true);
    try {
      console.log("📡 handleConfirmAvatarUpload: Making API call...");
      const response = await userService.uploadAvatar(selectedAvatar);
      console.log("📡 handleConfirmAvatarUpload: API response:", response);

      if (response.success && response.data?.avatar) {
        alert("Avatar erfolgreich hochgeladen!");
        setSelectedAvatar(null);
        setAvatarPreview(null);
        setShowAvatarModal(false);
        // Profil mit neuem Avatar aktualisieren
        setProfile(prev => ({
          ...prev,
          avatar: response.data!.avatar
        }));
        // useAuth Hook aktualisieren, damit Avatar auch in Navbar angezeigt wird
        updateUser({ avatar: response.data!.avatar });
        // Aktualisiere lokalen State statt Backend-Call
        setProfile(prev => ({
          ...prev,
          avatar: response.data!.avatar
        }));
      } else {
        alert(`Fehler beim Avatar-Upload: ${response.message}`);
        setShowAvatarModal(false);
        // Bei 403-Fehler spezielle Behandlung
        if (response.error === 'UNAUTHORIZED' || response.message.includes('403')) {
          alert("Sie sind nicht berechtigt, Ihren Avatar hochzuladen. Bitte melden Sie sich erneut an.");
          window.location.reload();
        }
      }
    } catch (error) {
      console.error("Fehler beim Avatar-Upload:", error);
      alert("Fehler beim Avatar-Upload. Bitte versuchen Sie es erneut.");
      setShowAvatarModal(false);
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  // Handler für Formularübermittlungen
  const handleSubmit = async (formType: string, retryCount = 0) => {
    // Use the validated auth state
    if (!authValid || !user) {
      alert("Sie müssen angemeldet sein, um Einstellungen zu speichern.");
      return;
    }

    try {
      if (formType === "profile") {
        console.log("📡 handleSubmit: Making profile update API call...");
        const response = await userService.updateProfile({
          username: profile.username,
          email: profile.email,
          name: profile.name,
          bio: profile.bio
        });
        console.log("📡 handleSubmit: Profile update response:", response);

        if (response.success) {
          alert("Profil erfolgreich gespeichert!");
        } else {
          console.error("⚠️ handleSubmit: Profile update error:", response.message);

          // Verbesserte Fehlerbehandlung
          if (response.error === 'UNAUTHORIZED' || response.message?.includes('403') || response.message?.includes('Nicht authentifiziert')) {
            console.log("🚪 handleSubmit: Authentifizierungsfehler erkannt");

            // Erhöhe Retry-Counter und versuche maximal 1 Mal erneut
            if (retryCount < 1) {
              console.log("🔄 Versuche Profil-Update erneut...");
              await new Promise(resolve => setTimeout(resolve, 1000)); // Kurze Pause
              await handleSubmit(formType, retryCount + 1);
              return;
            } else {
              console.log("🚪 Maximale Retry-Anzahl erreicht");
              alert("Sie sind nicht berechtigt, Ihr Profil zu aktualisieren. Bitte melden Sie sich erneut an.");
              window.location.reload();
            }
          } else {
            alert(`Fehler: ${response.message}`);
          }
        }
      } else if (formType === "notifications") {
        console.log("📡 handleSubmit: Making notifications update API call...");
        const response = await userService.updateSettings({
          notifications: notifications
        });
        console.log("📡 handleSubmit: Notifications update response:", response);

        if (response.success) {
          alert("Benachrichtigungseinstellungen erfolgreich gespeichert!");
        } else {
          console.error("⚠️ handleSubmit: Notifications update error:", response.message);

          // Verbesserte Fehlerbehandlung
          if (response.error === 'UNAUTHORIZED' || response.message?.includes('403') || response.message?.includes('Nicht authentifiziert')) {
            console.log("🚪 handleSubmit: Authentifizierungsfehler erkannt");

            // Erhöhe Retry-Counter und versuche maximal 1 Mal erneut
            if (retryCount < 1) {
              console.log("🔄 Versuche Benachrichtigungs-Update erneut...");
              await new Promise(resolve => setTimeout(resolve, 1000)); // Kurze Pause
              await handleSubmit(formType, retryCount + 1);
              return;
            } else {
              console.log("🚪 Maximale Retry-Anzahl erreicht");
              alert("Sie sind nicht berechtigt, Ihre Benachrichtigungseinstellungen zu aktualisieren. Bitte melden Sie sich erneut an.");
              window.location.reload();
            }
          } else {
            alert(`Fehler: ${response.message}`);
          }
        }
      } else if (formType === "appearance") {
        console.log("📡 handleSubmit: Making appearance update API call...");
        const response = await userService.updateSettings({
          theme: appearance.theme,
          language: appearance.language,
          fontSize: appearance.fontSize,
          highContrast: appearance.highContrast,
          animations: appearance.animations,
          compactMode: appearance.compactMode
        });
        console.log("📡 handleSubmit: Appearance update response:", response);

        if (response.success) {
          alert("Erscheinungsbild-Einstellungen erfolgreich gespeichert!");
        } else {
          console.error("⚠️ handleSubmit: Appearance update error:", response.message);

          // Verbesserte Fehlerbehandlung
          if (response.error === 'UNAUTHORIZED' || response.message?.includes('403') || response.message?.includes('Nicht authentifiziert')) {
            console.log("🚪 handleSubmit: Authentifizierungsfehler erkannt");

            // Erhöhe Retry-Counter und versuche maximal 1 Mal erneut
            if (retryCount < 1) {
              console.log("🔄 Versuche Erscheinungsbild-Update erneut...");
              await new Promise(resolve => setTimeout(resolve, 1000)); // Kurze Pause
              await handleSubmit(formType, retryCount + 1);
              return;
            } else {
              console.log("🚪 Maximale Retry-Anzahl erreicht");
              alert("Sie sind nicht berechtigt, Ihre Erscheinungsbild-Einstellungen zu aktualisieren. Bitte melden Sie sich erneut an.");
              window.location.reload();
            }
          } else {
            alert(`Fehler: ${response.message}`);
          }
        }
      }
    } catch (error) {
      console.error(`Fehler beim Speichern von ${formType}:`, error);
      alert(`Fehler beim Speichern der ${formType}-Einstellungen.`);
    }
  };

  // Loading state - also wait for auth validation and data loading
  if (isLoading || !authValidated || isLoadingProfile || isLoadingSettings) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">
          {!authValidated ? "Validiere Authentifizierung..." :
           isLoadingProfile || isLoadingSettings ? "Lade Daten..." :
           "Lade Benutzereinstellungen..."}
        </div>
      </div>
    );
  }

  // Redirect if not authenticated or token is invalid
  if (!user || !isAuthenticated) {
    console.log("🚪 UserSettingsPage: User not authenticated, showing login message");
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-lg">Sie müssen angemeldet sein, um auf diese Seite zuzugreifen.</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Erneut versuchen
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-bg min-h-screen p-8">
      <div className="max-w-5xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between w-full mb-8">
            <div className="flex-1">
              <h1 className="text-4xl font-heading tracking-tight text-text flex items-center gap-2">
              <User className="h-8 w-8" />
              Benutzereinstellungen
            </h1>
            <p className="text-lg text-text font-base mt-2 opacity-70">Verwalte deine Konto- und Präferenzeinstellungen</p>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="space-y-6">
          <div className="flex justify-center">
            <div className="w-full max-w-4xl">
              <SmoothTab
                items={[
                   {
                      id: "profile",
                      title: "Profil",
                      icon: User,
                      color: "bg-blue-500 hover:bg-blue-600",
                    },
                    {
                      id: "notifications",
                      title: "Benachrichtigungen",
                      icon: Bell,
                      color: "bg-green-500 hover:bg-green-600",
                    },
                    {
                      id: "appearance",
                      title: "Erscheinungsbild",
                      icon: Palette,
                      color: "bg-orange-500 hover:bg-orange-600",
                    },
                    {
                      id: "security",
                      title: "Sicherheit",
                      icon: Shield,
                      color: "bg-red-500 hover:bg-red-600",
                    },
                 ]}
                defaultTabId="profile"
                onChange={handleTabChange}
                hideCardContent={true}
                className="w-full"
              />
            </div>
          </div>

          {/* Tab Content */}
          <div className="space-y-8 w-full">
            {/* Profil Tab */}
            {activeTab === "profile" && (
              <Card className="group border-blue-500/10 hover:border-blue-500/30 overflow-hidden transition-all duration-500 relative bg-blue-50/30 dark:bg-blue-950/10">
                <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>
                <div className="text-blue-500/50 group-hover:text-blue-500 absolute right-1 bottom-3 scale-[6] transition-all duration-700 group-hover:scale-[6.2]">
                  <User className="size-6" />
                </div>
                <div className="from-blue-500 to-blue-500/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
                <CardHeader className="relative z-10">
                  <CardTitle className="flex items-center gap-2">
                    <div className="bg-blue-500/10 text-blue-500 shadow-blue-500/10 group-hover:bg-blue-500/20 group-hover:shadow-blue-500/20 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-500">
                      <User className="h-8 w-8" />
                    </div>
                    Profil
                  </CardTitle>
                  <CardDescription>
                    Verwalte deine persönlichen Profildetails.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 px-8 py-6 relative z-20">
                  <div className="flex items-center space-x-6 relative z-30">
                    <div className="relative">
                      <Avatar className="h-16 w-16 relative z-40">
                        <AvatarImage src={avatarPreview || profile.avatar} />
                        <AvatarFallback className="text-lg">
                          {(profile.name || profile.username).charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <input
                        id="avatar-input"
                        type="file"
                        accept="image/*"
                        onChange={handleAvatarSelect}
                        className="hidden"
                      />
                    </div>
                    <div className="relative z-40">
                      <h3 className="text-lg font-medium">{profile.name || profile.username}</h3>
                      <p className="text-sm text-gray-500">{profile.email}</p>
                      <p className="text-sm text-gray-500">Rolle: {profile.role}</p>
                      <div className="flex flex-col gap-2 mt-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => document.getElementById('avatar-input')?.click()}
                          className="w-fit"
                        >
                          <Camera className="h-4 w-4 mr-2" />
                          Upload Image
                        </Button>
                        {profile.avatar && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleDeleteAvatar}
                            className="text-red-500 hover:text-red-700 p-0 h-auto w-fit"
                          >
                            <X className="h-3 w-3 mr-1" />
                            Avatar entfernen
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Avatar Upload Modal */}
                  {showAvatarModal && selectedAvatar && avatarPreview && (
                    <div
                      className="fixed inset-0 flex items-center justify-center z-50"
                      style={{
                        backgroundColor: 'rgba(0, 0, 0, 0.05)'
                      }}
                      onClick={handleCloseAvatarModal}
                    >
                      <div
                        className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-2xl border border-gray-200 dark:border-gray-700"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <h3 className="text-lg font-semibold mb-4">Avatar bestätigen</h3>
                        <div className="flex flex-col items-center space-y-4">
                          <img
                            src={avatarPreview}
                            alt="Avatar Preview"
                            className="h-24 w-24 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
                          />
                          <div className="text-center">
                            <p className="text-sm font-medium">Neuer Avatar ausgewählt</p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">{selectedAvatar.name}</p>
                          </div>
                          <div className="flex gap-3 w-full">
                            <Button
                              onClick={handleCloseAvatarModal}
                              variant="outline"
                              className="flex-1"
                              disabled={isUploadingAvatar}
                            >
                              Abbrechen
                            </Button>
                            <Button
                              onClick={handleConfirmAvatarUpload}
                              disabled={isUploadingAvatar}
                              className="flex-1"
                            >
                              {isUploadingAvatar ? (
                                "Wird hochgeladen..."
                              ) : (
                                <>
                                  <Upload className="h-4 w-4 mr-2" />
                                  Bestätigen
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <Separator />

                  <div className="grid gap-6 w-full relative z-20">
                    <div className="grid gap-2 relative z-30">
                      <Label htmlFor="username">Benutzername</Label>
                      <Input
                        id="username"
                        value={profile.username}
                        onChange={(e) => handleProfileChange('username', e.target.value)}
                        className="relative z-40"
                      />
                    </div>

                    <div className="grid gap-2 relative z-30">
                      <Label htmlFor="email">E-Mail-Adresse</Label>
                      <Input
                        id="email"
                        type="email"
                        value={profile.email}
                        onChange={(e) => handleProfileChange('email', e.target.value)}
                        className="relative z-40"
                      />
                    </div>

                    <div className="grid gap-2 relative z-30">
                      <Label htmlFor="name">Vollständiger Name</Label>
                      <Input
                        id="name"
                        value={profile.name}
                        onChange={(e) => handleProfileChange('name', e.target.value)}
                        className="relative z-40"
                      />
                    </div>

                    <div className="grid gap-2 relative z-30">
                      <Label htmlFor="bio">Bio</Label>
                      <Textarea
                        id="bio"
                        placeholder="Erzähle etwas über dich..."
                        value={profile.bio}
                        onChange={(e) => handleProfileChange('bio', e.target.value)}
                        rows={3}
                        className="relative z-40"
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="relative z-20">
                  <Button onClick={() => handleSubmit('profile')} className="ml-auto relative z-40">
                    <Save className="h-4 w-4 mr-2" />
                    Profil speichern
                  </Button>
                </CardFooter>
              </Card>
            )}

            {/* Benachrichtigungen Tab */}
            {activeTab === "notifications" && (
              <Card className="group border-green-500/10 hover:border-green-500/30 overflow-hidden transition-all duration-500 relative bg-green-50/30 dark:bg-green-950/10">
                <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>
                <div className="text-green-500/5 group-hover:text-green-500/10 absolute right-1 bottom-3 scale-[6] transition-all duration-700 group-hover:scale-[6.2]">
                  <Bell className="size-6" />
                </div>
                <div className="from-green-500 to-green-500/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
                <CardHeader className="relative z-10">
                  <CardTitle className="flex items-center gap-2">
                    <div className="bg-green-500/10 text-green-500 shadow-green-500/10 group-hover:bg-green-500/20 group-hover:shadow-green-500/20 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-500">
                      <Bell className="h-8 w-8" />
                    </div>
                    Benachrichtigungen
                  </CardTitle>
                  <CardDescription>
                    Verwalte deine Benachrichtigungseinstellungen und Präferenzen.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 px-8 py-6 relative z-20">
                  <div className="space-y-6 w-full">
                    <div className="flex items-center justify-between relative z-30">
                      <div className="space-y-0.5">
                        <Label htmlFor="email-notifications">E-Mail-Benachrichtigungen</Label>
                        <p className="text-sm text-gray-500">Erhalte wichtige Updates per E-Mail</p>
                      </div>
                      <Switch
                        id="email-notifications"
                        checked={notifications.emailNotifications}
                        onCheckedChange={(checked: boolean) => handleNotificationChange('emailNotifications', checked)}
                        className="relative z-40"
                      />
                    </div>

                    <div className="flex items-center justify-between relative z-30">
                      <div className="space-y-0.5">
                        <Label htmlFor="push-notifications">Push-Benachrichtigungen</Label>
                        <p className="text-sm text-gray-500">Erhalte sofortige Benachrichtigungen</p>
                      </div>
                      <Switch
                        id="push-notifications"
                        checked={notifications.pushNotifications}
                        onCheckedChange={(checked: boolean) => handleNotificationChange('pushNotifications', checked)}
                        className="relative z-40"
                      />
                    </div>

                    <div className="flex items-center justify-between relative z-30">
                      <div className="space-y-0.5">
                        <Label htmlFor="desktop-notifications">Desktop-Benachrichtigungen</Label>
                        <p className="text-sm text-gray-500">Benachrichtigungen auf dem Desktop anzeigen</p>
                      </div>
                      <Switch
                        id="desktop-notifications"
                        checked={notifications.desktopNotifications}
                        onCheckedChange={(checked: boolean) => handleNotificationChange('desktopNotifications', checked)}
                        className="relative z-40"
                      />
                    </div>

                    <div className="flex items-center justify-between relative z-30">
                      <div className="space-y-0.5">
                        <Label htmlFor="sound-notifications">Ton-Benachrichtigungen</Label>
                        <p className="text-sm text-gray-500">Akustische Signale bei Benachrichtigungen</p>
                      </div>
                      <Switch
                        id="sound-notifications"
                        checked={notifications.soundNotifications}
                        onCheckedChange={(checked: boolean) => handleNotificationChange('soundNotifications', checked)}
                        className="relative z-40"
                      />
                    </div>

                    <Separator />

                    <h3 className="text-lg font-medium">Benachrichtigungstypen</h3>

                    <div className="flex items-center justify-between relative z-30">
                      <Label htmlFor="new-messages">Neue Nachrichten</Label>
                      <Switch
                        id="new-messages"
                        checked={notifications.newMessages}
                        onCheckedChange={(checked: boolean) => handleNotificationChange('newMessages', checked)}
                        className="relative z-40"
                      />
                    </div>

                    <div className="flex items-center justify-between relative z-30">
                      <Label htmlFor="new-mentions">Erwähnungen</Label>
                      <Switch
                        id="new-mentions"
                        checked={notifications.newMentions}
                        onCheckedChange={(checked: boolean) => handleNotificationChange('newMentions', checked)}
                        className="relative z-40"
                      />
                    </div>

                    <div className="flex items-center justify-between relative z-30">
                      <Label htmlFor="new-orders">Neue Bestellungen</Label>
                      <Switch
                        id="new-orders"
                        checked={notifications.newOrders}
                        onCheckedChange={(checked: boolean) => handleNotificationChange('newOrders', checked)}
                        className="relative z-40"
                      />
                    </div>

                    <div className="flex items-center justify-between relative z-30">
                      <Label htmlFor="new-alerts">Neue Warnungen</Label>
                      <Switch
                        id="new-alerts"
                        checked={notifications.newAlerts}
                        onCheckedChange={(checked: boolean) => handleNotificationChange('newAlerts', checked)}
                        className="relative z-40"
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={() => handleSubmit('notifications')} className="ml-auto">
                    <Save className="h-4 w-4 mr-2" />
                    Einstellungen speichern
                  </Button>
                </CardFooter>
              </Card>
            )}

            {/* Erscheinungsbild Tab */}
            {activeTab === "appearance" && (
              <Card className="group border-orange-500/10 hover:border-orange-500/30 overflow-hidden transition-all duration-500 relative bg-orange-50/30 dark:bg-orange-950/10">
                <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>
                <div className="text-orange-500/5 group-hover:text-orange-500/10 absolute right-1 bottom-3 scale-[6] transition-all duration-700 group-hover:scale-[6.2]">
                  <Palette className="size-6" />
                </div>
                <div className="from-orange-500 to-orange-500/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
                <CardHeader className="relative z-10">
                  <CardTitle className="flex items-center gap-2">
                    <div className="bg-orange-500/10 text-orange-500 shadow-orange-500/10 group-hover:bg-orange-500/20 group-hover:shadow-orange-500/20 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-500">
                      <Palette className="h-5 w-5" />
                    </div>
                    Darstellung
                  </CardTitle>
                  <CardDescription>
                    Passe die Darstellung der Anwendung an.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 px-8 py-6 relative z-20">
                  <div className="space-y-6 w-full">
                    <div className="grid gap-2 relative z-30">
                      <Label htmlFor="theme">Theme</Label>
                      <Select value={appearance.theme} onValueChange={(value) => handleAppearanceChange('theme', value)}>
                        <SelectTrigger className="relative z-40">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="relative z-50">
                          <SelectItem value="light">Hell</SelectItem>
                          <SelectItem value="dark">Dunkel</SelectItem>
                          <SelectItem value="system">System</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid gap-2 relative z-30">
                      <Label htmlFor="language">Sprache</Label>
                      <Select value={appearance.language} onValueChange={(value) => handleAppearanceChange('language', value)}>
                        <SelectTrigger className="relative z-40">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="relative z-50">
                          <SelectItem value="de">Deutsch</SelectItem>
                          <SelectItem value="en">English</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid gap-2 relative z-30">
                      <Label htmlFor="font-size">Schriftgröße</Label>
                      <Select value={appearance.fontSize} onValueChange={(value) => handleAppearanceChange('fontSize', value)}>
                        <SelectTrigger className="relative z-40">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="relative z-50">
                          <SelectItem value="small">Klein</SelectItem>
                          <SelectItem value="medium">Mittel</SelectItem>
                          <SelectItem value="large">Groß</SelectItem>
                          <SelectItem value="extra-large">Sehr groß</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <Separator />

                    <div className="flex items-center justify-between relative z-30">
                      <div className="space-y-0.5">
                        <Label htmlFor="high-contrast">Hoher Kontrast</Label>
                        <p className="text-sm text-gray-500">Erhöhter Kontrast für bessere Lesbarkeit</p>
                      </div>
                      <Switch
                        id="high-contrast"
                        checked={appearance.highContrast}
                        onCheckedChange={(checked: boolean) => handleAppearanceChange('highContrast', checked)}
                        className="relative z-40"
                      />
                    </div>

                    <div className="flex items-center justify-between relative z-30">
                      <div className="space-y-0.5">
                        <Label htmlFor="animations">Animationen</Label>
                        <p className="text-sm text-gray-500">Animationen und Übergänge aktivieren</p>
                      </div>
                      <Switch
                        id="animations"
                        checked={appearance.animations}
                        onCheckedChange={(checked: boolean) => handleAppearanceChange('animations', checked)}
                        className="relative z-40"
                      />
                    </div>

                    <div className="flex items-center justify-between relative z-30">
                      <div className="space-y-0.5">
                        <Label htmlFor="compact-mode">Kompakter Modus</Label>
                        <p className="text-sm text-gray-500">Reduzierte Abstände für mehr Inhalt</p>
                      </div>
                      <Switch
                        id="compact-mode"
                        checked={appearance.compactMode}
                        onCheckedChange={(checked: boolean) => handleAppearanceChange('compactMode', checked)}
                        className="relative z-40"
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={() => handleSubmit('appearance')} className="ml-auto">
                    <Save className="h-4 w-4 mr-2" />
                    Einstellungen speichern
                  </Button>
                </CardFooter>
              </Card>
            )}

            {/* Sicherheit Tab */}
            {activeTab === "security" && (
              <Card className="group border-red-500/10 hover:border-red-500/30 overflow-hidden transition-all duration-500 relative bg-red-50/30 dark:bg-red-950/10">
                <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>
                <div className="text-red-500/5 group-hover:text-red-500/10 absolute right-1 bottom-3 scale-[6] transition-all duration-700 group-hover:scale-[6.2]">
                  <Shield className="size-6" />
                </div>
                <div className="from-red-500 to-red-500/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
                <CardHeader className="relative z-10">
                  <CardTitle className="flex items-center gap-2">
                    <div className="bg-red-500/10 text-red-500 shadow-red-500/10 group-hover:bg-red-500/20 group-hover:shadow-red-500/20 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-500">
                      <Shield className="h-5 w-5" />
                    </div>
                    Sicherheit
                  </CardTitle>
                  <CardDescription>
                    Verwalte deine Sicherheitseinstellungen und Zugangsdaten.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Passwort ändern</h3>

                    <div className="grid gap-6 w-full relative z-20">
                      <div className="grid gap-2 relative z-30">
                        <Label htmlFor="current-password">Aktuelles Passwort</Label>
                        <Input id="current-password" type="password" className="relative z-40" />
                      </div>

                      <div className="grid gap-2 relative z-30">
                        <Label htmlFor="new-password">Neues Passwort</Label>
                        <Input id="new-password" type="password" className="relative z-40" />
                      </div>

                      <Button variant="outline" className="w-auto relative z-40">
                        Passwort ändern
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
