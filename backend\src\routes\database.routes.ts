/**
 * Datenbank-API-Routen
 *
 * Diese Routen stellen die Datenbankfunktionen als REST-API zur Verfügung.
 * Das Frontend kann diese API verwenden, um auf die Datenbank zuzugreifen.
 */

import express from "express";
import { DatabaseService } from "../services/database.service";
import { ApiResponse } from "../types/database.types";
import {
  validators,
  sanitizationMiddleware,
} from "../middleware/validation.middleware";
import { rateLimitConfig } from "../middleware/rate-limiting.middleware";
import {
  getDispatchRepository,
  getWarehouseRepository,
  getCuttingRepository,
} from "../repositories/repository.factory";

const router = express.Router();

// Debug-Ausgabe beim Laden der Routen
console.log("[ROUTES] Lade Database-Routen...");

// DatabaseService instanziieren mit Error-Handling
let databaseService: DatabaseService;
try {
  databaseService = new DatabaseService();
  console.log("[ROUTES] DatabaseService erfolgreich initialisiert");
} catch (error) {
  console.error(
    "[ROUTES] Fehler beim Initialisieren des DatabaseService:",
    error,
  );
  throw error;
}

// WICHTIG: Router-Debugging
router.use((req, res, next) => {
  console.log(
    `[ROUTES] Router erreicht: ${req.method} ${req.originalUrl} ${req.path}`,
  );
  next();
});

// Basis-Middleware für alle Database-Routen
router.use(sanitizationMiddleware); // Eingabe-Sanitization
router.use(rateLimitConfig.dataEndpoint); // Rate-Limiting für Daten-Endpunkte

/**
 * Hilfsfunktion zum Verarbeiten von Anfragen und Fehlerbehandlung
 */
async function handleRequest<T>(
  req: express.Request,
  res: express.Response,
  operation: () => Promise<T>,
): Promise<void> {
  try {
    console.log(
      `[ROUTES] Verarbeite Anfrage: ${req.method} ${req.originalUrl}`,
    );
    const result = await operation();
    const response: ApiResponse<T> = {
      success: true,
      data: result,
    };
    console.log(`[ROUTES] Erfolgreiche Antwort für ${req.originalUrl}`);
    res.json(response);
  } catch (error) {
    console.error(`[ROUTES] API-Fehler für ${req.originalUrl}:`, error);
    const response: ApiResponse<T> = {
      success: false,
      error: error instanceof Error ? error.message : "Unbekannter Fehler",
    };
    res.status(500).json(response);
  }
}

// Test-Route (vereinfacht)
router.get("/test", (req, res) => {
  console.log("[ROUTES] Test-Route aufgerufen!");
  res.json({
    message: "Database-Routen funktionieren!",
    timestamp: new Date().toISOString(),
    path: req.path,
    originalUrl: req.originalUrl,
  });
});

// Testroute für Ablaengerei-Daten (direkt über Repository)
router.get("/test-ablaengerei", async (req, res) => {
  console.log("[ROUTES] Test-Ablaengerei-Route aufgerufen!");
  try {
    // Zugriff über das Repository statt direkt auf die Datenbank
    const cuttingRepo = getCuttingRepository();
    const ablaengereiData = await cuttingRepo.getAblaengereiData();

    res.json({
      success: true,
      message: "Ablaengerei-Tabelle Statistik",
      count: ablaengereiData.length,
      firstFew: ablaengereiData.slice(0, 3),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error(
      "[ROUTES] Fehler beim Zugriff auf Ablaengerei-Tabelle:",
      error,
    );
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : "Unbekannter Fehler",
      timestamp: new Date().toISOString(),
    });
  }
});

// Service Level Daten
router.get("/service-level", async (req, res) => {
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
  await handleRequest(req, res, () =>
    getDispatchRepository().getServiceLevelData(dateRange),
  );
});

// Tägliche Leistungsdaten
router.get("/daily-performance", async (req, res) => {
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
  await handleRequest(req, res, () =>
    getDispatchRepository().getDailyPerformanceData(dateRange),
  );
});

// Kommissionierungsdaten
router.get("/picking", async (req, res) => {
  console.log("[ROUTES] Picking-Route aufgerufen!");
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  console.log(
    `[ROUTES] Picking Datumsbereich: ${startDate || "nicht angegeben"} bis ${endDate || "nicht angegeben"}`,
  );
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
  await handleRequest(req, res, () =>
    getDispatchRepository().getPickingData(dateRange),
  );
});

// Retourendaten
router.get("/returns", async (req, res) => {
  await handleRequest(req, res, () => getDispatchRepository().getReturnsData());
});

// Lieferpositionsdaten
router.get("/delivery-positions", async (req, res) => {
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
  await handleRequest(req, res, () =>
    getDispatchRepository().getDeliveryPositionsData(dateRange),
  );
});

// Tagesleistungsdaten
router.get("/tagesleistung", async (req, res) => {
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
  await handleRequest(req, res, () =>
    getDispatchRepository().getTagesleistungData(dateRange),
  );
});

// Importiert Ablaengerei-CSV-Daten (mit strengerem Rate-Limiting)
router.post(
  "/import-ablaengerei-csv",
  rateLimitConfig.writeOperation,
  validators.csvImport,
  async (req, res) => {
    await handleRequest(req, res, () =>
      databaseService.importAblaengereiCsvData(),
    );
  },
);

// Ablaengerei-Daten
router.get("/ablaengerei", async (req, res) => {
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
  await handleRequest(req, res, () =>
    getCuttingRepository().getAblaengereiData(dateRange),
  );
});

// WE-Daten (Wareneingang)
router.get("/we", async (req, res) => {
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
  await handleRequest(req, res, () =>
    getCuttingRepository().getWEData(dateRange),
  );
});

// Lagerauslastung200-Daten
router.get("/lagerauslastung200", validators.dateRange, async (req, res) => {
  // Datumsparameter aus der validierten Anfrage extrahieren
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;

  // Parameter an das Warehouse Repository übergeben
  await handleRequest(req, res, () =>
    getWarehouseRepository().getLagerauslastung200Data(dateRange),
  );
});

// Lagerauslastung240-Daten
router.get("/lagerauslastung240", validators.dateRange, async (req, res) => {
  // Datumsparameter aus der validierten Anfrage extrahieren
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;

  // Parameter an das Warehouse Repository übergeben
  await handleRequest(req, res, () =>
    getWarehouseRepository().getLagerauslastung240Data(dateRange),
  );
});

// Schnitte-Daten
router.get("/schnitte-data", async (req, res) => {
  await handleRequest(req, res, () => getCuttingRepository().getSchnitteData());
});

// Maschinen-Effizienz-Daten
router.get(
  "/maschinen-efficiency",
  validators.maschinenEfficiency,
  async (req, res) => {
    const { startDate, endDate } = req.query as {
      startDate?: string;
      endDate?: string;
    };
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () =>
      getCuttingRepository().getMaschinenEfficiency(dateRange),
    );
  },
);

// Cutting-Chart-Daten
router.get("/cutting-chart-data", async (req, res) => {
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
  await handleRequest(req, res, () =>
    getCuttingRepository().getCuttingChartData(dateRange),
  );
});

// Lager-Cuts-Chart-Daten
router.get("/lager-cuts-chart-data", async (req, res) => {
  console.log("🔍 [DEBUG] Lager-Cuts-Chart-Daten-Endpunkt aufgerufen!");
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  console.log(
    `🔍 [DEBUG] Datumsbereich für Lager-Cuts: ${startDate || "nicht angegeben"} bis ${endDate || "nicht angegeben"}`,
  );

  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;

  try {
    console.log(
      "🔍 [DEBUG] Rufe getCuttingRepository().getLagerCutsChartData auf...",
    );
    const result =
      await getCuttingRepository().getLagerCutsChartData(dateRange);
    console.log(
      `🔍 [DEBUG] Lager-Cuts-Chart-Daten erhalten: ${result ? JSON.stringify(result).substring(0, 100) + "..." : "keine Daten"}`,
    );

    const response = {
      success: true,
      data: result,
    };

    console.log(
      `✅ [DEBUG] Sende Lager-Cuts-Chart-Daten-Antwort: ${JSON.stringify(response).substring(0, 100)}...`,
    );
    res.json(response);
  } catch (error) {
    console.error(
      `❌ [DEBUG] Fehler beim Abrufen der Lager-Cuts-Chart-Daten:`,
      error,
    );
    const response = {
      success: false,
      error: error instanceof Error ? error.message : "Unbekannter Fehler",
    };
    res.status(500).json(response);
  }
});

// ATrL-Daten
router.get("/atrl-data", validators.dateRange, async (req, res) => {
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
  await handleRequest(req, res, () =>
    getWarehouseRepository().getAtrlData(dateRange),
  );
});

// ARiL-Daten
router.get("/aril-data", validators.dateRange, async (req, res) => {
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
  await handleRequest(req, res, () =>
    getWarehouseRepository().getArilData(dateRange),
  );
});

// System FTS Verfügbarkeitsdaten
router.get("/system-fts-data", validators.dateRange, async (req, res) => {
  const { startDate, endDate } = req.query as {
    startDate?: string;
    endDate?: string;
  };
  const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
  await handleRequest(req, res, () =>
    databaseService.getSystemFTSData(dateRange),
  );
});

// Materialdaten aus der Datenbank abrufen
router.get("/materialdaten", async (req, res) => {
  console.log("[ROUTES] Materialdaten-Route aufgerufen!");
  await handleRequest(req, res, () => databaseService.getMaterialdaten());
});

// Materialdaten mit Suchfunktionalität
router.get("/materialdaten/search", async (req, res) => {
  console.log("[ROUTES] Materialdaten-Search-Route aufgerufen!");
  const {
    search = "",
    limit = 100,
    offset = 0,
  } = req.query as {
    search?: string;
    limit?: string;
    offset?: string;
  };

  try {
    // Verwende den bestehenden Service und filtere im Backend
    const allMaterials = await databaseService.getMaterialdaten();

    // Filtere basierend auf der Suche (MATNR oder Materialkurztext)
    const filteredMaterials = search
      ? allMaterials.filter(
          (material) =>
            material.MATNR.toLowerCase().includes(search.toLowerCase()) ||
            (material.Materialkurztext &&
              material.Materialkurztext.toLowerCase().includes(
                search.toLowerCase(),
              )),
        )
      : allMaterials;

    // Paginierung anwenden
    const limitNum = parseInt(String(limit)) || 100;
    const offsetNum = parseInt(String(offset)) || 0;
    const paginatedResults = filteredMaterials.slice(
      offsetNum,
      offsetNum + limitNum,
    );

    const response = {
      success: true,
      data: paginatedResults,
      total: filteredMaterials.length,
      limit: limitNum,
      offset: offsetNum,
    };

    console.log(
      `[ROUTES] Materialdaten-Search erfolgreich: ${paginatedResults.length} von ${filteredMaterials.length} Ergebnissen`,
    );
    res.json(response);
  } catch (error) {
    console.error("[ROUTES] Fehler bei Materialdaten-Search:", error);
    const response = {
      success: false,
      error: error instanceof Error ? error.message : "Unbekannter Fehler",
    };
    res.status(500).json(response);
  }
});

// Trommeldaten aus der Datenbank abrufen
router.get("/trommeldaten", async (req, res) => {
  console.log("[ROUTES] Trommeldaten-Route aufgerufen!");
  await handleRequest(req, res, () => databaseService.getTrommeldaten());
});

// Health Check für Frontend
router.get("/health", async (req, res) => {
  try {
    const health = await databaseService.getServiceHealth();
    res.json(health);
  } catch (error) {
    res.status(500).json({
      status: "unhealthy",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// System Stats für Frontend
router.get("/system-stats", async (req, res) => {
  try {
    const stats = await databaseService.getSystemStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// Cache Stats für Frontend
router.get("/cache-stats", async (req, res) => {
  try {
    const stats = await databaseService.getCacheStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// Knowledge Bases für Frontend (RAG)
router.get("/knowledge-bases", async (req, res) => {
  try {
    const knowledgeBases = await databaseService.getKnowledgeBases();
    res.json(knowledgeBases);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

export default router;
