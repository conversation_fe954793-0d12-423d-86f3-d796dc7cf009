"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemRepositoryImpl = void 0;
const db_1 = require("../db");
const drizzle_orm_1 = require("drizzle-orm");
const cache_service_1 = require("../services/cache.service");
const schema_1 = require("../db/schema");
/**
 * Cache-TTL für System-Daten (verschiedene Datentypen)
 */
const SYSTEM_CACHE_TTL = {
    SERVICE_LEVEL: 5 * 60 * 1000, // 5 Minuten
    PERFORMANCE: 10 * 60 * 1000, // 10 Minuten
    PICKING: 3 * 60 * 1000, // 3 Minuten
    RETURNS: 15 * 60 * 1000, // 15 Minuten
    DELIVERY: 5 * 60 * 1000, // 5 Minuten
    TAGESLEISTUNG: 10 * 60 * 1000, // 10 Minuten
    STATS: 15 * 60 * 1000, // 15 Minuten
};
/**
 * Service Level Repository Implementation
 */
class ServiceLevelRepositoryImpl {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)();
    }
    async getAll(filter) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('system', 'serviceLevel', filter);
        try {
            return await this.cache.cachedQuery(cacheKey, async () => {
                let conditions = [];
                if (filter) {
                    if (filter.startDate && filter.endDate) {
                        conditions.push((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.dispatchData.datum, filter.startDate), (0, drizzle_orm_1.lte)(schema_1.dispatchData.datum, filter.endDate)));
                    }
                    if (filter.systemType) {
                        // For now, we'll use dispatchData for service level data
                        // In a real implementation, you might have a separate service level table
                    }
                    if (filter.minServiceLevel !== undefined) {
                        conditions.push((0, drizzle_orm_1.gte)(schema_1.dispatchData.servicegrad, filter.minServiceLevel));
                    }
                    if (filter.maxServiceLevel !== undefined) {
                        conditions.push((0, drizzle_orm_1.lte)(schema_1.dispatchData.servicegrad, filter.maxServiceLevel));
                    }
                }
                let query = db_1.db.select().from(schema_1.dispatchData);
                if (conditions.length > 0) {
                    query = query.where(conditions.length === 1 ? conditions[0] : (0, drizzle_orm_1.and)(...conditions));
                }
                return await query.orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum)).limit(1000);
            }, SYSTEM_CACHE_TTL.SERVICE_LEVEL);
        }
        catch (error) {
            console.error('Error fetching service level data:', error);
            throw error;
        }
    }
    async getCurrentServiceLevel() {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('system', 'currentServiceLevel');
        try {
            return await this.cache.cachedQuery(cacheKey, async () => {
                const result = await db_1.db.select().from(schema_1.dispatchData)
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
                    .limit(1);
                return result[0] || null;
            }, SYSTEM_CACHE_TTL.SERVICE_LEVEL);
        }
        catch (error) {
            console.error('Error fetching current service level:', error);
            throw error;
        }
    }
    async getServiceLevelStats(filter) {
        const data = await this.getAll(filter);
        const serviceLevels = data.map(item => item.servicegrad || 0);
        if (serviceLevels.length === 0) {
            return {
                average: 0,
                current: 0,
                trend: 'stable',
                aboveTarget: 0,
                belowTarget: 0,
                targetPercentage: 0
            };
        }
        const average = serviceLevels.reduce((sum, level) => sum + level, 0) / serviceLevels.length;
        const current = serviceLevels[0];
        const target = 95; // 95% Service Level Target
        return {
            average,
            current,
            trend: current > average ? 'improving' : current < average ? 'declining' : 'stable',
            aboveTarget: serviceLevels.filter(level => level >= target).length,
            belowTarget: serviceLevels.filter(level => level < target).length,
            targetPercentage: (serviceLevels.filter(level => level >= target).length / serviceLevels.length) * 100
        };
    }
}
/**
 * Daily Performance Repository Implementation
 */
class DailyPerformanceRepositoryImpl {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)();
    }
    async getAll(filter) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('system', 'dailyPerformance', filter);
        try {
            return await this.cache.cachedQuery(cacheKey, async () => {
                let query = db_1.db.select({
                    date: schema_1.dispatchData.datum,
                    performance: schema_1.dispatchData.servicegrad
                }).from(schema_1.dispatchData);
                if ((filter === null || filter === void 0 ? void 0 : filter.startDate) && (filter === null || filter === void 0 ? void 0 : filter.endDate)) {
                    query = query.where((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.dispatchData.datum, filter.startDate), (0, drizzle_orm_1.lte)(schema_1.dispatchData.datum, filter.endDate)));
                }
                const data = await query.orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum)).limit(1000);
                return data.map(item => ({
                    date: item.date || '',
                    performance: item.performance || 0,
                    target: 95 // Default target
                }));
            }, SYSTEM_CACHE_TTL.PERFORMANCE);
        }
        catch (error) {
            console.error('Error fetching daily performance data:', error);
            throw error;
        }
    }
    async getPerformanceTrend(days = 30) {
        const data = await this.getAll({ startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0] });
        const performances = data.map(item => item.performance || 0);
        if (performances.length === 0) {
            return {
                trend: 'stable',
                averageDaily: 0,
                bestDay: null,
                worstDay: null,
                consistency: 0
            };
        }
        const averageDaily = performances.reduce((sum, perf) => sum + perf, 0) / performances.length;
        const bestDay = data.find(item => item.performance === Math.max(...performances)) || null;
        const worstDay = data.find(item => item.performance === Math.min(...performances)) || null;
        // Calculate consistency (lower variance = higher consistency)
        const variance = performances.reduce((sum, perf) => sum + Math.pow(perf - averageDaily, 2), 0) / performances.length;
        const consistency = Math.max(0, 100 - (variance / averageDaily) * 100);
        // Determine trend
        const recent = performances.slice(0, Math.floor(performances.length / 2));
        const older = performances.slice(Math.floor(performances.length / 2));
        const recentAvg = recent.reduce((sum, perf) => sum + perf, 0) / recent.length;
        const olderAvg = older.reduce((sum, perf) => sum + perf, 0) / older.length;
        let trend = 'stable';
        if (recentAvg > olderAvg * 1.05)
            trend = 'improving';
        else if (recentAvg < olderAvg * 0.95)
            trend = 'declining';
        return {
            trend,
            averageDaily,
            bestDay,
            worstDay,
            consistency
        };
    }
}
/**
 * Picking Repository Implementation
 */
class PickingRepositoryImpl {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)();
    }
    async getAll() {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('system', 'picking');
        try {
            return await this.cache.cachedQuery(cacheKey, async () => {
                // Use ARiL data for picking information - using correct column names
                return await db_1.db.select({
                    date: schema_1.ariL.Datum,
                    picks: schema_1.ariL.waTaPositionen,
                    aril: schema_1.ariL.belegtePlaetze, // Using available column
                    fuellgrad_aril: schema_1.ariL.auslastung // Using available column
                }).from(schema_1.ariL)
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.ariL.Datum))
                    .limit(1000);
            }, SYSTEM_CACHE_TTL.PICKING);
        }
        catch (error) {
            console.error('Error fetching picking data:', error);
            throw error;
        }
    }
    async getPickingEfficiency() {
        const data = await this.getAll();
        const picks = data.map(item => item.picks || 0);
        if (picks.length === 0) {
            return {
                averagePicksPerDay: 0,
                totalPicks: 0,
                efficiency: 0,
                trends: []
            };
        }
        const totalPicks = picks.reduce((sum, pick) => sum + pick, 0);
        const averagePicksPerDay = totalPicks / picks.length;
        // Calculate efficiency based on target (assuming 1000 picks per day target)
        const target = 1000;
        const efficiency = (averagePicksPerDay / target) * 100;
        const trends = data.slice(0, 30).map(item => ({
            date: item.date,
            picks: item.picks || 0
        }));
        return {
            averagePicksPerDay,
            totalPicks,
            efficiency,
            trends
        };
    }
}
/**
 * Returns Repository Implementation
 */
class ReturnsRepositoryImpl {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)();
    }
    async getAll() {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('system', 'returns');
        try {
            return await this.cache.cachedQuery(cacheKey, async () => {
                // Use dispatch data for returns information (qm_abgelehnt as returns)
                const data = await db_1.db.select({
                    date: schema_1.dispatchData.datum,
                    returns: schema_1.dispatchData.qm_abgelehnt
                }).from(schema_1.dispatchData)
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
                    .limit(1000);
                return data.map(item => ({
                    date: item.date || '',
                    returns: item.returns || 0
                }));
            }, SYSTEM_CACHE_TTL.RETURNS);
        }
        catch (error) {
            console.error('Error fetching returns data:', error);
            throw error;
        }
    }
    async getReturnsAnalysis() {
        const data = await this.getAll();
        const returnsData = data.map(item => item.returns || 0);
        if (returnsData.length === 0) {
            return {
                totalReturns: 0,
                averageReturnsPerDay: 0,
                returnRate: 0,
                trends: [],
                categories: {}
            };
        }
        const totalReturns = returnsData.reduce((sum, returns) => sum + returns, 0);
        const averageReturnsPerDay = totalReturns / returnsData.length;
        // Calculate return rate (assuming 5% of total orders)
        const returnRate = (totalReturns / (totalReturns * 20)) * 100; // Assuming 20x more orders than returns
        const trends = data.slice(0, 30).map(item => ({
            date: item.date,
            returns: item.returns || 0
        }));
        // Group by category (mock implementation)
        const categories = {
            'quality': totalReturns * 0.6,
            'damage': totalReturns * 0.3,
            'other': totalReturns * 0.1
        };
        return {
            totalReturns,
            averageReturnsPerDay,
            returnRate,
            trends,
            categories
        };
    }
}
/**
 * Delivery Positions Repository Implementation
 */
class DeliveryPositionsRepositoryImpl {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)();
    }
    async getAll() {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('system', 'deliveryPositions');
        try {
            return await this.cache.cachedQuery(cacheKey, async () => {
                // Use dispatch data for delivery positions
                const data = await db_1.db.select({
                    date: schema_1.dispatchData.datum,
                    positions: schema_1.dispatchData.ausgeliefert_lup
                }).from(schema_1.dispatchData)
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
                    .limit(1000);
                return data.map(item => ({
                    date: item.date || '',
                    positions: item.positions || 0,
                    onTime: true, // Mock data
                    delayed: false // Mock data
                }));
            }, SYSTEM_CACHE_TTL.DELIVERY);
        }
        catch (error) {
            console.error('Error fetching delivery positions data:', error);
            throw error;
        }
    }
    async getDeliveryAnalysis() {
        const data = await this.getAll();
        const positions = data.map(item => item.positions || 0);
        if (positions.length === 0) {
            return {
                totalPositions: 0,
                averagePositionsPerDay: 0,
                deliveryEfficiency: 0,
                onTimeDeliveries: 0,
                delays: 0
            };
        }
        const totalPositions = positions.reduce((sum, pos) => sum + pos, 0);
        const averagePositionsPerDay = totalPositions / positions.length;
        // Calculate efficiency based on target (assuming 100 positions per day target)
        const target = 100;
        const deliveryEfficiency = (averagePositionsPerDay / target) * 100;
        const onTimeDeliveries = data.filter(item => item.onTime === true).length;
        const delays = data.filter(item => item.delayed === true).length;
        return {
            totalPositions,
            averagePositionsPerDay,
            deliveryEfficiency,
            onTimeDeliveries,
            delays
        };
    }
}
/**
 * Tagesleistung Repository Implementation
 */
class TagesleistungRepositoryImpl {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)();
    }
    async getAll() {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('system', 'tagesleistung');
        try {
            return await this.cache.cachedQuery(cacheKey, async () => {
                // Use dispatch data for tagesleistung
                return await db_1.db.select({
                    date: schema_1.dispatchData.datum,
                    performance: schema_1.dispatchData.servicegrad,
                    produzierte_tonnagen: schema_1.dispatchData.produzierte_tonnagen,
                    direktverladung_kiaa: schema_1.dispatchData.direktverladung_kiaa,
                    umschlag: schema_1.dispatchData.umschlag,
                    kg_pro_colli: schema_1.dispatchData.kg_pro_colli,
                    elefanten: schema_1.dispatchData.elefanten
                }).from(schema_1.dispatchData)
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
                    .limit(1000);
            }, SYSTEM_CACHE_TTL.TAGESLEISTUNG);
        }
        catch (error) {
            console.error('Error fetching tagesleistung data:', error);
            throw error;
        }
    }
    async getDailyPerformanceStats() {
        const data = await this.getAll();
        const performances = data.map(item => item.performance || 0);
        if (performances.length === 0) {
            return {
                averageDaily: 0,
                bestPerformance: null,
                worstPerformance: null,
                targetAchievement: 0,
                consistency: 0
            };
        }
        const averageDaily = performances.reduce((sum, perf) => sum + perf, 0) / performances.length;
        const bestPerformance = data.find(item => item.performance === Math.max(...performances));
        const worstPerformance = data.find(item => item.performance === Math.min(...performances));
        // Calculate target achievement (assuming 100% target)
        const target = 100;
        const targetAchievement = (averageDaily / target) * 100;
        // Calculate consistency
        const variance = performances.reduce((sum, perf) => sum + Math.pow(perf - averageDaily, 2), 0) / performances.length;
        const consistency = Math.max(0, 100 - (variance / averageDaily) * 100);
        return {
            averageDaily,
            bestPerformance,
            worstPerformance,
            targetAchievement,
            consistency
        };
    }
}
/**
 * System Stats Repository Implementation
 */
class SystemStatsRepositoryImpl {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)();
    }
    async getAll(filter) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('system', 'systemStats', filter);
        try {
            return await this.cache.cachedQuery(cacheKey, async () => {
                // Use system status data
                let query = db_1.db.select().from(schema_1.systemStatus);
                if ((filter === null || filter === void 0 ? void 0 : filter.startDate) && (filter === null || filter === void 0 ? void 0 : filter.endDate)) {
                    query = query.where((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.systemStatus.lastUpdated, filter.startDate), (0, drizzle_orm_1.lte)(schema_1.systemStatus.lastUpdated, filter.endDate)));
                }
                return await query.orderBy((0, drizzle_orm_1.desc)(schema_1.systemStatus.lastUpdated)).limit(100);
            }, SYSTEM_CACHE_TTL.STATS);
        }
        catch (error) {
            console.error('Error fetching system stats:', error);
            throw error;
        }
    }
    async getSystemHealthDashboard() {
        const stats = await this.getAll();
        // Mock implementation - in real app, this would analyze actual system metrics
        const components = {
            'database': { status: 'healthy', value: 98 },
            'api': { status: 'healthy', value: 95 },
            'cache': { status: 'warning', value: 87 },
            'storage': { status: 'healthy', value: 92 }
        };
        const overall = Object.values(components).every(c => c.status === 'healthy') ? 'healthy' :
            Object.values(components).some(c => c.status === 'critical') ? 'critical' : 'warning';
        return {
            overall,
            components,
            recommendations: overall === 'healthy' ? [] : ['Check cache performance', 'Monitor system resources']
        };
    }
}
/**
 * System Repository Implementation
 */
class SystemRepositoryImpl {
    constructor() {
        this.serviceLevel = new ServiceLevelRepositoryImpl();
        this.dailyPerformance = new DailyPerformanceRepositoryImpl();
        this.picking = new PickingRepositoryImpl();
        this.returns = new ReturnsRepositoryImpl();
        this.deliveryPositions = new DeliveryPositionsRepositoryImpl();
        this.tagesleistung = new TagesleistungRepositoryImpl();
        this.systemStats = new SystemStatsRepositoryImpl();
    }
    invalidateAllCache() {
        console.log('Invalidating all system cache');
    }
    async getSystemDashboard(filter) {
        const [serviceLevelStats, performanceTrend, pickingEfficiency, returnsAnalysis, deliveryAnalysis, systemHealth] = await Promise.all([
            this.serviceLevel.getServiceLevelStats(filter ? { ...filter, systemType: 'performance' } : { systemType: 'performance' }),
            this.dailyPerformance.getPerformanceTrend(filter ? undefined : 30),
            this.picking.getPickingEfficiency(),
            this.returns.getReturnsAnalysis(),
            this.deliveryPositions.getDeliveryAnalysis(),
            this.systemStats.getSystemHealthDashboard()
        ]);
        return {
            serviceLevel: serviceLevelStats,
            performance: performanceTrend,
            picking: pickingEfficiency,
            delivery: deliveryAnalysis,
            systemHealth
        };
    }
}
exports.SystemRepositoryImpl = SystemRepositoryImpl;
