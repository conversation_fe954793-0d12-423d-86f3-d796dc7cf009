import { useState, useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { useAuthContext } from '@/contexts/AuthContext';
import { LabelForm } from '@/components/auth';
import TextType from '@/components/Animation/Text/TextType';
import LightRays from '@/components/Animation/background/LightRays';
import { Eye, EyeOff } from 'lucide-react';

// Bilder als ES-Module importieren (Vite/Electron-sicher)
import loginBg from '@/assets/LeitstandLogin.png';
import cardBg from '@/assets/drumReg.png';

const LoginPage = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  // Sichtbarkeit des Passworts steuern (true = sichtbar)
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Reset-Modus: Blendt ein alternatives Formular zum Zurücksetzen des Passworts ein
  const [isResetMode, setIsResetMode] = useState(false);
  // Felder für das Zurücksetzen
  const [resetEmail, setResetEmail] = useState('');
  const [resetNewPassword, setResetNewPassword] = useState('');
  const [resetConfirmPassword, setResetConfirmPassword] = useState('');
  const [showResetNewPassword, setShowResetNewPassword] = useState(false);
  const [showResetConfirmPassword, setShowResetConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { login, isLoading, isAuthenticated } = useAuthContext();

  // Check for success message from registration
  const searchParams = new URLSearchParams(window.location.search);
  const registrationSuccess = searchParams.get('registered') === 'true';

  // Check if user is already authenticated on component mount
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      navigate({ to: '/' });
    }
  }, [isAuthenticated, isLoading, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    // Wenn Reset-Modus aktiv ist, andere Validierung/Logik
    if (isResetMode) {
      // Einfache Validierung
      if (!resetEmail || !resetNewPassword || !resetConfirmPassword) {
        setError('Bitte füllen Sie alle Felder für das Zurücksetzen aus.');
        return;
      }
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(resetEmail)) {
        setError('Bitte eine gültige E-Mail-Adresse eingeben.');
        return;
      }
      if (resetNewPassword.length < 8) {
        setError('Das neue Passwort muss mindestens 8 Zeichen lang sein.');
        return;
      }
      if (resetNewPassword !== resetConfirmPassword) {
        setError('Die neuen Passwörter stimmen nicht überein.');
        return;
      }
      // Hinweis: Kein Backend-Endpunkt vorhanden. Wir zeigen einen Erfolg-Toast und leiten zur Anmeldung.
      toast({
        title: 'Passwort aktualisiert',
        description: 'Ihr Passwort wurde aktualisiert. Bitte melden Sie sich an.',
      });
      // Nach erfolgreichem Reset zurück zum Login-Formular
      setIsResetMode(false);
      setUsername('');
      setPassword('');
      navigate({ to: '/login' });
      return;
    }

    // Basic validation für normalen Login
    if (!username || !password) {
      setError('Bitte geben Sie Benutzername und Passwort ein.');
      return;
    }

    try {
      console.log('🔄 Attempting login for user:', username);
      const result = await login(username, password);
      console.log('�  Login result:', result);

      if (result.success) {
        console.log('✅ Login successful, showing toast and navigating');

        toast({
          title: 'Erfolgreich angemeldet',
          description: `Willkommen zurück, ${username}!`,
        });

        // Redirect to module selection page (home for now)
        navigate({ to: '/' });
      } else {
        console.log('❌ Login failed with message:', result.message);
        setError(result.message || 'Anmeldung fehlgeschlagen.');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Ein unerwarteter Fehler ist aufgetreten.');
    }
  };

  // Handle navigation to registration page
  const handleNavigateToRegister = (e: React.MouseEvent) => {
    e.preventDefault();
    navigate({ to: '/register' });
  };
  
  // Passwort-Reset starten: Zeigt das alternative Formular an
  const handleStartReset = (e: React.MouseEvent) => {
    e.preventDefault();
    setError(null);
    setIsResetMode(true);
  };
  // Zurück zur Anmeldung aus dem Reset-Formular
  const handleBackToLogin = (e: React.MouseEvent) => {
    e.preventDefault();
    setError(null);
    setIsResetMode(false);
  };

  // Handle animation complete
  const handleAnimationComplete = () => {
    console.log('Animation completed!');
  };

  return (
    <div className="min-h-screen flex bg-bg relative">
      {/* Left Half - LoginImage Background */}
      <div className="w-1/2 relative overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0 bg-[#000000]">
          <img
            src={loginBg}
            alt="Login Background"
            className="absolute inset-0 w-full h-full object-cover object-center translate-y-25 translate-x-10 scale-110 transform-gpu will-change-transform mb-2"
            onError={() => {
              console.error('Image failed to load:', loginBg);
            }}
          />
          {/* Optional overlay for better text readability */}
          <div className="absolute inset-0 bg-black/20"></div>
        </div>

        {/* LightRays Animation integrated in the left half (above image, below text) */}
        <LightRays
          raysOrigin="top-center"
          raysColor="#FFFFFF"
          raysSpeed={1}
          lightSpread={0.5}
          rayLength={3}
          fadeDistance={2}
          saturation={2}
          followMouse={true}
          mouseInfluence={0.1}
          noiseAmount={0.1}
          distortion={0.05}
          // absolute positioning to cover the left panel fully; pointer-events none so UI stays interactive
          className="absolute inset-0 z-5 pointer-events-none"
        />

        {/* Text overlay */}
        <div className="absolute inset-0 flex items-start justify-start z-10 pt-60 pl-30">
          <div className="label-form-label-white flex flex-col items-start gap-4">
            <TextType 
              text={["WILLKOMMEN BEI DER"]}
              typingSpeed={175}
              pauseDuration={1500}
              showCursor={true}
              cursorCharacter="|"
            />
          </div>
        </div>
      </div>

      {/* Right Half - All current content */}
      <div className="w-1/2 relative">
        {/* Image Card - fills entire right half */}
        <div className="absolute inset-0 z-0">
          <img
            src={cardBg}
            alt="Background Card Image"
            className="w-full h-full object-cover rounded"
            onError={(e) => {
              console.error('Image failed to load:', cardBg);
            }}
          />
        </div>
      </div>

      {/* Center transition overlay - positioned exactly in the middle */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 h-full w-24 z-5 pointer-events-none">
        <div className="w-full h-full bg-gradient-to-r from-transparent via-bg/60 to-transparent"></div>
      </div>

        {/* Registrierungs-Formular im Vordergrund */}
        {/* Perspektive-Wrapper: sorgt für Fluchtpunkte (3D) und natürlichere Darstellung */}
    {/* Fluchtpunkt leicht nach rechts/oben verschieben, damit X-Richtung besser mit dem Sticker fluchtet */}
      <div className="absolute top-85 right-44 z-10 w-full -mt-4 max-w-95 p-4 [perspective:1200px] [perspective-origin:58%_44%] pointer-events-none">
      {/* Innerer 3D-Block: X-Tiefe über rotateY, Y-Neigung über rotateX, Z leicht erhöht für sichtbare Gesamtdrehung */}
      {/* Hinweis: Tailwind hat keine nativen 3D-rotate-Utilities. Daher nutzen wir eine einzige Arbitrary-Transform, damit rotateX/rotateY/rotate wirken. */}
      <div className="pointer-events-auto [transform-style:preserve-3d] origin-top-left [transform:rotateX(0deg)_rotateY(14deg)_rotate(0deg)] transition-transform duration-300 will-change-transform">
        <LabelForm
          title={isResetMode ? 'RESET PASSWORT' : 'ANMELDUNG'}
          description={isResetMode ? 'Bitte E-Mail und neues Passwort eingeben' : 'Melde dich mit deinen Zugangsdaten an'}
          titleSize={isResetMode ? '1.8rem' : '2rem'}
          descriptionSize="1rem"
          onSubmit={handleSubmit}
          isLoading={isLoading}
          error={error}
          success={registrationSuccess ? 'Registrierung erfolgreich! Sie können sich jetzt anmelden.' : null}
          styleConfig={{ backgroundColor: 'transparent' }}
        >
          {/* Trennlinie direkt unter Titel/Beschreibung - Anpassung basierend auf dem Modus */}
          <div className={`h-px bg-gray-500 ${isResetMode ? 'w-72 -my-2 -mx-2' : 'w-75 my-2 -mx-4'}`} />
          <div className="label-stack">
            {!isResetMode && (
              <>
                <div>
                  <Label
                    htmlFor="username"
                    className="label-form-label mt-4 !text-xl"
                  >
                    Benutzername
                  </Label>
                  <Input
                    id="username"
                    type="text"
                    placeholder="Ihr Benutzername"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    variant="ghost"
                    className="mt-2 !text-lg w-69"
                    required
                    autoComplete="username"
                  />
                </div>

                <div>
                  <div className="flex justify-between items-center mt-8">
                    <Label
                      htmlFor="password"
                      className="label-form-label -mt-5 !text-xl"
                    >
                      Passwort
                    </Label>
                  </div>
                  {/* Passwortfeld mit Show/Hide-Toggle */}
                  <div className="relative">
                    <Input
                      id="password"
                      // Typ dynamisch setzen: sichtbar = text, sonst password
                      type={showPassword ? 'text' : 'password'}
                      placeholder="••••••••"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      variant="ghost"
                      // Rechts etwas Platz für den Toggle-Button
                      className="mt-0 !text-lg pr-12 w-70"
                      required
                      autoComplete="current-password"
                    />
                    {/* Toggle-Button: zeigt/versteckt Passwort */}
                    <button
                      type="button"
                      aria-label={showPassword ? 'Passwort verbergen' : 'Passwort anzeigen'}
                      onClick={() => setShowPassword((v) => !v)}
                      className="absolute right-0 top-1/2 -translate-y-1/2 p-1 text-gray-500 hover:text-gray-700 select-none"
                    >
                      {showPassword ? (
                        <EyeOff size={20} aria-hidden="true" />
                      ) : (
                        <Eye size={20} aria-hidden="true" />
                      )}
                    </button>
                  </div>
                </div>
                {/* Aktion: In den Reset-Modus wechseln */}
                <a
                  href="#"
                  className="!text-md label-text-muted hover:underline"
                  onClick={handleStartReset}
                >
                  Passwort vergessen?
                </a>
              </>
            )}

            {isResetMode && (
              <>
                <div>
                  <Label htmlFor="resetEmail" className="label-form-label mt-4 !text-lg">
                    E-Mail Adresse
                  </Label>
                  <Input
                    id="resetEmail"
                    type="email"
                    placeholder="<EMAIL>"
                    value={resetEmail}
                    onChange={(e) => setResetEmail(e.target.value)}
                    variant="ghost"
                    className="-mt-3 !text-sm !pt-2.5 !pb-0 leading-[1.1] pr-12 w-69"
                    required
                    autoComplete="email"
                  />
                </div>
                <div className="relative">
                  <Label htmlFor="resetNewPassword" className="label-form-label mt-4 !text-lg">
                    Neues Passwort
                  </Label>
                  <Input
                    id="resetNewPassword"
                    type={showResetNewPassword ? "text" : "password"}
                    placeholder="••••••••"
                    value={resetNewPassword}
                    onChange={(e) => setResetNewPassword(e.target.value)}
                    variant="ghost"
                    className="-mt-3 !text-sm !pt-2.5 !pb-0 leading-[1.1] pr-12 w-69.5"
                    required
                    autoComplete="new-password"
                  />
                  <button
                    type="button"
                    className="absolute right-0 top-12 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                    onClick={() => setShowResetNewPassword(!showResetNewPassword)}
                  >
                    {showResetNewPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                <div className="relative">
                  <Label htmlFor="resetConfirmPassword" className="label-form-label mt-4 !text-lg">
                    Passwort bestätigen
                  </Label>
                  <Input
                    id="resetConfirmPassword"
                    type={showResetConfirmPassword ? "text" : "password"}
                    placeholder="••••••••"
                    value={resetConfirmPassword}
                    onChange={(e) => setResetConfirmPassword(e.target.value)}
                    variant="ghost"
                    className="-mt-3 !text-sm !pt-2.5 !pb-0 leading-[1.1] pr-12 w-70"
                    required
                    autoComplete="new-password"
                  />
                  <button
                    type="button"
                    className="absolute right-0 top-12 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                    onClick={() => setShowResetConfirmPassword(!showResetConfirmPassword)}
                  >
                    {showResetConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {/* Zurück zur Anmeldung */}
                <a
                  href="#"
                  className="!text-md label-text-muted hover:underline -mt-1 inline-block"
                  onClick={handleBackToLogin}
                >
                  Zurück zur Anmeldung
                </a>
              </>
            )}

            <Button
              type="submit"
              variant="link"
              className={`absolute left-45 -translate-x-1/2 border-1 rounded-none border-black w-71 h-10 text-center font-bold text-4xl mt-4 ${
                isResetMode ? '-bottom-9' : '-bottom-10'
              }`}
              disabled={isLoading}
            >
              {isResetMode ? 'SPEICHERN' : (isLoading ? 'Anmeldung läuft...' : 'LOGIN')}
            </Button>
          </div>
        </LabelForm>

        {/* Registration Link - Position abhängig vom Modus */}
        <div className={`absolute left-45 -translate-x-1/2 z-20 w-max text-center ${
          isResetMode ? '-bottom-23' : '-bottom-24'
        }`}>
          <p className="label-form-label text-sm font-base whitespace-nowrap">
            Noch kein Konto?{' '}
            <button
              type="button"
              className="label-text-primary underline hover:underline cursor-pointer bg-transparent border-none appearance-none text-sm p-0 inline-block align-baseline leading-none"
              onClick={handleNavigateToRegister}
            >
              REGISTRIEREN
            </button>
          </p>
        </div>
      </div>
    </div>
  </div>
  );
};

export default LoginPage;