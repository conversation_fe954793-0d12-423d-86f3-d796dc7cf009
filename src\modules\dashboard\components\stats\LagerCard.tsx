import React from 'react';
import { Gauge } from 'lucide-react';
import { BaseKPICardProps } from './types';

/**
 * KPI-Karte für Lager
 * 
 * Zeigt den Umschlag aus den Dispatch-Daten an.
 * Verwendet lime-grünes Farbschema mit Gauge-Icon aus Lucide.
 */
export const LagerCard: React.FC<BaseKPICardProps> = ({ 
  departmentKPIs, 
  className = '' 
}) => {
  return (
    <>
      <div className="flex items-center mb-2">
        <div className="p-2 bg-lime-100 rounded-lg mr-3">
          <Gauge className="h-6 w-6 text-lime-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Lager</h3>
      </div>
      
      <div className="text-2xl font-bold text-gray-900">
        {departmentKPIs?.dispatch.umschlag.toLocaleString() || '0'}
        <span className="text-sm font-normal text-gray-500 ml-1">Pos</span>
      </div>
      
      <div className="text-sm text-gray-600 mt-1">
        Ziel: 5.000 Pos
      </div>
    </>
  );
};

export default LagerCard;