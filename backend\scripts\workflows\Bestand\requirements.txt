# Python-Abhängigkeiten für Bestand Workflow
# 
# Installation: pip install -r requirements.txt

# Excel-Automatisierung
xlwings>=0.30.0

# Datenverarbeitung
pandas>=2.0.0

# Windows COM-Objekte (für SAP GUI)
pywin32>=306

# PostgreSQL-Datenbanktreiber
psycopg2-binary

# Zusätzliche Abhängigkeiten falls benötigt
openpyxl>=3.1.0  # Für Excel-Dateien ohne xlwings
python-dateutil>=2.8.0  # Für erweiterte Datums-Funktionen