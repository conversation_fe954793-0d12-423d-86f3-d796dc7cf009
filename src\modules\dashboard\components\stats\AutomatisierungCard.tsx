import React from 'react';
import { Activity } from 'lucide-react';
import { BaseKPICardProps } from './types';

/**
 * KPI-Karte für Automatisierung
 * 
 * Zeigt die automatischen Wareneingangspositionen (weAtrlPos) an.
 * Verwendet cyan Farbschema mit Activity-Icon aus Lucide.
 */
export const AutomatisierungCard: React.FC<BaseKPICardProps> = ({ 
  departmentKPIs, 
  className = '' 
}) => {
  return (
    <>
      <div className="flex items-center mb-2">
        <div className="p-2 bg-cyan-100 rounded-lg mr-3">
          <Activity className="h-6 w-6 text-cyan-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Automatisierung</h3>
      </div>
      
      <div className="text-2xl font-bold text-gray-900">
        {departmentKPIs?.wareneingang.weAtrlPos.toLocaleString() || '0'}
        <span className="text-sm font-normal text-gray-500 ml-1">Pos</span>
      </div>
      
      <div className="text-sm text-gray-600 mt-1">
        Ziel: 900 Pos
      </div>
    </>
  );
};

export default AutomatisierungCard;