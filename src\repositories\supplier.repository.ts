/**
 * Supplier Repository Interfaces
 *
 * Type definitions for supplier data and performance management
 */

import { Supplier, SupplierPerformanceMetrics } from '@/types/supply-chain-optimization';

export interface SupplierStats {
  totalSuppliers: number;
  activeSuppliers: number;
  averagePerformanceScore: number;
  topPerformers: number;
  riskSuppliers: number;
}

export interface SupplierPerformanceData {
  supplierId: string;
  onTimeDeliveryRate: number;
  qualityRate: number;
  responseTime: number;
  financialStabilityScore: number;
  reliabilityScore: number;
  costCompetitiveness: number;
  totalOrders: number;
  totalValue: number;
  lastOrderDate: Date;
  performanceTrend: 'improving' | 'stable' | 'declining';
}

export interface SupplierDeliveryHistory {
  deliveryId: string;
  supplierId: string;
  orderDate: Date;
  deliveryDate: Date;
  promisedTime: number;
  deliveryTime: number;
  wasLate: boolean;
  productType: string;
  urgency: 'low' | 'medium' | 'high';
  quantity: number;
  value: number;
}

/**
 * Supplier Repository Interface
 * Defines the contract for supplier data operations
 */
export interface ISupplierRepository {
  /**
   * Get all suppliers
   */
  getAll(): Promise<Supplier[]>;

  /**
   * Get overall supplier statistics
   */
  getSupplierStats(): Promise<SupplierStats>;

  /**
   * Get supplier performance data
   */
  getSupplierPerformance(
    supplierId: string,
    timeRange: { days: number }
  ): Promise<SupplierPerformanceData>;

  /**
   * Get all suppliers
   */
  getAllSuppliers(): Promise<Supplier[]>;

  /**
   * Get supplier by ID
   */
  getSupplierById(supplierId: string): Promise<Supplier | null>;

  /**
   * Get suppliers by category
   */
  getSuppliersByCategory(category: string): Promise<Supplier[]>;

  /**
   * Get top performing suppliers
   */
  getTopPerformingSuppliers(limit?: number): Promise<Supplier[]>;

  /**
   * Get suppliers with risk issues
   */
  getRiskSuppliers(): Promise<Supplier[]>;

  /**
   * Update supplier performance metrics
   */
  updateSupplierPerformance(
    supplierId: string,
    metrics: Partial<SupplierPerformanceMetrics>
  ): Promise<void>;

  /**
   * Search suppliers by name or category
   */
  searchSuppliers(query: string): Promise<Supplier[]>;

  /**
   * Get supplier delivery history summary
   */
  getSupplierDeliveryHistory(supplierId: string, timeRange: { days: number }): Promise<SupplierDeliveryHistory[]>;
}

/**
 * Supplier Repository Implementation
 * Concrete implementation of ISupplierRepository interface
 */
export class SupplierRepository implements ISupplierRepository {
  protected repositoryName = 'SupplierRepository';

  /**
   * Get all suppliers
   */
  async getAll(): Promise<Supplier[]> {
    // Mock implementation - in real app this would fetch from API/database
    return [
      {
        id: 'supplier-1',
        name: 'TechComponents GmbH',
        location: 'Berlin, Germany',
        category: 'electronics',
        contactInfo: {
          email: '<EMAIL>',
          phone: '+49-30-12345678',
          address: 'Berlin, Germany',
          contactPerson: 'Max Mustermann'
        },
        performanceMetrics: {
          onTimeDeliveryRate: 0.92,
          qualityRate: 0.95,
          responseTime: 2.5,
          financialStabilityScore: 8.5,
          reliabilityScore: 9.2,
          costCompetitiveness: 7.8
        }
      },
      {
        id: 'supplier-2',
        name: 'GlobalParts AG',
        location: 'Hamburg, Germany',
        category: 'mechanical',
        contactInfo: {
          email: '<EMAIL>',
          phone: '+49-40-98765432',
          address: 'Hamburg, Germany',
          contactPerson: 'Anna Schmidt'
        },
        performanceMetrics: {
          onTimeDeliveryRate: 0.78,
          qualityRate: 0.88,
          responseTime: 4.2,
          financialStabilityScore: 6.5,
          reliabilityScore: 7.2,
          costCompetitiveness: 8.1
        }
      }
    ];
  }

  /**
   * Get overall supplier statistics
   */
  async getSupplierStats(): Promise<SupplierStats> {
    const suppliers = await this.getAll();

    return {
      totalSuppliers: suppliers.length,
      activeSuppliers: suppliers.filter(s => s.performanceMetrics.onTimeDeliveryRate > 0.8).length,
      averagePerformanceScore: suppliers.reduce((sum, s) => sum + s.performanceMetrics.reliabilityScore, 0) / suppliers.length,
      topPerformers: suppliers.filter(s => s.performanceMetrics.reliabilityScore >= 8.5).length,
      riskSuppliers: suppliers.filter(s => s.performanceMetrics.financialStabilityScore < 6).length
    };
  }

  /**
   * Get supplier performance data
   */
  async getSupplierPerformance(
    supplierId: string,
    timeRange: { days: number }
  ): Promise<SupplierPerformanceData> {
    // Mock implementation
    return {
      supplierId,
      onTimeDeliveryRate: 0.85,
      qualityRate: 0.92,
      responseTime: 2.5,
      financialStabilityScore: 7.5,
      reliabilityScore: 8.2,
      costCompetitiveness: 7.8,
      totalOrders: 45,
      totalValue: 125000,
      lastOrderDate: new Date(),
      performanceTrend: 'stable'
    };
  }

  /**
   * Get all suppliers
   */
  async getAllSuppliers(): Promise<Supplier[]> {
    return this.getAll();
  }

  /**
   * Get supplier by ID
   */
  async getSupplierById(supplierId: string): Promise<Supplier | null> {
    const suppliers = await this.getAll();
    return suppliers.find(s => s.id === supplierId) || null;
  }

  /**
   * Get suppliers by category
   */
  async getSuppliersByCategory(category: string): Promise<Supplier[]> {
    const suppliers = await this.getAll();
    return suppliers.filter(s => s.category === category);
  }

  /**
   * Get top performing suppliers
   */
  async getTopPerformingSuppliers(limit: number = 5): Promise<Supplier[]> {
    const suppliers = await this.getAll();
    return suppliers
      .sort((a, b) => b.performanceMetrics.reliabilityScore - a.performanceMetrics.reliabilityScore)
      .slice(0, limit);
  }

  /**
   * Get suppliers with risk issues
   */
  async getRiskSuppliers(): Promise<Supplier[]> {
    const suppliers = await this.getAll();
    return suppliers.filter(s => s.performanceMetrics.financialStabilityScore < 6);
  }

  /**
   * Update supplier performance metrics
   */
  async updateSupplierPerformance(
    supplierId: string,
    metrics: Partial<SupplierPerformanceMetrics>
  ): Promise<void> {
    // Mock implementation - in real app this would update database
    console.log(`Updating supplier ${supplierId} with metrics:`, metrics);
  }

  /**
   * Search suppliers by name or category
   */
  async searchSuppliers(query: string): Promise<Supplier[]> {
    const suppliers = await this.getAll();
    const lowerQuery = query.toLowerCase();

    return suppliers.filter(s =>
      s.name.toLowerCase().includes(lowerQuery) ||
      s.category.toLowerCase().includes(lowerQuery) ||
      s.location.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * Get supplier delivery history summary
   */
  async getSupplierDeliveryHistory(supplierId: string, timeRange: { days: number }): Promise<SupplierDeliveryHistory[]> {
    // Mock implementation
    return [
      {
        deliveryId: 'del-1',
        supplierId,
        orderDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        deliveryDate: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
        promisedTime: 7,
        deliveryTime: 5,
        wasLate: false,
        productType: 'electronics',
        urgency: 'medium',
        quantity: 100,
        value: 5000
      },
      {
        deliveryId: 'del-2',
        supplierId,
        orderDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
        deliveryDate: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000),
        promisedTime: 5,
        deliveryTime: 3,
        wasLate: false,
        productType: 'mechanical',
        urgency: 'high',
        quantity: 50,
        value: 7500
      }
    ];
  }
}