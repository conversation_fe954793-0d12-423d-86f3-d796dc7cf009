# Routes - Routing-Architektur

## Übersicht

Der `src/routes`-Ordner implementiert die komplette Routing-Architektur der Leitstand-App basierend auf **TanStack Router**. Die Architektur folgt einem modularen Ansatz mit rollenbasierter Zugriffskontrolle und unterstützt sowohl öffentliche als auch geschützte Routen.

## 📁 Dateistruktur

```
src/routes/
├── __root.tsx              # Root-Route mit Layout-Management
├── router.tsx               # Router-Konfiguration und -Setup
├── routes.tsx               # Legacy-Routen (wird schrittweise migriert)
└── modules/                 # Modulspezifische Routen
    ├── index.ts            # Zentrale Exports aller Modul-Routen
    ├── dashboard.routes.tsx # Dashboard-Modul Routen
    ├── stoerungen.routes.tsx# Störungen-Modul Routen
    ├── backend.routes.tsx   # Backend-Modul Routen
    └── ai.routes.tsx        # AI-Modul Routen
```

## 🏗️ Architektur-Übersicht

### Router-Pattern

- **TanStack Router**: Moderne, typsichere Routing-Lösung
- **Hash-History**: Optimiert für Electron-Apps (`file://` Protokoll)
- **Lazy Loading**: Code-Splitting für bessere Performance
- **Nested Routes**: Hierarchische Route-Struktur

### Sicherheitsarchitektur

- **AuthGuard**: Globale Authentifizierungsprüfung
- **ModuleGuard**: Rollenbasierte Modulzugriffskontrolle
- **ProtectedRoute**: Route-spezifischer Schutz
- **NavigationProvider**: Kontext für Navigation-State

### Layout-Management

- **DashboardLayout**: Standard-Layout für authentifizierte Bereiche
- **Public Routes**: Minimales Layout für Login/Register
- **Conditional Rendering**: Dynamisches Layout basierend auf Route

## 📄 Detaillierte Dateibeschreibungen

### `__root.tsx`

**Zweck**: Root-Route der Anwendung mit Layout-Management

**Hauptfunktionen**:
- Implementiert die Root-Route mit `createRootRoute()`
- Unterscheidet zwischen öffentlichen und geschützten Routen
- Integriert `AuthGuard` für globale Authentifizierungsprüfung
- Verwaltet `NavigationProvider` für Navigation-Context
- Rendert `DashboardLayout` für authentifizierte Bereiche
- Integriert `AuthenticatedChatBot` für AI-Funktionen

**Besonderheiten**:
- Conditional Layout Rendering basierend auf Route-Pfad
- Öffentliche Routen (`/login`, `/register`) ohne DashboardLayout
- Outlet-Pattern für verschachtelte Routen

### `router.tsx`

**Zweck**: Zentrale Router-Konfiguration und -Setup

**Hauptfunktionen**:
- Erstellt den Haupt-Router mit `createRouter()`
- Konfiguriert Hash-History für Electron-Kompatibilität
- Definiert Basis-Routen (Login, Register, Landing, Settings)
- Integriert alle Modul-Routen aus `modules/`
- Implementiert 404-Handling mit Weiterleitung
- Registriert TypeScript-Typen für Router

**Route-Struktur**:
```typescript
// Basis-Routen
/login          -> LoginPage
/register       -> RegistrationPage
/               -> UserLandingPage
/user-settings  -> UserSettingsPage
/settings       -> SettingsPage

// Modul-Routen (aus modules/)
/modules/*      -> Verschiedene Modul-Routen
```

### `modules/index.ts`

**Zweck**: Zentrale Exportstelle für alle Modul-Routen

**Hauptfunktionen**:
- Exportiert individuelle Modul-Routen
- Kombiniert alle Routen in `allModuleRoutes` Array
- Vereinfacht Import-Management für Router

**Exportierte Module**:
- `dashboardRoutes`: Dashboard-Modul Routen
- `stoerungenRoutes`: Störungen-Modul Routen
- `backendRoutes`: Backend-Modul Routen
- `aiRoutes`: AI-Modul Routen

### `modules/dashboard.routes.tsx`

**Zweck**: Routen für das Dashboard-Modul

**Hauptfunktionen**:
- Definiert 8 Dashboard-Routen mit `ModuleGuard`
- Implementiert Lazy Loading für alle Komponenten
- Nutzt `ModuleWrapper` für konsistentes Layout
- Rollenbasierte Zugriffskontrolle über `dashboardModuleConfig`

**Route-Struktur**:
```typescript
/modules/dashboard              -> DashboardPage
/modules/dashboard/dispatch     -> DispatchPage
/modules/dashboard/cutting      -> CuttingPage
/modules/dashboard/incoming-goods -> IncomingGoodsPage
/modules/dashboard/CSR          -> CSRPage
/modules/dashboard/aril         -> ArilPage
/modules/dashboard/atrl         -> AtrlPage
/modules/dashboard/machines     -> MachinesPage
```

### `modules/stoerungen.routes.tsx`

**Zweck**: Routen für das Störungen-Modul

**Hauptfunktionen**:
- Definiert Basis-Route für Störungen-Management
- Implementiert `ModuleGuard` mit `stoerungenModuleConfig`
- Vorbereitet für zukünftige Monitoring-Routen
- Lazy Loading für `StoerungenPage`

**Route-Struktur**:
```typescript
/modules/stoerungen -> StoerungenPage
// /modules/stoerungen/monitoring (geplant)
```

### `modules/backend.routes.tsx`

**Zweck**: Routen für das Backend & Automatisierung-Modul

**Hauptfunktionen**:
- Implementiert verschachtelte Route-Struktur
- Basis-Route mit `Outlet` für Sub-Routen
- Index-Route leitet zu System-Seite weiter
- `ModuleWrapper` für konsistentes Layout

**Route-Struktur**:
```typescript
/modules/backend/          -> SystemPage (Index)
/modules/backend/system    -> SystemPage
/modules/backend/workflows -> WorkflowPage
```

### `modules/ai.routes.tsx`

**Zweck**: Umfassende Routen für das AI-Modul

**Hauptfunktionen**:
- Definiert 15 AI-spezifische Routen
- Differenzierte Rollenberechtigung pro Route
- Lazy Loading für alle AI-Komponenten
- Spezielle Security-Route nur für Administratoren

**Route-Struktur**:
```typescript
/modules/ai                        -> AIDashboardPage
/modules/ai/chat                   -> AIChatPage
/modules/ai/rag-management         -> RAGManagementPage
/modules/ai/analysis               -> AIDashboardPage
/modules/ai/cutting-optimization   -> CuttingOptimizationPage
/modules/ai/inventory-intelligence -> InventoryIntelligencePage
/modules/ai/warehouse-optimization -> WarehouseOptimizationPage
/modules/ai/process-optimization   -> ProcessOptimizationPage
/modules/ai/predictive-analytics   -> PredictiveAnalyticsPage
/modules/ai/supply-chain-analytics -> SupplyChainAnalyticsPage
/modules/ai/supply-chain-optimization -> SupplyChainAnalyticsPage
/modules/ai/reporting              -> ReportingPage
/modules/ai/automated-reporting    -> ReportingPage
/modules/ai/security               -> AISecurityDashboard (Admin only)
/modules/ai/settings               -> AISettingsPage (Admin only)
```

## 🔧 Technische Details

### Abhängigkeiten

```json
{
  "@tanstack/react-router": "^1.x",
  "react": "^18.x",
  "@/components/auth": "AuthGuard, ModuleGuard, ProtectedRoute",
  "@/contexts": "NavigationContext, AuthContext",
  "@/layouts": "DashboardLayout",
  "@/modules/lazy-loading": "Lazy-loaded Komponenten"
}
```

### Router-Konfiguration

```typescript
// Hash-History für Electron
history: createHashHistory()

// 404-Handling
defaultNotFoundComponent: () => <Navigate to="/" />

// TypeScript-Integration
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}
```

### Sicherheitsfeatures

#### AuthGuard
- Globale Authentifizierungsprüfung
- Automatische Weiterleitung zu `/login`
- Token-Validierung beim App-Start

#### ModuleGuard
- Rollenbasierte Zugriffskontrolle
- Modulspezifische Berechtigungen
- Fallback-Route bei fehlenden Rechten

#### Route-Protection
```typescript
<ModuleGuard 
  moduleId="dashboard"
  requiredRoles={['Benutzer', 'Administrator']}
>
  <Component />
</ModuleGuard>
```

## 🎯 Verwendungsbeispiele

### Neue Route hinzufügen

```typescript
// 1. Route in entsprechendem Modul definieren
const NewFeatureRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/modules/dashboard/new-feature",
  component: () => (
    <ModuleGuard 
      moduleId={dashboardModuleConfig.id}
      requiredRoles={dashboardModuleConfig.requiredRoles}
    >
      <ModuleWrapper>
        <LazyNewFeaturePage />
      </ModuleWrapper>
    </ModuleGuard>
  ),
});

// 2. Route zu Export-Array hinzufügen
export const dashboardRoutes = [
  // ... existing routes
  NewFeatureRoute,
];
```

### Navigation zwischen Routen

```typescript
import { useNavigate } from '@tanstack/react-router';

const Component = () => {
  const navigate = useNavigate();
  
  const handleNavigation = () => {
    navigate({ to: '/modules/dashboard/cutting' });
  };
  
  return <button onClick={handleNavigation}>Zu Cutting</button>;
};
```

### Route-Parameter verwenden

```typescript
// Route mit Parameter definieren
const DetailRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/modules/dashboard/item/$itemId",
  component: () => <ItemDetailPage />
});

// Parameter in Komponente verwenden
const ItemDetailPage = () => {
  const { itemId } = useParams();
  return <div>Item ID: {itemId}</div>;
};
```

## 🚀 Performance-Optimierung

### Lazy Loading

```typescript
// Komponenten werden erst bei Bedarf geladen
const LazyDashboardPage = lazy(() => 
  import('@/modules/dashboard/pages/DashboardPage')
);
```

### Code-Splitting

- Automatisches Code-Splitting pro Route
- Reduzierte initiale Bundle-Größe
- Bessere Ladezeiten

### Route-Preloading

```typescript
// Preload-Strategien für häufig besuchte Routen
const preloadRoutes = [
  '/modules/dashboard',
  '/modules/stoerungen'
];
```

## 🧪 Testing

### Route-Tests

```typescript
// Beispiel für Route-Testing
describe('Dashboard Routes', () => {
  it('should render dashboard page', async () => {
    const router = createMemoryRouter({
      routes: dashboardRoutes,
      initialEntries: ['/modules/dashboard']
    });
    
    render(<RouterProvider router={router} />);
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
  });
});
```

### Guard-Tests

```typescript
// ModuleGuard Testing
describe('ModuleGuard', () => {
  it('should redirect unauthorized users', () => {
    const mockNavigate = jest.fn();
    render(
      <ModuleGuard 
        moduleId="dashboard" 
        requiredRoles={['Administrator']}
      >
        <div>Protected Content</div>
      </ModuleGuard>
    );
    
    expect(mockNavigate).toHaveBeenCalledWith({ to: '/' });
  });
});
```

## 🔄 Migration & Wartung

### Legacy Route Migration

- `routes.tsx` wird schrittweise zu modularen Routen migriert
- Bestehende Routen bleiben funktional während der Migration
- Neue Features nutzen modulare Route-Struktur

### Route-Wartung

```typescript
// Regelmäßige Überprüfung auf:
// 1. Unused Routes
// 2. Broken Links
// 3. Permission Updates
// 4. Performance Issues
```

## 📋 Entwicklungsrichtlinien

### Route-Naming

- Verwende kebab-case für Route-Pfade
- Modulpräfix für Organisation: `/modules/{module-name}`
- Beschreibende Namen für Klarheit

### Security Best Practices

- Immer `ModuleGuard` für geschützte Routen verwenden
- Minimale erforderliche Rollen definieren
- Fallback-Routen für unauthorized Access

### Performance Guidelines

- Lazy Loading für alle Routen-Komponenten
- Minimale Bundle-Größe pro Route
- Preloading für kritische Pfade

### Code-Organisation

- Ein Modul = Eine Route-Datei
- Zentrale Exports über `index.ts`
- Konsistente Namenskonventionen

## 🔗 Integration mit anderen Modulen

### Navigation-Integration

```typescript
// Navigation-Items werden automatisch aus Routen generiert
const navigationItems = allModuleRoutes.map(route => ({
  path: route.path,
  label: route.component.displayName
}));
```

### Auth-Integration

```typescript
// Routen nutzen AuthContext für Benutzerinformationen
const { user, isAuthenticated } = useAuthContext();
```

### Module-Integration

```typescript
// Routen referenzieren Module-Konfigurationen
import { dashboardModuleConfig } from '@/modules/dashboard/module.config';
```

---

**Hinweis**: Diese Routing-Architektur ist darauf ausgelegt, skalierbar und wartbar zu sein. Bei Fragen zur Route-Implementierung oder -Erweiterung, konsultiere die entsprechenden Modul-Dokumentationen oder die TanStack Router-Dokumentation.