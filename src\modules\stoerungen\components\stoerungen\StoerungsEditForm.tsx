import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Save,
  AlertTriangle,
  X,
  Image as ImageIcon,
  Trash2,
} from "lucide-react";
import {
  Stoerung,
  StoerungUpdateData,
  StoerungStatus,
  StoerungAttachment,
} from "@/types/stoerungen.types";
import stoerungenService from "@/services/stoerungen.service";
import stoerungKategorienService from "@/services/stoerung-kategorien.service";
import { useToast } from "@/components/ui/use-toast";
import FileUploadStoerung from "@/components/Animation/kokonutui/file-upload-stoerung";
import { bereitschaftsService } from "@/services/bereitschaftsService";
import { BereitschaftsPerson, BereitschaftsWoche } from "@/types/bereitschafts";

interface StoerungsEditFormProps {
  open: boolean;
  stoerung: Stoerung;
  onOpenChange: (open: boolean) => void;
  onClose: () => void;
  onSubmit: () => void;
}

/**
 * Komponente für die Bearbeitung bestehender Störungen
 * Ermöglicht das Ändern aller relevanten Felder einer Störung
 */
export const StoerungsEditForm: React.FC<StoerungsEditFormProps> = ({
  open,
  stoerung,
  onOpenChange,
  onClose,
  onSubmit,
}) => {
  // Funktion zur Konvertierung von Backend-Status zu Frontend-kompatiblen Werten für die Anzeige
  const mapStatusFromBackend = (
    backendStatus: string | StoerungStatus,
  ): StoerungStatus => {
    const statusMapping: { [key: string]: StoerungStatus } = {
      // Neue Werte
      NEW: StoerungStatus.NEU,
      IN_PROGRESS: StoerungStatus.IN_BEARBEITUNG,
      RESOLVED: StoerungStatus.GELÖST,
      // Alte Enum-Werte
      [String(StoerungStatus.NEU)]: StoerungStatus.NEU,
      [String(StoerungStatus.IN_BEARBEITUNG)]: StoerungStatus.IN_BEARBEITUNG,
      [String(StoerungStatus.GELÖST)]: StoerungStatus.GELÖST,
      [String(StoerungStatus.ESKALIERT_L2)]: StoerungStatus.IN_BEARBEITUNG,
      [String(StoerungStatus.ESKALIERT_L3)]: StoerungStatus.IN_BEARBEITUNG,
      [String(StoerungStatus.ABGESCHLOSSEN)]: StoerungStatus.GELÖST,
      [String(StoerungStatus.REVIEW)]: StoerungStatus.GELÖST,
    };

    return statusMapping[String(backendStatus)] || StoerungStatus.NEU;
  };

  // Funktion zur Konvertierung von Frontend-Status zu Backend-kompatiblen Werten
  const mapStatusToBackend = (
    frontendStatus: string | StoerungStatus,
  ): string => {
    const statusMapping: { [key: string]: string } = {
      [StoerungStatus.NEU]: "NEW",
      [StoerungStatus.IN_BEARBEITUNG]: "IN_PROGRESS",
      [StoerungStatus.GELÖST]: "RESOLVED",
      [String(StoerungStatus.NEU)]: "NEW",
      [String(StoerungStatus.IN_BEARBEITUNG)]: "IN_PROGRESS",
      [String(StoerungStatus.GELÖST)]: "RESOLVED",
      [String(StoerungStatus.ESKALIERT_L2)]: "IN_PROGRESS",
      [String(StoerungStatus.ESKALIERT_L3)]: "IN_PROGRESS",
      [String(StoerungStatus.ABGESCHLOSSEN)]: "RESOLVED",
      [String(StoerungStatus.REVIEW)]: "RESOLVED",
    };

    return statusMapping[String(frontendStatus)] || "NEW";
  };

  // Initialisiere Formular mit bestehenden Störungsdaten
  const [formData, setFormData] = useState<StoerungUpdateData>({
    title: stoerung.title,
    description: stoerung.description || "",
    severity: stoerung.severity,
    status: mapStatusFromBackend(stoerung.status),
    category: stoerung.category || "",
    affected_system: stoerung.affected_system || "",
    location: stoerung.location || "Ludwigsburg", // Standardwert auf Ludwigsburg
    assigned_to: stoerung.assigned_to || "",
    tags: stoerung.tags || [],
    resolution_steps: stoerung.resolution_steps || "",
    root_cause: stoerung.root_cause || "",
    lessons_learned: stoerung.lessons_learned || "",
  });

  // Backend-kompatibler Status-Wert für das Select-Feld
  const [backendStatusValue, setBackendStatusValue] = useState(() =>
    mapStatusToBackend(mapStatusFromBackend(stoerung.status))
  );

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Toast für Erfolgsmeldungen
  const { toast } = useToast();

  // State für Bild-Uploads und bestehende Anhänge
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [existingAttachments, setExistingAttachments] = useState<
    StoerungAttachment[]
  >([]);
  const [attachmentsLoading, setAttachmentsLoading] = useState(true);
  const [backendStatus, setBackendStatus] = useState<'unknown' | 'online' | 'offline'>('unknown');

  // API-Konfiguration laden
  const [apiConfig, setApiConfig] = useState<any>(null);

  // State für Bereitschaftsperson-Zuweisung
  const [bereitschaftsperson, setBereitschaftsperson] =
    useState<BereitschaftsPerson | null>(null);
  const [bereitschaftsLoading, setBereitschaftsLoading] = useState(false);

  // Kategorien-Management
  const [kategorien, setKategorien] = useState<any[]>([]);
  const [kategorienLoading, setKategorienLoading] = useState(true);
  const [kategorienError, setKategorienError] = useState<string | null>(null);

  // Optionen für Schweregrade
  const severityOptions = [
    { value: "LOW", label: "Niedrig", color: "text-green-600" },
    { value: "MEDIUM", label: "Mittel", color: "text-yellow-600" },
    { value: "HIGH", label: "Hoch", color: "text-orange-600" },
    { value: "CRITICAL", label: "Kritisch", color: "text-red-600" },
  ];

  // Status-Optionen - Nur Backend-kompatible Werte verwenden
  // Backend akzeptiert nur: 'NEW', 'IN_PROGRESS', 'RESOLVED'
  const statusOptions = [
    { value: "NEW", label: "Neu", color: "text-blue-600" },
    { value: "IN_PROGRESS", label: "In Bearbeitung", color: "text-orange-600" },
    { value: "RESOLVED", label: "Gelöst", color: "text-green-600" },
  ];

  // Sichere Erstellung der Kategorie-Optionen mit verbessertem Null-Check
  const categoryOptions =
    kategorien
      ?.map((k) => k?.name)
      .filter(
        (name): name is string =>
          name != null && typeof name === "string" && name.trim().length > 0,
      ) || [];
  const [affectedSystemOptions, setAffectedSystemOptions] = useState<string[]>(
    [],
  );

  // Initialisiere API-Konfiguration
  useEffect(() => {
    const initializeApiConfig = async () => {
      try {
        const config = await import("@/services/api-config.service");
        const apiConfigData = config.getApiConfigSync();
        setApiConfig(apiConfigData);
        console.log("[DEBUG] API-Konfiguration geladen:", apiConfigData.apiBaseUrl);
      } catch (error) {
        console.error("[DEBUG] Fehler beim Laden der API-Konfiguration:", error);
      }
    };

    initializeApiConfig();
  }, []);

  // Funktion zum Prüfen des Backend-Status
  const checkBackendStatus = useCallback(async () => {
    try {
      const config = await import("@/services/api-config.service");
      const isConnected = await config.testApiConnection();

      if (isConnected) {
        setBackendStatus('online');
        return true;
      } else {
        setBackendStatus('offline');
        return false;
      }
    } catch (error) {
      console.error("[DEBUG] Backend-Statusprüfung fehlgeschlagen:", error);
      setBackendStatus('offline');
      return false;
    }
  }, []);

  const loadAttachments = useCallback(async () => {
    try {
      setAttachmentsLoading(true);
      console.log("[DEBUG] loadAttachments wird aufgerufen für Störung ID:", stoerung.id);

      // Stelle sicher, dass API-Konfiguration verfügbar ist
      let currentApiConfig = apiConfig;
      if (!currentApiConfig) {
        console.log("[DEBUG] API-Konfiguration wird geladen...");
        const config = await import("@/services/api-config.service");
        currentApiConfig = config.getApiConfigSync();
        setApiConfig(currentApiConfig);
      }

      const baseUrl = currentApiConfig.apiBaseUrl;
      const url = `${baseUrl}/stoerungen/${stoerung.id}/attachments`;

      console.log("[DEBUG] Lade Anhänge von URL:", url);
      console.log("[DEBUG] API-Basis-URL:", baseUrl);

      // Prüfe Backend-Status zuerst
      const isBackendOnline = await checkBackendStatus();
      console.log("[DEBUG] Backend-Status geprüft:", isBackendOnline, "Status:", backendStatus);

      // Lade Attachments auch wenn Backend-Status unbekannt ist (versuche es einfach)
      if (!isBackendOnline && backendStatus !== 'unknown') {
        console.error("[DEBUG] Backend ist offline - kann Anhänge nicht laden");
        toast({
          title: "Backend nicht verfügbar",
          description: "Das Backend läuft nicht. Bitte starten Sie den Backend-Server auf Port 3001.",
          type: "error",
        });
        setExistingAttachments([]);
        return;
      }

      // Erstelle authentifizierte Headers
      const configModule = await import("@/services/api-config.service");
      const headers = configModule.createAuthHeaders();

      const response = await fetch(url, {
        method: 'GET',
        headers: headers,
      });

      console.log("[DEBUG] Lade Anhänge - Response status:", response.status);
      console.log("[DEBUG] Lade Anhänge - Response ok:", response.ok);

      if (response.ok) {
        const contentType = response.headers.get("content-type");
        console.log("[DEBUG] Content-Type:", contentType);

        if (contentType && contentType.includes("application/json")) {
          const result = await response.json();
          console.log("[DEBUG] Anhänge geladen - Rohdaten:", result);

          // Prüfe die Struktur der Antwort
          if (result.success && result.data) {
            console.log("[DEBUG] Anhänge erfolgreich geladen:", result.data.length, "Bilder");
            setExistingAttachments(result.data);
          } else if (Array.isArray(result)) {
            // Fallback für direkte Array-Antwort
            console.log("[DEBUG] Direkte Array-Antwort erhalten:", result.length, "Bilder");
            setExistingAttachments(result);
          } else {
            console.warn("[DEBUG] Unerwartete Antwortstruktur:", result);
            setExistingAttachments(result.data || []);
          }
        } else {
          console.error("Unerwarteter Content-Type für Anhänge:", contentType);
          const textResponse = await response.text();
          console.error("Antwort-Content:", textResponse);

          if (textResponse.includes("<!DOCTYPE") || textResponse.includes("<html")) {
            console.error("API gab HTML-Fehlerseite zurück statt JSON - Backend-Problem");
            toast({
              title: "Backend-Fehler",
              description: "Das Backend hat eine HTML-Fehlerseite zurückgegeben. Bitte prüfen Sie die Backend-Konsole.",
              type: "error",
            });
          }

          setExistingAttachments([]);
        }
      } else {
        console.error("Fehler beim Laden der Anhänge - Status:", response.status);
        console.error("Fehler beim Laden der Anhänge - StatusText:", response.statusText);

        const errorText = await response.text();
        console.error("Fehler-Antwort:", errorText);

        if (response.status === 404) {
          console.log("[DEBUG] Keine Anhänge für diese Störung gefunden (404) - das ist normal");
          setExistingAttachments([]);
        } else if (response.status === 401 || response.status === 403) {
          console.error("[DEBUG] Authentifizierungsfehler beim Laden der Anhänge");
          toast({
            title: "Authentifizierungsfehler",
            description: "Sie haben keine Berechtigung, die Anhänge zu laden.",
            type: "error",
          });
          setExistingAttachments([]);
        } else if (errorText.includes("<!DOCTYPE") || errorText.includes("<html")) {
          console.error("API gab HTML-Fehlerseite zurück statt JSON - Backend läuft nicht korrekt");
          toast({
            title: "Backend-Verbindungsfehler",
            description: "Das Backend ist nicht korrekt konfiguriert oder läuft nicht.",
            type: "error",
          });
          setExistingAttachments([]);
        } else {
          try {
            const errorData = JSON.parse(errorText);
            console.error("[DEBUG] JSON-Fehlerantwort:", errorData);
            toast({
              title: "Fehler beim Laden der Anhänge",
              description: errorData.message || errorData.error || "Unbekannter Fehler",
              type: "error",
            });
          } catch {
            console.error("[DEBUG] Nicht-JSON-Fehlerantwort:", errorText);
          }
          setExistingAttachments([]);
        }
      }
    } catch (error) {
      console.error("Netzwerk- oder anderer Fehler beim Laden der Anhänge:", error);

      if (error instanceof TypeError && error.message.includes('fetch')) {
        console.error("[DEBUG] Netzwerkfehler - Backend ist wahrscheinlich nicht erreichbar");
        toast({
          title: "Netzwerkfehler",
          description: "Das Backend ist nicht erreichbar. Bitte prüfen Sie Ihre Internetverbindung und starten Sie den Backend-Server.",
          type: "error",
        });
      } else {
        toast({
          title: "Fehler beim Laden der Anhänge",
          description: error instanceof Error ? error.message : "Unbekannter Fehler",
          type: "error",
        });
      }

      setExistingAttachments([]);
    } finally {
      setAttachmentsLoading(false);
    }
  }, [apiConfig, stoerung.id, toast, checkBackendStatus]);

  // Lade Attachments wenn API-Konfiguration verfügbar wird
  useEffect(() => {
    if (apiConfig && open) {
      console.log("[DEBUG] API-Konfiguration verfügbar, lade Attachments...");
      console.log("[DEBUG] Aktuelle Attachments-Anzahl:", existingAttachments.length);
      console.log("[DEBUG] Backend-Status:", backendStatus);
      console.log("[DEBUG] Loading-Status:", attachmentsLoading);
      loadAttachments();
    }
  }, [apiConfig, open, loadAttachments]);

  // Debug: Zeige Attachments-Status
  useEffect(() => {
    console.log("[DEBUG] Attachments-Status geändert:", {
      loading: attachmentsLoading,
      count: existingAttachments.length,
      backendStatus: backendStatus,
      apiConfig: !!apiConfig,
      open: open
    });
  }, [attachmentsLoading, existingAttachments.length, backendStatus, apiConfig, open]);

  // Lade Kategorien beim ersten Rendern
  useEffect(() => {
    const loadKategorien = async () => {
      try {
        setKategorienLoading(true);
        const data = await stoerungKategorienService.getKategorien();
        setKategorien(data);
        setKategorienError(null);
      } catch (err) {
        console.error("Fehler beim Laden der Kategorien:", err);
        setKategorienError("Fehler beim Laden der Kategorien");

        // Fallback auf hardcoded Daten falls Service nicht verfügbar
        const fallbackData = [
          {
            name: "Systeme",
            systeme: ["SAP", "ITM", "Wamas", "Mail", "Citrix", "MFR"],
          },
          { name: "Infrastruktur", systeme: ["Netzwerk", "WLAN"] },
          {
            name: "Technische Komponenten",
            systeme: [
              "Förderband",
              "FTS",
              "RBG",
              "Greifer",
              "Schrumpfanlage",
              "Ablängmaschinen",
            ],
          },
          {
            name: "Kommissionierung",
            systeme: [
              "ATrL",
              "ARiL",
              "SCS",
            ],
          },
          {
            name: "Hardware",
            systeme: [
              "Mobile Drucker",
              "Label Drucker",
              "Lieferschein Drucker",
              "Terminal",
              "PC",
            ],
          },
        ];
        setKategorien(fallbackData);
      } finally {
        setKategorienLoading(false);
      }
    };

    loadKategorien();

    // Lade Bereitschaftsperson basierend auf Erstellungsdatum
    if (stoerung.created_at) {
      getBereitschaftspersonAtDate(stoerung.created_at).then((person) => {
        setBereitschaftsperson(person);
        if (person && !formData.assigned_to) {
          // Setze den Namen der Bereitschaftsperson, falls noch nicht zugewiesen
          setFormData((prev) => ({
            ...prev,
            assigned_to: person.name,
          }));
        }
      });
    }
  }, [stoerung.created_at]);

  // Aktualisiere betroffene Systeme basierend auf gewählter Kategorie
  useEffect(() => {
    if (formData.category && kategorien?.length > 0) {
      const selectedCategory = kategorien.find(
        (k) => k?.name === formData.category,
      );
      if (selectedCategory?.systeme) {
        // Verbesserte Filterung für betroffene Systeme
        const validSystems = selectedCategory.systeme.filter(
          (system: string) =>
            system && typeof system === "string" && system.trim().length > 0,
        );
        setAffectedSystemOptions(validSystems || []);
      } else {
        setAffectedSystemOptions([]);
      }
    } else {
      // Wenn keine Kategorie gewählt ist, aber ein affected_system aus der DB existiert,
      // füge es zu den Optionen hinzu, damit es angezeigt werden kann
      if (
        formData.affected_system &&
        formData.affected_system.trim().length > 0
      ) {
        setAffectedSystemOptions([formData.affected_system]);
      } else {
        setAffectedSystemOptions([]);
      }
    }
  }, [formData.category, kategorien, formData.affected_system]);

  // Initialer useEffect: Stelle sicher, dass das affected_system aus der DB angezeigt wird
  useEffect(() => {
    // Debug: Zeige an, was aus der Datenbank geladen wird
    console.log("[StoerungsEditForm] Lade affected_system aus DB:", {
      stoerung_affected_system: stoerung.affected_system,
      formData_affected_system: formData.affected_system,
      current_affectedSystemOptions: affectedSystemOptions,
    });

    // Beim ersten Laden der Komponente: Falls ein affected_system aus der DB existiert,
    // aber noch keine Kategorien geladen sind, füge es zu den Optionen hinzu
    if (
      stoerung.affected_system &&
      typeof stoerung.affected_system === 'string' &&
      stoerung.affected_system.trim().length > 0
    ) {
      setAffectedSystemOptions((prev) => {
        const affectedSystem = stoerung.affected_system!; // Sicher da wir oben prüfen
        if (!prev.includes(affectedSystem)) {
          console.log(
            "[StoerungsEditForm] Füge affected_system zu Optionen hinzu:",
            affectedSystem,
          );
          return [affectedSystem, ...prev];
        }
        return prev;
      });
    }
  }, [stoerung.affected_system]); // Nur beim Ändern der ursprünglichen Störung

  // Funktion zur Ermittlung der Bereitschaftsperson basierend auf dem Erstellungsdatum
  const getBereitschaftspersonAtDate = async (
    erstellungsDatum: string,
  ): Promise<BereitschaftsPerson | null> => {
    try {
      setBereitschaftsLoading(true);

      // Berechne das Jahr und die KW basierend auf dem Erstellungsdatum
      const createdDate = new Date(erstellungsDatum);
      const startOfWeek = new Date(createdDate);
      const dayOfWeek = createdDate.getDay();
      const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // Montag als Wochenstart
      startOfWeek.setDate(createdDate.getDate() + mondayOffset);

      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);

      // Hole alle Bereitschaftswochen für den relevanten Zeitraum (erweitert um ±1 Woche)
      const searchStart = new Date(startOfWeek);
      searchStart.setDate(startOfWeek.getDate() - 7);
      const searchEnd = new Date(endOfWeek);
      searchEnd.setDate(endOfWeek.getDate() + 7);

      console.log("[DEBUG] getBereitschaftspersonAtDate - Suche für Datum:", {
        erstellungsDatum,
        createdDate: createdDate.toISOString(),
        startOfWeek: startOfWeek.toISOString(),
        endOfWeek: endOfWeek.toISOString(),
        searchStart: searchStart.toISOString().split("T")[0],
        searchEnd: searchEnd.toISOString().split("T")[0],
      });

      const wochenplan = await bereitschaftsService.getWochenplan(
        searchStart.toISOString().split("T")[0],
        3, // 3 Wochen: -1, 0, +1
      );

      console.log("[DEBUG] getBereitschaftspersonAtDate - Erhaltener Wochenplan:", wochenplan);

      // Finde die passende Bereitschaftswoche
      const passendeBereitschaft = wochenplan.find(
        (woche: BereitschaftsWoche) => {
          console.log("[DEBUG] getBereitschaftspersonAtDate - Prüfe Woche:", {
            wochenStart: woche.wochenStart,
            wochenEnde: woche.wochenEnde,
            aktiv: woche.aktiv,
            personId: woche.personId,
            createdDate: createdDate.toISOString(),
          });

          // Prüfe, ob die Woche aktiv ist
          if (!woche.aktiv) {
            console.log("[DEBUG] Woche ist nicht aktiv, überspringe");
            return false;
          }

          // Normalisiere und konvertiere die Datenbank-Daten in Date-Objekte
          const normalizeDateString = (dateStr: string): Date | null => {
            try {
              if (typeof dateStr === 'string') {
                console.log("[DEBUG] Normalizing date string:", dateStr);

                // Handle malformed strings like '2025-08-17T22:00:00T00:00:00.000ZZ'
                // Pattern: YYYY-MM-DDTHH:mm:ssT00:00:00.000ZZ
                let normalized = dateStr;

                // Remove duplicate 'T' and 'Z' characters and extra parts
                normalized = normalized.replace(/T(\d{2}:\d{2}:\d{2})T/g, 'T$1').replace(/Z+/g, 'Z');

                // Try to extract valid date components using regex
                const dateMatch = normalized.match(/(\d{4}-\d{2}-\d{2})T(\d{2}:\d{2}:\d{2})/);
                if (dateMatch) {
                  // Reconstruct proper ISO string: YYYY-MM-DDTHH:mm:ssZ
                  const properISO = `${dateMatch[1]}T${dateMatch[2]}Z`;
                  console.log("[DEBUG] Reconstructed ISO string:", properISO);

                  const parsed = new Date(properISO);
                  if (!isNaN(parsed.getTime())) {
                    console.log("[DEBUG] Successfully parsed reconstructed date:", parsed.toISOString());
                    return parsed;
                  }
                }

                // More aggressive cleanup: look for the first valid date pattern
                const aggressiveMatch = dateStr.match(/(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})/);
                if (aggressiveMatch) {
                  const extractedDateTime = aggressiveMatch[1];
                  // Add Z if missing
                  const withZ = extractedDateTime.endsWith('Z') ? extractedDateTime : extractedDateTime + 'Z';
                  console.log("[DEBUG] Aggressively extracted date:", withZ);

                  const parsed = new Date(withZ);
                  if (!isNaN(parsed.getTime())) {
                    console.log("[DEBUG] Successfully parsed aggressively extracted date:", parsed.toISOString());
                    return parsed;
                  }
                }

                // If the string still has issues, try to extract the valid ISO part
                if (normalized.includes('T') && normalized.includes('Z')) {
                  const parsed = new Date(normalized);
                  if (!isNaN(parsed.getTime())) {
                    console.log("[DEBUG] Successfully parsed normalized date:", parsed.toISOString());
                    return parsed;
                  }
                }

                // Last resort: try to parse the original string
                const fallbackParsed = new Date(dateStr);
                if (!isNaN(fallbackParsed.getTime())) {
                  console.log("[DEBUG] Successfully parsed original date:", fallbackParsed.toISOString());
                  return fallbackParsed;
                }

                console.warn("[DEBUG] All parsing attempts failed for:", dateStr);
              }
              return null;
            } catch (error) {
              console.warn("Failed to normalize date string:", dateStr, error);
              return null;
            }
          };

          // Try multiple approaches to parse the dates
          const wochenStart = normalizeDateString(woche.wochenStart);
          const wochenEnde = normalizeDateString(woche.wochenEnde);

          // If normalization failed, try a simpler approach
          let startDate: Date | null = wochenStart;
          let endDate: Date | null = wochenEnde;

          if (!startDate || !endDate) {
            // Try to extract just the date part and create reasonable time ranges
            const extractDateFromString = (dateStr: string): Date | null => {
              try {
                // Look for YYYY-MM-DD pattern
                const dateMatch = dateStr.match(/(\d{4}-\d{2}-\d{2})/);
                if (dateMatch) {
                  const datePart = dateMatch[1];
                  // Create a date object from just the date part
                  const extractedDate = new Date(datePart + 'T00:00:00.000Z');
                  if (!isNaN(extractedDate.getTime())) {
                    console.log("[DEBUG] Extracted date from string:", dateStr, "->", extractedDate.toISOString());
                    return extractedDate;
                  }
                }
                return null;
              } catch (error) {
                return null;
              }
            };

            if (!startDate) {
              startDate = extractDateFromString(woche.wochenStart);
              console.log("[DEBUG] Fallback startDate result:", startDate);
            }
            if (!endDate) {
              endDate = extractDateFromString(woche.wochenEnde);
              console.log("[DEBUG] Fallback endDate result:", endDate);
            }
          }

          if (!startDate || !endDate) {
            console.warn("Invalid date strings in week data:", {
              wochenStart: woche.wochenStart,
              wochenEnde: woche.wochenEnde,
            });
            return false;
          }

          // If we only have date parts, create reasonable time ranges
          if (startDate.getHours() === 0 && startDate.getMinutes() === 0) {
            // This looks like a date-only string, set to start of day
            startDate.setHours(0, 0, 0, 0);
          }
          if (endDate.getHours() === 0 && endDate.getMinutes() === 0) {
            // This looks like a date-only string, set to end of day
            endDate.setHours(23, 59, 59, 999);
          }

          console.log("[DEBUG] getBereitschaftspersonAtDate - Zeitvergleich:", {
            wochenStart: startDate.toISOString(),
            wochenEnde: endDate.toISOString(),
            createdDate: createdDate.toISOString(),
            isAfterStart: createdDate >= startDate,
            isBeforeEnd: createdDate <= endDate,
          });

          // Prüfe, ob das Erstellungsdatum in die Woche fällt
          let dateInRange = createdDate >= startDate && createdDate <= endDate;

          // Fallback: Simple string-based comparison if date comparison fails
          if (!dateInRange) {
            const createdDateStr = createdDate.toISOString().split('T')[0]; // YYYY-MM-DD
            const startDateStr = startDate.toISOString().split('T')[0];
            const endDateStr = endDate.toISOString().split('T')[0];

            // Simple string comparison as last resort
            if (createdDateStr >= startDateStr && createdDateStr <= endDateStr) {
              console.log("[DEBUG] String-based fallback matched:", {
                createdDateStr,
                startDateStr,
                endDateStr
              });
              dateInRange = true;
            }
          }

          return dateInRange;
        }
      );

      if (passendeBereitschaft && passendeBereitschaft.person) {
        console.log("[DEBUG] getBereitschaftspersonAtDate - Gefundene Bereitschaft:", {
          personId: passendeBereitschaft.personId,
          wochenStart: passendeBereitschaft.wochenStart,
          wochenEnde: passendeBereitschaft.wochenEnde,
          aktiv: passendeBereitschaft.aktiv,
        });
        return passendeBereitschaft.person;
      } else {
        console.warn(
          "Keine Bereitschaftsperson für das Datum gefunden:",
          erstellungsDatum,
          {
            startOfWeek: startOfWeek.toISOString(),
            endOfWeek: endOfWeek.toISOString(),
            wochenplanLength: wochenplan.length,
            wochenplan,
          }
        );
        return null;
      }
    } catch (error) {
      console.error("Fehler bei der Ermittlung der Bereitschaftsperson:", error);
      return null;
    } finally {
      setBereitschaftsLoading(false);
    }
  };


  // Funktion zum Löschen bestehender Anhänge
  const handleDeleteAttachment = async (attachmentId: number) => {
    try {
      const config = await import("@/services/api-config.service");
      const apiConfig = config.getApiConfigSync();
      const baseUrl = apiConfig.apiBaseUrl;
      const url = `${baseUrl}/stoerungen/attachments/${attachmentId}`;

      console.log("[DEBUG] Lösche Anhang von URL:", url);

      // Erstelle authentifizierte Headers
      const configModule = await import("@/services/api-config.service");
      const headers = configModule.createAuthHeaders();

      const response = await fetch(url, {
        method: "DELETE",
        headers: headers,
      });

      console.log("[DEBUG] Lösche Anhang - Response status:", response.status);

      if (response.ok) {
        const result = await response.json();
        console.log("[DEBUG] Anhang erfolgreich gelöscht:", result);

        setExistingAttachments((prev) =>
          prev.filter((att) => att.id !== attachmentId),
        );

        toast({
          title: "Anhang gelöscht",
          description: "Der Anhang wurde erfolgreich entfernt.",
          type: "success",
        });
      } else {
        const errorText = await response.text();
        console.error("[DEBUG] Fehler beim Löschen - Status:", response.status);
        console.error("[DEBUG] Fehler beim Löschen - Antwort:", errorText);

        let errorMessage = "Der Anhang konnte nicht gelöscht werden.";

        if (response.status === 404) {
          errorMessage = "Der Anhang wurde nicht gefunden.";
        } else if (response.status === 401 || response.status === 403) {
          errorMessage = "Sie haben keine Berechtigung, diesen Anhang zu löschen.";
        } else if (errorText.includes("<!DOCTYPE") || errorText.includes("<html")) {
          errorMessage = "Backend-Verbindungsfehler beim Löschen des Anhangs.";
        } else {
          try {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.message || errorData.error || errorMessage;
          } catch {
            // Nicht-JSON-Antwort, verwende Standardnachricht
          }
        }

        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error("Fehler beim Löschen des Anhangs:", error);

      const errorMessage = error instanceof Error ? error.message : "Unbekannter Fehler";

      toast({
        title: "Fehler beim Löschen",
        description: errorMessage,
        type: "error",
      });
    }
  };

  const handleInputChange = (
    field: keyof StoerungUpdateData,
    value: string,
  ) => {
    // Blockiere Änderungen an unverzichtbaren Feldern
    if (field === "location" || field === "assigned_to") {
      console.warn(
        `Feld ${field} ist unverzichtbar und kann nicht geändert werden`,
      );
      return;
    }

    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Update backend status value when status changes
    if (field === 'status') {
      setBackendStatusValue(mapStatusToBackend(value as StoerungStatus));
    }

    // Reset affected_system wenn category geändert wird
    if (field === "category") {
      setFormData((prev) => ({
        ...prev,
        affected_system: "",
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log("[DEBUG] handleSubmit wurde aufgerufen!");

    if (!formData.title?.trim()) {
      console.log("[DEBUG] Validierung fehlgeschlagen: Titel ist erforderlich");
      setError("Titel ist erforderlich");
      return;
    }

    console.log("[DEBUG] Formulardaten vor dem Senden:", formData);

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Bereite Daten für Backend vor - konvertiere Status zu Backend-kompatiblen Werten
      const backendData: StoerungUpdateData = {
        ...formData,
        status: formData.status
          ? mapStatusFromBackend(mapStatusToBackend(formData.status))
          : StoerungStatus.NEU,
        location: "Ludwigsburg", // Standort ist immer Ludwigsburg
        assigned_to: bereitschaftsperson
          ? bereitschaftsperson.name
          : formData.assigned_to, // Verwende Bereitschaftsperson falls vorhanden
      };

      console.log("Sende Daten an Backend:", backendData);
      console.log("[DEBUG] Rufe stoerungenService.updateStoerung auf mit ID:", stoerung.id);

      // Aktualisiere die Störung mit Backend-kompatiblen Daten
      const result = await stoerungenService.updateStoerung(stoerung.id, backendData);
      console.log("[DEBUG] API-Aufruf erfolgreich, Ergebnis:", result);

      // Lade neue Bilder hoch, falls vorhanden
      if (uploadedImages.length > 0) {
        try {
          const config = await import("@/services/api-config.service");
          const apiConfig = config.getApiConfigSync();
          const baseUrl = apiConfig.apiBaseUrl;
          const uploadUrl = `${baseUrl}/stoerungen/${stoerung.id}/upload-image`;

          console.log("[DEBUG] Lade Bilder hoch zu URL:", uploadUrl);

          let uploadedCount = 0;
          let failedUploads: string[] = [];

          for (const image of uploadedImages) {
            try {
              const formData = new FormData();
              formData.append("image", image);

              // Erstelle authentifizierte Headers für Upload
              const configModule = await import("@/services/api-config.service");
              const headers = configModule.createAuthHeaders();
              // Entferne Content-Type für FormData (wird automatisch gesetzt)
              delete headers['Content-Type'];

              const uploadResponse = await fetch(uploadUrl, {
                method: "POST",
                headers: headers,
                body: formData,
              });

              console.log(`[DEBUG] Upload ${image.name} - Status:`, uploadResponse.status);

              if (uploadResponse.ok) {
                const result = await uploadResponse.json();
                console.log(`[DEBUG] Upload ${image.name} erfolgreich:`, result);
                uploadedCount++;
              } else {
                const errorText = await uploadResponse.text();
                console.error(`[DEBUG] Upload ${image.name} fehlgeschlagen:`, errorText);

                let errorMessage = `Fehler beim Hochladen von ${image.name}`;
                if (uploadResponse.status === 413) {
                  errorMessage = `${image.name} ist zu groß (max. 5MB)`;
                } else if (uploadResponse.status === 415) {
                  errorMessage = `${image.name} hat ein ungültiges Format`;
                } else if (errorText.includes("<!DOCTYPE") || errorText.includes("<html")) {
                  errorMessage = `${image.name}: Backend-Verbindungsfehler`;
                } else {
                  try {
                    const errorData = JSON.parse(errorText);
                    errorMessage = errorData.message || errorData.error || errorMessage;
                  } catch {
                    // Nicht-JSON-Antwort
                  }
                }

                failedUploads.push(errorMessage);
              }
            } catch (singleUploadError) {
              console.error(`[DEBUG] Upload ${image.name} fehlgeschlagen:`, singleUploadError);
              failedUploads.push(`${image.name}: ${singleUploadError instanceof Error ? singleUploadError.message : 'Unbekannter Fehler'}`);
            }
          }

          // Bereinige hochgeladene Bilder aus dem State
          setUploadedImages([]);

          if (uploadedCount > 0) {
            if (failedUploads.length === 0) {
              // Alle Bilder erfolgreich hochgeladen
              toast({
                title: "Störung aktualisiert",
                description: `Die Störung wurde erfolgreich gespeichert und ${uploadedCount} Bild(er) hochgeladen.`,
                type: "success",
              });
            } else {
              // Teilweise erfolgreich
              toast({
                title: "Teilweise erfolgreich",
                description: `${uploadedCount} Bild(er) wurden hochgeladen, aber ${failedUploads.length} fehlgeschlagen: ${failedUploads.join(', ')}`,
                type: "warning",
              });
            }
          } else {
            // Alle Uploads fehlgeschlagen
            toast({
              title: "Upload fehlgeschlagen",
              description: `Alle ${failedUploads.length} Bilder konnten nicht hochgeladen werden: ${failedUploads.join(', ')}`,
              type: "error",
            });
          }

          // Lade Anhänge neu, um die neuen Bilder anzuzeigen
          // Note: loadAttachments wird im useEffect definiert und ist hier nicht verfügbar
          // Die Bilder werden beim nächsten Öffnen des Dialogs geladen

        } catch (uploadError) {
          console.error("Allgemeiner Fehler beim Hochladen der Bilder:", uploadError);

          // Bereinige hochgeladene Bilder aus dem State
          setUploadedImages([]);

          toast({
            title: "Upload-Fehler",
            description: "Es gab Probleme beim Hochladen der Bilder. Bitte versuchen Sie es erneut.",
            type: "error",
          });
        }
      } else {
        // Zeige Toast-Erfolgsmeldung ohne Bild-Upload
        toast({
          title: "Störung aktualisiert",
          description: "Die Störung wurde erfolgreich gespeichert.",
          type: "success",
        });
      }

      // Erfolgreich - schließe Dialog und aktualisiere Liste
      onSubmit();
      onClose();
    } catch (err) {
      console.error("Fehler beim Aktualisieren der Störung:", err);
      setError("Fehler beim Speichern der Änderungen");
    } finally {
      setLoading(false);
    }
  };

  // Bedingte Rückgabe nach allen Hook-Aufrufen, um React Rules of Hooks zu befolgen
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/50" onClick={onClose} />

      {/* Dialog Content */}
      <div className="relative mx-4 flex max-h-[90vh] w-full max-w-6xl flex-col overflow-hidden rounded-lg bg-white shadow-lg">
        {/* Header */}
        <div className="flex items-center justify-between border-b bg-gray-100 px-6 py-2">
          <h2 className="flex items-center gap-2 text-xl font-semibold">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            Störung bearbeiten
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="max-h-[calc(90vh-80px)] overflow-y-auto p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="rounded-md border border-red-200 bg-red-50 p-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            )}

            {kategorienError && (
              <div className="rounded-md border border-yellow-200 bg-yellow-50 p-3">
                <p className="text-sm text-yellow-800">
                  ⚠️ {kategorienError} - Verwende Fallback-Daten
                </p>
              </div>
            )}

            {backendStatus === 'offline' && (
              <div className="rounded-md border border-red-200 bg-red-50 p-3">
                <div className="flex items-start">
                  <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5 mr-2" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-red-800">
                      Backend-Server nicht verfügbar
                    </p>
                    <p className="text-sm text-red-700 mt-1">
                      Das Backend läuft nicht auf Port 3001. Bilder können nicht geladen oder hochgeladen werden.
                    </p>
                    <div className="mt-2 text-xs text-red-600">
                      <p><strong>So starten Sie das Backend:</strong></p>
                      <ol className="list-decimal list-inside mt-1 space-y-1">
                        <li>Öffnen Sie ein Terminal im Backend-Ordner</li>
                        <li>Führen Sie <code className="bg-red-100 px-1 rounded">npm run dev</code> aus</li>
                        <li>Warten Sie bis "✅ Server läuft auf Port 3001" angezeigt wird</li>
                        <li>Laden Sie diese Seite neu</li>
                      </ol>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Titel */}
            <div className="space-y-2">
              <Label htmlFor="title">Titel *</Label>
              <Input
                id="title"
                value={formData.title || ""}
                onChange={(e) => handleInputChange("title", e.target.value)}
                placeholder="Kurze Beschreibung der Störung"
                required
              />
            </div>

            {/* Beschreibung */}
            <div className="space-y-2">
              <Label htmlFor="description">Beschreibung</Label>
              <Textarea
                id="description"
                value={formData.description || ""}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                placeholder="Detaillierte Beschreibung der Störung"
                rows={3}
              />
            </div>

            {/* Schweregrad, Kategorie, Status, betroffenes System, Standort und Zugewiesen an in einer Reihe */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-6">
              <div className="space-y-2">
                <Label htmlFor="severity">Schweregrad</Label>
                <Select
                  value={formData.severity}
                  onValueChange={(value) =>
                    handleInputChange("severity", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Schweregrad wählen" />
                  </SelectTrigger>
                  <SelectContent>
                    {severityOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <span className={option.color}>{option.label}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Kategorie</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) =>
                    handleInputChange("category", value)
                  }
                  disabled={kategorienLoading}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        kategorienLoading
                          ? "Lade Kategorien..."
                          : "Kategorie wählen"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryOptions.map((category, index) => {
                      if (!category || typeof category !== "string") {
                        return null;
                      }
                      return (
                        <SelectItem
                          key={`category-${index}-${category}`}
                          value={category}
                        >
                          {category}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={backendStatusValue}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Status wählen" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <span className={option.color}>{option.label}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="affected_system">Betroffenes System</Label>
                <Select
                  value={formData.affected_system}
                  onValueChange={(value) =>
                    handleInputChange("affected_system", value)
                  }
                  disabled={!formData.category && !formData.affected_system}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        !formData.category && !formData.affected_system
                          ? "Erst Kategorie wählen"
                          : "System wählen"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {/* Wenn ein affected_system aus der DB existiert, aber nicht in den aktuellen Optionen ist, füge es hinzu */}
                    {formData.affected_system &&
                      !affectedSystemOptions.includes(
                        formData.affected_system,
                      ) && (
                        <SelectItem
                          key={`current-system-${formData.affected_system}`}
                          value={formData.affected_system}
                        >
                          {formData.affected_system} (Aktuell)
                        </SelectItem>
                      )}
                    {affectedSystemOptions.map((system, index) => {
                      if (!system || typeof system !== "string") {
                        return null;
                      }
                      return (
                        <SelectItem
                          key={`system-${index}-${system}`}
                          value={system}
                        >
                          {system}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">Standort</Label>
                <Input
                  id="location"
                  value={formData.location || "Ludwigsburg"}
                  readOnly
                  disabled
                  className="cursor-not-allowed bg-gray-100 text-gray-600"
                  placeholder="Standort (unveränderbar)"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="assigned_to">Zugewiesen an</Label>
                <div className="relative">
                  <Input
                    id="assigned_to"
                    value={
                      bereitschaftsperson
                        ? bereitschaftsperson.name
                        : formData.assigned_to || ""
                    }
                    readOnly
                    disabled
                    className="cursor-not-allowed bg-gray-100 pr-8 text-gray-600"
                    placeholder="Wird automatisch zugewiesen"
                  />
                  {bereitschaftsLoading && (
                    <div className="absolute top-1/2 right-2 -translate-y-1/2 transform">
                      <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-blue-600"></div>
                    </div>
                  )}
                </div>
                {bereitschaftsperson && (
                  <p className="mt-1 text-xs text-gray-500">
                    Bereitschaft zur Zeit der Störungserstellung (KW{" "}
                    {new Date(stoerung.created_at).toLocaleDateString("de-DE", {
                      year: "numeric",
                      month: "2-digit",
                      day: "2-digit",
                    })}
                    )
                  </p>
                )}
              </div>
            </div>

            {/* Lösungsschritte */}
            <div className="space-y-2">
              <Label htmlFor="resolution_steps">Lösungsschritte</Label>
              <Textarea
                id="resolution_steps"
                value={formData.resolution_steps || ""}
                onChange={(e) =>
                  handleInputChange("resolution_steps", e.target.value)
                }
                placeholder="Durchgeführte oder geplante Lösungsschritte"
                rows={3}
              />
            </div>

            {/* Grundursache */}
            <div className="space-y-2">
              <Label htmlFor="root_cause">Grundursache</Label>
              <Textarea
                id="root_cause"
                value={formData.root_cause || ""}
                onChange={(e) =>
                  handleInputChange("root_cause", e.target.value)
                }
                placeholder="Identifizierte Grundursache der Störung"
                rows={2}
              />
            </div>

            {/* Lessons Learned */}
            <div className="space-y-2">
              <Label htmlFor="lessons_learned">Lessons Learned</Label>
              <Textarea
                id="lessons_learned"
                value={formData.lessons_learned || ""}
                onChange={(e) =>
                  handleInputChange("lessons_learned", e.target.value)
                }
                placeholder="Erkenntnisse und Verbesserungsvorschläge"
                rows={2}
              />
            </div>

            {/* Bestehende Anhänge und neuer Upload in einem Grid Layout */}
            <div className="space-y-4">
              <Label className="flex items-center gap-2">
                <ImageIcon className="h-4 w-4" />
                Bilder verwalten
              </Label>

              <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                {/* Linke Seite: Bestehende Anhänge */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Bestehende Bilder ({existingAttachments.length})
                  </h4>
                  {existingAttachments.length > 0 ? (
                    <div className="grid grid-cols-2 gap-3">
                      {existingAttachments.map((attachment) => (
                        <div key={attachment.id} className="group relative">
                          <div className="aspect-square overflow-hidden rounded-lg border border-gray-200">
                            <img
                              src={`${apiConfig.apiBaseUrl}/stoerungen/attachments/${attachment.id}/file`}
                              alt={
                                attachment.storedName ||
                                attachment.filename ||
                                "Störungsbild"
                              }
                              className="h-full w-full bg-gray-50 object-contain"
                              onError={(e) => {
                                console.error("Fehler beim Laden des Bildes:", {
                                  attachmentId: attachment.id,
                                  originalUrl: e.currentTarget.src,
                                  storedName: attachment.storedName,
                                  filename: attachment.filename,
                                });

                                // Fallback: versuche andere URL-Strukturen
                                const target = e.target as HTMLImageElement;
                                if (target.src.includes("/api/stoerungen/attachments/")) {
                                  // Mehrere Fallback-Optionen versuchen
                                  const fallbacks = [
                                    `${apiConfig.apiBaseUrl}/uploads/stoerungen/${attachment.storedName || attachment.filename}`,
                                    `http://localhost:3001/uploads/stoerungen/${attachment.storedName || attachment.filename}`,
                                    `http://localhost:3001/api/uploads/stoerungen/${attachment.storedName || attachment.filename}`,
                                  ];

                                  const currentIndex = fallbacks.findIndex(url => target.src.includes(url.split('/').pop() || ''));
                                  const nextIndex = (currentIndex + 1) % fallbacks.length;

                                  if (nextIndex !== currentIndex) {
                                    const fallbackUrl = fallbacks[nextIndex];
                                    console.log("Versuche Fallback-URL:", fallbackUrl);
                                    target.src = fallbackUrl;
                                  }
                                }
                              }}
                              crossOrigin="anonymous"
                            />
                          </div>
                          <div className="absolute top-2 right-2 opacity-0 transition-opacity group-hover:opacity-100">
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={() =>
                                handleDeleteAttachment(attachment.id)
                              }
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                          <p
                            className="mt-1 truncate text-xs text-gray-600"
                            title={
                              attachment.storedName ||
                              attachment.filename ||
                              "Störungsbild"
                            }
                          >
                            {attachment.storedName ||
                              attachment.filename ||
                              "Unbekanntes Bild"}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="rounded-lg border-2 border-dashed border-gray-300 py-8 text-center text-gray-500">
                      <ImageIcon className="mx-auto mb-3 h-12 w-12 opacity-50" />
                      <p>
                        {backendStatus === 'offline'
                          ? 'Backend nicht verfügbar - Bilder können nicht geladen werden'
                          : 'Keine Bilder vorhanden'
                        }
                      </p>
                      {existingAttachments.length === 0 && !attachmentsLoading && backendStatus === 'offline' && (
                        <div className="mt-2 text-xs text-red-600">
                          <p>🔴 <strong>Backend-Server erforderlich:</strong></p>
                          <ul className="mt-1 text-left">
                            <li>• Backend-Server muss auf Port 3001 laufen</li>
                            <li>• Datenbank muss verbunden sein</li>
                            <li>• API-Routen müssen verfügbar sein</li>
                          </ul>
                          <p className="mt-2 text-blue-600">
                            📋 Siehe die rote Warnung oben für Anweisungen zum Starten des Backends
                          </p>
                        </div>
                      )}
                      {existingAttachments.length === 0 && !attachmentsLoading && backendStatus === 'online' && (
                        <div className="mt-2 text-xs text-gray-400">
                          <p>💡 <strong>Problembehebung:</strong></p>
                          <ul className="mt-1 text-left">
                            <li>• Störung hat tatsächlich Anhänge?</li>
                            <li>• Datenbank enthält die Anhangsdaten?</li>
                            <li>• API-Routen funktionieren korrekt?</li>
                          </ul>
                          <p className="mt-2 text-yellow-600">
                            📋 Prüfen Sie die Browser-Konsole für detaillierte Logs
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Rechte Seite: Neuer Upload */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Weitere Bilder zu der Störung hinzufügen
                  </h4>

                  {backendStatus === 'offline' ? (
                    <div className="rounded-lg border-2 border-dashed border-red-300 bg-red-50 py-8 text-center">
                      <AlertTriangle className="mx-auto mb-3 h-12 w-12 text-red-500 opacity-50" />
                      <p className="text-red-700 font-medium">Upload nicht verfügbar</p>
                      <p className="text-sm text-red-600 mt-1">
                        Backend-Server muss laufen, um Bilder hochzuladen
                      </p>
                      <p className="text-xs text-red-500 mt-2">
                        Siehe die Warnung oben für Anweisungen zum Starten des Backends
                      </p>
                    </div>
                  ) : (
                    <FileUploadStoerung
                      acceptedFileTypes={[
                        "image/jpeg",
                        "image/jpg",
                        "image/png",
                        "image/gif",
                        "image/webp",
                      ]}
                      maxFileSize={5 * 1024 * 1024} // 5MB
                      onUploadSuccess={(file) =>
                        setUploadedImages((prev) => [...prev, file])
                      }
                      onUploadError={(error) => {
                        console.error("Upload error:", error);
                        toast({
                          title: "Upload-Fehler",
                          description: error.message,
                          type: "error",
                        });
                      }}
                      uploadDelay={0} // Sofortiger Upload ohne Simulation
                      className="h-full min-h-[240px]"
                    />
                  )}

                  {/* Vorschau der neuen Uploads */}
                  {uploadedImages.length > 0 && (
                    <div className="space-y-2">
                      <p className="text-sm text-gray-600">
                        Neue Uploads ({uploadedImages.length})
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {uploadedImages.map((file, index) => (
                          <div key={index} className="group relative">
                            <div className="h-16 w-16 overflow-hidden rounded border border-gray-200">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={file.name}
                                className="h-full w-full bg-gray-50 object-contain"
                              />
                            </div>
                            <button
                              type="button"
                              onClick={() =>
                                setUploadedImages((prev) =>
                                  prev.filter((_, i) => i !== index),
                                )
                              }
                              className="absolute -top-1 -right-1 rounded-full bg-red-500 p-1 text-white opacity-0 transition-opacity group-hover:opacity-100"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Submit buttons */}
            <div className="flex gap-2 pt-4">
              <Button
                type="submit"
                variant="accept"
                disabled={loading}
                className=""
              >
                {loading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                    Speichere...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Änderungen speichern
                  </>
                )}
              </Button>
              <Button type="button" variant="ghost" onClick={onClose}>
                Abbrechen
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
