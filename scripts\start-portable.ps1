# Portables Startskript für die Lapp Dashboard Anwendung
# Dieses Skript startet die Anwendung ohne Installation

param(
    [string]$ConfigPath = "",
    [switch]$Verbose
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Write-Host $logMessage
    
    if ($Verbose) {
        $logFile = Join-Path $PSScriptRoot "portable-start.log"
        Add-Content -Path $logFile -Value $logMessage
    }
}

function Test-ApplicationRunning {
    $processes = Get-Process -Name "LappDashboard" -ErrorAction SilentlyContinue
    return $processes.Count -gt 0
}

function Start-PortableApplication {
    Write-Log "===================================="
    Write-Log "Lapp Dashboard - Portable Anwendung"
    Write-Log "===================================="
    Write-Log ""

    # Prüfe, ob die Anwendung bereits läuft
    if (Test-ApplicationRunning) {
        Write-Log "Anwendung läuft bereits. Beenden Sie zuerst die laufende Instanz." "WARNING"
        Read-Host "Drücken Sie Enter, um fortzufahren..."
        return $false
    }

    # Finde das Installationsverzeichnis
    $appDir = Split-Path -Parent $PSScriptRoot
    $appExe = Join-Path $appDir "LappDashboard.exe"

    # Prüfe, ob die ausführbare Datei existiert
    if (-not (Test-Path $appExe)) {
        Write-Log "Fehler: Anwendung nicht gefunden unter $appExe" "ERROR"
        Write-Log "Bitte stellen Sie sicher, dass Sie das Skript aus dem richtigen Verzeichnis starten." "ERROR"
        Read-Host "Drücken Sie Enter, um fortzufahren..."
        return $false
    }

    # Prüfe, ob die portale Konfigurationsdatei existiert
    $configFile = Join-Path $appDir "portable-config.json"
    if ($ConfigPath -and (Test-Path $ConfigPath)) {
        $configFile = $ConfigPath
    }

    if (Test-Path $configFile) {
        Write-Log "Konfigurationsdatei gefunden: $configFile"
        
        try {
            # Lese Datenbankkonfiguration
            $config = Get-Content $configFile | ConvertFrom-Json
            
            if ($config.database) {
                Write-Log "Datenbank-Host: $($config.database.host)"
                Write-Log "Datenbank-Name: $($config.database.database)"
                Write-Log "Datenbank-Port: $($config.database.port)"
            } else {
                Write-Log "Keine Datenbankkonfiguration gefunden" "WARNING"
            }
        } catch {
            Write-Log "Fehler beim Lesen der Konfiguration: $_" "WARNING"
        }
    } else {
        Write-Log "Keine Konfigurationsdatei gefunden. Verwende Standardeinstellungen." "INFO"
    }

    Write-Log ""
    Write-Log "Starte Anwendung..."
    Write-Log ""

    # Starte die Anwendung
    try {
        Start-Process -FilePath $appExe -WorkingDirectory $appDir
        Write-Log "Anwendung wurde gestartet." "SUCCESS"
        
        # Warte kurz, damit die Anwendung starten kann
        Start-Sleep -Seconds 3
        
        # Prüfe, ob die Anwendung erfolgreich gestartet wurde
        if (Test-ApplicationRunning) {
            Write-Log "Anwendung wurde erfolgreich gestartet." "SUCCESS"
            Write-Log ""
            Write-Log "Hinweis: Die Anwendung läuft im Hintergrund." "INFO"
            Write-Log "Um die Anwendung zu beenden, klicken Sie auf das X im Anwendungsfenster." "INFO"
            Write-Log ""
            Write-Log "Wenn die Anwendung nicht sichtbar ist, überprüfen Sie die Taskleiste." "INFO"
            
            return $true
        } else {
            Write-Log "Anwendung konnte nicht gestartet werden oder wurde sofort beendet." "ERROR"
            Write-Log "Bitte überprüfen Sie die Log-Dateien im logs-Verzeichnis." "ERROR"
            
            return $false
        }
    } catch {
        Write-Log "Fehler beim Starten der Anwendung: $_" "ERROR"
        return $false
    }
}

# Hauptfunktion
try {
    $success = Start-PortableApplication
    
    if (-not $success) {
        Write-Log "Start der Anwendung fehlgeschlagen." "ERROR"
        exit 1
    }
    
    Write-Log ""
    Read-Host "Drücken Sie Enter, um dieses Fenster zu schließen..."
} catch {
    Write-Log "Unerwarteter Fehler: $_" "ERROR"
    exit 1
}