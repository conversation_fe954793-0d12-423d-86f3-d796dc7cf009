# Projektübersicht: JOZI1 Lapp Dashboard

Dieses Dokument wurde von <PERSON> generiert, um eine schnelle Übersicht über das Projekt zu geben.

## Projektbeschreibung

Das Projekt "JOZI1 Lapp Dashboard" (Name in `package.json`: `lapp-dashboard`) ist eine Electron-Anwendung zur Visualisierung von Logistik- und Produktionsdaten. Es dient als Dashboard, um wichtige Kennzahlen durch interaktive Diagramme und Statistiken darzustellen.

**Kernfunktionen:**
- Zentrales Dashboard mit den wichtigsten Diagrammen.
- Interaktive Visualisierungen für Wareneingang, Lieferungen, Schnitte, Kommissionierung, QM-Meldungen etc.
- Detaillierte Datentabellen.
- Unterstützung für Deutsch und einen Dunkel-/Hell-Modus.

## Technologie-Stack

- **Core:** Electron, Vite
- **UI-Framework:** React
- **Styling:** Tailwind CSS, shadcn/ui
- **Diagramme:** Recharts
- **State Management:** React Query (TanStack)
- **Routing:** TanStack Router
- **Datenbank:** better-sqlite3
- **Sprachen:** TypeScript
- **Testing:** Vitest, Playwright

## Wichtige NPM-Skripte

- `pnpm run dev`: Startet die Anwendung und den Backend-Server im Entwicklungsmodus.
- `pnpm run package`: Verpackt die Anwendung für die Zielplattform.
- `pnpm run make`: Erstellt eine installierbare Distribution der Anwendung.
- `pnpm run test:unit`: Führt die Unit-Tests aus.
- `pnpm run test:e2e`: Führt die End-to-End-Tests aus.
- `pnpm run db:generate`: Generiert Drizzle-Migrationsskripte basierend auf den Schema-Änderungen.

## Autor und Lizenz

- **Autor:** Johann Zimmer
- **Lizenz:** MIT
