/**
 * Datenqualitäts-Warnung UI-Komponente
 * 
 * <PERSON>eigt <PERSON>ern Warnungen bei Datenqualitätsproblemen in Charts an.
 */

import React from 'react';
import { AlertTriangle, Info, XCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface DataQualityWarningProps {
  warning: {
    level: 'info' | 'warning' | 'error';
    message: string;
    details: string;
  } | null;
  className?: string;
  compact?: boolean;
}

export function DataQualityWarning({ 
  warning, 
  className = '', 
  compact = false 
}: DataQualityWarningProps) {
  if (!warning) return null;

  const getAlertProps = () => {
    switch (warning.level) {
      case 'error':
        return {
          variant: 'destructive' as const,
          icon: XCircle,
          borderColor: 'border-red-200',
          bgColor: 'bg-red-50'
        };
      case 'warning':
        return {
          variant: 'default' as const,
          icon: AlertTriangle,
          borderColor: 'border-yellow-200',
          bgColor: 'bg-yellow-50'
        };
      case 'info':
        return {
          variant: 'default' as const,
          icon: Info,
          borderColor: 'border-blue-200',
          bgColor: 'bg-blue-50'
        };
      default:
        return {
          variant: 'default' as const,
          icon: Info,
          borderColor: 'border-gray-200',
          bgColor: 'bg-gray-50'
        };
    }
  };

  const { variant, icon: Icon, borderColor, bgColor } = getAlertProps();

  if (compact) {
    return (
      <div className={`flex items-center gap-2 p-2 rounded-md text-sm ${bgColor} ${borderColor} border ${className}`}>
        <Icon className="h-4 w-4 flex-shrink-0" />
        <span className="font-medium">{warning.message}</span>
      </div>
    );
  }

  return (
    <Alert variant={variant} className={`${bgColor} ${borderColor} ${className}`}>
      <Icon className="h-4 w-4" />
      <AlertTitle className="text-sm font-semibold">
        {warning.message}
      </AlertTitle>
      <AlertDescription className="text-sm mt-1">
        {warning.details}
      </AlertDescription>
    </Alert>
  );
}

/**
 * Kompakte Inline-Warnung für kleinere Räume
 */
export function DataQualityInlineWarning({ warning }: { warning: DataQualityWarningProps['warning'] }) {
  return <DataQualityWarning warning={warning} compact />;
}

/**
 * Voll ausgebaute Warnung mit Details
 */
export function DataQualityDetailedWarning({ warning }: { warning: DataQualityWarningProps['warning'] }) {
  return <DataQualityWarning warning={warning} className="mb-4" />;
}
