"use strict";
/**
 * RAG Routes - Document Upload and Management API
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const ragController_1 = __importStar(require("../controllers/ragController"));
const router = (0, express_1.Router)();
const ragController = new ragController_1.default();
// Note: Authentication is handled at the server level based on NODE_ENV
// Document upload and management
router.post('/documents/upload', ragController_1.uploadMiddleware, ragController.uploadDocument);
router.get('/documents', ragController.getDocuments);
router.delete('/documents/:documentId', ragController.deleteDocument);
router.get('/documents/search', ragController.searchDocuments);
// Knowledge base management
router.get('/knowledge-bases', ragController.getKnowledgeBases);
router.post('/knowledge-bases', ragController.createKnowledgeBase);
// Analytics and monitoring
router.get('/statistics', ragController.getStatistics);
router.get('/queries/recent', ragController.getRecentQueries);
// Settings management
router.get('/settings', ragController.getSettings);
router.put('/settings', ragController.saveSettings);
router.get('/settings/all', ragController.getAllSettings);
router.delete('/settings/:settingsId', ragController.deleteSettings);
// Test endpoint - matches expected ApiResponse structure for frontend
router.get('/test', (req, res) => {
    res.json({
        success: true,
        data: {
            message: 'RAG service is running',
            timestamp: new Date().toISOString(),
            status: 'operational'
        }
    });
});
exports.default = router;
