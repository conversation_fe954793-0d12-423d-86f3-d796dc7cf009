import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Settings,
  Database,
  Mail,
  Calendar,
  FolderOpen,
  Clock,
  Save,
  X,
  AlertCircle
} from 'lucide-react';
import { workflowService } from '@/services/workflowService';
import { SAPWorkflowProcess, WorkflowConfig } from '@/types/workflow';

interface WorkflowConfigDialogProps {
  process: SAPWorkflowProcess;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Default-Konfiguration für Servicegrad
const defaultConfig: WorkflowConfig = {
  id: 'servicegrad',
  name: 'Servicegrad Automatisierung',
  description: 'SAP Servicegrad Export mit automatischer Excel-Verarbeitung',
  // PostgreSQL wird über Backend-Konfiguration verwendet - keine lokalen DB-Pfade mehr nötig
  databasePath: '', // Entfernt - PostgreSQL wird verwendet
  exportPath: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\SG',
  emailRecipients: ['<EMAIL>'],
  // SAP-Konfiguration
  sapExecutablePath: 'C:\\Program Files (x86)\\SAP\\FrontEnd\\SapGui\\sapshcut.exe',
  sapSystemId: 'PS4',
  sapClient: '009',
  sapLanguage: 'DE',
  dbPath: '', // Entfernt - PostgreSQL wird verwendet
  schedule: {
    enabled: false,
    frequency: 'daily',
    time: '08:00',
    dayOfWeek: 1,
    interval: 1
  },
  isActive: true,
  lastModified: new Date()
};

export function WorkflowConfigDialog({ process, open, onOpenChange }: WorkflowConfigDialogProps) {
  const [config, setConfig] = useState<WorkflowConfig>(defaultConfig);
  const [tempConfig, setTempConfig] = useState<WorkflowConfig>(defaultConfig);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (open) {
      loadConfig();
    }
  }, [open, process.id]);

  const loadConfig = async () => {
    setLoading(true);
    try {
      const loadedConfig = await workflowService.getWorkflowConfig(process.id);
      if (loadedConfig) {
        const mergedConfig = { ...defaultConfig, ...loadedConfig };
        setConfig(mergedConfig);
        setTempConfig(mergedConfig);
      }
    } catch (error) {
      console.error('Fehler beim Laden der Workflow-Konfiguration:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async () => {
    setSaving(true);
    try {
      await workflowService.updateWorkflowConfig(process.id, tempConfig);
      setConfig(tempConfig);
      onOpenChange(false);
      console.log('Workflow-Konfiguration gespeichert:', tempConfig);
    } catch (error) {
      console.error('Fehler beim Speichern der Workflow-Konfiguration:', error);
    } finally {
      setSaving(false);
    }
  };

  const cancelConfig = () => {
    setTempConfig(config);
    onOpenChange(false);
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="workflow-name">Workflow-Name</Label>
          <Input
            id="workflow-name"
            value={tempConfig.name}
            onChange={(e) =>
              setTempConfig(prev => ({ ...prev, name: e.target.value }))
            }
            placeholder="Name des Workflows"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="workflow-description">Beschreibung</Label>
          <Input
            id="workflow-description"
            value={tempConfig.description}
            onChange={(e) =>
              setTempConfig(prev => ({ ...prev, description: e.target.value }))
            }
            placeholder="Beschreibung des Workflows"
          />
        </div>
      </div>

      <Separator />

      <div className="space-y-2">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <Database className="h-4 w-4" />
          Datenbank-Einstellungen
        </h4>

        <div className="space-y-1">
          <Label className="space-y-1 text-xs" htmlFor="database-path">Vollständiger Pfad zur SQLite-Datenbankdatei</Label>
          <Input
            id="database-path"
            value={tempConfig.databasePath || ''}
            onChange={(e) =>
              setTempConfig(prev => ({ ...prev, databasePath: e.target.value }))
            }
            placeholder="Pfad zur SQLite-Datenbank"
          />
        </div>
      </div>

      <Separator />

      <div className="space-y-2">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <FolderOpen className="h-4 w-4" />
          SAP Excel Export-Einstellungen
        </h4>

        <div className="space-y-1">
          <Label className="space-y-1 text-xs" htmlFor="export-path">Export-Verzeichnis</Label>
          <Input
            id="export-path"
            value={tempConfig.exportPath || ''}
            onChange={(e) =>
              setTempConfig(prev => ({ ...prev, exportPath: e.target.value }))
            }
            placeholder="Verzeichnis für SAP-Exports"
          />
        </div>
      </div>

      <Separator />

      <div className="space-y-2">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <Database className="h-4 w-4" />
          SAP-Verbindung
        </h4>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <Label className="space-y-1 text-xs" htmlFor="sap-system-id">System-ID</Label>
            <Input
              id="sap-system-id"
              value={tempConfig.sapSystemId || ''}
              onChange={(e) =>
                setTempConfig(prev => ({ ...prev, sapSystemId: e.target.value }))
              }
              placeholder="PS4"
            />
          </div>

          <div className="space-y-1">
            <Label className="space-y-1 text-xs" htmlFor="sap-client">Client</Label>
            <Input
              id="sap-client"
              value={tempConfig.sapClient || ''}
              onChange={(e) =>
                setTempConfig(prev => ({ ...prev, sapClient: e.target.value }))
              }
              placeholder="009"
            />
          </div>
        </div>

        <div className="space-y-1">
          <Label className="space-y-1 text-xs" htmlFor="sap-executable">SAP GUI Pfad</Label>
          <Input
            id="sap-executable"
            value={tempConfig.sapExecutablePath || ''}
            onChange={(e) =>
              setTempConfig(prev => ({ ...prev, sapExecutablePath: e.target.value }))
            }
            placeholder="Pfad zur sapshcut.exe"
          />
        </div>
      </div>
    </div>
  );

  const renderEmailSettings = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <Mail className="h-4 w-4" />
          E-Mail-Konfiguration
        </h4>

        <div className="space-y-2">
          <Label htmlFor="email-recipients">E-Mail-Empfänger</Label>
          <Input
            id="email-recipients"
            value={tempConfig.emailRecipients?.join(', ') || ''}
            onChange={(e) =>
              setTempConfig(prev => ({
                ...prev,
                emailRecipients: e.target.value.split(',').map(email => email.trim()).filter(Boolean)
              }))
            }
            placeholder="E-Mail-Adressen (kommagetrennt)"
          />
          <div className="text-xs text-muted-foreground">
            Mehrere E-Mail-Adressen mit Komma trennen
          </div>
        </div>

        {tempConfig.emailRecipients && tempConfig.emailRecipients.length > 0 && (
          <div className="space-y-2 text-sm">
            <Label>Mehrere E-Mail-Adressen mit Komma trennen</Label>
            <div className="flex flex-wrap gap-2">
              {tempConfig.emailRecipients.map((email, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {email}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderScheduleSettings = () => (
      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 bg-purple-50 rounded-lg border border-purple-200">
          <div className="space-y-0.5">
            <Label htmlFor="schedule-enabled" className="text-base font-medium text-purple-900">
              Automatische Ausführung
            </Label>
            <div className="text-sm text-purple-700">
              Zeitplan für für den Workflow Einstellen
            </div>
          </div>
          <Switch
            id="schedule-enabled"
            checked={tempConfig.schedule.enabled}
            onCheckedChange={(checked) =>
              setTempConfig(prev => ({
                ...prev,
                schedule: { ...prev.schedule, enabled: checked }
              }))
            }
            className="data-[state=checked]:bg-purple-500 data-[state=unchecked]:bg-gray-300"
          />
        </div>

        <Separator />

        {/* Zeitplan-Einstellungen immer anzeigen */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="frequency">Häufigkeit</Label>
            <Select
              value={tempConfig.schedule.frequency}
              onValueChange={(value: 'hourly' | 'daily' | 'weekly') =>
                setTempConfig(prev => ({
                  ...prev,
                  schedule: { ...prev.schedule, frequency: value }
                }))
              }
              disabled={!tempConfig.schedule.enabled}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hourly">Stündlich</SelectItem>
                <SelectItem value="daily">Täglich</SelectItem>
                <SelectItem value="weekly">Wöchentlich</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {tempConfig.schedule.frequency === 'hourly' && (
            <div className="space-y-2">
              <Label htmlFor="interval">Alle X Stunden</Label>
              <Input
                id="interval"
                type="number"
                min="1"
                max="24"
                value={tempConfig.schedule.interval || 1}
                onChange={(e) =>
                  setTempConfig(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, interval: parseInt(e.target.value) || 1 }
                  }))
                }
                disabled={!tempConfig.schedule.enabled}
              />
              <div className="text-xs text-muted-foreground">
                Workflow wird alle {tempConfig.schedule.interval || 1} Stunde(n) ausgeführt
              </div>
            </div>
          )}

          {(tempConfig.schedule.frequency === 'daily' || tempConfig.schedule.frequency === 'weekly') && (
            <div className="space-y-2">
              <Label htmlFor="time">Uhrzeit</Label>
              <Input
                id="time"
                type="time"
                value={tempConfig.schedule.time || '08:00'}
                onChange={(e) =>
                  setTempConfig(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, time: e.target.value }
                  }))
                }
                disabled={!tempConfig.schedule.enabled}
              />
              <div className="text-xs text-muted-foreground">
                Workflow wird täglich um {tempConfig.schedule.time || '08:00'} Uhr ausgeführt
              </div>
            </div>
          )}

          {tempConfig.schedule.frequency === 'weekly' && (
            <div className="space-y-2">
              <Label htmlFor="dayOfWeek">Wochentag</Label>
              <Select
                value={tempConfig.schedule.dayOfWeek?.toString() || '1'}
                onValueChange={(value) =>
                  setTempConfig(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, dayOfWeek: parseInt(value) }
                  }))
                }
                disabled={!tempConfig.schedule.enabled}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Montag</SelectItem>
                  <SelectItem value="2">Dienstag</SelectItem>
                  <SelectItem value="3">Mittwoch</SelectItem>
                  <SelectItem value="4">Donnerstag</SelectItem>
                  <SelectItem value="5">Freitag</SelectItem>
                  <SelectItem value="6">Samstag</SelectItem>
                  <SelectItem value="0">Sonntag</SelectItem>
                </SelectContent>
              </Select>
              <div className="text-xs text-muted-foreground">
                Workflow wird wöchentlich am {['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag'][tempConfig.schedule.dayOfWeek || 1]} ausgeführt
              </div>
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <div className="font-medium text-blue-900">Zeitplan-Vorschau</div>
                <div className="text-blue-700 mt-1">
                  {!tempConfig.schedule.enabled && 'Automatische Ausführung ist deaktiviert'}
                  {tempConfig.schedule.enabled && tempConfig.schedule.frequency === 'hourly' && `Ausführung alle ${tempConfig.schedule.interval} Stunde(n)`}
                  {tempConfig.schedule.enabled && tempConfig.schedule.frequency === 'daily' && `Tägliche Ausführung um ${tempConfig.schedule.time}`}
                  {tempConfig.schedule.enabled && tempConfig.schedule.frequency === 'weekly' && `Wöchentliche Ausführung am ${['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag'][tempConfig.schedule.dayOfWeek || 1]} um ${tempConfig.schedule.time}`}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
  );

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
      <div className="relative max-w-2xl w-full max-h-[120vh] bg-white rounded-lg shadow-2xl border overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Workflow-Konfiguration: {process.name}
            </h2>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={cancelConfig}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>



        {/* Content */}
        <div className="h-[calc(90vh-50px)] flex flex-col">
          {loading ? (
            <div className="flex items-center justify-center flex-1">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="flex-1 flex flex-col p-4 bg-gray-50">
              <Tabs defaultValue="general" className="flex-1 flex flex-col">
                <TabsList className="grid w-full grid-cols-3 bg-white shadow-sm mb-2">
                  <TabsTrigger
                    value="general"
                    className="flex items-center gap-2 data-[state=active]:bg-blue-500 data-[state=active]:text-white hover:bg-blue-50 transition-colors"
                  >
                    <Settings className="h-4 w-4" />
                    Allgemein
                  </TabsTrigger>
                  <TabsTrigger
                    value="email"
                    className="flex items-center gap-2 data-[state=active]:bg-green-500 data-[state=active]:text-white hover:bg-green-50 transition-colors"
                  >
                    <Mail className="h-4 w-4" />
                    E-Mail
                  </TabsTrigger>
                  <TabsTrigger
                    value="schedule"
                    className="flex items-center gap-2 data-[state=active]:bg-purple-500 data-[state=active]:text-white hover:bg-purple-50 transition-colors"
                  >
                    <Calendar className="h-4 w-4" />
                    Zeitplan
                  </TabsTrigger>
                </TabsList>

                <div className="flex-1 flex flex-col">
                  <TabsContent value="general" className="flex-1 flex flex-col data-[state=inactive]:hidden">
                    <div className="flex-1 bg-white rounded-lg p-6 shadow-sm border overflow-y-auto">
                      {renderGeneralSettings()}
                    </div>

                    {/* Buttons für Allgemein Tab */}
                    <div className="flex items-center justify-end gap-2 pt-4 mt-4 border-t bg-white rounded-b-lg p-4">
                      <Button variant="outline" onClick={cancelConfig} disabled={saving} className="border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400">
                        <X className="h-4 w-4 mr-2" />
                        Abbrechen
                      </Button>
                      <Button onClick={saveConfig} disabled={saving || loading} className="bg-blue-500 hover:bg-blue-600 text-white">
                        {saving ? (
                          <>
                            <Clock className="h-4 w-4 mr-2 animate-spin" />
                            Speichern...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Speichern
                          </>
                        )}
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="email" className="flex-1 flex flex-col data-[state=inactive]:hidden">
                    <div className="flex-1 bg-white rounded-lg p-6 shadow-sm border overflow-y-auto">
                      {renderEmailSettings()}
                    </div>

                    {/* Buttons für E-Mail Tab */}
                    <div className="flex items-center justify-end gap-2 pt-4 mt-4 border-t bg-white rounded-b-lg p-4">
                      <Button variant="outline" onClick={cancelConfig} disabled={saving} className="border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400">
                        <X className="h-4 w-4 mr-2" />
                        Abbrechen
                      </Button>
                      <Button onClick={saveConfig} disabled={saving || loading} className="bg-green-500 hover:bg-green-600 text-white">
                        {saving ? (
                          <>
                            <Clock className="h-4 w-4 mr-2 animate-spin" />
                            Speichern...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Speichern
                          </>
                        )}
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="schedule" className="flex-1 flex flex-col data-[state=inactive]:hidden">
                    <div className="flex-1 bg-white rounded-lg p-6 shadow-sm border overflow-y-auto">
                      {renderScheduleSettings()}
                    </div>

                    {/* Buttons für Zeitplan Tab */}
                    <div className="flex items-center justify-end gap-2 pt-4 mt-4 border-t bg-white rounded-b-lg p-4">
                      <Button variant="outline" onClick={cancelConfig} disabled={saving} className="border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400">
                        <X className="h-4 w-4 mr-2" />
                        Abbrechen
                      </Button>
                      <Button onClick={saveConfig} disabled={saving || loading} className="bg-purple-500 hover:bg-purple-600 text-white">
                        {saving ? (
                          <>
                            <Clock className="h-4 w-4 mr-2 animate-spin" />
                            Speichern...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Speichern
                          </>
                        )}
                      </Button>
                    </div>
                  </TabsContent>
                </div>
              </Tabs>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}