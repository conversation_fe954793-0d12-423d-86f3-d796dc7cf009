/**
 * Chart Data Types für Dashboard-Visualisierungen
 */

/**
 * Basis-Interface für Chart-Daten
 * Unterstützt verschiedene Datentypen für unterschiedliche Chart-Arten
 */
export interface ChartData {
  /** Name/Bezeichnung des Datenpunkts */
  name: string;
  /** Datum des Datenpunkts */
  date?: Date;
  /** Servicegrad-Wert */
  servicegrad?: number;
  /** Produzierte Tonnagen */
  produzierte_tonnagen?: number;
  /** Direktverladung KIAA */
  direktverladung_kiaa?: number;
  /** Umschlag */
  umschlag?: number;
  /** kg pro Colli */
  kg_pro_colli?: number;
  /** Elefanten */
  elefanten?: number;
  /** Ausgeliefert LUP */
  ausgeliefert_lup?: number;
  /** Rückständig */
  rueckstaendig?: number;
  /** ATRL */
  atrl?: number;
  /** ARiL */
  aril?: number;
  /** Füllgrad ARiL */
  fuellgrad_aril?: number;
  /** Wert für Q-Meldungen */
  value?: number;
  /** Füll-Farbe für Visualisierung */
  fill?: string;

  // Cutting-spezifische Properties
  /** Cutting Trommel-Trommel */
  cutTT?: number;
  /** Cutting Trommel-Ring */
  cutTR?: number;
  /** Cutting Ring-Ring */
  cutRR?: number;
  /** PickCut */
  pickCut?: number;
  /** Lager Summe */
  lagerSumme?: number;
  /** Cut Lager Kunden Summe */
  cutLagerKSumme?: number;
  /** Cut Lager Rest Summe */
  cutLagerRSumme?: number;
  /** Maschinen Effizienz */
  efficiency?: number;
  /** Summe H1 */
  sumH1?: number;
  /** Summe H3 */
  sumH3?: number;
  /** Total Summe */
  total?: number;

  // Maschinen-spezifische Properties
  /** Soll-Schnitte */
  sollSchnitte?: number;
  /** Tages-Schnitte */
  tagesSchnitte?: number;
  /** Ist-Schnitte pro Stunde */
  istSchnitteProStunde?: number;
  /** Effizienz in Prozent */
  effizienzProzent?: number;
  /** Maschinen-Typ (H1/H3) */
  maschinenTyp?: string;
  /** Maschinen-Name */
  machine?: string;

  // ARiL-spezifische Properties
  /** WaTa Positionen */
  waTaPositionen?: number;
  /** Cutting Lager Kunde */
  cuttingLagerKunde?: number;
  /** Cutting Lager Rest */
  cuttingLagerRest?: number;
  /** Umlagerungen */
  Umlagerungen?: number;
  /** Lager Cutting */
  lagerCutting?: number;

  // ATrL-spezifische Properties
  /** WE ATrL */
  weAtrl?: number;
  /** Einlagerung Kunde */
  EinlagerungAblKunde?: number;
  /** Einlagerung Rest */
  EinlagerungAblRest?: number;
  /** ATrL Umlagerungen */
  umlagerungen?: number;
  /** Auslagerung */
  AuslagerungAbl?: number;
}

/**
 * Struktur für Chart-Daten-Sammlungen
 */
export interface ChartDataCollection {
  /** Name des Charts */
  name: string;
  /** Array von Chart-Daten */
  data: ChartData[];
}

/**
 * Datumsbereich für Chart-Filterung
 */
export interface DateRange {
  /** Start-Datum */
  from?: Date;
  /** End-Datum */
  to?: Date;
}