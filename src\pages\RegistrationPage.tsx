import { useState, useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { authService } from '@/services/auth.service';
import { useToast } from '@/components/ui/use-toast';
import { useAuthContext } from '@/contexts/AuthContext';
import { LabelForm } from '@/components/auth';
import TextType from '@/components/Animation/Text/TextType';
import LightRays from '@/components/Animation/background/LightRays';
import { Eye, EyeOff } from 'lucide-react';

// Bilder als ES-Module importieren (Vite/Electron-sicher)
import loginBg from '@/assets/LeitstandLogin.png';
import cardBg from '@/assets/drumReg.png';

const RegistrationPage = () => {
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [name, setName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  // Sichtbarkeit der Passwörter steuern (true = sichtbar)
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { isAuthenticated, isLoading: authLoading } = useAuthContext();

  // Check if user is already authenticated
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      navigate({ to: '/' });
    }
  }, [isAuthenticated, authLoading, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Basic validation
    if (!email || !username || !name || !password || !confirmPassword) {
      setError('Bitte füllen Sie alle Felder aus.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Die Passwörter stimmen nicht überein.');
      return;
    }

    if (password.length < 8) {
      setError('Das Passwort muss mindestens 8 Zeichen lang sein.');
      return;
    }

    setIsLoading(true);

    try {
      console.log(' Attempting registration for user:', username);
      const result = await authService.register({
        email,
        username,
        name,
        password,
      });

      if (result.success) {
        console.log(' Registration successful');

        toast({
          title: 'Registrierung erfolgreich',
          description: 'Sie können sich jetzt anmelden.',
        });

        // Navigate to login page with success parameter
        navigate({ to: '/login', search: { registered: 'true' } });
      } else {
        console.log(' Registration failed:', result.message);
        setError(result.message || 'Registrierung fehlgeschlagen.');
      }
    } catch (error) {
      console.error('Registration error:', error);
      setError('Ein unerwarteter Fehler ist aufgetreten.');
    } finally {
      setIsLoading(false);
    }
  };

  // Navigation zur Login-Seite (für Link unter dem Formular)
  const handleNavigateToRegister = () => {
    // Hinweis: Wir nutzen den Router, um zur Login-Seite zu wechseln
    navigate({ to: '/login' });
  };

  // Passwort vergessen: Leitet zur Login-Seite und zeigt Hinweis
  // Hinweis: Es existiert aktuell keine eigene Reset-Seite. Wir lotsen die Nutzer:innen zur Login-Seite.
  const handleForgotPassword = () => {
    toast({
      title: 'Passwort zurücksetzen',
      description: 'Bitte nutze auf der Login-Seite den Link „Passwort vergessen?“ oder kontaktiere den Support.',
    });
    navigate({ to: '/login' });
  };

  return (
    <div className="min-h-screen flex bg-bg relative">
      {/* Linke Hälfte - Hintergrundbild */}
      <div className="w-1/2 relative overflow-hidden">
        {/* Hintergrundbild */}
        <div className="absolute inset-0 z-0 bg-[#000000]">
          <img
            src={loginBg}
            alt="Login Background"
            className="absolute inset-0 w-full h-full object-cover object-center translate-y-25 translate-x-10 scale-110 transform-gpu will-change-transform mb-2"
            onError={() => {
              console.error('Image failed to load:', loginBg);
            }}
          />
          {/* Leichter Overlay für bessere Lesbarkeit */}
          <div className="absolute inset-0 bg-black/20"></div>
        </div>

        {/* LightRays Animation */}
        <LightRays
          raysOrigin="top-center"
          raysColor="#FFFFFF"
          raysSpeed={1}
          lightSpread={0.5}
          rayLength={3}
          fadeDistance={2}
          saturation={2}
          followMouse={true}
          mouseInfluence={0.1}
          noiseAmount={0.1}
          distortion={0.05}
          className="absolute inset-0 z-5 pointer-events-none"
        />

        {/* Text Overlay */}
        <div className="label-form-label-white absolute inset-0 flex items-start justify-start z-10 pt-60 pl-30">
          <div className="flex flex-col items-start gap-4">
            <TextType 
              text={["WILLKOMMEN BEI DER"]}
              typingSpeed={175}
              pauseDuration={1500}
              showCursor={true}
              cursorCharacter="|"
            />
          </div>
        </div>
      </div>

      {/* Rechte Hälfte - Kartenbild */}
      <div className="w-1/2 relative">
        <div className="absolute inset-0 z-0">
          <img
            src={cardBg}
            alt="Background Card Image"
            className="w-full h-full object-cover rounded"
            onError={(e) => {
              console.error('Image failed to load:', cardBg);
            }}
          />
        </div>
      </div>

      {/* Mittleres Übergangs-Overlay */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 h-full w-24 z-5 pointer-events-none">
        <div className="w-full h-full bg-gradient-to-r from-transparent via-bg/60 to-transparent"></div>
      </div>

      {/* Registrierungs-Formular im Vordergrund */}
      {/* Perspektive-Wrapper: sorgt für Fluchtpunkte (3D) und natürlichere Darstellung */}
  {/* Fluchtpunkt leicht nach rechts/oben verschieben, damit X-Richtung besser mit dem Sticker fluchtet */}
  <div className="absolute top-85 right-44 z-10 w-full -mt-4 max-w-95 p-4 [perspective:1200px] [perspective-origin:58%_44%] pointer-events-none">
    {/* Innerer 3D-Block: X-Tiefe über rotateY, Y-Neigung über rotateX, Z leicht erhöht für sichtbare Gesamtdrehung */}
    {/* Hinweis: Tailwind hat keine nativen 3D-rotate-Utilities. Daher nutzen wir eine einzige Arbitrary-Transform, damit rotateX/rotateY/rotate wirken. */}
    <div className="pointer-events-auto [transform-style:preserve-3d] origin-top-left [transform:rotateX(0deg)_rotateY(14deg)_rotate(0deg)] transition-transform duration-300 will-change-transform">
          <LabelForm
            title="REGISTRIERUNG"
            description="Benutzerkonto erstellen"
            titleSize="2rem"
            descriptionSize="1.1rem"
            titleMarginBottom="0rem"
            descriptionMarginTop="-0.5rem"
            onSubmit={handleSubmit}
            isLoading={isLoading}
            error={error}
            styleConfig={{ backgroundColor: 'transparent' }}
          >
            <div className="label-stack mt-0">
              <div>
                <Label htmlFor="name" className="label-form-label mt-0 !text-md">
                  Vollständiger Name
                </Label>
                <Input
                  id="name"
                  type="text"
                  placeholder="Dein Name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  variant="ghost"
                  className="-mt-4 !text-sm !pt-2.5 !pb-0 leading-[1.1] w-68"
                  required
                  autoComplete="name"
                />
              </div>

              <div>
                <Label htmlFor="email" className="label-form-label mt-2 !text-md">
                  E-Mail-Adresse
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  variant="ghost"
                  className="-mt-4 !text-sm !pt-2.5 !pb-0 leading-[1.1] w-68.5"
                  required
                  autoComplete="email"
                />
              </div>

              <div>
                <Label htmlFor="username" className="label-form-label mt-2 !text-md">
                  Benutzername
                </Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="Dein Benutzername"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  variant="ghost"
                  className="-mt-4 !text-sm !pt-2.5 !pb-0 leading-[1.1] w-69"
                  required
                  autoComplete="username"
                />
              </div>

              <div>
                <Label htmlFor="password" className="label-form-label mt-2 !text-md">
                  Passwort
                </Label>
                {/* Passwortfeld mit Show/Hide-Toggle */}
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    variant="ghost"
                    className="-mt-4 !text-sm !pt-2.5 !pb-0 leading-[1.1] pr-12 w-70"
                    required
                    autoComplete="new-password"
                  />
                  <button
                    type="button"
                    aria-label={showPassword ? 'Passwort verbergen' : 'Passwort anzeigen'}
                    onClick={() => setShowPassword((v) => !v)}
                    className="absolute right-0 top-1/2 -translate-y-1/2 p-1 text-gray-500 hover:text-gray-700 select-none"
                  >
                    {showPassword ? (
                      <EyeOff size={18} aria-hidden="true" />
                    ) : (
                      <Eye size={18} aria-hidden="true" />
                    )}
                  </button>
                </div>
              </div>

              <div>
                <Label htmlFor="confirmPassword" className="label-form-label mt-2 !text-md">
                  Passwort bestätigen
                </Label>
                {/* Bestätigungs-Passwort mit Show/Hide-Toggle */}
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="••••••••"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    variant="ghost"
                    className="-mt-4 !text-sm !pt-2.5 !pb-0 leading-[1.1] pr-12 w-70"
                    required
                    autoComplete="new-password"
                  />
                  <button
                    type="button"
                    aria-label={showConfirmPassword ? 'Passwort verbergen' : 'Passwort anzeigen'}
                    onClick={() => setShowConfirmPassword((v) => !v)}
                    className="absolute right-0 top-1/2 -translate-y-1/2 p-1 text-gray-500 hover:text-gray-700 select-none"
                  >
                    {showConfirmPassword ? (
                      <EyeOff size={18} aria-hidden="true" />
                    ) : (
                      <Eye size={18} aria-hidden="true" />
                    )}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                variant="link"
                className="absolute left-45 -translate-x-1/2 -bottom-9 border-1 rounded-none border-black w-71 h-10 text-center font-bold text-4xl mt-4"
                disabled={isLoading}
              >
                {isLoading ? 'Registrierung läuft...' : 'SIGN-UP'}
              </Button>

              {/* Registration Link */}
              <div className="absolute left-45 -translate-x-1/2 -bottom-22 z-20 w-max text-center">
                <p className="label-form-label text-sm font-base whitespace-nowrap">
                  Du besitzt ein Konto?{' '}
                  <button
                    type="button"
                    className="label-text-primary underline hover:underline cursor-pointer bg-transparent border-none appearance-none text-sm p-0 inline-block align-baseline leading-none"
                    onClick={handleNavigateToRegister}
                  >
                    ANMELDUNG
                  </button>
                </p>
              </div>
            </div>
          </LabelForm>
        </div>
      </div>
    </div>
  );
};

export default RegistrationPage;