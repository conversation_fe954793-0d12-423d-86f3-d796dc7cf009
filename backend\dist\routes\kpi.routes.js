"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const router = express_1.default.Router();
// GET /api/servicegrad - Servicegrad-Daten aus dispatch_data
router.get('/servicegrad', async (req, res) => {
    try {
        const data = await db_1.db
            .select({ datum: schema_1.dispatchData.datum, servicegrad: schema_1.dispatchData.servicegrad })
            .from(schema_1.dispatchData)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.datum), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.servicegrad)))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
            .limit(30);
        res.json({ success: true, data });
    }
    catch (error) {
        console.error('Fehler bei GET /api/servicegrad:', error);
        // Fallback: Dummy-Daten zurückgeben
        res.json({
            success: true,
            data: [
                { datum: '2025-01-08', servicegrad: 95.5 },
                { datum: '2025-01-07', servicegrad: 92.3 },
                { datum: '2025-01-06', servicegrad: 97.1 }
            ],
            message: 'Fallback-Daten (Tabelle nicht verfügbar)'
        });
    }
});
// GET /api/bestand - Aktuelle Bestandsdaten (fallback: bestand200)
router.get('/bestand', async (req, res) => {
    try {
        // Hinweis: Ziel ist lx03_bin_status. Bis zur Migration liefern wir Daten aus bestand200.
        const rows = await db_1.db
            .select({
            aufnahmeDatum: schema_1.bestand200.aufnahmeDatum,
            lagerplatz: schema_1.bestand200.lagerplatz,
            material: schema_1.bestand200.material,
            charge: schema_1.bestand200.charge,
            gesamtbestand: schema_1.bestand200.gesamtbestand,
        })
            .from(schema_1.bestand200)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.bestand200.aufnahmeDatum))
            .limit(100);
        res.json({ success: true, data: rows, source: 'bestand200' });
    }
    catch (error) {
        console.error('Fehler bei GET /api/bestand:', error);
        // Fallback: Dummy-Daten zurückgeben
        res.json({
            success: true,
            data: [
                { aufnahmeDatum: '2025-01-08', lagerplatz: 'A-01-001', material: 'MAT001', charge: 'CH001', gesamtbestand: 150.5 },
                { aufnahmeDatum: '2025-01-08', lagerplatz: 'A-01-002', material: 'MAT002', charge: 'CH002', gesamtbestand: 200.0 },
                { aufnahmeDatum: '2025-01-07', lagerplatz: 'B-02-001', material: 'MAT003', charge: 'CH003', gesamtbestand: 75.2 }
            ],
            source: 'fallback',
            message: 'Fallback-Daten (Tabelle nicht verfügbar)'
        });
    }
});
exports.default = router;
