# Servicegrad-Workflow Server Deployment Dokumentation

## Inhaltsverzeichnis
1. [Übersicht](#übersicht)
2. [Verzeichnisstruktur](#verzeichnisstruktur)
3. [Erforderliche Installationen](#erforderliche-installationen)
4. [Dateifunktionen](#dateifunktionen)
5. [Verbindungsarchitektur](#verbindungsarchitektur)
6. [Konfiguration](#konfiguration)
7. [Deployment-Schritte](#deployment-schritte)
8. [Testverfahren](#testverfahren)
9. [Fehlerbehebung](#fehlerbehebung)

## Übersicht

Dieses Dokument beschreibt die Deployment-Architektur für den Servicegrad-Workflow auf einem zentralen Server. Der Workflow ist Teil einer größeren Anwendung, die aus einem Frontend (diese App), einem Backend-API und dem eigentlichen Workflow-System auf dem Server besteht.

**Wichtige Klarstellung**: Das Frontend ist Teil dieser App und wird nicht auf dem Server installiert. Der Server hostet nur das Backend-API und die Workflow-Skripte, die vom Frontend über die API gesteuert werden.

## Verzeichnisstruktur

### Server-Verzeichnisstruktur

```
C:\app\Workflows\
├── Servicegrad\
│   ├── Servicegrad_Workflow.py          # Haupt-Workflow-Datei
│   ├── workflow_logger.py              # PostgreSQL-Logger
│   ├── config.json                     # Workflow-Konfiguration
│   ├── test_servicegrad.py             # Testdatei
│   └── Server-Deployment-Dokumentation.md  # Diese Dokumentation
└── Common\
    └── workflow_logger.py              # Gemeinsamer Logger
```

### Backend-API-Verzeichnisstruktur (auf dem Server)

```
C:\app\backend\
├── src\
│   ├── config\
│   │   └── database.config.ts           # Datenbankkonfiguration
│   ├── db\
│   │   ├── index.ts                     # Datenbankverbindung
│   │   └── schema.ts                    # Datenbankschema
│   ├── services\
│   │   └── workflowService.ts           # Workflow-Service
│   └── routes\
│       └── workflow.routes.ts           # API-Routen
└── .env                                 # Umgebungsvariablen
```

### Datenverzeichnis (auf dem Server)

```
C:\app\data\
└── excel\
    └── Servicegrad_BluePrint.xlsx       # Excel-Vorlage
```

### Frontend-Verzeichnisstruktur (nur in dieser App)

```
src\modules\backend\components\workflows\
├── ServicegradWorkflowCard.tsx         # Frontend-Komponente
├── WorkflowGrid.tsx                    # Workflow-Grid
└── WorkflowPage.tsx                    # Workflow-Seite
```

## Erforderliche Installationen

### Server-seitige Software

1. **Python 3.8+**
   - Ausführungsumgebung für die Workflow-Skripte
   - Installation: `python --version` (überprüfen) oder von python.org herunterladen

2. **Python-Pakete**
   ```bash
   pip install psycopg2-binary pandas xlwings pywin32
   ```

3. **Node.js 16+**
   - Ausführungsumgebung für das Backend-API
   - Installation: Von nodejs.org herunterladen

4. **PostgreSQL 12+**
   - Datenbank für die Persistierung von Workflow-Logs und Servicegrad-Daten
   - Installation: Von postgresql.org herunterladen

5. **SAP GUI for Windows**
   - Für die Interaktion mit dem SAP-System
   - Muss auf dem Server installiert und konfiguriert sein

6. **Microsoft Excel**
   - Für die Verarbeitung der Excel-Dateien
   - Muss auf dem Server installiert sein

### Systemanforderungen

- **Betriebssystem**: Windows Server 2016+ (für SAP GUI und Excel)
- **RAM**: Mindestens 8 GB, empfohlen 16 GB
- **Speicher**: Mindestens 50 GB freier Speicherplatz
- **CPU**: Mindestens 4 Kerne

## Dateifunktionen

### Server-Workflow-Dateien

#### `C:\Workflows\Servicegrad\Servicegrad_Workflow.py`
- **Zweck**: Haupt-Workflow-Datei, die die gesamte Servicegrad-Automatisierung durchführt
- **Funktionen**:
  - Verbindung zum SAP-System herstellen
  - SAP-Transaktion ausführen
  - Excel-Dateien verarbeiten
  - Servicegrad berechnen
  - E-Mail-Berichte senden
  - Daten in PostgreSQL speichern
- **Abhängigkeiten**: SAP GUI, Excel, PostgreSQL

#### `C:\Workflows\Servicegrad\workflow_logger.py`
- **Zweck**: Logging-System für Workflow-Prozesse mit PostgreSQL-Integration
- **Funktionen**:
  - Strukturiertes Logging von Workflow-Ereignissen
  - Speicherung von Logs in PostgreSQL
  - Abfrage von Logs nach verschiedenen Kriterien
- **Abhängigkeiten**: PostgreSQL, psycopg2

#### `C:\Workflows\Servicegrad\config.json`
- **Zweck**: Konfigurationsdatei für den Servicegrad-Workflow
- **Inhalt**:
  - SAP-Verbindungseinstellungen
  - Dateipfade für Exporte und Excel-Dateien
  - E-Mail-Konfiguration
  - Datenbankeinstellungen
- **Anpassung**: Muss an die Server-Umgebung angepasst werden

#### `C:\Workflows\Servicegrad\test_servicegrad.py`
- **Zweck**: Testdatei zur Überprüfung der Workflow-Funktionalität
- **Funktionen**:
  - Test der PostgreSQL-Verbindung
  - Test der Workflow-Logger-Funktionalität
  - Test der Konfigurationsladung
- **Verwendung**: Zur Überprüfung nach der Installation

#### `C:\Workflows\Common\workflow_logger.py`
- **Zweck**: Gemeinsamer Logger, der von mehreren Workflows verwendet wird
- **Funktionen**:
  - Bereitstellung von Logging-Funktionalitäten für alle Workflows
  - Zentrale Verwaltung von Datenbankverbindungen für das Logging
- **Abhängigkeiten**: PostgreSQL, psycopg2

### Backend-API-Dateien

#### `C:\app\backend\src\services\workflowService.ts`
- **Zweck**: Service-Klasse für die Workflow-Ausführung über die API
- **Funktionen**:
  - Starten von Workflows
  - Überwachung des Workflow-Status
  - Abrufen von Workflow-Konfigurationen
- **Schnittstelle**: Stellt REST-API-Endpunkte für das Frontend bereit

#### `C:\app\backend\src\config\database.config.ts`
- **Zweck**: Zentralisierte Konfiguration der Datenbankverbindung
- **Funktionen**:
  - Laden der Datenbankkonfiguration aus verschiedenen Quellen
  - Erstellen von Verbindungspools
  - Validierung der Konfiguration
- **Flexibilität**: Unterstützt Umgebungsvariablen, Konfigurationsdateien und Standardwerte

#### `C:\app\backend\src\db\schema.ts`
- **Zweck**: Datenbankschema-Definition mit Drizzle ORM
- **Inhalt**:
  - Definition aller Datenbanktabellen
  - Festlegung von Datentypen und Beziehungen
  - Index-Definitionen für Performance
- **Verwendung**: Wird für die Migration und den Zugriff auf die Datenbank verwendet

### Frontend-Dateien (nur in dieser App)

#### `src\modules\backend\components\workflows\ServicegradWorkflowCard.tsx`
- **Zweck**: React-Komponente für die Anzeige und Steuerung des Servicegrad-Workflows
- **Funktionen**:
  - Anzeige des Workflow-Status
  - Starten des Workflows
  - Konfiguration des Workflows
  - Anzeige von Workflow-Logs
- **Schnittstelle**: Kommuniziert mit dem Backend über die API

## Verbindungsarchitektur

### Gesamtarchitektur

```
Frontend (diese App) ←→ Backend-API (auf Server) ←→ Workflow-System (auf Server) ←→ SAP-System
                                        ↓
                                   PostgreSQL-Datenbank (auf Server)
```

### Frontend zu Backend-API Verbindung

1. **Kommunikationsweg**: RESTful API über HTTP/HTTPS
2. **Authentifizierung**: JWT-Token
3. **Hauptendpunkte**:
   - `GET /api/workflows` - Liste aller verfügbaren Workflows
   - `POST /api/workflows/:id/execute` - Startet einen Workflow
   - `GET /api/workflows/:id/status` - Status eines Workflows
   - `GET /api/workflows/:id/logs` - Logs eines Workflows

### Backend-API zu Workflow-System Verbindung

1. **Kommunikationsweg**: Lokale Prozessausführung auf dem Server
2. **Ausführungsmethode**: Python-Skripte werden als Child-Prozesse gestartet
3. **Datenfluss**:
   - Backend empfängt API-Anfrage vom Frontend
   - Backend startet entsprechendes Workflow-Skript in `C:\Workflows\Servicegrad\`
   - Backend überwacht den Prozess und meldet Status zurück
   - Backend speichert Logs in PostgreSQL

### Workflow-System zu SAP-System Verbindung

1. **Kommunikationsweg**: SAP GUI Scripting
2. **Authentifizierung**: SAP-Anmeldedaten aus der Konfiguration
3. **Datenfluss**:
   - Workflow-Skript startet SAP GUI
   - Skript meldet sich am SAP-System an
   - Skript führt Transaktionen aus
   - Skript extrahiert Daten und schließt SAP

### Workflow-System zu PostgreSQL Verbindung

1. **Kommunikationsweg**: Direkte Datenbankverbindung mit psycopg2
2. **Authentifizierung**: Datenbank-Anmeldeinformationen aus Umgebungsvariablen
3. **Datenfluss**:
   - Workflow-Logger schreibt Logs in die Datenbank
   - Servicegrad-Daten werden in der dispatch_data-Tabelle gespeichert
   - Backend-API liest Status und Logs aus der Datenbank

## Konfiguration

### Umgebungsvariablen (.env-Datei)

```bash
# Datenbankkonfiguration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=lapp_dashboard
DB_USER=postgres
DB_PASSWORD=your_password

# Servicegrad-Workflow Pfade
SERVICEGRAD_EXPORT_DIR=C:\app\data\export
SERVICEGRAD_SAP_EXCEL_PATH=C:\app\data\excel
SERVICEGRAD_MAIN_EXCEL_PATH=C:\app\data\excel\Servicegrad_BluePrint.xlsx
SERVICEGRAD_TARGET_EXCEL_PATH=C:\app\data\export\Servicegrad_Kennzahlen.xlsx

# SAP-Konfiguration
SAP_EXECUTABLE_PATH=C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe
SAP_SYSTEM_ID=PS4
SAP_CLIENT=009
SAP_LANGUAGE=DE

# E-Mail-Konfiguration
SERVICEGRAD_EMAIL_RECIPIENT=<EMAIL>
SERVICEGRAD_SMTP_SERVER=smtp.example.com
SERVICEGRAD_SMTP_PORT=587
SERVICEGRAD_SMTP_USERNAME=<EMAIL>
SERVICEGRAD_SMTP_PASSWORD=email_password
```

### config.json-Anpassung

```json
{
  "sap": {
    "executable_path": "C:\\Program Files (x86)\\SAP\\FrontEnd\\SapGui\\sapshcut.exe",
    "system_id": "PS4",
    "client": "009",
    "language": "DE",
    "tcode": "/n/LSGIT/VS_DLV_CHECK"
  },
  "paths": {
    "export_dir": "C:\\app\\data\\export",
    "export_basename": "SG",
    "sap_excel_path": "C:\\app\\data\\excel",
    "main_excel_path": "C:\\app\\data\\excel\\Servicegrad_BluePrint.xlsx",
    "target_excel_path": "C:\\app\\data\\export\\Servicegrad_Kennzahlen.xlsx"
  },
  "email": {
    "recipient": "<EMAIL>",
    "subject": "Servicegrad",
    "smtp_server": "smtp.example.com",
    "smtp_port": 587,
    "smtp_username": "<EMAIL>",
    "use_smtp_fallback": true
  }
}
```

## Deployment-Schritte

### 1. Server-Vorbereitung

1. **Verzeichnisstruktur erstellen**
   ```bash
   mkdir C:\Workflows
   mkdir C:\Workflows\Servicegrad
   mkdir C:\Workflows\Common
   mkdir C:\app\backend\src\config
   mkdir C:\app\backend\src\db
   mkdir C:\app\backend\src\services
   mkdir C:\app\backend\src\routes
   mkdir C:\app\data\excel
   mkdir C:\app\data\export
   ```

2. **Benutzerkonto einrichten**
   - Dediziertes Servicekonto für die Workflow-Ausführung
   - Mitgliedschaft in lokalen Administratorengruppe

### 2. Softwareinstallation

1. **Python installieren**
   - Download von python.org
   - Installation mit "Add Python to PATH" Option
   - Überprüfen: `python --version`

2. **Python-Pakete installieren**
   ```bash
   pip install psycopg2-binary pandas xlwings pywin32
   ```

3. **Node.js installieren**
   - Download von nodejs.org
   - Installation mit Standardoptionen
   - Überprüfen: `node --version` und `npm --version`

4. **PostgreSQL installieren**
   - Download von postgresql.org
   - Installation mit pgAdmin und Kommandozeilentools
   - Während der Installation: Passwort für postgres-Benutzer festlegen

5. **SAP GUI installieren**
   - Von SAP Marketplace herunterladen
   - Mit Server-System verbinden
   - Testen der Verbindung

6. **Microsoft Excel installieren**
   - Volume-Lizenz für Server erforderlich
   - Automatisierung über COM-Schnittstelle aktivieren

### 3. Dateien kopieren

1. **Workflow-Dateien kopieren**
   - `Servicegrad_Workflow.py` nach `C:\Workflows\Servicegrad\`
   - `workflow_logger.py` nach `C:\Workflows\Servicegrad\`
   - `config.json` nach `C:\Workflows\Servicegrad\`
   - `test_servicegrad.py` nach `C:\Workflows\Servicegrad\`
   - `workflow_logger.py` nach `C:\Workflows\Common\`

2. **Backend-Dateien kopieren**
   - Alle Dateien aus `backend/src/` nach `C:\app\backend\src\`
   - `.env`-Datei nach `C:\app\backend\`

3. **Excel-Vorlage kopieren**
   - `Servicegrad_BluePrint.xlsx` nach `C:\app\data\excel\`

### 4. Datenbank einrichten

1. **PostgreSQL-Datenbank erstellen**
   ```sql
   CREATE DATABASE lapp_dashboard;
   CREATE USER lapp_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE lapp_dashboard TO lapp_user;
   ```

2. **Schema migrieren**
   ```bash
   cd C:\app\backend
   npm run db:push  # Drizzle-Migration durchführen
   ```

### 5. Konfiguration anpassen

1. **Umgebungsvariablen setzen**
   - Systemsteuerung > System > Erweiterte Systemeinstellungen > Umgebungsvariablen
   - Alle Variablen aus dem Abschnitt "Umgebungsvariablen" hinzufügen

2. **config.json anpassen**
   - Pfade an die Server-Verzeichnisstruktur anpassen
   - SAP-Verbindungseinstellungen überprüfen

### 6. Backend-API starten

1. **Abhängigkeiten installieren**
   ```bash
   cd C:\app\backend
   npm install
   ```

2. **Backend starten**
   ```bash
   npm start
   # Oder für Produktionsumgebung:
   npm run build
   npm run start:prod
   ```

### 7. Dienst einrichten

1. **Windows-Dienst für Backend-API erstellen**
   - Mit Tools wie NSSM oder node-windows
   - Automatischer Start beim Systemstart

2. **Geplante Aufgabe für Workflow einrichten**
   - Windows Aufgabenplanung
   - Täglich zur gewünschten Zeit
   - Ausführung von `C:\Workflows\Servicegrad\Servicegrad_Workflow.py`

## Testverfahren

### 1. Backend-API testen

```bash
cd C:\app\backend
npm test
```

### 2. Workflow-Logger testen

```bash
cd C:\Workflows\Servicegrad
python test_servicegrad.py
```

### 3. Workflow manuell testen

```bash
cd C:\Workflows\Servicegrad
python Servicegrad_Workflow.py
```

### 4. Frontend-Backend-Verbindung testen

1. Frontend in dieser App öffnen
2. Zur Workflow-Seite navigieren
3. Servicegrad-Workflow starten
4. Status und Logs überprüfen

### 5. End-to-End-Test

1. Workflow zur geplanten Zeit ausführen
2. SAP-Export überprüfen
3. Excel-Dateien überprüfen
4. E-Mail-Empfang überprüfen
5. Datenbankeinträge überprüfen

## Fehlerbehebung

### Häufige Probleme und Lösungen

#### 1. SAP-Verbindung fehlgeschlagen

**Symptome**:
- Fehlermeldung "SAP GUI konnte nicht gestartet werden"
- Timeout bei der Anmeldung

**Lösungen**:
- SAP GUI korrekt installiert?
- SAP-Verbindungseinstellungen in config.json überprüfen
- Firewall-Einstellungen für SAP-Ports überprüfen

#### 2. Excel-Verarbeitung fehlgeschlagen

**Symptome**:
- Fehlermeldung "Excel konnte nicht gestartet werden"
- COM-Automatisierungsfehler

**Lösungen**:
- Excel korrekt installiert?
- COM-Automatisierung in Excel-Einstellungen aktiviert?
- Ausführungsberechtigungen für Excel überprüfen

#### 3. Datenbankverbindung fehlgeschlagen

**Symptome**:
- Fehlermeldung "Verbindung zur Datenbank fehlgeschlagen"
- Timeout bei der Verbindung

**Lösungen**:
- PostgreSQL-Dienst läuft?
- Verbindungsparameter in .env-Datei überprüfen
- Firewall-Einstellungen für PostgreSQL-Port überprüfen

#### 4. E-Mail-Versand fehlgeschlagen

**Symptome**:
- Fehlermeldung "E-Mail konnte nicht gesendet werden"
- SMTP-Authentifizierungsfehler

**Lösungen**:
- SMTP-Server-Einstellungen in .env-Datei überprüfen
- Anmeldeinformationen für SMTP-Konto überprüfen
- Firewall-Einstellungen für SMTP-Port überprüfen

#### 5. Workflow hängt sich auf

**Symptome**:
- Keine Fortschrittsmeldungen
- Prozess läuft ohne Ende

**Lösungen**:
- Timeout-Einstellungen in Workflow überprüfen
- SAP- oder Excel-Prozesse manuell beenden
- Logs in PostgreSQL überprüfen

### Debugging-Tools

1. **PostgreSQL-Logs**
   - Location: `C:\Program Files\PostgreSQL\<version>\data\log\`
   - Enthält Datenbankfehler und Verbindungsprobleme

2. **Windows-Ereignisanzeige**
   - Enthält System- und Anwendungsfehler
   - Nützlich für SAP- und Excel-Probleme

3. **Browser-Entwicklertools**
   - Netzwerk-Tab für API-Anfragen
   - Konsole für JavaScript-Fehler

4. **Python-Debugger**
   - Für detaillierte Analyse von Workflow-Problemen
   - Breakpoints in kritischen Abschnitten setzen

### Wartung

1. **Regelmäßige Updates**
   - Python-Pakete aktualisieren: `pip list --outdated --format=freeze | grep -v '^\-e' | cut -d = -f 1 | xargs -n1 pip install -U`
   - Node.js-Pakete aktualisieren: `npm update`
   - Betriebssystem-Updates installieren

2. **Log-Rotation**
   - PostgreSQL-Logs regelmäßig archivieren
   - Windows-Ereignisprotokolle regelmäßig bereinigen

3. **Performance-Monitoring**
   - CPU- und RAM-Auslastung überwachen
   - Datenbankperformance überwachen
   - Workflow-Ausführungszeiten protokollieren

4. **Backup-Strategie**
   - Regelmäßige Backups der PostgreSQL-Datenbank
   - Backup der Konfigurationsdateien
   - Backup der Excel-Vorlagen