import express, { Request, Response } from 'express';
import { db } from '../db';
import { dispatchData, bestand200 } from '../db/schema';
import { and, desc, isNotNull } from 'drizzle-orm';

const router = express.Router();

// GET /api/servicegrad - Servicegrad-Daten aus dispatch_data
router.get('/servicegrad', async (req: Request, res: Response) => {
  try {
    const data = await db
      .select({ datum: dispatchData.datum, servicegrad: dispatchData.servicegrad })
      .from(dispatchData)
      .where(
        and(
          isNotNull(dispatchData.datum),
          isNotNull(dispatchData.servicegrad)
        )
      )
      .orderBy(desc(dispatchData.datum))
      .limit(30);

    res.json({ success: true, data });
  } catch (error) {
    console.error('Fehler bei GET /api/servicegrad:', error);
    // Fallback: Dummy-Daten zurückgeben
    res.json({
      success: true,
      data: [
        { datum: '2025-01-08', servicegrad: 95.5 },
        { datum: '2025-01-07', servicegrad: 92.3 },
        { datum: '2025-01-06', servicegrad: 97.1 }
      ],
      message: 'Fallback-Daten (Tabelle nicht verfügbar)'
    });
  }
});

// GET /api/bestand - Aktuelle Bestandsdaten (fallback: bestand200)
router.get('/bestand', async (req: Request, res: Response) => {
  try {
    // Hinweis: Ziel ist lx03_bin_status. Bis zur Migration liefern wir Daten aus bestand200.
    const rows = await db
      .select({
        aufnahmeDatum: bestand200.aufnahmeDatum,
        lagerplatz: bestand200.lagerplatz,
        material: bestand200.material,
        charge: bestand200.charge,
        gesamtbestand: bestand200.gesamtbestand,
      })
      .from(bestand200)
      .orderBy(desc(bestand200.aufnahmeDatum))
      .limit(100);

    res.json({ success: true, data: rows, source: 'bestand200' });
  } catch (error) {
    console.error('Fehler bei GET /api/bestand:', error);
    // Fallback: Dummy-Daten zurückgeben
    res.json({
      success: true,
      data: [
        { aufnahmeDatum: '2025-01-08', lagerplatz: 'A-01-001', material: 'MAT001', charge: 'CH001', gesamtbestand: 150.5 },
        { aufnahmeDatum: '2025-01-08', lagerplatz: 'A-01-002', material: 'MAT002', charge: 'CH002', gesamtbestand: 200.0 },
        { aufnahmeDatum: '2025-01-07', lagerplatz: 'B-02-001', material: 'MAT003', charge: 'CH003', gesamtbestand: 75.2 }
      ],
      source: 'fallback',
      message: 'Fallback-Daten (Tabelle nicht verfügbar)'
    });
  }
});

export default router;

