import { Link } from "@tanstack/react-router";
import { useAuthContext } from "@/contexts/AuthContext";
import {
    User,
    LogOut,
    Bell,
    Shield,
    Settings
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

/**
 * User Menu Component
 * 
 * Dropdown-Menü für Benutzeroptionen mit Avatar und Benutzerinformationen
 * Verwendet echte Benutzerdaten aus dem AuthContext
 */
export const UserMenu = () => {
    const { user, logout } = useAuthContext();

    // Fallback falls kein Benutzer angemeldet ist
    if (!user) {
        return null;
    }

    // Rolle aus dem roles Array extrahieren (erste Rolle als primäre Rolle)
    const primaryRole = user.roles?.[0] || 'Benutzer';

    // Rollenübersetzung für deutsche Anzeige
    const roleTranslation: Record<string, string> = {
        'Administrator': 'Administrator',
        'Ben<PERSON>er': 'Benutzer',
        'Besucher': 'Besucher',
        'admin': 'Administrator',
        'user': 'Benutzer',
        'visitor': 'Besucher'
    };

    const displayRole = roleTranslation[primaryRole] || primaryRole;
    // Name aus user.name oder fallback zu username
    const displayName = user.name || user.username;

    const handleLogout = () => {
        logout();
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                        <AvatarImage src={undefined} alt={displayName} />
                        <AvatarFallback className="bg-white text-black text-xs font-semibold">
                            {displayName.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                    </Avatar>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
                className="w-56 bg-white border border-gray-200 shadow-lg rounded-md z-50"
                align="end"
                forceMount
            >
                <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{displayName}</p>
                        <p className="text-xs leading-none text-muted-foreground">
                            {user.email}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                            {displayRole}
                        </p>
                    </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                    <Link to="/user-settings" className="flex items-center gap-2 w-full">
                        <User className="h-4 w-4" />
                        <span>Profil</span>
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                    <Link to="/settings" className="flex items-center gap-2 w-full">
                        <Settings className="h-4 w-4" />
                        <span>Einstellungen</span>
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                    <Link to="/user-settings" className="flex items-center gap-2 w-full">
                        <Bell className="h-4 w-4" />
                        <span>Benachrichtigungen</span>
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                    <Link to="/user-settings" className="flex items-center gap-2 w-full">
                        <Shield className="h-4 w-4" />
                        <span>Sicherheit</span>
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                    onClick={handleLogout}
                    className="flex items-center gap-2 cursor-pointer text-red-600 focus:text-red-600"
                >
                    <LogOut className="h-4 w-4" />
                    <span>Abmelden</span>
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};