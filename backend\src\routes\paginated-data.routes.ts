/**
 * Paginated Data API Routes
 * 
 * TODO: Nach Migration vollständig auf Drizzle ORM umstellen
 * Temporär deaktiviert während der Migration.
 */

import express from 'express';
import { ApiResponse } from '../types/database.types';

const router = express.Router();

/**
 * Temporäre Platzhalter-Route während der Drizzle-Migration
 */
router.use('*', (req, res) => {
  const response: ApiResponse<null> = {
    success: false,
    error: 'Paginated Data Service ist temporär nicht verfügbar während der Migration von Prisma zu Drizzle ORM.',
    data: null
  };
  res.status(501).json(response);
});

// TODO: Implementiere alle ursprünglichen Routen mit Drizzle ORM:
// - /alle-daten/cursor
// - /alle-daten/offset  
// - /alle-daten
// - /ablaengerei/cursor
// - /ablaengerei/offset
// - /performance-comparison
// - /schema-info

export default router;