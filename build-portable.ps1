#!/usr/bin/env pwsh
# Portable Build Script for JOZI1 Lapp Dashboard

Write-Host "Starting portable build process..." -ForegroundColor Green

# 1. Backend vorbereiten und sicherstellen, dass dist existiert
Write-Host "Preparing backend..." -ForegroundColor Yellow
Push-Location backend

# Installiere nur, falls node_modules fehlt oder leer ist
if (-not (Test-Path "node_modules") -or (Get-ChildItem "node_modules" -ErrorAction SilentlyContinue | Measure-Object).Count -eq 0) {
    Write-Host "Installing backend dependencies..." -ForegroundColor Yellow
    npm ci --silent
} else {
    Write-Host "backend/node_modules already present, skipping install" -ForegroundColor DarkYellow
}

# Backend TypeScript kompilieren
if (-not (Test-Path "dist")) {
    Write-Host "Building backend TypeScript..." -ForegroundColor Yellow
    pnpm run build
    if (-not (Test-Path "dist")) {
        Write-Host "ERROR: Backend build failed - dist/ directory not created." -ForegroundColor Red
        Pop-Location
        exit 1
    }
} else {
    Write-Host "Backend dist already exists" -ForegroundColor Green
}

Pop-Location

# 2. Sicherstellen, dass public/ Verzeichnis existiert
Write-Host "Ensuring public directory exists..." -ForegroundColor Yellow
if (-not (Test-Path "public" -PathType Container)) {
    if (Test-Path "public" -PathType Leaf) {
        Move-Item "public" "public.backup" -Force
        Write-Host "Moved public file to public.backup" -ForegroundColor Yellow
    }
    New-Item -ItemType Directory -Path "public" -Force | Out-Null
    # Kopiere Assets
    if (Test-Path "assets") {
        Copy-Item -Path "assets/*" -Destination "public/" -Recurse -Force -ErrorAction SilentlyContinue
    }
    if (Test-Path "images") {
        Copy-Item -Path "images/*" -Destination "public/" -Recurse -Force -ErrorAction SilentlyContinue
    }
    Write-Host "Public directory created and assets copied" -ForegroundColor Green
}

# 3. Assets kopieren
Write-Host "Copying assets..." -ForegroundColor Yellow
if (Test-Path "assets") {
    Copy-Item -Path "assets/*" -Destination "public/" -Recurse -Force
}
if (Test-Path "public/*.png") {
    Copy-Item -Path "public/*.png" -Destination "dist/assets/" -Force -ErrorAction SilentlyContinue
}
if (Test-Path "public/*.ico") {
    Copy-Item -Path "public/*.ico" -Destination "dist/assets/" -Force -ErrorAction SilentlyContinue
}

# 3. Frontend bauen
Write-Host "Building frontend..." -ForegroundColor Yellow
npx vite build --config vite.renderer.config.mts
npx vite build --config vite.main.config.ts
npx vite build --config vite.preload.config.ts

# 4. Portable Build erstellen
Write-Host "Creating portable build..." -ForegroundColor Yellow
npx electron-packager . LappDashboard --platform=win32 --arch=x64 --out=portable-build --overwrite

# Prüfe ob Build erfolgreich war
if (-not (Test-Path "portable-build")) {
    Write-Host "ERROR: Portable build failed - portable-build directory not created." -ForegroundColor Red
    exit 1
}

# electron-packager erzeugt in der Regel einen Ordner "LappDashboard-win32-x64" (ggf. mit Suffix)
$pkgDir = Get-ChildItem -Directory "portable-build" | Where-Object { $_.Name -like "LappDashboard-win32-x64*" } | Select-Object -First 1
if (-not $pkgDir) {
    Write-Host "ERROR: Konnte den erzeugten App-Ordner unter portable-build nicht finden." -ForegroundColor Red
    Write-Host "Verfügbare Ordner:" -ForegroundColor Yellow
    Get-ChildItem -Directory "portable-build" | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor Cyan }
    exit 1
}
# electron-packager erzeugt in der Regel einen Ordner "LappDashboard-win32-x64" oder "LappDashboard-win32-x64 (electron version)".
# Wir ermitteln den tatsächlich erstellten Ordner dynamisch:
$pkgDir = Get-ChildItem -Directory "portable-build-new" | Where-Object { $_.Name -like "LappDashboard-win32-x64*" } | Select-Object -First 1
if (-not $pkgDir) {
    Write-Host "ERROR: Konnte den erzeugten App-Ordner unter portable-build-new nicht finden." -ForegroundColor Red
    exit 1
}

# 5. Frontend-Dateien (dist) in resources/app kopieren
Write-Host "Copying frontend (dist) to portable resources/app..." -ForegroundColor Yellow

# Pfade robust mit Join-Path zusammensetzen
$portableRoot = $pkgDir.FullName
$portableResources = Join-Path -Path $portableRoot -ChildPath "resources"
$portableApp = Join-Path -Path $portableResources -ChildPath "app"

# resources/app leeren und neu anlegen (saubere Übernahme des Frontends)
if (Test-Path $portableApp) {
    Remove-Item -Path $portableApp -Recurse -Force -ErrorAction SilentlyContinue
}
New-Item -ItemType Directory -Force -Path $portableApp | Out-Null

# dist nach resources/app kopieren (index.html, assets, etc.)
if (Test-Path "dist") {
    Copy-Item -Path "dist\*" -Destination $portableApp -Recurse -Force
    Write-Host "Frontend dist copied to resources/app" -ForegroundColor Green
} else {
    Write-Host "WARN: dist Ordner fehlt. Bitte sicherstellen, dass Vite-Renderer-Build erfolgreich war." -ForegroundColor Yellow
}

# 6. Backend-Dateien in portable build kopieren
Write-Host "Copying backend files to portable build..." -ForegroundColor Yellow

$portableBackend = Join-Path -Path $portableApp -ChildPath "backend"

# 6.1 Minimal package.json in resources/app erzeugen, damit Electron das Main-Entry findet
try {
    $appPkgPath = Join-Path $portableApp "package.json"
    $appPkg = @{
        name = "app"
        main = "main/electron-main.js"
    } | ConvertTo-Json -Depth 5
    $appPkg | Out-File -FilePath $appPkgPath -Encoding UTF8 -Force
    Write-Host "Wrote resources/app/package.json with main=main/electron-main.js" -ForegroundColor Green
} catch {
    Write-Host "WARN: Failed writing resources/app/package.json: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Exe-Datei ermitteln (Name kann variieren):
$exe = Get-ChildItem -Path $portableRoot -Filter "*.exe" | Select-Object -First 1
$portableExe = $exe?.FullName

# Sicherstellen, dass Zielverzeichnisse existieren
New-Item -ItemType Directory -Force -Path $portableBackend | Out-Null

# Backend dist files
if (Test-Path "backend\dist") {
    Copy-Item -Path "backend\dist" -Destination (Join-Path $portableBackend "dist") -Recurse -Force
    Write-Host "Backend dist files copied" -ForegroundColor Green
}

# Backend production dependencies
Write-Host "Installing production dependencies..." -ForegroundColor Yellow
Push-Location backend
try {
    npm ci --omit=dev --silent
} catch {
    Write-Host "Warning: Production dependency install failed, using existing node_modules" -ForegroundColor Yellow
}
Pop-Location

# Kopiere node_modules mit besserer Fehlerbehandlung
if (Test-Path "backend\node_modules") {
    Write-Host "Copying backend node_modules (this may take a while)..." -ForegroundColor Yellow
    try {
        robocopy "backend\node_modules" "$(Join-Path $portableBackend "node_modules")" /E /R:1 /W:1 /NP /NDL /NJH /NJS | Out-Null
        if ($LASTEXITCODE -le 1) {  # robocopy returns 0 or 1 for success
            Write-Host "Backend node_modules copied successfully" -ForegroundColor Green
        } else {
            Write-Host "Warning: Some files in node_modules could not be copied" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "Warning: node_modules copy failed, trying alternative method..." -ForegroundColor Yellow
        Copy-Item -Path "backend\node_modules" -Destination (Join-Path $portableBackend "node_modules") -Recurse -Force -ErrorAction SilentlyContinue
    }
} else {
    Write-Host "Warning: backend\node_modules not found" -ForegroundColor Yellow
}

# Backend package.json
if (Test-Path "backend\package.json") {
    Copy-Item -Path "backend\package.json" -Destination (Join-Path $portableBackend "package.json") -Force
    Write-Host "Backend package.json copied" -ForegroundColor Green
}

# Create .env file for portable deployment
$envContent = @"
# Portable deployment environment variables
NODE_ENV=production
# PostgreSQL-Konfiguration für portable Bereitstellung
DB_HOST=localhost
DB_PORT=5434
DB_USER=leitstand_dashboard
DB_PASSWORD=dashboard_password
DB_NAME=leitstand_dashboard
DB_SSL=false
PORT=3001
"@

$envPath = Join-Path $portableBackend ".env"
$envContent | Out-File -FilePath $envPath -Encoding UTF8 -Force
Write-Host "Environment file created for portable deployment" -ForegroundColor Green

# Database files
if (Test-Path "backend\database") {
    Copy-Item -Path "backend\database" -Destination (Join-Path $portableBackend "database") -Recurse -Force
    Write-Host "Database files copied" -ForegroundColor Green
}

# Prisma files (both schemas)
if (Test-Path "backend\prisma") {
    Copy-Item -Path "backend\prisma" -Destination (Join-Path $portableBackend "prisma") -Recurse -Force
    Write-Host "Prisma files copied" -ForegroundColor Green
}

# Prisma SFM Dashboard schema
if (Test-Path "backend\prisma-sfm-dashboard") {
    Copy-Item -Path "backend\prisma-sfm-dashboard" -Destination (Join-Path $portableBackend "prisma-sfm-dashboard") -Recurse -Force
    Write-Host "Prisma SFM Dashboard schema copied" -ForegroundColor Green
}

# Prisma RAG schema
if (Test-Path "backend\prisma-rag") {
    Copy-Item -Path "backend\prisma-rag" -Destination (Join-Path $portableBackend "prisma-rag") -Recurse -Force
    Write-Host "Prisma RAG schema copied" -ForegroundColor Green
}

# Backend configuration files
if (Test-Path "backend\config") {
    Copy-Item -Path "backend\config" -Destination (Join-Path $portableBackend "config") -Recurse -Force
    Write-Host "Backend config files copied" -ForegroundColor Green
}

# Backend templates (email, etc.)
if (Test-Path "backend\templates") {
    Copy-Item -Path "backend\templates" -Destination (Join-Path $portableBackend "templates") -Recurse -Force
    Write-Host "Backend templates copied" -ForegroundColor Green
}

# Backend uploads directory structure
if (Test-Path "backend\uploads") {
    Copy-Item -Path "backend\uploads" -Destination (Join-Path $portableBackend "uploads") -Recurse -Force
    Write-Host "Backend uploads directory copied" -ForegroundColor Green
}

# Generate Prisma clients for both schemas in portable build
Write-Host "Generating Prisma clients for portable build (dual schemas)..." -ForegroundColor Yellow
Push-Location $portableBackend
try {
    # Generate SFM Dashboard schema client
    npx prisma generate --schema=./prisma-sfm-dashboard/schema.prisma --silent
    Write-Host "SFM Dashboard Prisma client generated" -ForegroundColor Green
    
    # Generate RAG schema client
    npx prisma generate --schema=./prisma-rag/schema.prisma --silent
    Write-Host "RAG Prisma client generated" -ForegroundColor Green
} catch {
    Write-Host "Warning: Prisma generate failed, but continuing..." -ForegroundColor Yellow
}
Pop-Location

# Sicherstellen, dass main/preload Bundles vorhanden sind
if (-not (Test-Path "dist\main\electron-main.js")) {
    Write-Host "WARN: dist\main\electron-main.js fehlt. Bitte Vite Main Build prüfen." -ForegroundColor Yellow
}
if (-not (Test-Path "dist\main\preload.js")) {
    Write-Host "WARN: dist\main\preload.js fehlt. Bitte Vite Preload Build prüfen." -ForegroundColor Yellow
}

Write-Host "Portable build completed successfully!" -ForegroundColor Green
Write-Host ("Build location: {0}" -f $portableRoot) -ForegroundColor Cyan
Write-Host ("Frontend served from: {0}" -f $portableApp) -ForegroundColor Cyan
Write-Host ("Backend placed at: {0}" -f $portableBackend) -ForegroundColor Cyan

# Validate AI/RAG modules
Write-Host "Validating AI/RAG modules..." -ForegroundColor Yellow
$ragSchemaPath = Join-Path $portableBackend "prisma-rag\schema.prisma"
$aiServicePath = Join-Path $portableBackend "dist\services\ai.service.js"
if (Test-Path $ragSchemaPath) {
    Write-Host "✓ RAG Schema found" -ForegroundColor Green
} else {
    Write-Host "⚠ RAG Schema missing - AI features may not work" -ForegroundColor Yellow
}
if (Test-Path $aiServicePath) {
    Write-Host "✓ AI Service found" -ForegroundColor Green
} else {
    Write-Host "⚠ AI Service missing - AI features may not work" -ForegroundColor Yellow
}

# Optional: Start the app
$startApp = Read-Host "Start the portable app now? (y/N)"
if ($startApp -eq "y" -or $startApp -eq "Y") {

    # 1) Bevorzugt die eigentliche App-Exe im Root des App-Ordners: *Dashboard*.exe
    $exePreferred = Get-ChildItem -Path $portableRoot -Filter "*Dashboard*.exe" -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($exePreferred) {
        $portableExe = $exePreferred.FullName
        $portableRoot = $exePreferred.DirectoryName
    }

    # 2) Falls nicht gefunden: Rekursiv suchen, aber nur im App-Root und NICHT in resources\app\backend\node_modules
    if (-not $portableExe) {
        Write-Host "INFO: Suche nach App-EXE im Portable-Ordner (ohne Backend-Binaries)..." -ForegroundColor Cyan
        $exclude = Join-Path $portableRoot "resources\app\backend\node_modules"
        $exeCandidates = Get-ChildItem -Path $portableRoot -Recurse -Filter "*.exe" -ErrorAction SilentlyContinue `
            | Where-Object {
                # ausschließen: Prisma/Engine/Node-Binaries
                $_.FullName -notlike "*resources\app\backend\node_modules\*"
            } `
            | Where-Object {
                # bevorzugt *Dashboard*.exe
                $_.Name -match "Dashboard.*\.exe"
            } `
            | Sort-Object -Property LastWriteTime -Descending

        if (-not $exeCandidates -or $exeCandidates.Count -eq 0) {
            # letzte Chance: irgendeine .exe im Root (aber weiterhin Node-Module ausschließen)
            $exeCandidates = Get-ChildItem -Path $portableRoot -Recurse -Filter "*.exe" -ErrorAction SilentlyContinue `
                | Where-Object { $_.FullName -notlike "*resources\app\backend\node_modules\*" } `
                | Sort-Object -Property LastWriteTime -Descending
        }

        if ($exeCandidates -and $exeCandidates.Count -gt 0) {
            $portableExe = $exeCandidates[0].FullName
            $portableRoot = $exeCandidates[0].DirectoryName
        }
    }

    if (-not $portableExe) {
        Write-Host "WARN: Konnte keine App-EXE finden. Bitte manuell prüfen unter: $portableRoot" -ForegroundColor Yellow
    } else {
        Write-Host ("Starting portable app: {0}" -f $portableExe) -ForegroundColor Green
        Start-Process -FilePath $portableExe -WorkingDirectory $portableRoot
    }
}