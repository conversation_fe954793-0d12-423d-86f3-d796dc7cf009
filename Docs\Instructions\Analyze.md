Prompt für README.md Generierung:
Analysiere den Ordner [lib](../../src/lib) in diesem Projekt und erstelle eine detaillierte README.md Datei für diesen Ordner. Die README sollte folgende Struktur und Inhalte haben:
Aufgabenstellung:

1. Übersicht & Zweck:
Erkläre die Hauptfunktion dieses Ordners im Gesamtkontext der Anwendung
Beschreibe, welche Geschäftslogik oder welcher Anwendungsbereich hier implementiert wird
Identifiziere die Rolle im Architekturmuster (z.B. Controller, Service Layer, UI Components, etc.)

2. Dateistruktur-Analyse:
Liste alle Dateien und Unterordner mit kurzer Beschreibung auf
Verwende eine Baumstruktur zur visuellen Darstellung
Gruppiere zusammengehörige Dateien logisch

3. Detaillierte Dateibeschreibungen:
F<PERSON><PERSON> <PERSON><PERSON>: <PERSON><PERSON><PERSON>, Hauptfunktionalitäten, exportierte Funktionen/Klassen
Wichtige Methoden und deren Parameter
Verwendete Design Patterns falls vorhanden

4. Abhängigkeiten & Verbindungen:
Externe npm/pip/gem Pakete die verwendet werden
Interne Abhängigkeiten zu anderen Projektordnern
API-Endpunkte oder Services die konsumiert werden
Datenfluss zwischen diesem und anderen Modulen

5. Technische Details:
Verwendete Technologien, Frameworks und Libraries
Konfigurationsdateien und deren Zweck
Umgebungsvariablen die benötigt werden
Performance-kritische Bereiche oder Optimierungen

6. Verwendungsbeispiele:
Code-Snippets die zeigen, wie die Hauptfunktionen verwendet werden
Typische Import-Statements
Beispielhafte Funktionsaufrufe mit erwarteten Ein- und Ausgaben

7. Datenmodelle & Schnittstellen:
Beschreibung von Datenstrukturen, Interfaces oder Typen
Datenbankschemas falls relevant
API Request/Response Formate

8. Testing:
Vorhandene Tests und was sie abdecken
Anleitung zum Ausführen der Tests
Testabdeckung falls messbar

9. Entwicklungshinweise:
Best Practices für Erweiterungen
Häufige Fehlerquellen und deren Vermeidung
TODOs oder bekannte Verbesserungspotentiale
Coding-Standards die in diesem Ordner befolgt werden

# Formatierungsanforderungen:
* Verwende Markdown-Formatierung mit klaren Überschriften (##, ###)
* Nutze Code-Blöcke mit Syntax-Highlighting
* Erstelle Tabellen für übersichtliche Darstellung von Parametern/Optionen
* Verwende Badge-Icons für Technologien falls passend
* Füge interne Links für Navigation innerhalb der README ein

# Zusätzliche Anforderungen:
* Schreibe in klarer, verständlicher Sprache ohne unnötigen Fachjargon
* Gehe davon aus, dass der Leser mit dem Projekt nicht vertraut ist
* Priorisiere die wichtigsten Informationen am Anfang
* Halte die Beschreibungen präzise aber vollständig
* Markiere veraltete oder zu überarbeitende Bereiche deutlich

Beginne die Analyse mit dem Ordner und erstelle eine README.md die sowohl für neue Entwickler als auch für die Wartung des Codes wertvoll ist.