/**
 * Available Drums Routes
 * 
 * API-Endpunkte für die Abfrage verfügbarer Trommeln basierend auf:
 * - aufnahmeDatum: Datum der Trommelaufnahme
 * - Material: Materialcode (matnr)
 * - Gesamtbestand: Mindestlänge in Metern
 */

import { Router, Request, Response } from 'express';
// TODO: bestand200-Tabelle existiert nicht im Drizzle-Schema
// Diese Route muss nach der vollständigen Migration neu implementiert werden
// import { db } from '../db';
// import { trommeldaten } from '../db/schema';
import { z } from 'zod';

const router = Router();
// Drizzle ORM wird über db-Import verwendet

/**
 * Konvertiert ein Datum von YYYY-MM-DD Format zu DD.MM.YY Format
 * für die Datenbankabfrage, da die Datenbank Daten im DD.MM.YY Format speichert
 * 
 * @param dateString - Datum im Format YYYY-MM-DD (z.B. "2025-07-31")
 * @returns Datum im Format DD.MM.YY (z.B. "31.07.25")
 */
function convertDateForDatabase(dateString: string): string {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear().toString().slice(-2); // Nur die letzten 2 Ziffern
  
  return `${day}.${month}.${year}`;
}

// Validierungsschema für die Trommelabfrage
const availableDrumsSchema = z.object({
  aufnahmeDatum: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Datum muss im Format YYYY-MM-DD sein'),
  material: z.string().optional(), // Material ist optional - kann auch leer sein
  minGesamtbestand: z.number().min(0, 'Mindestbestand muss >= 0 sein')
});

/**
 * POST /api/available-drums/filter
 * 
 * Filtert verfügbare Trommeln basierend auf den angegebenen Kriterien
 * 
 * @body {
 *   aufnahmeDatum: string (YYYY-MM-DD)
 *   material: string (Materialcode)
 *   minGesamtbestand: number (Mindestlänge in Metern)
 * }
 * 
 * @returns Array von verfügbaren Trommeln mit allen relevanten Feldern
 */
router.post('/filter', async (req: Request, res: Response) => {
  try {
    // Eingabevalidierung
    const validationResult = availableDrumsSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({
        error: 'Ungültige Eingabedaten',
        details: validationResult.error.errors
      });
    }

    const { aufnahmeDatum, material, minGesamtbestand } = validationResult.data;

    // Konvertiere das Datum von YYYY-MM-DD zu DD.MM.YY für die Datenbankabfrage
    const dbDateFormat = convertDateForDatabase(aufnahmeDatum);
    
    console.log(`Suche nach Trommeln mit Aufnahmedatum: ${aufnahmeDatum} -> DB-Format: ${dbDateFormat}`);

    // Datenbankabfrage mit Filtern - Material nur filtern wenn vorhanden
    const whereClause: any = {
      aufnahmeDatum: {
        equals: dbDateFormat
      },
      Gesamtbestand: {
        gte: minGesamtbestand
      },
      // Nur Trommeln ohne Lieferung anzeigen (verfügbare Trommeln)
      // Verfügbare Trommeln haben leere Strings in der Lieferung-Spalte
      Lieferung: {
        equals: ''
      }
    };

    // Material-Filter nur hinzufügen wenn Material angegeben wurde
    if (material && material.trim() !== '') {
      whereClause.Material = {
        equals: material
      };
    }

    // TODO: Dummy-Implementierung - bestand200-Tabelle existiert nicht im Drizzle-Schema
    // Diese Route muss nach der vollständigen Migration neu implementiert werden
    console.log('WARNUNG: Dummy-Implementierung - bestand200-Tabelle fehlt im Drizzle-Schema');
    
    const formattedDrums: any[] = [];
    // Dummy-Daten für Entwicklungszwecke
    // const availableDrums = await db.select().from(bestand200).where(...);

    console.log(`Verfügbare Trommeln gefunden: ${formattedDrums.length} für Material ${material}, Datum ${aufnahmeDatum}, Min-Bestand ${minGesamtbestand}m - Neustart erzwungen`);

    res.json({
      success: true,
      count: formattedDrums.length,
      drums: formattedDrums,
      filters: {
        aufnahmeDatum,
        material,
        minGesamtbestand
      }
    });

  } catch (error) {
    console.error('Fehler beim Laden verfügbarer Trommeln:', error);
    res.status(500).json({
      error: 'Interner Serverfehler beim Laden der verfügbaren Trommeln',
      message: error instanceof Error ? error.message : 'Unbekannter Fehler'
    });
  }
});

/**
 * GET /api/available-drums/materials
 * 
 * Gibt alle verfügbaren Materialcodes zurück für die Dropdown-Auswahl
 */
router.get('/materials', async (req: Request, res: Response) => {
  try {
    // TODO: Dummy-Implementierung - bestand200-Tabelle existiert nicht im Drizzle-Schema
    console.log('WARNUNG: Dummy-Implementierung - bestand200-Tabelle fehlt im Drizzle-Schema');
    
    const materialList: string[] = [];
    // const materials = await db.select({ Material: bestand200.Material }).from(bestand200)...

    res.json({
      success: true,
      materials: materialList
    });

  } catch (error) {
    console.error('Fehler beim Laden der Materialien:', error);
    res.status(500).json({
      error: 'Fehler beim Laden der verfügbaren Materialien',
      message: error instanceof Error ? error.message : 'Unbekannter Fehler'
    });
  }
});

/**
 * GET /api/available-drums/dates
 * 
 * Gibt alle verfügbaren Aufnahmedaten zurück für die Datumsauswahl
 */
router.get('/dates', async (req: Request, res: Response) => {
  try {
    // TODO: Dummy-Implementierung - bestand200-Tabelle existiert nicht im Drizzle-Schema
    console.log('WARNUNG: Dummy-Implementierung - bestand200-Tabelle fehlt im Drizzle-Schema');
    
    const dateList: string[] = [];
    // const dates = await db.select({ aufnahmeDatum: bestand200.aufnahmeDatum }).from(bestand200)...

    console.log(`Verfügbare Aufnahmedaten gefunden: ${dateList.length}`);
    console.log('Erste 10 Daten:', dateList.slice(0, 10));

    res.json({
      success: true,
      dates: dateList,
      count: dateList.length
    });

  } catch (error) {
    console.error('Fehler beim Laden der Aufnahmedaten:', error);
    res.status(500).json({
      error: 'Fehler beim Laden der verfügbaren Aufnahmedaten',
      message: error instanceof Error ? error.message : 'Unbekannter Fehler'
    });
  }
});

export default router;