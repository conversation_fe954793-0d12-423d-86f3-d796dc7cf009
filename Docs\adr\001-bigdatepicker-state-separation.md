# ADR 001: BigDatePicker State-Trennung

## Status
Akzeptiert (06.09.2025)

## Kontext
Die `BigDatePicker`-Komponente hatte ein UX-Problem: Beim <PERSON> auf einen Tag im linken Kalender trat ein 800ms-Delay auf, bevor die Tagesmarkierung wechselte. Dies lag <PERSON>, dass sowohl die Kalendermarkierung als auch die rechte Flip-Animation denselben State (`currentDate`) verwendeten.

## Entscheidung
Wir haben den State in drei separate Zuständige getrennt:

1. **`selectedDate`**: Steuert die sofortige Markierung im linken Kalender
2. **`displayedDate`**: Steuert die animierte Flip-Anzeige auf der rechten Seite  
3. **`currentMonth`**: Steuert die Kalendernavigation (Monatswechsel)

### Technische Umsetzung
- `updateDate()` ruft sofort `setSelectedDate()` auf → sofortige UI-Rückmeldung
- `animateFlip()` erhält einen Callback und aktualisiert `displayedDate` nach 800ms
- Die Animation bleibt unverändert, behält ihre 800ms-Dauer

## Konsequenzen

### Vorteile
✅ **Sofortige Benutzer-Rückmeldung**: Kalendermarkierung wechselt ohne Delay  
✅ **Erhaltung der Animation**: Die rechte Flip-Animation bleibt visuell ansprechend  
✅ **Saubere Trennung**: Jeder State hat eine klar definierte Single Responsibility  
✅ **Keine Breaking Changes**: Public API bleibt unverändert  

### Nachteile
⚠️ **Erhöhte Komplexität**: Drei States statt einem  
⚠️ **Synchronisation**: States müssen konsistent gehalten werden  

### Tests
- Unit-Tests bestätigen sofortige Kalendermarkierung
- Animation-Tests prüfen 800ms-Delay der rechten Anzeige
- Timer-Mocks für deterministische Test-Ausführung

## Alternativ-Optionen
- **Animation entfernen**: Hätte UX-Appeal reduziert
- **Animation beschleunigen**: Hätte Konsistenz mit Design-System gebrochen
- **Zwei getrennte Komponenten**: Hätte zu viel Refactoring erfordert

## Implementierung
Datei: `src/modules/dashboard/components/Calendar/BigDatePicker.tsx`  
Tests: `src/modules/dashboard/components/Calendar/__tests__/BigDatePicker.test.tsx`

---
*Architekturentscheidung nach Clean Architecture & Single Responsibility Prinzipien*
