import { Request, Response } from "express";
import { UserRepository, UserSettings } from "../repositories/user.repository";
import multer from "multer";
import path from "path";
import fs from "fs";

export class UserController {
  private repository: UserRepository;

  constructor() {
    this.repository = new UserRepository();
  }

  // Configure multer for avatar uploads
  private avatarStorage = multer.diskStorage({
    destination: (req, file, cb) => {
      const uploadDir = path.join(process.cwd(), 'uploads', 'avatars');
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
      cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      cb(null, 'avatar-' + uniqueSuffix + path.extname(file.originalname));
    }
  });

  private avatarUpload = multer({
    storage: this.avatarStorage,
    limits: { fileSize: 2 * 1024 * 1024 }, // 2MB
    fileFilter: (req, file, cb) => {
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(new Error('Nur Bilddateien sind erlaubt'));
      }
    }
  });


  /**
   * Find user by email or username
   * GET /api/user/search?email=<EMAIL>&username=testuser
   */
  async findByEmailOrUsername(req: Request, res: Response) {
    try {
      const { email, username } = req.query;

      if (!email && !username) {
        return res.status(400).json({
          success: false,
          error: 'E-Mail oder Benutzername ist erforderlich'
        });
      }

      const user = await this.repository.findByEmailOrUsername(
        email as string,
        username as string
      );

      res.json({
        success: true,
        data: user,
        found: !!user
      });
    } catch (error) {
      console.error('Error finding user by email or username:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Suchen des Benutzers',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Find user by email
   * GET /api/user/email/:email
   */
  async findByEmail(req: Request, res: Response) {
    try {
      const { email } = req.params;

      if (!email) {
        return res.status(400).json({
          success: false,
          error: 'E-Mail ist erforderlich'
        });
      }

      const user = await this.repository.findByEmail(email);

      res.json({
        success: true,
        data: user,
        found: !!user
      });
    } catch (error) {
      console.error('Error finding user by email:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Suchen des Benutzers nach E-Mail',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Find user by username
   * GET /api/user/username/:username
   */
  async findByUsername(req: Request, res: Response) {
    try {
      const { username } = req.params;

      if (!username) {
        return res.status(400).json({
          success: false,
          error: 'Benutzername ist erforderlich'
        });
      }

      const user = await this.repository.findByUsername(username);

      res.json({
        success: true,
        data: user,
        found: !!user
      });
    } catch (error) {
      console.error('Error finding user by username:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Suchen des Benutzers nach Benutzername',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Find user by ID
   * GET /api/user/:id
   */
  async findById(req: Request, res: Response) {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          error: 'Gültige Benutzer-ID ist erforderlich'
        });
      }

      const user = await this.repository.findById(parseInt(id));

      res.json({
        success: true,
        data: user,
        found: !!user
      });
    } catch (error) {
      console.error('Error finding user by ID:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Suchen des Benutzers nach ID',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Create new user
   * POST /api/user
   */
  async createUser(req: Request, res: Response) {
    try {
      const { email, username, firstName, lastName, passwordHash } = req.body;

      if (!email || !username || !passwordHash) {
        return res.status(400).json({
          success: false,
          error: 'E-Mail, Benutzername und Passwort-Hash sind erforderlich'
        });
      }

      const userData = {
        email,
        username,
        firstName,
        lastName,
        passwordHash
      };

      const user = await this.repository.createUser(userData);

      res.status(201).json({
        success: true,
        data: user,
        message: 'Benutzer erfolgreich erstellt'
      });
    } catch (error) {
      console.error('Error creating user:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Erstellen des Benutzers',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get all users (basic implementation)
   * GET /api/user/all
   */
  async getAllUsers(req: Request, res: Response) {
    try {
      // For now, return a message that this endpoint is not fully implemented
      // In a real implementation, you would need to add pagination support to the repository
      res.status(501).json({
        success: false,
        error: 'Endpoint noch nicht vollständig implementiert',
        message: 'Pagination für User-Repository muss noch implementiert werden'
      });
    } catch (error) {
      console.error('Error fetching all users:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen aller Benutzer',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get users by role (basic implementation)
   * GET /api/user/role/:roleName
   */
  async getUsersByRole(req: Request, res: Response) {
    try {
      const { roleName } = req.params;

      if (!roleName) {
        return res.status(400).json({
          success: false,
          error: 'Rollenname ist erforderlich'
        });
      }

      // For now, return a message that this endpoint is not fully implemented
      // In a real implementation, you would need to add role filtering to the repository
      res.status(501).json({
        success: false,
        error: 'Endpoint noch nicht vollständig implementiert',
        message: 'Role-Filtering für User-Repository muss noch implementiert werden'
      });
    } catch (error) {
      console.error('Error fetching users by role:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Benutzer nach Rolle',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get current user profile
   * GET /api/user/profile
   */
  async getProfile(req: Request, res: Response) {
    try {
      // Get user ID from JWT token (assuming middleware sets req.user)
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Nicht authentifiziert'
        });
      }

      const userData = await this.repository.findById(userId);

      if (!userData) {
        return res.status(404).json({
          success: false,
          error: 'Benutzer nicht gefunden'
        });
      }

      // Kombiniere firstName und lastName zu einem vollständigen Namen
      const fullName = userData.firstName && userData.lastName
        ? `${userData.firstName} ${userData.lastName}`
        : userData.firstName || userData.lastName || userData.username;

      res.json({
        success: true,
        data: {
          id: userData.id,
          username: userData.username,
          email: userData.email,
          name: fullName,
          avatar: userData.avatar,
          roles: [userData.role],
          updatedAt: userData.updatedAt
        }
      });
    } catch (error) {
      console.error('Error getting user profile:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Laden des Benutzerprofils',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Update current user profile
   * PUT /api/user/profile
   */
  async updateProfile(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Nicht authentifiziert'
        });
      }

      const { username, email, name, bio } = req.body;

      // Split name into firstName and lastName if provided
      let firstName, lastName;
      if (name) {
        const nameParts = name.split(' ');
        firstName = nameParts[0];
        lastName = nameParts.slice(1).join(' ');
      }

      const profileData = {
        username,
        email,
        firstName,
        lastName,
        bio
      };

      const updatedUser = await this.repository.updateUserProfile(userId, profileData);

      if (!updatedUser) {
        return res.status(404).json({
          success: false,
          error: 'Benutzer nicht gefunden'
        });
      }

      // Kombiniere firstName und lastName zu einem vollständigen Namen
      const fullName = updatedUser.firstName && updatedUser.lastName
        ? `${updatedUser.firstName} ${updatedUser.lastName}`
        : updatedUser.firstName || updatedUser.lastName || updatedUser.username;

      res.json({
        success: true,
        message: 'Profil erfolgreich aktualisiert',
        data: {
          id: updatedUser.id,
          username: updatedUser.username,
          email: updatedUser.email,
          name: fullName,
          bio: updatedUser.bio,
          avatar: updatedUser.avatar,
          roles: [updatedUser.role],
          updatedAt: updatedUser.updatedAt
        }
      });
    } catch (error) {
      console.error('Error updating user profile:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Aktualisieren des Benutzerprofils',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get current user settings
   * GET /api/user/settings
   */
  async getSettings(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Nicht authentifiziert'
        });
      }

      const settings = await this.repository.getUserSettings(userId);

      res.json({
        success: true,
        data: settings
      });
    } catch (error) {
      console.error('Error getting user settings:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Laden der Benutzereinstellungen',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Update current user settings
   * PUT /api/user/settings
   */
  async updateSettings(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Nicht authentifiziert'
        });
      }

      const settings: UserSettings = req.body;

      const updatedUser = await this.repository.updateUserSettings(userId, settings);

      if (!updatedUser) {
        return res.status(404).json({
          success: false,
          error: 'Benutzer nicht gefunden'
        });
      }

      res.json({
        success: true,
        message: 'Einstellungen erfolgreich aktualisiert',
        data: {
          bio: updatedUser.bio,
          fontSize: updatedUser.fontSize,
          highContrast: updatedUser.highContrast,
          animations: updatedUser.animations,
          compactMode: updatedUser.compactMode,
          theme: updatedUser.theme,
          language: updatedUser.language,
          notifications: updatedUser.notifications,
          preferences: updatedUser.preferences
        }
      });
    } catch (error) {
      console.error('Error updating user settings:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Aktualisieren der Benutzereinstellungen',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Upload avatar for current user
   * POST /api/user/avatar
   */
  async uploadAvatar(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Nicht authentifiziert'
        });
      }

      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: 'Keine Datei hochgeladen'
        });
      }

      const avatarPath = `/uploads/avatars/${req.file.filename}`;

      const updatedUser = await this.repository.updateUserProfile(userId, {
        avatar: avatarPath
      });

      if (!updatedUser) {
        return res.status(404).json({
          success: false,
          error: 'Benutzer nicht gefunden'
        });
      }

      res.json({
        success: true,
        message: 'Avatar erfolgreich hochgeladen',
        data: { avatar: avatarPath }
      });
    } catch (error) {
      console.error('Error uploading avatar:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Hochladen des Avatars',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  // Export avatar upload middleware for use in routes (keeping for compatibility)
  getAvatarUploadMiddleware() {
    return this.avatarUpload;
  }

  /**
   * Delete avatar for current user
   * DELETE /api/user/avatar
   */
  async deleteAvatar(req: Request, res: Response) {
    try {
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Nicht authentifiziert'
        });
      }

      const updatedUser = await this.repository.updateUserProfile(userId, {
        avatar: null
      });

      if (!updatedUser) {
        return res.status(404).json({
          success: false,
          error: 'Benutzer nicht gefunden'
        });
      }

      res.json({
        success: true,
        message: 'Avatar erfolgreich entfernt'
      });
    } catch (error) {
      console.error('Error deleting avatar:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Entfernen des Avatars',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }
}