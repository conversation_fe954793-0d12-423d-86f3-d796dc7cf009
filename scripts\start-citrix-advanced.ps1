# Advanced Citrix Startup Script für Lapp Dashboard
# Optimiert für Enterprise Citrix-Umgebungen mit erweiterten Diagnose-Features

param(
    [string]$ConfigPath = "",
    [switch]$Verbose,
    [switch]$DiagnoseMode,
    [switch]$ForceRepair,
    [string]$LogLevel = "INFO"
)

# Konfiguration
$script:AppName = "LappDashboard"
$script:AppDisplayName = "JOZI1 Lapp Dashboard - Citrix Edition"
$script:RequiredRAM = 2048  # MB
$script:RecommendedRAM = 4096  # MB
$script:MaxRetries = 3
$script:RetryDelay = 5  # Sekunden

# Logging-Konfiguration
$script:LogFile = Join-Path $PSScriptRoot "citrix-startup.log"
$script:DiagnosticLogFile = Join-Path $PSScriptRoot "citrix-diagnostics.log"

function Write-EnhancedLog {
    param(
        [string]$Message, 
        [ValidateSet("INFO", "WARN", "ERROR", "SUCCE<PERSON>", "DEBUG")]
        [string]$Level = "INFO",
        [switch]$ConsoleOnly
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # Farben für Console-Output
    $color = switch ($Level) {
        "SUCCESS" { "Green" }
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "DEBUG" { "Gray" }
        default { "White" }
    }
    
    Write-Host $logMessage -ForegroundColor $color
    
    # File-Logging (außer bei ConsoleOnly)
    if (-not $ConsoleOnly) {
        if ($Verbose -or $Level -ne "DEBUG") {
            Add-Content -Path $script:LogFile -Value $logMessage -ErrorAction SilentlyContinue
        }
        
        # Zusätzliches Diagnostic-Logging
        if ($DiagnoseMode) {
            Add-Content -Path $script:DiagnosticLogFile -Value $logMessage -ErrorAction SilentlyContinue
        }
    }
}

function Test-CitrixEnvironment {
    Write-EnhancedLog "Analyzing Citrix environment..." "INFO"
    
    $citrixInfo = @{
        IsCitrix = $false
        SessionId = $null
        ClientName = $null
        ClientPlatform = $null
        SessionType = $null
        AvailableMemory = $null
        NetworkLatency = $null
        DisplayInfo = @{}
    }
    
    # Citrix-Erkennung
    try {
        # Method 1: Umgebungsvariablen
        if ($env:CLIENTNAME -or $env:SESSIONNAME -like "*ICA*" -or $env:XDG_SESSION_TYPE -eq "x11") {
            $citrixInfo.IsCitrix = $true
            $citrixInfo.ClientName = $env:CLIENTNAME
            $citrixInfo.SessionId = $env:SESSIONNAME
        }
        
        # Method 2: Prozess-basierte Erkennung
        $citrixProcesses = @("wfshell", "picadm", "concentr", "ctxsession", "winlogon")
        foreach ($proc in $citrixProcesses) {
            if (Get-Process -Name $proc -ErrorAction SilentlyContinue) {
                $citrixInfo.IsCitrix = $true
                break
            }
        }
        
        # Method 3: Registry-Check (Windows)
        try {
            $citrixKey = Get-ItemProperty -Path "HKLM:\SOFTWARE\Citrix\ICA Client" -ErrorAction SilentlyContinue
            if ($citrixKey) {
                $citrixInfo.IsCitrix = $true
            }
        } catch { }
        
    } catch {
        Write-EnhancedLog "Warning during Citrix detection: $($_.Exception.Message)" "WARN"
    }
    
    # Speicher-Analyse
    try {
        $memory = Get-WmiObject -Class Win32_ComputerSystem -ErrorAction SilentlyContinue
        if ($memory) {
            $totalRAM = [math]::Round($memory.TotalPhysicalMemory / 1MB, 0)
            $citrixInfo.AvailableMemory = $totalRAM
            
            Write-EnhancedLog "Available RAM: ${totalRAM}MB" "INFO"
            
            if ($totalRAM -lt $script:RequiredRAM) {
                Write-EnhancedLog "WARNING: Available RAM (${totalRAM}MB) is below minimum requirement (${script:RequiredRAM}MB)" "WARN"
            } elseif ($totalRAM -lt $script:RecommendedRAM) {
                Write-EnhancedLog "INFO: Available RAM (${totalRAM}MB) is below recommended (${script:RecommendedRAM}MB)" "WARN"
            } else {
                Write-EnhancedLog "RAM allocation looks good" "SUCCESS"
            }
        }
    } catch {
        Write-EnhancedLog "Could not determine available memory: $($_.Exception.Message)" "WARN"
    }
    
    # Display-Information
    try {
        $displays = Get-WmiObject -Class Win32_VideoController -ErrorAction SilentlyContinue
        if ($displays) {
            foreach ($display in $displays) {
                $citrixInfo.DisplayInfo[$display.Name] = @{
                    DriverVersion = $display.DriverVersion
                    VideoMemory = $display.AdapterRAM
                    CurrentResolution = "$($display.CurrentHorizontalResolution)x$($display.CurrentVerticalResolution)"
                }
            }
        }
    } catch { }
    
    Write-EnhancedLog "Citrix Environment Analysis Complete" "SUCCESS"
    Write-EnhancedLog "Is Citrix: $($citrixInfo.IsCitrix)" "INFO"
    Write-EnhancedLog "Client Name: $($citrixInfo.ClientName)" "DEBUG"
    Write-EnhancedLog "Session ID: $($citrixInfo.SessionId)" "DEBUG"
    
    return $citrixInfo
}

function Get-OptimalStartupParameters {
    param($CitrixInfo, $PortableConfig)
    
    Write-EnhancedLog "Determining optimal startup parameters..." "INFO"
    
    $params = @()
    
    # Citrix-spezifische Parameter
    if ($CitrixInfo.IsCitrix) {
        Write-EnhancedLog "Applying Citrix optimizations..." "INFO"
        
        # Basis Citrix-Optimierungen
        $params += @(
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor,UseSurfaceLayerForVideo",
            "--no-sandbox",
            "--disable-software-rasterizer",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-extensions",
            "--disable-plugins-discovery"
        )
        
        # Memory-basierte Optimierungen
        if ($CitrixInfo.AvailableMemory -lt $script:RecommendedRAM) {
            Write-EnhancedLog "Applying low-memory optimizations..." "INFO"
            $params += @(
                "--memory-pressure-off",
                "--max_old_space_size=1024",
                "--disable-background-networking",
                "--aggressive"
            )
        }
        
        # GPU-Optimierungen für Citrix
        $params += @(
            "--disable-gpu-sandbox",
            "--enable-gpu-rasterization=false",
            "--disable-accelerated-2d-canvas"
        )
    }
    
    # Portable Config-basierte Parameter
    if ($PortableConfig -and $PortableConfig.citrix) {
        Write-EnhancedLog "Applying portable config Citrix settings..." "INFO"
        
        if ($PortableConfig.citrix.disableGpu) {
            $params += "--disable-gpu"
        }
        
        if ($PortableConfig.citrix.memoryLimit) {
            $params += "--max_old_space_size=$($PortableConfig.citrix.memoryLimit)"
        }
    }
    
    # Netzwerk-Optimierungen
    $params += @(
        "--aggressive-cache-discard",
        "--enable-features=NetworkService",
        "--disable-dev-shm-usage"
    )
    
    Write-EnhancedLog "Startup parameters configured: $($params.Count) parameters" "SUCCESS"
    Write-EnhancedLog "Parameters: $($params -join ' ')" "DEBUG"
    
    return $params
}

function Test-ApplicationHealth {
    param($AppPath)
    
    Write-EnhancedLog "Performing application health check..." "INFO"
    
    $healthStatus = @{
        IsHealthy = $true
        Issues = @()
        Warnings = @()
    }
    
    # Executable vorhanden?
    if (-not (Test-Path $AppPath)) {
        $healthStatus.IsHealthy = $false
        $healthStatus.Issues += "Application executable not found: $AppPath"
        return $healthStatus
    }
    
    # Backend verfügbar?
    $backendPath = Join-Path (Split-Path $AppPath -Parent) "resources\app\backend\dist\server.js"
    if (-not (Test-Path $backendPath)) {
        $healthStatus.Warnings += "Backend server not found at expected location: $backendPath"
    } else {
        Write-EnhancedLog "Backend server found: $backendPath" "SUCCESS"
    }
    
    # Portable Config verfügbar?
    $configPath = Join-Path (Split-Path $AppPath -Parent) "portable-config.json"
    if (-not (Test-Path $configPath)) {
        $healthStatus.Warnings += "Portable config not found: $configPath"
    } else {
        Write-EnhancedLog "Portable config found: $configPath" "SUCCESS"
    }
    
    # Schreibrechte im App-Verzeichnis?
    try {
        $testFile = Join-Path (Split-Path $AppPath -Parent) "write-test-$(Get-Random).tmp"
        "test" | Out-File -FilePath $testFile -ErrorAction Stop
        Remove-Item $testFile -ErrorAction SilentlyContinue
        Write-EnhancedLog "Write permissions verified" "SUCCESS"
    } catch {
        $healthStatus.Warnings += "Limited write permissions in application directory"
    }
    
    Write-EnhancedLog "Health check completed. Healthy: $($healthStatus.IsHealthy)" "INFO"
    return $healthStatus
}

function Start-ApplicationWithRetry {
    param($AppPath, $Parameters, $CitrixInfo)
    
    Write-EnhancedLog "Starting application with retry logic..." "INFO"
    
    for ($attempt = 1; $attempt -le $script:MaxRetries; $attempt++) {
        Write-EnhancedLog "Launch attempt $attempt of $script:MaxRetries" "INFO"
        
        try {
            # Prozess-Start mit erweiterten Optionen
            $processArgs = @{
                FilePath = $AppPath
                ArgumentList = $Parameters
                WorkingDirectory = Split-Path $AppPath -Parent
                PassThru = $true
                ErrorAction = "Stop"
            }
            
            # Citrix-spezifische Umgebungsvariablen
            if ($CitrixInfo.IsCitrix) {
                $env:ELECTRON_DISABLE_SANDBOX = "1"
                $env:ELECTRON_NO_ATTACH_CONSOLE = "1"
                $env:CITRIX_OPTIMIZED = "1"
            }
            
            $process = Start-Process @processArgs
            
            # Kurz warten um zu prüfen ob der Prozess direkt abstürzt
            Start-Sleep -Seconds 3
            
            if (-not $process.HasExited) {
                Write-EnhancedLog "Application started successfully (PID: $($process.Id))" "SUCCESS"
                
                # Extended monitoring für Citrix
                if ($CitrixInfo.IsCitrix -and $DiagnoseMode) {
                    Start-ProcessMonitoring -ProcessId $process.Id
                }
                
                return $true
            } else {
                Write-EnhancedLog "Process exited immediately (Exit Code: $($process.ExitCode))" "ERROR"
            }
            
        } catch {
            Write-EnhancedLog "Launch attempt $attempt failed: $($_.Exception.Message)" "ERROR"
        }
        
        if ($attempt -lt $script:MaxRetries) {
            Write-EnhancedLog "Waiting $script:RetryDelay seconds before retry..." "INFO"
            Start-Sleep -Seconds $script:RetryDelay
        }
    }
    
    return $false
}

function Start-ProcessMonitoring {
    param($ProcessId)
    
    Write-EnhancedLog "Starting extended process monitoring for PID $ProcessId..." "DEBUG"
    
    # Background-Job für Monitoring
    $monitoringJob = Start-Job -ScriptBlock {
        param($pid, $logFile)
        
        $startTime = Get-Date
        $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
        
        while ($process -and -not $process.HasExited) {
            $runtime = (Get-Date) - $startTime
            $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
            $cpuPercent = [math]::Round($process.CPU, 2)
            
            $status = "[$($runtime.ToString('hh\:mm\:ss'))] PID: $pid, Memory: ${memoryMB}MB, CPU: ${cpuPercent}s"
            Add-Content -Path $logFile -Value $status
            
            Start-Sleep -Seconds 30
            $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
        }
        
        if ($process -and $process.HasExited) {
            Add-Content -Path $logFile -Value "Process $pid exited with code: $($process.ExitCode)"
        }
    } -ArgumentList $ProcessId, $script:DiagnosticLogFile
    
    Write-EnhancedLog "Process monitoring started as background job" "DEBUG"
}

function Repair-CommonIssues {
    Write-EnhancedLog "Attempting to repair common issues..." "INFO"
    
    $repairActions = @()
    
    # Temp-Verzeichnis bereinigen
    try {
        $tempDir = Join-Path $env:TEMP "LappDashboard*"
        $tempFiles = Get-ChildItem $tempDir -ErrorAction SilentlyContinue
        if ($tempFiles) {
            $tempFiles | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
            $repairActions += "Cleared temporary files"
        }
    } catch { }
    
    # Registry-Einträge für Citrix zurücksetzen (falls erforderlich)
    try {
        $userRegPath = "HKCU:\SOFTWARE\LappDashboard"
        if (Test-Path $userRegPath) {
            Remove-Item $userRegPath -Recurse -Force -ErrorAction SilentlyContinue
            $repairActions += "Reset user registry settings"
        }
    } catch { }
    
    # Neustart des Audio-Services (häufiges Citrix-Problem)
    try {
        Restart-Service -Name "AudioSrv" -Force -ErrorAction SilentlyContinue
        $repairActions += "Restarted Audio service"
    } catch { }
    
    if ($repairActions.Count -gt 0) {
        Write-EnhancedLog "Repair actions completed: $($repairActions -join ', ')" "SUCCESS"
    } else {
        Write-EnhancedLog "No repair actions were needed" "INFO"
    }
}

function Show-DiagnosticSummary {
    param($CitrixInfo, $HealthStatus, $StartupSuccess)
    
    Write-EnhancedLog "=== DIAGNOSTIC SUMMARY ===" "INFO"
    Write-EnhancedLog "Citrix Environment: $($CitrixInfo.IsCitrix)" "INFO"
    Write-EnhancedLog "Available Memory: $($CitrixInfo.AvailableMemory)MB" "INFO"
    Write-EnhancedLog "Application Health: $($HealthStatus.IsHealthy)" "INFO"
    Write-EnhancedLog "Startup Success: $StartupSuccess" "INFO"
    Write-EnhancedLog "Issues Found: $($HealthStatus.Issues.Count)" "INFO"
    Write-EnhancedLog "Warnings: $($HealthStatus.Warnings.Count)" "INFO"
    
    if ($HealthStatus.Issues.Count -gt 0) {
        Write-EnhancedLog "ISSUES:" "ERROR"
        foreach ($issue in $HealthStatus.Issues) {
            Write-EnhancedLog "  - $issue" "ERROR"
        }
    }
    
    if ($HealthStatus.Warnings.Count -gt 0) {
        Write-EnhancedLog "WARNINGS:" "WARN"
        foreach ($warning in $HealthStatus.Warnings) {
            Write-EnhancedLog "  - $warning" "WARN"
        }
    }
    
    Write-EnhancedLog "Log Files:" "INFO"
    Write-EnhancedLog "  - Main Log: $script:LogFile" "INFO"
    if ($DiagnoseMode) {
        Write-EnhancedLog "  - Diagnostic Log: $script:DiagnosticLogFile" "INFO"
    }
    Write-EnhancedLog "=== END SUMMARY ===" "INFO"
}

# === MAIN SCRIPT EXECUTION ===

try {
    Clear-Host
    Write-EnhancedLog "========================================" "INFO"
    Write-EnhancedLog "$script:AppDisplayName" "INFO"
    Write-EnhancedLog "Advanced Citrix Startup Script v2.0" "INFO"
    Write-EnhancedLog "========================================" "INFO"
    Write-EnhancedLog ""
    
    # Parameter-Validierung und Logging-Setup
    if ($Verbose) { Write-EnhancedLog "Verbose mode enabled" "INFO" }
    if ($DiagnoseMode) { Write-EnhancedLog "Diagnostic mode enabled" "INFO" }
    if ($ForceRepair) { Write-EnhancedLog "Force repair mode enabled" "WARN" }
    
    # App-Pfad ermitteln
    $appDir = Split-Path -Parent $PSScriptRoot
    $appExe = Join-Path $appDir "$script:AppName.exe"
    
    Write-EnhancedLog "Application Directory: $appDir" "DEBUG"
    Write-EnhancedLog "Application Executable: $appExe" "DEBUG"
    
    # Prüfe ob App bereits läuft
    $existingProcess = Get-Process -Name $script:AppName -ErrorAction SilentlyContinue
    if ($existingProcess) {
        Write-EnhancedLog "Application is already running (PID: $($existingProcess.Id))" "WARN"
        $choice = Read-Host "Kill existing process and restart? (y/N)"
        if ($choice.ToLower() -eq 'y') {
            $existingProcess | Stop-Process -Force
            Write-EnhancedLog "Existing process terminated" "INFO"
            Start-Sleep -Seconds 2
        } else {
            Write-EnhancedLog "Startup cancelled by user" "INFO"
            exit 0
        }
    }
    
    # Citrix-Umgebung analysieren
    $citrixInfo = Test-CitrixEnvironment
    
    # Portable Konfiguration laden
    $portableConfigPath = if ($ConfigPath -and (Test-Path $ConfigPath)) {
        $ConfigPath
    } else {
        Join-Path $appDir "portable-config.json"
    }
    
    $portableConfig = $null
    if (Test-Path $portableConfigPath) {
        try {
            $portableConfig = Get-Content $portableConfigPath | ConvertFrom-Json
            Write-EnhancedLog "Loaded portable configuration from: $portableConfigPath" "SUCCESS"
        } catch {
            Write-EnhancedLog "Failed to parse portable configuration: $($_.Exception.Message)" "ERROR"
        }
    } else {
        Write-EnhancedLog "No portable configuration found at: $portableConfigPath" "WARN"
    }
    
    # Application Health Check
    $healthStatus = Test-ApplicationHealth -AppPath $appExe
    
    # Force Repair wenn angefordert
    if ($ForceRepair -or -not $healthStatus.IsHealthy) {
        Repair-CommonIssues
    }
    
    # Optimale Startup-Parameter ermitteln
    $startupParams = Get-OptimalStartupParameters -CitrixInfo $citrixInfo -PortableConfig $portableConfig
    
    # Anwendung starten
    Write-EnhancedLog ""
    Write-EnhancedLog "Starting application..." "INFO"
    $startupSuccess = Start-ApplicationWithRetry -AppPath $appExe -Parameters $startupParams -CitrixInfo $citrixInfo
    
    # Ergebnis und Diagnostic Summary
    Write-EnhancedLog ""
    if ($startupSuccess) {
        Write-EnhancedLog "Application startup completed successfully!" "SUCCESS"
        Write-EnhancedLog ""
        Write-EnhancedLog "The application is now running in the background." "INFO"
        Write-EnhancedLog "Close the application window to exit, or use Task Manager if needed." "INFO"
        
        if ($citrixInfo.IsCitrix) {
            Write-EnhancedLog ""
            Write-EnhancedLog "Citrix-optimized settings are active." "SUCCESS"
            Write-EnhancedLog "For best performance, ensure sufficient memory allocation in Citrix policies." "INFO"
        }
    } else {
        Write-EnhancedLog "Application startup failed after $script:MaxRetries attempts!" "ERROR"
        Write-EnhancedLog ""
        Write-EnhancedLog "Please check the log files for detailed error information:" "ERROR"
        Write-EnhancedLog "- $script:LogFile" "ERROR"
        if ($DiagnoseMode) {
            Write-EnhancedLog "- $script:DiagnosticLogFile" "ERROR"
        }
    }
    
    # Diagnostic Summary falls angefordert
    if ($DiagnoseMode) {
        Write-EnhancedLog ""
        Show-DiagnosticSummary -CitrixInfo $citrixInfo -HealthStatus $healthStatus -StartupSuccess $startupSuccess
    }
    
} catch {
    Write-EnhancedLog "Unexpected error in startup script: $($_.Exception.Message)" "ERROR"
    Write-EnhancedLog "Stack Trace: $($_.ScriptStackTrace)" "ERROR"
    exit 1
} finally {
    Write-EnhancedLog ""
    if (-not $DiagnoseMode) {
        Write-EnhancedLog "Press any key to close this window..."
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
}