# Repositories

Das Repository-System der Lapp Leitstand Anwendung implementiert das Repository Pattern für eine saubere Datenabstraktion zwischen der Anwendungslogik und verschiedenen Datenquellen. Es bietet eine einheitliche Schnittstelle für den Zugriff auf Produktions-, Lager-, System- und Benutzerdaten.

## 📁 Dateistruktur

```
src/repositories/
├── index.ts                    # Zentrale Exportdatei und RepositoryManager
├── base.repository.ts          # Basis-Repository mit gemeinsamen Funktionen
├── warehouse.repository.ts     # Lager- und Warehouse-Daten
├── production.repository.ts    # Produktions- und Fertigungsdaten
├── system.repository.ts        # System-Monitoring und Performance
├── stoerungen.repository.ts    # Störungsmanagement
├── user.repository.ts          # Benutzerverwaltung und Authentifizierung
├── delivery.repository.ts      # Lieferungen und Logistik
└── supplier.repository.ts      # Lieferanten-Performance
```

## 🏗️ Architektur-Übersicht

### Repository Pattern
Das System implementiert das Repository Pattern mit folgenden Kernkomponenten:

- **IRepository Interface**: Definiert die grundlegenden CRUD-Operationen
- **BaseRepository**: Abstrakte Basisklasse mit gemeinsamen Funktionen
- **Domain-spezifische Repositories**: Spezialisierte Implementierungen für verschiedene Datenbereiche
- **RepositoryManager**: Singleton für konsistente Repository-Instanzen

### Caching-System
- Integriertes Cache-Management über `cacheManager`
- Automatische Cache-Invalidierung
- Konfigurierbare TTL-Werte
- Performance-Optimierung durch intelligentes Caching

## 📋 Detaillierte Dateibeschreibungen

### `index.ts` - Repository Manager
**Zweck**: Zentrale Koordination aller Repository-Instanzen

**Hauptfunktionen**:
- `RepositoryManager` (Singleton-Pattern)
- Instanzen für Warehouse, Production und System Repositories
- Hilfsfunktionen für Cache-Invalidierung
- Batch-Operationen über mehrere Repositories

**Verwendung**:
```typescript
import { repositoryManager } from '@/repositories';

const warehouseData = await repositoryManager.warehouse.getAll();
const productionStats = await repositoryManager.production.getEfficiencyStats();
```

### `base.repository.ts` - Basis-Repository
**Zweck**: Gemeinsame Funktionalität für alle Repositories

**Interfaces**:
- `IRepository<T, TFilter>`: Basis-Interface mit CRUD-Operationen
- `DateRangeFilter`: Filter für datumbasierte Abfragen
- `PaginationFilter`: Paginierung-Unterstützung
- `BaseFilter`: Kombiniertes Filter-Interface

**Funktionen**:
- Cache-Management (`invalidateCache()`)
- Datum-Parsing (`parseDate()`)
- Fehlerbehandlung und Logging
- Basis-CRUD-Operationen

### `warehouse.repository.ts` - Lager-Daten
**Zweck**: Verwaltung von Lager- und Warehouse-Daten

**Repository-Klassen**:
- `AtrlRepository`: ATrL Lagerdaten
- `ArilRepository`: ARiL Lagerdaten  
- `WERepository`: Wareneingang-Daten
- `WarehouseRepository`: Übergeordnete Lager-Verwaltung

**Hauptfunktionen**:
- Lagerauslastung und Kapazitätsdaten
- Bestandsübersichten
- Wareneingang-Statistiken
- Gruppierung nach Zeiträumen
- Performance-Metriken

**Beispiel**:
```typescript
const atrlRepo = new AtrlRepository();
const auslastung = await atrlRepo.getCapacityData({ days: 30 });
const trends = await atrlRepo.getCapacityTrends();
```

### `production.repository.ts` - Produktions-Daten
**Zweck**: Verwaltung von Produktions- und Fertigungsdaten

**Repository-Klassen**:
- `SchnitteRepository`: Schnitt-Daten und Analysen
- `MaschinenEfficiencyRepository`: Maschinen-Effizienz
- `AblaengereiRepository`: Ablaengerei-Daten
- `ProductionRepository`: Übergeordnete Produktions-Verwaltung

**Hauptfunktionen**:
- Produktionsleistung und KPIs
- Maschinen-Effizienz-Analysen
- Schnitt-Performance
- Trend-Berechnungen
- Gruppierung nach Schichten/Zeiträumen

### `system.repository.ts` - System-Monitoring
**Zweck**: System-Performance und Monitoring-Daten

**Repository-Klassen**:
- `ServiceLevelRepository`: Service-Level-Daten
- `DailyPerformanceRepository`: Tägliche Performance
- `SystemRepository`: Übergeordnete System-Verwaltung

**Hauptfunktionen**:
- Service-Level-Monitoring
- Performance-Metriken
- System-Gesundheitschecks
- Verfügbarkeits-Statistiken

### `stoerungen.repository.ts` - Störungsmanagement
**Zweck**: Verwaltung von Störungen und Incidents

**Funktionen**:
- CRUD-Operationen für Störungen
- Status-Verwaltung (offen, in Bearbeitung, geschlossen)
- Kategorisierung und Priorisierung
- In-Memory-Cache für aktive Störungen
- Eskalations-Management

### `user.repository.ts` - Benutzerverwaltung
**Zweck**: Benutzer- und Rollen-Management

**Funktionen**:
- Benutzer-Authentifizierung
- Rollen-basierte Zugriffskontrolle
- Benutzer-CRUD-Operationen
- Join-Operationen für Benutzer-Rollen
- Passwort-Hash-Verwaltung

### `delivery.repository.ts` - Lieferungen
**Zweck**: Lieferungs- und Logistik-Daten

**Funktionen**:
- Lieferhistorie und -tracking
- Performance-Metriken (Pünktlichkeit, Kosten)
- Route-Optimierung
- Lieferanten-Performance
- Verzögerungs-Analysen

### `supplier.repository.ts` - Lieferanten
**Zweck**: Lieferanten-Performance und -Management

**Funktionen**:
- Lieferanten-Bewertungen
- Performance-Tracking
- Risiko-Bewertung
- Kosten-Analysen
- Trend-Analysen

## 🔧 Technische Details

### Abhängigkeiten
- **Drizzle ORM**: Datenbankabstraktion
- **API Service**: HTTP-Client für Backend-Kommunikation
- **Cache Manager**: Intelligentes Caching-System
- **TypeScript**: Vollständige Typisierung

### Filter-System
```typescript
// Basis-Filter
interface DateRangeFilter {
  startDate?: string;
  endDate?: string;
}

// Erweiterte Filter
interface ProductionFilter extends DateRangeFilter {
  machineId?: string;
  productionType?: 'cutting' | 'ablängerei' | 'efficiency';
  minEfficiency?: number;
}
```

### Cache-Management
```typescript
// Cache invalidieren
repository.invalidateCache();

// Cache-Status prüfen
const stats = await repository.getCacheStats();
```

## 📊 Datenmodelle

### Warehouse-Daten
```typescript
interface AtrlDataPoint {
  datum: Date;
  auslastung: number;
  kapazitaet: number;
  verfuegbar: number;
}
```

### Produktions-Daten
```typescript
interface SchnitteDataPoint {
  datum: Date;
  anzahl_schnitte: number;
  effizienz: number;
  maschinenzeit: number;
}
```

### System-Daten
```typescript
interface ServiceLevelDataPoint {
  datum: Date;
  servicegrad: number;
  verfuegbarkeit: number;
  response_time: number;
}
```

## 🧪 Testing

### Unit Tests
```bash
# Alle Repository-Tests ausführen
pnpm test src/repositories

# Spezifische Repository-Tests
pnpm test src/repositories/warehouse.repository.test.ts
```

### Test-Struktur
- Mock-Implementierungen für API-Services
- In-Memory-Cache für Tests
- Fixture-Daten für konsistente Tests
- Integration-Tests mit echter Datenbank

## 🚀 Verwendungsbeispiele

### Basis-Verwendung
```typescript
import { repositoryManager } from '@/repositories';

// Warehouse-Daten abrufen
const warehouseData = await repositoryManager.warehouse.getAll({
  startDate: '2024-01-01',
  endDate: '2024-01-31'
});

// Produktions-Effizienz
const efficiency = await repositoryManager.production.getEfficiencyStats({
  machineId: 'M001',
  days: 7
});
```

### Erweiterte Abfragen
```typescript
// Kombinierte Datenabfrage
const [warehouse, production, system] = await Promise.all([
  repositoryManager.warehouse.getCapacityTrends(),
  repositoryManager.production.getPerformanceMetrics(),
  repositoryManager.system.getServiceLevelData()
]);

// Cache-optimierte Abfrage
const cachedData = await repositoryManager.warehouse.getAll();
// Zweite Abfrage nutzt Cache
const sameCachedData = await repositoryManager.warehouse.getAll();
```

### Fehlerbehandlung
```typescript
try {
  const data = await repositoryManager.production.getAll();
} catch (error) {
  console.error('Repository-Fehler:', error);
  // Fallback-Strategie
  const fallbackData = await repositoryManager.production.getCachedData();
}
```

## 🔍 Performance-Optimierung

### Caching-Strategien
- **Time-based Caching**: Automatische Invalidierung nach TTL
- **Event-based Caching**: Invalidierung bei Datenänderungen
- **Selective Caching**: Nur häufig abgefragte Daten cachen

### Batch-Operationen
```typescript
// Mehrere Repositories gleichzeitig invalidieren
await repositoryManager.invalidateAllCaches();

// Batch-Datenabfrage
const batchResults = await repositoryManager.getBatchData([
  'warehouse',
  'production',
  'system'
]);
```

## 🛠️ Entwicklung

### Neues Repository hinzufügen
1. Repository-Klasse erstellen (erweitert `BaseRepository`)
2. Domain-spezifische Interfaces definieren
3. In `RepositoryManager` registrieren
4. Tests implementieren
5. Dokumentation aktualisieren

### Best Practices
- Immer TypeScript-Interfaces verwenden
- Cache-Strategien dokumentieren
- Fehlerbehandlung implementieren
- Unit-Tests für alle öffentlichen Methoden
- Performance-Metriken überwachen

### Code-Konventionen
- Repository-Namen enden mit `Repository`
- Async/Await für alle Datenbankoperationen
- Konsistente Fehlerbehandlung
- Ausführliche JSDoc-Kommentare
- TypeScript strict mode

## 🔗 Integration

### Mit API-Service
```typescript
// Repository nutzt API-Service für Datenabfrage
const data = await apiService.getWarehouseData();
return this.transformData(data);
```

### Mit Cache-Manager
```typescript
// Automatisches Caching
const cacheKey = this.generateCacheKey(filter);
const cachedData = await cacheManager.get(cacheKey);
if (cachedData) return cachedData;
```

### Mit Frontend-Komponenten
```typescript
// React-Komponente nutzt Repository
const { data, loading, error } = useQuery(
  ['warehouse-data'],
  () => repositoryManager.warehouse.getAll()
);
```

---

## Wartung und Support

**Letzte Aktualisierung**: Januar 2025  
**Maintainer**: Lapp Development Team  
**Version**: 1.0.0  

**Hinweis**: Diese README beschreibt die aktuelle Implementierung des Repository-Systems. Bei Änderungen oder Erweiterungen sollte diese Dokumentation entsprechend aktualisiert werden.