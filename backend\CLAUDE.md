# Backend CLAUDE.md

Backend-specific guidance for <PERSON> when working with the Express.js API server.

## Development Commands

```bash
# Backend Development (PNPM ONLY!)
pnpm run dev                  # Start Express server on port 3001 with hot reload
pnpm start                    # Start backend in production mode

# Drizzle Database Management
pnpm run db:generate          # Generate Drizzle migrations
pnpm run db:push              # Push schema changes to database
pnpm run db:introspect        # Introspect existing database

# Testing
pnpm run test                 # Run Jest backend tests
pnpm run test:watch           # Run tests in watch mode
pnpm run test:coverage        # Run tests with coverage report

# API Testing
curl -X GET "http://localhost:3001/api/health"
curl -X GET "http://localhost:3001/api/performance/overview"
curl -X GET "http://localhost:3001/api/errors/stats"
curl -X GET "http://localhost:3001/api/paginated/alle-daten?limit=50"
curl -X GET "http://localhost:3001/api/database/system-fts-data?startDate=2014-05-01&endDate=2014-05-15"
curl -X GET "http://localhost:3001/api/stoerungen"
curl -X GET "http://localhost:3001/api/stoerungen/stats"
```

## Backend Architecture

### Tech Stack
- **Runtime**: Node.js with Express.js  
- **Package Manager**: pnpm (NO npm!)
- **Database**: SQLite with Drizzle ORM (NO Prisma!)
- **Language**: TypeScript
- **Testing**: Jest with SuperTest
- **Validation**: Zod schemas
- **Security**: Helmet.js, rate limiting, API authentication

### Directory Structure

```
backend/
├── src/
│   ├── server.ts              # Main Express server entry point
│   ├── controllers/           # Request handlers
│   ├── middleware/            # Express middleware (auth, rate limiting, validation)
│   ├── repositories/          # Repository Pattern implementation with caching
│   ├── routes/               # API route definitions
│   ├── services/             # Core business logic services
│   ├── types/                # TypeScript type definitions
│   └── utils/                # Utility functions
├── tests/                    # Comprehensive testing infrastructure
├── prisma/                   # Database schema and configuration
└── dist/                     # Compiled JavaScript output
```

### Repository Pattern Architecture

**Data Flow**:
```
API Routes → Repository Layer → Backend Cache → Database Service → Prisma → SQLite
```

**Key Repositories**:
- **DispatchRepository** (`repositories/dispatch.repository.ts`) - Versand/Shipping data
- **WarehouseRepository** (`repositories/warehouse.repository.ts`) - Lager data (200/240, ARiL, ATrL)
- **CuttingRepository** (`repositories/cutting.repository.ts`) - Ablängerei/Cutting operations
- **StoerungenRepository** (`repositories/stoerungen.repository.ts`) - Störungsmanagement und System-Status
- **Repository Factory** (`repositories/repository.factory.ts`) - Singleton management

### Core Services

#### Cache Service (`services/cache.service.ts`)
- **In-memory LRU cache** with intelligent memory management
- **Configurable TTL** per data type (15min-2h)
- **Memory limits**: 100MB max, 2000 entries max
- **Performance monitoring** with hit rates and eviction tracking
- **40-70% query performance improvement**

#### Database Service (`services/database.service.ts`)
- **Core data access layer** with Prisma integration
- **Connection management** and query optimization
- **Error handling** for database operations
- **Transaction support** for complex operations
- **System FTS data access** - `getSystemFTSData()` with date filtering and cache support

#### Performance Monitor (`services/performance-monitor.service.ts`)
- **24/7 system monitoring** with real-time metrics
- **Query performance tracking** with optimization recommendations
- **Memory usage analysis** and leak detection
- **Health scoring** with automated alerting

#### Error Handler (`services/error-handler.service.ts`)
- **Unified error categorization** with severity levels
- **User-friendly error messages** with technical details
- **Error trend analysis** and diagnostic recommendations
- **Graceful degradation** strategies

#### Pagination Service (`services/pagination.service.ts`)
- **Cursor-based pagination** for large datasets (1M+ records)
- **Offset-based pagination** for smaller datasets
- **Auto-strategy detection** based on data size
- **Performance comparison** and optimization guidance

### API Endpoints

#### Core Data Endpoints
```bash
# Database Operations
GET /api/database/alle-daten      # All production data
GET /api/database/ablaengerei     # Cutting operations
GET /api/database/dispatch        # Shipping data
GET /api/database/warehouse       # Warehouse operations
GET /api/database/aril            # Automated high-bay warehouse
GET /api/database/atrl            # Automated drum warehouse
GET /api/database/system-fts-data # System FTS availability data with date filtering

# Health & Status
GET /api/health                   # Basic health check
GET /api/health/detailed          # Detailed health with DB status
GET /api/metrics                  # System metrics
```

#### Performance Monitoring
```bash
GET /api/performance/overview     # Performance dashboard
GET /api/performance/cache        # Cache analytics
GET /api/performance/database     # Database performance
GET /api/performance/system       # System metrics
GET /api/performance/health       # Health check with scores
GET /api/performance/trends       # Performance trends
GET /api/performance/memory       # Memory analysis
```

#### Error Monitoring
```bash
GET /api/errors/stats             # Error statistics
GET /api/errors/history           # Error history with filtering
GET /api/errors/critical          # Critical errors (24h)
GET /api/errors/trends            # Error trends
GET /api/errors/diagnostics       # Error diagnostics
```

#### Pagination
```bash
GET /api/paginated/alle-daten     # Auto-detecting pagination
GET /api/paginated/alle-daten/cursor    # Cursor-based
GET /api/paginated/alle-daten/offset    # Offset-based
GET /api/paginated/ablaengerei    # Paginated cutting data
GET /api/paginated/performance-comparison # Strategy comparison
```

#### Störungsmanagement
```bash
GET /api/stoerungen                # Alle Störungen (mit Filteroptionen)
GET /api/stoerungen/stats          # Störungsstatistiken (MTTR, MTBF, etc.)
GET /api/stoerungen/active         # Aktive Störungen
GET /api/stoerungen/:id            # Einzelne Störung mit Kommentaren
POST /api/stoerungen               # Neue Störung erstellen
PUT /api/stoerungen/:id            # Störung aktualisieren (Status, MTTR)
DELETE /api/stoerungen/:id         # Störung löschen
POST /api/stoerungen/comments      # Kommentar zu Störung hinzufügen

# System Status Monitoring
GET /api/stoerungen/system/status  # Live-Systemstatus aller Systeme
POST /api/stoerungen/system/status # Systemstatus aktualisieren
```

### Environment Configuration

Required variables in `.env`:
```bash
# Database
DATABASE_URL="file:../database/sfm_dashboard.db"

# API Configuration  
API_PORT=3001
API_SECRET_KEY=sfm_api_[your-secret-key]
NODE_ENV=development

# AI Integration (Optional)
OPENROUTER_API_KEY=sk-or-v1-[your-key]
OPENROUTER_PRESET_MODEL=@preset/lapp

# Performance Monitoring
CACHE_MAX_MEMORY_MB=100
CACHE_MAX_ENTRIES=2000
PERFORMANCE_MONITORING_ENABLED=true
```

### Security Configuration

#### Authentication
- **API Key validation** in production mode
- **Development mode bypass** for easier testing
- **Request validation** with Zod schemas

#### Rate Limiting
- **Development**: 500 requests/15 minutes
- **Production**: 30 requests/15 minutes for data endpoints
- **Health endpoints**: Higher limits for monitoring

#### Security Headers
- **Helmet.js** with Electron-compatible CSP
- **CORS** with environment-specific origins
- **Input sanitization** and validation

### Database Schema (Prisma)

#### Key Tables
- **Cutting Operations**: `Ablaengerei`, `Schnitte`, `maschinen`
- **Warehouse Management**: `Bestand200`, `bestand240`, `bestandRest`, `auslastung200`, `auslastung240`
- **Logistics**: `WE` (Wareneingang), `ARiL`, `ATrL`, `ManL`
- **System Monitoring**: `System` (includes `verfuegbarkeitFTS` column), `DispatchData`
- **Störungsmanagement**: `Stoerungen`, `StoerungsComments`, `SystemStatus`
- **Aggregated Data**: `alle_daten` (comprehensive metrics)

#### Performance Optimizations
- **Date-based indexes** on all time-critical tables
- **Composite indexes** for complex queries
- **Query optimization** with proper filtering and ordering
- **Schema consistency** fixes (e.g., ARiL.Datum String vs DateTime)

### Testing Infrastructure

#### Test Organization
```
tests/
├── unit/                     # Unit tests for services/repositories
├── integration/              # API endpoint testing
├── fixtures/                 # Test fixtures (ONLY for tests, not production)
├── helpers/                  # Test utilities and database setup
└── mocks/                    # Service mocks for isolated testing
```

#### Test Configuration
- **Jest** with TypeScript support
- **SuperTest** for API testing
- **Test database** with automatic setup/teardown
- **Coverage reporting** with 80%+ target
- **Parallel execution** for faster test runs

#### Key Test Files
- `unit/repositories/*.test.ts` - Repository pattern tests
- `unit/services/*.test.ts` - Service layer tests
- `integration/api/*.test.ts` - API endpoint tests
- `helpers/test-database.ts` - Test database management
- `fixtures/mock-data.ts` - Realistic test data

### Development Workflow

#### Starting Development
1. **Install dependencies**: `pnpm install`
2. **Setup environment**: Copy `.env.example` to `.env`
3. **Generate Prisma client**: `npx prisma generate`
4. **Start development server**: `pnpm run dev`
5. **Verify health**: `curl http://localhost:3001/api/health`

#### Testing Workflow
1. **Run all tests**: `pnpm run test`
2. **Watch mode**: `pnpm run test:watch`
3. **Coverage report**: `pnpm run test:coverage`
4. **Integration only**: `pnpm run test:integration`

#### Database Development
1. **Schema changes**: Edit `prisma/schema.prisma`
2. **Generate client**: `npx prisma generate`
3. **Push to database**: `npx prisma db push`
4. **View data**: `npx prisma studio`

### Performance Monitoring

#### Cache Analytics
- **Hit rates**: Monitor cache effectiveness (target >80%)
- **Memory usage**: Track memory consumption and eviction patterns
- **TTL optimization**: Adjust cache expiration based on usage patterns
- **Hot keys**: Identify most frequently accessed data

#### Database Performance
- **Query analysis**: Monitor slow queries and optimization opportunities
- **Connection pooling**: Manage database connections efficiently
- **Index usage**: Verify indexes are being used effectively
- **Transaction performance**: Monitor transaction duration and conflicts

#### System Metrics
- **Memory usage**: Track Node.js heap and RSS memory
- **Event loop lag**: Monitor Node.js event loop performance
- **CPU usage**: Track CPU utilization patterns
- **Uptime**: Monitor service availability and restart patterns

### Error Handling

#### Error Categories
- **Database Errors**: Connection, query, transaction failures
- **Validation Errors**: Input validation and schema violations
- **Authentication Errors**: API key and authorization failures
- **System Errors**: Memory, CPU, and resource exhaustion
- **Application Errors**: Business logic and processing failures

#### Error Severity Levels
- **Critical**: System-threatening errors requiring immediate attention
- **High**: Significant functionality impact, needs urgent resolution
- **Medium**: Moderate impact, should be addressed soon
- **Low**: Minor issues, can be addressed during maintenance

#### Error Recovery
- **Graceful degradation**: Continue operation with reduced functionality
- **Automatic retry**: Retry failed operations with exponential backoff
- **Circuit breaker**: Prevent cascade failures in dependent services
- **Fallback strategies**: Alternative data sources and processing paths

### ⚠️ CRITICAL DEVELOPMENT RULES

#### **NEVER USE MOCK DATA IN BACKEND**
- **Mock data is STRICTLY FORBIDDEN** in all backend services and endpoints
- **Always implement real database queries** with Prisma
- **Create proper API endpoints** instead of returning fake data
- **Use test fixtures only in test files** - never in production code
- Mock data in backend creates integration issues and false API contracts

### Best Practices

#### Code Organization
- **Single responsibility**: Each service/repository has one clear purpose
- **Dependency injection**: Services receive dependencies via constructor
- **Interface segregation**: Use specific interfaces for different concerns
- **Error boundaries**: Handle errors at appropriate abstraction levels

#### Performance
- **Lazy loading**: Load data only when needed
- **Caching strategy**: Cache frequently accessed data with appropriate TTL
- **Query optimization**: Use database indexes and efficient query patterns
- **Memory management**: Monitor and prevent memory leaks

#### Security
- **Input validation**: Validate all inputs with Zod schemas
- **SQL injection prevention**: Use Prisma's type-safe query builder
- **Authentication**: Secure API endpoints with proper authentication
- **Rate limiting**: Prevent abuse with appropriate rate limits

#### Testing
- **Unit tests**: Test individual functions and classes in isolation
- **Integration tests**: Test API endpoints with real database operations
- **Mock external dependencies**: Use mocks for external services
- **Test data management**: Use fixtures for consistent test data

### Troubleshooting

#### Common Issues
- **Database connection errors**: Check SQLite file permissions and path
- **Rate limiting 429 errors**: Verify rate limit configuration for environment
- **Schema changes not applied**: Run `npx prisma generate` after schema updates
- **Test failures**: Verify test database setup and cleanup
- **Performance degradation**: Check cache hit rates and database query performance

#### Debugging
- **Enable debug logging**: Set `NODE_ENV=development` for detailed logs
- **Database queries**: Use Prisma's query logging for SQL debugging
- **Performance profiling**: Use performance monitoring endpoints
- **Memory analysis**: Monitor memory usage and garbage collection
- **Error tracking**: Check error monitoring endpoints for issue patterns

### AI Integration

#### OpenRouter Configuration
- **API Key**: Required for AI chatbot functionality
- **Model Selection**: Uses preset configuration for consistent responses
- **Context Integration**: Real-time dashboard data for contextual AI responses
- **Error Handling**: Graceful fallback when AI service is unavailable

#### Chatbot Endpoints
```bash
POST /api/ai/chat              # Send message to AI chatbot
GET /api/ai/models             # Available AI models
POST /api/ai/context           # Update AI context with dashboard data
```

## Recent Backend Achievements ✅

### **System FTS API Implementation**
- **New Endpoint**: `GET /api/database/system-fts-data` with date filtering
- **Database Service**: `getSystemFTSData()` method with Prisma integration
- **Cache Support**: 5-minute TTL for FTS availability data
- **Type Safety**: `SystemFTSDataPoint` interface for consistent data structure
- **Date Filtering**: Supports `startDate` and `endDate` query parameters
- **Data Source**: Queries `System` table `verfuegbarkeitFTS` column directly

### **API Architecture Improvements**
- **Mock Data Elimination**: All mock data removed from backend services
- **Real Database Integration**: All endpoints now use actual database queries
- **Error Handling**: Comprehensive error handling for all new endpoints
- **Cache Integration**: Proper cache key generation and TTL management

### **Störungsmanagement System Implementation** ✅
- **Complete API**: Full CRUD operations for Störungen with `/api/stoerungen/*` endpoints
- **Database Schema**: 3 new tables - `Stoerungen`, `StoerungsComments`, `SystemStatus`
- **Repository Pattern**: StoerungenRepository with simple in-memory caching (5-15min TTL)
- **Type Safety**: Complete TypeScript interfaces and Zod validation schemas
- **Features Implemented**:
  - ✅ Störung creation, update, deletion with status tracking (NEW → IN_PROGRESS → RESOLVED)
  - ✅ Comment system for störung discussions and updates
  - ✅ System status monitoring for live infrastructure oversight
  - ✅ Statistics API with MTTR, MTBF, resolution rates, and trend analysis
  - ✅ Advanced filtering by status, severity, category, affected_system
  - ✅ Automatic MTTR calculation when störungen are resolved

### **Database Integration** ✅
- **Schema Applied**: All Störungen tables created in main SQLite database (`/database/sfm_dashboard.db`)
- **Prisma Configuration**: Fixed database path from `file:../../database/` to `file:../database/` 
- **Indexes**: Performance-optimized indexes on `created_at`, `status`, `severity`, `affected_system`
- **Data Persistence**: Real database storage with proper relationship management