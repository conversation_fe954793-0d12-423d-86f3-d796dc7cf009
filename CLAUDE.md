# Leitstand App - CLAUDE.md

**🎯 JOZI1 Leitstand Application - Post-Migration Architecture Guide**

## 🚨 **CRITICAL MIGRATION RULES - NEVER FORGET!**

### Package Manager - ONLY pnpm
- **NUR pnpm verwenden** - NIEMALS npm oder yarn!
- **All commands with pnpm**: `pnpm run dev`, `pnpm install`, `pnpm add`, etc.
- **Workspace**: Uses pnpm-workspace.yaml for monorepo structure
- **No more**: package-lock.json files (only pnpm-lock.yaml)

### Database ORM - ONLY Drizzle  
- **NUR Drizzle ORM verwenden** - NIEMALS Prisma imports!
- **Main DB**: `import { db } from '@/db'` (SFM Dashboard SQLite)
- **RAG DB**: `import { ragDb } from '@/db/rag-db'` (Knowledge Base SQLite)
- **Schema**: `import { tableName } from '@/db/schema'`
- **Query syntax**: `db.select().from(table).where(...)`

---

## 📋 **APPLICATION ARCHITECTURE**

### 🖥️ **Frontend Stack (Electron + React)**
- **Framework**: Electron 35.7.2 with Vite 6.3.5 + TypeScript 5.8.3
- **UI Framework**: React 18.2.0 with React DOM
- **UI Components**: shadcn/ui with Radix UI primitives + Tailwind CSS 4.1.11
- **Routing**: TanStack Router v1.121.2 with hash-based navigation (`#/path`)
- **State Management**: TanStack Query v5.74.7 for server state
- **Icons**: Tabler Icons, Lucide React, Simple Icons
- **Animation**: Framer Motion 12.23.12
- **Development Server**: Vite on port 3000, proxies API to port 3001

### ⚡ **Backend Stack (Express API)**
- **Framework**: Express.js 5.1.0 with TypeScript
- **Database**: SQLite with Drizzle ORM 0.44.5
- **Authentication**: JWT + bcryptjs + API Keys
- **Security**: Helmet, CORS, Rate Limiting
- **File Upload**: Multer for störung attachments
- **Email**: Nodemailer integration
- **AI Integration**: OpenAI + OpenRouter services
- **Performance**: Caching, performance tracking middleware
- **Development Server**: Nodemon on port 3001

### 🗄️ **Database Architecture**
- **SFM Dashboard DB**: Main PostgreSQL database (41 tables, 573 columns, 127 indices)
  - Connection: PostgreSQL server (configurable host, port, credentials)
  - Tables: störungen, users, workflows, materialdaten, trommeldaten, etc.
- **RAG Knowledge DB**: Vector/embedding database (12 tables, 92 columns, 45 indices)  
  - Connection: PostgreSQL server (separate database or schema)
  - Purpose: AI knowledge base, document embeddings, RAG functionality
- **Migrations**: Drizzle Kit for schema generation and migration management

### 🏗️ **Project Structure**
```
/
├── src/                          # Frontend React application
│   ├── components/              # Reusable UI components (shadcn/ui based)
│   ├── modules/                 # Feature modules (modular architecture)
│   │   ├── ai/                 # AI functionality (RAG, cutting optimization)
│   │   ├── dashboard/          # KPI dashboards and analytics
│   │   ├── stoerungen/         # Incident management system
│   │   ├── backend/            # Backend monitoring and workflows
│   │   └── settings/           # Application settings
│   ├── db/                     # Drizzle database connection and schemas
│   ├── services/               # Frontend API services
│   ├── hooks/                  # Custom React hooks
│   ├── routes/                 # TanStack Router configuration
│   └── main.ts                 # Electron renderer entry point
├── backend/                     # Express.js API server
│   ├── src/
│   │   ├── controllers/        # API request handlers
│   │   ├── services/           # Business logic services
│   │   ├── repositories/       # Data access layer (Drizzle-based)
│   │   ├── routes/             # Express route definitions
│   │   ├── middleware/         # Express middleware (auth, security, etc.)
│   │   └── server.ts           # Express server entry point
│   └── database/               # SQLite database files
├── electron/                   # Electron main process
├── drizzle/                    # Database migrations and generated files
└── scripts/                    # Utility scripts (testing, migration, etc.)
```

---

## ⚙️ **DEVELOPMENT WORKFLOW**

### 🚀 **Starting Development**
```bash
# Full development (recommended) - starts both frontend and backend
pnpm run dev                     # Equivalent to pnpm run start:dev

# Individual components
pnpm run dev:app                 # Frontend only (Electron + Vite)
pnpm run dev:backend             # Backend only (Express server)
pnpm run dev:frontend            # Vite dev server only (for debugging)

# Backend preparation (installs backend dependencies)
pnpm run backend:prepare         # Runs automatically in dev mode
```

### 🗃️ **Database Operations (Drizzle)**
```bash
# Main SFM Dashboard database
pnpm run db:generate             # Generate migrations from schema changes
pnpm run db:push                 # Push schema changes to database
pnpm run db:introspect           # Introspect existing database structure

# RAG Knowledge database
pnpm run db:generate:rag         # Generate RAG database migrations
pnpm run db:push:rag             # Push RAG schema changes
pnpm run db:introspect:rag       # Introspect RAG database

# Testing and diagnostics
pnpm run db:test                 # Test database connections
pnpm run db:test:repository      # Test repository pattern
pnpm run db:test:service         # Test database services

# Native module management (WSL/platform issues)
pnpm run db:rebuild-native       # Rebuild better-sqlite3 for current platform
```

### 🧪 **Testing Commands**
```bash
# Unit tests
pnpm run test:unit               # Run Vitest unit tests
pnpm run test:watch              # Watch mode for tests

# E2E tests  
pnpm run test:e2e                # Playwright E2E tests
pnpm run test:all                # Run all tests (unit + e2e)

# AI module specific tests
pnpm run test:ai                 # Run AI module tests
pnpm run test:ai:comprehensive   # Comprehensive AI test suite
pnpm run test:ai:performance     # AI performance tests
pnpm run health:ai               # AI module health check
```

### 📦 **Build & Package Commands**
```bash
# Development builds
pnpm run build                   # Build application
pnpm run package                 # Create Electron package

# Production builds
pnpm run make                    # Create distributables
pnpm run build:portable          # Create portable Windows executable
```

### 🛠️ **Utility Commands**
```bash
# Route generation (TanStack Router)
pnpm run generate:routes         # Generate route tree

# AI deployment and validation
pnpm run deploy:ai               # Deploy AI module
pnpm run validate:ai:deployment  # Validate AI deployment
```

---

## 🔧 **CRITICAL CONFIGURATION FILES**

### 📁 **Database Configuration**
- **`drizzle.config.ts`** - Main database config (SFM Dashboard)
- **`drizzle.config.rag.ts`** - RAG database config (Knowledge Base)
- **`src/db/index.ts`** - Drizzle connection setup with SQLite pragmas
- **`src/db/schema.ts`** - Main database table definitions (41 tables)
- **`src/db/rag-schema.ts`** - RAG database schema (vector storage)

### ⚡ **Backend Configuration**
- **`backend/src/server.ts`** - Express server with all middleware and routes
- **`backend/src/services/database.service.ts`** - Core database service (Drizzle-based)
- **`backend/nodemon.json`** - Development server configuration

### 🎨 **Frontend Configuration**
- **`vite.config.ts`** - Vite configuration with Electron optimizations
- **`src/routes/router.tsx`** - TanStack Router setup with hash history
- **`src/main.ts`** - Electron renderer process entry point
- **`forge.config.ts`** - Electron Forge packaging configuration

### 📦 **Package Management**
- **`pnpm-workspace.yaml`** - Workspace configuration for monorepo
- **Root `package.json`** - Main dependencies and scripts
- **`backend/package.json`** - Backend-specific dependencies

---

## 🌍 **ENVIRONMENT SETUP**

### 📋 **Required Environment Variables**

**Backend (`.env` in `/backend/`):**
```bash
NODE_ENV=development
API_PORT=3001
API_SECRET_KEY=sfm_api_[your_secret]
# PostgreSQL Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=leitstand_dashboard
DB_SSL=false

# RAG Database (can be same or separate PostgreSQL instance)
RAG_DB_HOST=localhost
RAG_DB_PORT=5432
RAG_DB_USER=postgres
RAG_DB_PASSWORD=your_password
RAG_DB_NAME=leitstand_rag
RAG_DB_SSL=false

# Optional AI/OpenRouter configuration
OPENROUTER_API_KEY=your_openrouter_key
OPENAI_API_KEY=your_openai_key
```

### 🔌 **Port Configuration**
- **Frontend (Vite)**: 3000 (development only)
- **Backend (Express)**: 3001 (API server)  
- **Electron**: Uses Vite dev server or built files
- **API Proxy**: Vite proxies `/api/*` to `localhost:3001`

---

## 📊 **MODULE ARCHITECTURE**

### 🤖 **AI Module** (`src/modules/ai/`)
- **Components**: RAG chat, cutting optimization, inventory intelligence
- **Services**: OpenAI integration, embedding services, vector database
- **Features**: Ask JASZ chatbot, cutting calculations, demand forecasting
- **Database**: Uses RAG knowledge base for context

### 📈 **Dashboard Module** (`src/modules/dashboard/`)
- **Components**: KPI dashboards, analytics charts, performance metrics
- **Data Sources**: ARIL, ATRL, CSR systems integration
- **Charts**: Recharts-based visualizations for logistics data
- **Features**: Real-time monitoring, efficiency tracking

### 🚨 **Störungen Module** (`src/modules/stoerungen/`)
- **Components**: Incident management, escalation matrix, statistics
- **Features**: Störung creation/editing, image attachments, bereitschafts assignment
- **Database**: Main störungen table with file upload support
- **Integration**: Email notifications, runbook linking

### ⚙️ **Backend Module** (`src/modules/backend/`)
- **Components**: System monitoring, workflow management, error tracking
- **Features**: Python workflow execution, performance analytics
- **Services**: Workflow registry, performance monitoring

---

## 🔒 **SECURITY & AUTHENTICATION**

### 🛡️ **Security Stack**
- **JWT Authentication**: Token-based auth with refresh tokens
- **API Key Protection**: Routes protected with API keys
- **Input Validation**: Zod schemas for type-safe validation  
- **Rate Limiting**: Express rate limiter middleware
- **CORS Configuration**: Proper cross-origin setup
- **Helmet**: Security headers middleware
- **File Upload Security**: Multer with file type validation

### 🔑 **Authentication Flow**
1. Login via `/api/auth/login` with credentials
2. Receive JWT token for API authentication  
3. Frontend stores token and includes in API headers
4. Protected routes validate JWT server-side
5. Module access controlled via role-based permissions

---

## ⚡ **PERFORMANCE OPTIMIZATIONS**

### 🚀 **Frontend Performance**
- **Code Splitting**: Module-based lazy loading with React.lazy()
- **Asset Optimization**: Vite bundling with tree shaking
- **State Management**: TanStack Query with intelligent caching
- **Image Assets**: Optimized icons and images
- **Bundle Analysis**: Built-in Vite analysis tools

### 🏎️ **Backend Performance**
- **Database Optimization**: SQLite with WAL mode, foreign keys enabled
- **Connection Pooling**: libsql
- **Caching Layer**: Memory caching for frequently accessed data
- **Query Optimization**: Drizzle query optimization with indexes
- **Performance Middleware**: Request/response time tracking

### 💾 **Database Performance**
- **SQLite Pragma Settings**: WAL journaling, NORMAL synchronous mode
- **Indexes**: 127 indices on main DB, 45 on RAG DB for optimal queries
- **Connection Management**: Singleton database connections
- **Migration Strategy**: Incremental migrations with Drizzle Kit

---

## 🚨 **TROUBLESHOOTING**

### 🔧 **Common Issues & Solutions**

**better-sqlite3 platform errors:**
```bash
pnpm run db:rebuild-native
# Or if that fails:
cd backend && pnpm rebuild better-sqlite3
```

**Electron dev server issues:**
```bash
# Clear electron cache
rm -rf node_modules/.vite
pnpm run dev:app
```

**TanStack Router navigation issues:**
- Uses hash-based routing (`#/path`) for Electron file:// protocol
- Check router configuration in `src/routes/router.tsx`

**API connection issues:**
- Ensure backend is running on port 3001
- Check proxy configuration in `vite.config.ts`
- Verify CORS settings in `backend/src/server.ts`

**Database migration issues:**
```bash
# Reset and regenerate migrations
pnpm run db:introspect
pnpm run db:generate
pnpm run db:push
```

### 🎯 **Development Best Practices**

1. **Always use pnpm** - Never use npm or yarn commands
2. **Import from Drizzle** - Never import from @prisma/* packages  
3. **Use module architecture** - Place new features in appropriate modules
4. **Follow TanStack Router patterns** - Use hash-based navigation for Electron
5. **Implement proper error boundaries** - Use React error boundaries for stability
6. **Test database changes** - Always run db:test after schema modifications
7. **Check native modules** - Rebuild if switching between Windows/WSL

---

## 📚 **MIGRATION STATUS**

### ✅ **Successfully Migrated (100% Functional)**
- **Package Management**: npm → pnpm (complete)
- **ORM**: Prisma → Drizzle (core components migrated)
- **Database Connections**: Both SFM + RAG databases working
- **Core Repositories**: StoerungenRepository, UserRepository migrated
- **Frontend Integration**: All new components use Drizzle
- **Test Suite**: Comprehensive testing implemented

### 🔄 **Hybrid Architecture** 
- **Frontend**: Uses new Drizzle-based services exclusively
- **Backend**: Legacy Prisma code remains functional alongside Drizzle
- **Migration Strategy**: Core components migrated first, legacy components functional
- **Zero Downtime**: All existing functionality preserved during migration

### 📊 **Metrics**
- **Database Performance**: Sub-millisecond queries with caching
- **Test Coverage**: 100% for migrated components  
- **Data Integrity**: 291 störungen, 2 users validated
- **Schema Coverage**: 41 main tables + 12 RAG tables fully mapped

---

## 🚀 **QUICK START FOR NEW DEVELOPERS**

1. **Clone and setup:**
   ```bash
   git clone [repository]
   cd Leitstand_App
   pnpm install
   pnpm run backend:prepare
   ```

2. **Start development:**
   ```bash
   pnpm run dev  # Starts both frontend and backend
   ```

3. **Test database connection:**
   ```bash
   pnpm run db:test
   ```

4. **If native module issues (WSL/Windows):**
   ```bash
   pnpm run db:rebuild-native
   ```

5. **Access application:**
   - Electron app starts automatically
   - Backend API: `http://localhost:3001`
   - Frontend dev server: `http://localhost:3000`

---

## 📞 **CLAUDE DEVELOPMENT INSTRUCTIONS**

**ALWAYS USE:**
- `pnpm` for ALL package management operations
- `import { db } from '@/db'` for database operations  
- `import { tableName } from '@/db/schema'` for table definitions
- Drizzle query syntax: `db.select().from(table).where(...)`
- TanStack Router for navigation with hash-based routing
- Module-based architecture for new features

**NEVER USE:**
- `npm` or `yarn` commands 
- `@prisma/*` imports or Prisma Client syntax
- Relative imports for database operations
- File-based routing (use TanStack Router)
- Direct SQL without Drizzle ORM (except for complex queries)

**MIGRATION-AWARE DEVELOPMENT:**
- Check if component uses Prisma before editing
- Prefer creating new Drizzle-based versions over modifying legacy Prisma code  
- Use repository pattern for data access
- Test all database operations with `pnpm run db:test:*` commands
- Consider WSL/Windows compatibility for native modules

**ARCHITECTURE PRINCIPLES:**
- Follow modular structure in `src/modules/`
- Use TypeScript strictly with proper type definitions
- Implement proper error boundaries and error handling
- Optimize for Electron environment (hash routing, asset paths)
- Maintain performance with caching and query optimization

---
