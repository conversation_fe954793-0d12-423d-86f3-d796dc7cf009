/**
 * RAG (Retrieval-Augmented Generation) Type Definitions
 * 
 * Defines interfaces for vector embeddings, document management,
 * and similarity search functionality
 */

// Vector Embedding Types
export interface VectorEmbedding {
  id: string;
  chunkId: string;
  model: string;
  embedding: Float32Array;
  dimensions: number;
  createdAt: Date;
}

export interface DocumentChunk {
  id: string;
  documentId: string;
  chunkIndex: number;
  content: string;
  tokenCount: number;
  startPosition: number;
  endPosition: number;
  metadata?: Record<string, any>;
  createdAt: Date;
  vectorEmbeddings?: VectorEmbedding[];
}

export interface Document {
  id: string;
  knowledgeBaseId: number;
  title: string;
  content: string;
  contentType: string;
  source?: string;
  language: string;
  metadata?: Record<string, any>;
  hash: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  chunks?: DocumentChunk[];
  chunkCount?: number; // For aggregated queries
}

export interface KnowledgeBase {
  id: number;
  name: string;
  description?: string;
  category: 'dispatch' | 'cutting' | 'incoming-goods' | 'system' | 'quality' | 'maintenance' | 'safety' | 'app' | 'procedures' | 'kpi';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  documents?: Document[];
}

// RAG Query Types
export interface RAGQuery {
  id: string;
  query: string;
  queryEmbedding?: Float32Array;
  intent?: string;
  language: string;
  resultsFound: number;
  topSimilarity?: number;
  responseGenerated: boolean;
  executionTimeMs: number;
  createdAt: Date;
}

export interface SimilarityResult {
  id: string;
  queryId: string;
  chunkId: string;
  similarity: number;
  rank: number;
  used: boolean;
  createdAt: Date;
  chunk?: DocumentChunk;
}

// Service Input/Output Types
export interface CreateDocumentRequest {
  knowledgeBaseId?: number;
  title: string;
  content: string;
  contentType?: string;
  source?: string;
  language?: string;
  metadata?: Record<string, any>;
}

export interface CreateKnowledgeBaseRequest {
  name: string;
  description?: string;
  category: 'business' | 'technical' | 'procedures' | 'kpi';
}

export interface ChunkingOptions {
  maxTokens?: number;
  overlapTokens?: number;
  preserveParagraphs?: boolean;
  minChunkSize?: number;
}

export interface EmbeddingRequest {
  text: string;
  model?: string;
}

export interface EmbeddingResponse {
  embedding: Float32Array;
  model: string;
  dimensions: number;
  tokenCount: number;
}

export interface SimilaritySearchRequest {
  query: string;
  queryEmbedding?: Float32Array;
  knowledgeBaseIds?: number[];
  limit?: number;
  threshold?: number;
  language?: string;
  categories?: string[];
}

export interface SimilaritySearchResult {
  chunk: DocumentChunk & {
    document: Document & {
      knowledgeBase: KnowledgeBase;
    };
  };
  similarity: number;
  rank: number;
}

export interface RAGResponse {
  query: string;
  results: SimilaritySearchResult[];
  context: string;
  intent?: string;
  executionTimeMs: number;
  totalResults: number;
}

// Vector Operations
export interface VectorOperations {
  cosineSimilarity(a: Float32Array, b: Float32Array): number;
  dotProduct(a: Float32Array, b: Float32Array): number;
  magnitude(vector: Float32Array): number;
  normalize(vector: Float32Array): Float32Array;
}

// Chunking Strategy
export interface ChunkingStrategy {
  name: string;
  maxTokens: number;
  overlapTokens: number;
  splitOn: 'sentence' | 'paragraph' | 'token' | 'character';
  preserveStructure: boolean;
}

// RAG Configuration
export interface RAGConfig {
  defaultModel: string;
  defaultDimensions: number;
  defaultChunkSize: number;
  defaultOverlap: number;
  similarityThreshold: number;
  maxResults: number;
  enableQueryLogging: boolean;
  enablePerformanceMetrics: boolean;
}

// Error Types
export enum RAGErrorCode {
  DOCUMENT_NOT_FOUND = 'DOCUMENT_NOT_FOUND',
  KNOWLEDGE_BASE_NOT_FOUND = 'KNOWLEDGE_BASE_NOT_FOUND',
  EMBEDDING_GENERATION_FAILED = 'EMBEDDING_GENERATION_FAILED',
  VECTOR_STORAGE_FAILED = 'VECTOR_STORAGE_FAILED',
  SIMILARITY_SEARCH_FAILED = 'SIMILARITY_SEARCH_FAILED',
  CHUNKING_FAILED = 'CHUNKING_FAILED',
  INVALID_VECTOR_DIMENSIONS = 'INVALID_VECTOR_DIMENSIONS',
  DUPLICATE_DOCUMENT = 'DUPLICATE_DOCUMENT',
  INSUFFICIENT_CONTEXT = 'INSUFFICIENT_CONTEXT'
}

export interface RAGError {
  code: RAGErrorCode;
  message: string;
  details?: Record<string, any>;
  timestamp: Date;
}

// Performance Metrics
export interface RAGPerformanceMetrics {
  totalQueries: number;
  averageResponseTime: number;
  averageSimilarity: number;
  cacheHitRate: number;
  embeddingGenerationTime: number;
  searchTime: number;
  totalDocuments: number;
  totalChunks: number;
  totalEmbeddings: number;
  storageSize: number;
}

// Batch Operations
export interface BatchEmbeddingRequest {
  texts: string[];
  model?: string;
  batchSize?: number;
}

export interface BatchEmbeddingResponse {
  embeddings: EmbeddingResponse[];
  totalProcessed: number;
  failedCount: number;
  processingTimeMs: number;
}

export interface BatchDocumentImport {
  knowledgeBaseId?: number;
  documents: CreateDocumentRequest[];
  chunkingOptions?: ChunkingOptions;
  generateEmbeddings?: boolean;
}

export interface BatchImportResult {
  totalDocuments: number;
  successfulImports: number;
  failedImports: number;
  totalChunks: number;
  totalEmbeddings: number;
  processingTimeMs: number;
  errors: RAGError[];
}