# JASZ AI-Assistant - <PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> Fragen (FAQ)

## Was ist JASZ?

JASZ ist der AI-Assistant der Leitstand-Anwendung. Er hilft bei der Navigation, erklärt KPIs und Diagramme, und bietet kontextuelle Unterstützung bei der Störungsbearbeitung.

## Wie verwende ich JASZ?

### Ask JASZ Buttons
- **Wo finde ich sie?**: Orange Buttons mit "Ask JASZ" oder Bot-Symbol
- **Wo erscheinen sie?**: 
  - Für die Page Informationen in der oberen linken Ecke neben der Überschrift für die Seite
  - Für die Tabs Informationen in der oberen rechten Ecke neben dem Tab-Label
  - Bei wichtigen Komponenten und Diagrammen
- **Wie funktionieren sie?**: Klick öffnet den Chat mit einer vorbereiteten, kontextuellen Frage

### Chat-Interface
- **Öffnen**: Über Ask JASZ Buttons oder das Logo unten rechts
- **Funktionen**: RAG-verstärkte Antworten mit Quellenangaben
- **AI+ Modus**: Erweiterte Datenbanksuche für tiefere Analysen

## Störungsmanagement

### "Wie melde ich eine neue Störung?"
1. Klicke auf "Störung Melden" (roter Button oben rechts auf der Störungen-Seite)
2. Fülle das Formular aus:
   - Titel und Beschreibung der Störung
   - Betroffenes System wählen
   - Schweregrad festlegen
   - Kategorie zuordnen
3. Speichern - Die Störung wird automatisch dem Bereitschaftsdienst zugewiesen

### "Wie interpretiere ich die System-Heatmap?"
- **Grün**: System läuft stabil, keine Probleme
- **Gelb**: Warnungen vorhanden, Überwachung erforderlich
- **Rot**: Kritische Störung, sofortiges Handeln erforderlich
- **Grau**: System offline oder keine Daten verfügbar
- **Klick auf Kacheln**: Zeigt Details und Historie des Systems

### "Was bedeuten die KPI-Werte?"

#### MTTR (Mean Time To Repair)
- **Bedeutung**: Durchschnittliche Reparaturzeit
- **Gut**: < 4 Stunden
- **Verbesserung**: Bessere Dokumentation, Automatisierung, Schulungen

#### MTTA (Mean Time To Acknowledge) 
- **Bedeutung**: Durchschnittliche Reaktionszeit
- **Gut**: < 15 Minuten
- **Verbesserung**: Bessere Benachrichtigungen, klarere Eskalation

#### Erstlösungsrate
- **Bedeutung**: Anteil beim ersten Versuch gelöster Störungen
- **Gut**: > 80%
- **Verbesserung**: Bessere Runbooks, Mitarbeiterschulungen

## Workflow-Management

### "Wie überwache ich SAP-Workflows?"
1. Gehe zum Workflows-Tab "SAP Workflows"
2. Status-Übersicht zeigt alle aktiven Workflows
3. Grüne Kacheln = läuft normal
4. Rote/Orange Kacheln = Attention erforderlich
5. Klick auf Workflow für Details und Logs

### "Was mache ich bei Workflow-Fehlern?"
1. Identifiziere fehlerhaften Workflow in der Übersicht
2. Öffne Workflow-Details für Error-Log
3. Prüfe System Logs für detaillierte Fehlermeldungen
4. Folge den Runbook-Prozeduren für den spezifischen Workflow-Typ
5. Dokumentiere Lösung für zukünftige Referenz

### "Wie analysiere ich Performance-Probleme?"
1. Nutze Performance Analytics Tab
2. Identifiziere Bottlenecks in den Trend-Diagrammen
3. Vergleiche aktuelle Werte mit historischen Daten
4. Prüfe Korrelation zwischen verschiedenen Metriken
5. Eskaliere bei anhaltenden Performance-Problemen

## Bereitschaftsdienst

### "Wer ist aktuell im Bereitschaftsdienst?"
- Bereitschaftsplan auf der Management-Seite einsehen
- Aktuelle Person ist hervorgehoben
- Kontaktdaten und Vertretungsregelungen sind aufgeführt
- Bei Eskalation automatische Weiterleitung an nächste Ebene

### "Wie funktioniert die Eskalation?"
1. **Level 1** (0-15 Min): Bereitschaftsdienst
2. **Level 2** (15-30 Min): Teamleiter
3. **Level 3** (30-60 Min): Management
4. **Level 4** (>60 Min): Externe Experten
- Automatische Eskalation nach definierten Zeiträumen
- Manuelle Eskalation jederzeit möglich

## Analytics und Reporting

### "Wie wähle ich den richtigen Zeitraum für Analysen?"
- **Tagesanalyse**: Für aktuelle Probleme und Trends
- **Wochenanalyse**: Für Pattern Recognition und Vergleiche
- **Monatsanalyse**: Für strategische Entscheidungen und KPI-Tracking
- **Quartalsanalyse**: Für Langzeit-Trends und Budgetplanung

### "Welche Diagramme zeigen was?"
- **Severity Distribution**: Verteilung nach Schweregrad
- **Status Distribution**: Offene vs. gelöste Störungen
- **MTTR Trend**: Entwicklung der Reparaturzeiten
- **Category Charts**: Störungen nach Kategorien und Systemen

## Runbooks

### "Wie erstelle ich ein neues Runbook?"
1. Gehe zum Runbooks-Tab
2. Klicke "Neues Runbook erstellen"
3. Definiere Anwendungsbereich und Auslöser
4. Erstelle Schritt-für-Schritt Anleitung
5. Teste das Runbook mit einem Dummy-Szenario
6. Veröffentliche und teile mit dem Team

### "Wie finde ich das richtige Runbook?"
- Suche nach Störungstyp oder System
- Nutze Kategorie-Filter
- Folge verlinkten Runbooks in Störungsdetails
- Ask JASZ nach empfohlenen Runbooks für spezifische Situationen

## Technische Probleme

### "JASZ reagiert nicht oder lädt nicht"
1. Prüfe Internet-Verbindung
2. Aktualisiere Browser (Ctrl+F5)
3. Prüfe, ob Backend-Services laufen
4. Kontaktiere IT-Support bei anhaltenden Problemen

### "KPIs oder Diagramme werden nicht angezeigt"
1. Prüfe Zeitraum-Einstellungen
2. Verifiziere Datenquelle-Verbindung
3. Kontrolliere Browser-Konsole auf Fehler
4. Versuche anderen Browser
5. Melde Problem an IT-Support

### "Chat-Antworten sind ungenau oder veraltet"
1. Sei spezifischer in deiner Frage
2. Gib mehr Kontext zu deinem Problem
3. Nutze Ask JASZ Buttons für bessere Kontextualisierung
4. Melde Feedback an das Entwicklungsteam

## Best Practices

### Effektive Nutzung von JASZ
- **Kontext nutzen**: Ask JASZ Buttons liefern bessere Antworten als freie Fragen
- **Spezifisch fragen**: "Warum ist die MTTR heute hoch?" statt "Was ist los?"
- **Iterativ arbeiten**: Baue auf JASZ-Antworten auf mit Folge-Fragen
- **Feedback geben**: Bewerte Antworten für kontinuierliche Verbesserung

### Störungsbearbeitung
- **Dokumentation**: Jede Störung vollständig dokumentieren
- **Kommunikation**: Regelmäßige Updates an Stakeholder
- **Lernen**: Post-Incident Reviews für kritische Störungen
- **Automatisierung**: Wiederkehrende Lösungen in Runbooks festhalten

### Monitoring und Überwachung
- **Proaktiv**: Regelmäßige Dashboard-Checks
- **Trends erkennen**: Langzeit-Analysen für präventive Maßnahmen
- **Alerts konfigurieren**: Intelligente Benachrichtigungen ohne False Positives
- **Team-Knowledge**: Wissen teilen und dokumentieren

## Kontakt und Support

- **IT-Support**: Für technische Probleme mit der Anwendung
- **Prozess-Support**: Für Fragen zu Workflows und Prozessen
- **JASZ-Feedback**: Für Verbesserungsvorschläge am AI-Assistant
- **Schulungen**: Regelmäßige Team-Schulungen zu neuen Features