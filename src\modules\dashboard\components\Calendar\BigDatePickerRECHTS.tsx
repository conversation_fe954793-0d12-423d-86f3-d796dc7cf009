"use client"

import React, { useState, useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface BigDatePickerProps {
  initialDate?: Date
  onDateChange?: (date: Date) => void
  className?: string
}

const BigDatePicker: React.FC<BigDatePickerProps> = ({
  initialDate = new Date(2025, 8, 3), // September 3, 2025
  onDateChange,
  className
}) => {
  const [currentDate, setCurrentDate] = useState<Date>(initialDate)
  const [isCalendarVisible, setIsCalendarVisible] = useState<boolean>(false)
  const [isAnimating, setIsAnimating] = useState<boolean>(false)
  
  const currentPageRef = useRef<HTMLDivElement>(null)
  const nextPageRef = useRef<HTMLDivElement>(null)

  // Deutsche Lokalisierung entsprechend der Memory-Anforderung
  const monthNamesLong = [
    "JANUAR", "FEBRUAR", "MÄRZ", "APRIL", "MAI", "JUN<PERSON>",
    "JULI", "AUGUST", "SEPTEMBER", "OKTOBER", "NOVEMBER", "DEZEMBER"
  ]
  
  const monthNamesShort = [
    "Jan", "Feb", "Mär", "Apr", "Mai", "Jun",
    "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"
  ]

  const dayNames = ["So", "Mo", "Di", "Mi", "Do", "Fr", "Sa"]

  // Hilfsfunktion für Datum-Suffix
  const getDateSuffix = (day: number): string => {
    if (day > 3 && day < 21) return 'th'
    switch (day % 10) {
      case 1: return "st"
      case 2: return "nd"
      case 3: return "rd"
      default: return "th"
    }
  }

  // Flip-Animation Funktion entsprechend der Memory-Anforderung
  const animateFlip = (newDate: Date) => {
    if (isAnimating) return
    
    const day = newDate.getDate()
    const newDateString = `${day}`
    
    // Prüfen ob sich das Datum tatsächlich geändert hat
    if (currentPageRef.current?.textContent?.trim() === newDateString) {
      return
    }

    setIsAnimating(true)
    
    // Das neue Datum hinter das aktuelle setzen
    if (nextPageRef.current) {
      nextPageRef.current.textContent = newDateString
      nextPageRef.current.className = 'date-page next'
    }
    
    // Das aktuelle Blatt wegklappen
    if (currentPageRef.current) {
      currentPageRef.current.classList.add('flipping-out')
    }
    
    // Nach der Animation das neue Datum als aktuell setzen
    setTimeout(() => {
      if (currentPageRef.current && nextPageRef.current) {
        currentPageRef.current.textContent = newDateString
        currentPageRef.current.className = 'date-page current'
        nextPageRef.current.className = 'date-page next'
        nextPageRef.current.textContent = ''
      }
      setIsAnimating(false)
    }, 800)
  }

  // Datum aktualisieren
  const updateDate = (newDate: Date) => {
    animateFlip(newDate)
    setCurrentDate(newDate)
    onDateChange?.(newDate)
  }

  // Kalender rendern
  const renderCalendar = () => {
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()
    const firstDayOfMonth = new Date(year, month, 1).getDay()
    const daysInMonth = new Date(year, month + 1, 0).getDate()
    
    const calendarDays: React.ReactElement[] = []
    
    // Leere Felder für den Monatsanfang
    for (let i = 0; i < firstDayOfMonth; i++) {
      calendarDays.push(
        <div key={`empty-${i}`} className="h-9 w-9"></div>
      )
    }
    
    // Tage des Monats
    for (let i = 1; i <= daysInMonth; i++) {
      const isSelected = i === currentDate.getDate()
      calendarDays.push(
        <button
          key={i}
          onClick={() => {
            const newDate = new Date(year, month, i)
            updateDate(newDate)
          }}
          className={cn(
            "h-9 w-9 rounded-full text-sm font-medium transition-colors hover:bg-slate-100",
            isSelected 
              ? "bg-indigo-500 text-white font-semibold hover:bg-indigo-600" 
              : "text-slate-700 hover:text-slate-900"
          )}
        >
          {i}
        </button>
      )
    }
    
    return calendarDays
  }

  // Navigation
  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  // Toggle Kalender
  const toggleCalendar = () => {
    setIsCalendarVisible(!isCalendarVisible)
  }

  // Initialisierung
  useEffect(() => {
    if (currentPageRef.current) {
      const initialDay = currentDate.getDate()
      currentPageRef.current.textContent = initialDay.toString()
    }
  }, [])

  return (
    <div className={cn("flex gap-2 flex-wrap justify-center flex-row-reverse", className)}>
      {/* Custom CSS für die Flip-Animation */}
      <style>{`
        .flipper-container {
          perspective: 1000px;
          overflow: hidden;
        }
        
        .date-page {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: white;
          transform-origin: center bottom;
          font-size: 6rem;
          font-weight: 600;
          color: #343D4F;
        }
        
        .date-page.current {
          z-index: 2;
          transform: rotateX(0deg);
        }
        
        .date-page.next {
          z-index: 1;
          transform: rotateX(0deg);
        }
        
        .date-page.flipping-out {
          z-index: 3;
          animation: flipOut 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }
        
        @keyframes flipOut {
          0% {
            transform: rotateX(0deg);
          }
          100% {
            transform: rotateX(90deg);
          }
        }
      `}</style>

      {/* Linke Anzeige für das ausgewählte Datum */}
      <div className="w-60 h-60 bg-white rounded-xl shadow-lg border-2 border-indigo-500 flex flex-col overflow-hidden">
        {/* Header */}
        <div className="bg-indigo-500 text-white flex justify-between items-center px-5 py-3 font-semibold text-lg tracking-wider">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleCalendar}
            className="text-white hover:bg-white/10 p-1 h-auto opacity-80"
            aria-label={isCalendarVisible ? "Kalender schließen" : "Kalender öffnen"}
          >
            {isCalendarVisible ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
          </Button>
          <span>{monthNamesLong[currentDate.getMonth()]}</span>
        </div>
        
        {/* Hauptanzeige mit Flip-Animation */}
        <div className="flex-grow flex items-center justify-center relative pt-5">
          <div className="flipper-container relative w-full h-full">
            <div className="relative w-full h-full">
              <div ref={currentPageRef} className="date-page current"></div>
              <div ref={nextPageRef} className="date-page next"></div>
            </div>
          </div>
        </div>
        
        {/* Footer */}
        <div className="p-4 text-center text-xl font-medium text-slate-400">
          <span>{currentDate.getFullYear()}</span>
        </div>
      </div>

      {/* Rechter Kalender zur Auswahl */}
      <div className={cn(
        "w-70 bg-white rounded-xl border-2 border-indigo-500 p-5 shadow-lg transition-all duration-300",
        isCalendarVisible 
          ? "opacity-100 scale-100 pointer-events-auto" 
          : "opacity-0 scale-95 pointer-events-none"
      )}>
        {/* Kalender Header */}
        <div className="flex justify-between items-center mb-5 font-semibold">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('prev')}
            className="text-slate-700 hover:bg-slate-100 p-1 h-auto"
            aria-label="Vorheriger Monat"
          >
            <ChevronLeft size={18} />
          </Button>
          <span className="text-base">
            {monthNamesShort[currentDate.getMonth()]} {currentDate.getFullYear()}
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('next')}
            className="text-slate-700 hover:bg-slate-100 p-1 h-auto"
            aria-label="Nächster Monat"
          >
            <ChevronRight size={18} />
          </Button>
        </div>
        
        {/* Wochentage */}
        <div className="grid grid-cols-7 gap-1 mb-3">
          {dayNames.map((day) => (
            <div key={day} className="text-center font-semibold text-slate-400 text-xs pb-2">
              {day}
            </div>
          ))}
        </div>
        
        {/* Kalendertage */}
        <div className="grid grid-cols-7 gap-1">
          {renderCalendar()}
        </div>
      </div>
    </div>
  )
}

export default BigDatePicker