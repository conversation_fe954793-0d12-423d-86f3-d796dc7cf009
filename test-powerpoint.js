/**
 * Test für PowerPoint Export Funktionalität
 */
import { collectCuttingChartData } from './src/services/chart-data-collector.service';
import { ExportService } from './src/services/export.service';

async function testPowerPointExport() {
  try {
    console.log('Starte PowerPoint Export Test...');

    // Sammle Test-Daten
    const chartData = await collectCuttingChartData({
      from: new Date(2025, 5, 1),
      to: new Date(2025, 5, 30)
    });

    console.log(`Gesammelt ${chartData.length} Charts für Export`);

    if (chartData.length === 0) {
      console.error('Keine Daten für Export verfügbar');
      return;
    }

    // Teste PowerPoint Export
    await ExportService.exportToPowerPoint(chartData, {
      dateRange: {
        from: new Date(2025, 5, 1),
        to: new Date(2025, 5, 30)
      },
      includeSummary: true
    });

    console.log('PowerPoint Export Test erfolgreich!');
  } catch (error) {
    console.error('PowerPoint Export Test fehlgeschlagen:', error);
  }
}

// Führe Test aus
testPowerPointExport();
