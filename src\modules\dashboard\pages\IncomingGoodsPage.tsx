import React, { useState } from "react";
import { DateRange } from "react-day-picker";
import { IncomingPositionsChart } from "@/modules/dashboard/components/charts/IncomingPositionsChart";
import { ReturnsChart } from "@/modules/dashboard/components/charts/QMeldungenChart";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { PackageCheck, FileSpreadsheet, FileText } from "lucide-react";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import weColor from "@/assets/iconWe-color.png";
import { AskJaszButton } from "@/modules/ai/components";
import { createPageContext } from "@/contexts/AskJaszContext";
import { ExportService } from "@/services/export.service";
import { collectIncomingGoodsChartData } from "@/services/chart-data-collector.service";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
/**
 * Incoming-Goods-Bereich Dashboard
 * 
 * Zeigt verschiedene Kennzahlen für den Incoming-Goods-Bereich an:
 * - Eingehende Positionen (WE-Daten) als Balkendiagramm
 * - Retouren als Kreisdiagramm
 */
export default function IncomingGoodsPage() {
  // Zustand für den ausgewählten Datumsbereich - Standard: letzten 30 Tage (!NICHT ÄNDERN!)
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 4, 1), // 1. Juni 2025
    to: new Date(2025, 4, 30), // 30. Juni 2025 (alle verfügbaren Daten)
  });

  // Zustand für Export-Loading
  const [isExporting, setIsExporting] = useState<{ excel: boolean; powerpoint: boolean }>({
    excel: false,
    powerpoint: false
  });

  // Export-Funktionen
  const handleExcelExport = async () => {
    if (!dateRange?.from || !dateRange?.to) {
      toast.error('Bitte wählen Sie einen Datumsbereich aus');
      return;
    }

    setIsExporting(prev => ({ ...prev, excel: true }));

    try {
      const chartData = await collectIncomingGoodsChartData(dateRange);

      if (chartData.length === 0) {
        toast.error('Keine Daten für den Export verfügbar');
        return;
      }

      await ExportService.exportToExcel(chartData, {
        dateRange,
        includeSummary: true,
        dashboardType: 'incoming'
      });

      toast.success('Excel-Datei wurde erfolgreich erstellt');
    } catch (error) {
      console.error('Excel-Export Fehler:', error);
      toast.error('Fehler beim Erstellen der Excel-Datei');
    } finally {
      setIsExporting(prev => ({ ...prev, excel: false }));
    }
  };

  const handlePowerPointExport = async () => {
    if (!dateRange?.from || !dateRange?.to) {
      toast.error('Bitte wählen Sie einen Datumsbereich aus');
      return;
    }

    setIsExporting(prev => ({ ...prev, powerpoint: true }));

    try {
      const chartData = await collectIncomingGoodsChartData(dateRange);

      if (chartData.length === 0) {
        toast.error('Keine Daten für den Export verfügbar');
        return;
      }

      await ExportService.exportToPowerPoint(chartData, {
        dateRange,
        includeSummary: true,
        dashboardType: 'incoming'
      });

      toast.success('PowerPoint-Datei wurde erfolgreich erstellt');
    } catch (error) {
      console.error('PowerPoint-Export Fehler:', error);
      toast.error('Fehler beim Erstellen der PowerPoint-Datei');
    } finally {
      setIsExporting(prev => ({ ...prev, powerpoint: false }));
    }
  };

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <img
            src={weColor}
            alt="Wareneingang"
            className="h-10 w-10 mr-2 object-contain"
          />
          <h1 className="text-4xl font-bold text-black">WARENEINGANG</h1>
          <div className="-mt-7">
            <AskJaszButton
              context={createPageContext(
                "Wareneingang Dashboard",
                [
                  "Wareneingang Performance Übersicht",
                  "Eingehende Positionen (WE-Daten)",
                  "Automatisches vs. Manuelles Lager",
                  "Q-Meldungen und Retouren Analyse",
                  "Lagerbestand und Kapazitätsauslastung"
                ],
                "Wareneingang Bereich Dashboard"
              )}
              position="inline"
              size="md"
              variant="default"
              tooltip="Hilfe zum Wareneingang Dashboard erhalten"
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex gap-2">
            <Button
              onClick={handleExcelExport}
              disabled={isExporting.excel}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <img src="/src/assets/excel-pn.png" alt="Excel Export" className="h-8 w-8" />
            </Button>
            <Button
              onClick={handlePowerPointExport}
              disabled={isExporting.powerpoint}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <img src="/src/assets/pp.png" alt="PowerPoint Export" className="h-8 w-8" />
            </Button>
          </div>
          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
            label="Datumsauswahl"
          />
        </div>
      </div>
      
      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-6">
          {/* Eingehende Positionen */}
          <div className="xl:col-span-2">
            <ChartErrorBoundary>
              <IncomingPositionsChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>

          {/* Retouren */}
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <ReturnsChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
}
