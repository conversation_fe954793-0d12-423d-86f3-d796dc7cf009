/**
 * Repository Factory
 * 
 * Zentrale Factory-Klasse für die Erstellung und Verwaltung
 * aller Repository-Instanzen mit Singleton-Pattern.
 */

import { db } from '../db';
import { RepositoryFactory } from './interfaces';
import { DispatchRepository } from './interfaces/dispatch.repository.interface';
import { WarehouseRepository } from './interfaces/warehouse.repository.interface';
import { CuttingRepository } from './interfaces/cutting.repository.interface';
import { DeliveryRepository } from './interfaces/delivery.repository.interface';
import { ProductionRepository } from './interfaces/production.repository.interface';
import { SupplierRepository } from './interfaces/supplier.repository.interface';
import { ISystemRepository } from './interfaces/system.interfaces';
import { UserRepositoryInterface } from './interfaces/user.repository.interface';
import { DispatchRepositoryImpl } from './dispatch.repository';
import { WarehouseRepositoryImpl } from './warehouse.repository';
import { CuttingRepositoryImpl } from './cutting.repository';
import { DeliveryRepositoryImpl } from './delivery.repository';
import { ProductionRepositoryImpl } from './production.repository';
import { SupplierRepositoryImpl } from './supplier.repository';
import { SystemRepositoryImpl } from './system.repository';
import { UserRepository } from './user.repository';
import { StoerungenRepository } from './stoerungen.repository';

/**
 * Repository Factory Implementation
 */
export class RepositoryFactoryImpl implements RepositoryFactory {
  private static instance: RepositoryFactoryImpl;
  private db = db;
  
  // Repository Singletons
  private _dispatchRepository?: any;
  private _warehouseRepository?: any;
  private _cuttingRepository?: any;
  private _deliveryRepository?: any;
  private _productionRepository?: any;
  private _supplierRepository?: any;
  private _systemRepository?: any;
  private _userRepository?: any;
  private _stoerungenRepository?: any;

  private constructor() {
    // Drizzle DB wird direkt importiert
  }

  /**
   * Singleton Instance abrufen
   */
  static getInstance(): RepositoryFactoryImpl {
    if (!RepositoryFactoryImpl.instance) {
      RepositoryFactoryImpl.instance = new RepositoryFactoryImpl();
    }
    return RepositoryFactoryImpl.instance;
  }

  /**
   * Dispatch Repository abrufen
   */
  dispatch(): DispatchRepository {
    if (!this._dispatchRepository) {
      this._dispatchRepository = new DispatchRepositoryImpl();
    }
    return this._dispatchRepository;
  }

  /**
   * Warehouse Repository abrufen
   */
  warehouse(): WarehouseRepository {
    if (!this._warehouseRepository) {
      this._warehouseRepository = new WarehouseRepositoryImpl();
    }
    return this._warehouseRepository;
  }

  /**
   * Cutting Repository abrufen
   */
  cutting(): any {
    if (!this._cuttingRepository) {
      this._cuttingRepository = new CuttingRepositoryImpl();
    }
    return this._cuttingRepository;
  }

  /**
   * Delivery Repository abrufen
   */
  delivery(): any {
    if (!this._deliveryRepository) {
      this._deliveryRepository = new DeliveryRepositoryImpl();
    }
    return this._deliveryRepository;
  }

  /**
   * Production Repository abrufen
   */
  production(): any {
    if (!this._productionRepository) {
      this._productionRepository = new ProductionRepositoryImpl();
    }
    return this._productionRepository;
  }

  /**
   * Supplier Repository abrufen
   */
  supplier(): any {
    if (!this._supplierRepository) {
      this._supplierRepository = new SupplierRepositoryImpl();
    }
    return this._supplierRepository;
  }

  /**
   * System Repository abrufen
   */
  system(): any {
    if (!this._systemRepository) {
      this._systemRepository = new SystemRepositoryImpl();
    }
    return this._systemRepository;
  }

  /**
   * User Repository abrufen
   */
  user(): any {
    if (!this._userRepository) {
      this._userRepository = new UserRepository();
    }
    return this._userRepository;
  }

  /**
   * Stoerungen Repository abrufen
   */
  stoerungen(): any {
    if (!this._stoerungenRepository) {
      this._stoerungenRepository = StoerungenRepository.getInstance();
    }
    return this._stoerungenRepository;
  }

  /**
   * Repository Statistics für Monitoring
   */
  async getAllRepositoryStats() {
    const stats = {
      timestamp: new Date().toISOString(),
      repositories: {} as any
    };

    // Dispatch Repository Stats
    if (this._dispatchRepository) {
      stats.repositories.dispatch = await this._dispatchRepository.getStats();
    }

    // Warehouse Repository Stats
    if (this._warehouseRepository) {
      stats.repositories.warehouse = await this._warehouseRepository.getStats();
    }

    // Cutting Repository Stats
    if (this._cuttingRepository) {
      stats.repositories.cutting = await this._cuttingRepository.getStats();
    }

    // Delivery Repository Stats
    if (this._deliveryRepository) {
      stats.repositories.delivery = await this._deliveryRepository.getStats();
    }

    // Production Repository Stats
    if (this._productionRepository) {
      stats.repositories.production = await this._productionRepository.getOverallProductionStats();
    }

    // Supplier Repository Stats
    if (this._supplierRepository) {
      stats.repositories.supplier = await this._supplierRepository.getStats();
    }

    // System Repository Stats
    if (this._systemRepository) {
      stats.repositories.system = await this._systemRepository.getSystemDashboard();
    }

    // User Repository Stats
    if (this._userRepository) {
      stats.repositories.user = { totalUsers: 0, activeUsers: 0 };
    }

    return stats;
  }

  /**
   * Alle Repository-Caches invalidieren
   */
  async invalidateAllCaches(): Promise<void> {
    const invalidationPromises: Promise<void>[] = [];

    if (this._dispatchRepository) {
      invalidationPromises.push(this._dispatchRepository.invalidateCache());
    }

    if (this._warehouseRepository) {
      invalidationPromises.push(this._warehouseRepository.invalidateCache());
    }

    if (this._cuttingRepository) {
      invalidationPromises.push(this._cuttingRepository.invalidateCache());
    }

    if (this._deliveryRepository) {
      invalidationPromises.push(this._deliveryRepository.invalidateCache());
    }

    if (this._productionRepository) {
      invalidationPromises.push(this._productionRepository.invalidateAllCache());
    }

    if (this._supplierRepository) {
      invalidationPromises.push(this._supplierRepository.invalidateCache());
    }

    if (this._systemRepository) {
      invalidationPromises.push(this._systemRepository.invalidateAllCache());
    }

    if (this._userRepository) {
      // User repository doesn't have cache invalidation yet
    }

    if (this._stoerungenRepository) {
      // Stoerungen repository cache invalidation
      invalidationPromises.push(this._stoerungenRepository.invalidateCache?.() || Promise.resolve());
    }

    await Promise.all(invalidationPromises);
  }

  /**
   * Factory zurücksetzen (für Testing)
   */
  static reset(): void {
    RepositoryFactoryImpl.instance = undefined as any;
  }
}

/**
 * Globale Repository Factory Instance
 */
let repositoryFactory: RepositoryFactoryImpl | undefined;

/**
 * Repository Factory initialisieren
 */
export function initializeRepositoryFactory(): RepositoryFactoryImpl {
  repositoryFactory = RepositoryFactoryImpl.getInstance();
  return repositoryFactory;
}

/**
 * Repository Factory abrufen
 */
export function getRepositoryFactory(): RepositoryFactoryImpl {
  if (!repositoryFactory) {
    throw new Error('Repository factory not initialized. Call initializeRepositoryFactory first.');
  }
  return repositoryFactory;
}

/**
 * Direkte Repository-Zugriffe (Convenience Functions)
 */
export function getDispatchRepository(): any {
  return getRepositoryFactory().dispatch();
}

export function getWarehouseRepository(): any {
  return getRepositoryFactory().warehouse();
}

export function getCuttingRepository(): any {
  return getRepositoryFactory().cutting();
}

export function getDeliveryRepository(): any {
  return getRepositoryFactory().delivery();
}

export function getProductionRepository(): any {
  return getRepositoryFactory().production();
}

export function getSupplierRepository(): any {
  return getRepositoryFactory().supplier();
}

export function getSystemRepository(): any {
  return getRepositoryFactory().system();
}

export function getUserRepository(): any {
  return getRepositoryFactory().user();
}

export function getStoerungenRepository(): any {
  return getRepositoryFactory().stoerungen();
}
