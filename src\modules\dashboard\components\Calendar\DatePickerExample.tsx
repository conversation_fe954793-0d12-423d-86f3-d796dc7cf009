// Example for How to Use
import React, { useState } from 'react'
import BigDatePicker from './BigDatePickerRECHTS'

const DatePickerExample: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())

  const handleDateChange = (date: Date) => {
    setSelectedDate(date)
    console.log('Neues Datum ausgewählt:', date.toLocaleDateString('de-DE'))
  }

  return (
    <div className="min-h-screen bg-slate-100 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        <h1 className="text-3xl font-bold text-slate-800 text-center mb-8">
          BigDatePicker Komponente
        </h1>
        
        <BigDatePicker
          initialDate={new Date(2025, 8, 3)} // 3. September 2025
          onDateChange={handleDateChange}
          className="mb-8"
        />
        
        <div className="bg-white rounded-lg p-6 shadow-sm border border-slate-200">
          <h2 className="text-lg font-semibold text-slate-700 mb-3">
            Ausgewähltes Datum:
          </h2>
          <p className="text-slate-600">
            {selectedDate.toLocaleDateString('de-DE', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </p>
        </div>
      </div>
    </div>
  )
}

export default DatePickerExample