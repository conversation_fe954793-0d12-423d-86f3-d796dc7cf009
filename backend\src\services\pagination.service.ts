/**
 * Drizzle ORM Pagination Service
 * 
 * Implementiert cursor-basierte und offset-basierte Paginierung
 * für große Datasets mit Performance-Optimierungen.
 * Vollständig kompatibel mit Drizzle ORM.
 */

import { SQL, and, or, gt, lt, gte, lte, eq, desc, asc, count } from 'drizzle-orm';
import { SQLiteSelectQueryBuilder } from 'drizzle-orm/sqlite-core';  // TODO: PostgreSQL QueryBuilder verwenden
import { db } from '../db';

/**
 * Pagination-Strategien
 */
export enum PaginationStrategy {
  CURSOR = 'cursor',
  OFFSET = 'offset'
}

/**
 * Sortierungs-Richtungen
 */
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc'
}

/**
 * Cursor-Pagination-Parameter
 */
export interface CursorPaginationParams {
  first?: number; // Anzahl der Datensätze nach dem Cursor
  after?: string; // Cursor-Position (base64-kodiert)
  last?: number; // Anzahl der Datensätze vor dem Cursor
  before?: string; // Cursor-Position (base64-kodiert)
  orderBy?: string; // Sortierung-Feld
  orderDirection?: SortDirection; // Sortierung-Richtung
}

/**
 * Offset-Pagination-Parameter
 */
export interface OffsetPaginationParams {
  page?: number; // Seiten-Nummer (1-basiert)
  limit?: number; // Anzahl der Datensätze pro Seite
  offset?: number; // Direkte Offset-Angabe
  orderBy?: string; // Sortierung-Feld
  orderDirection?: SortDirection; // Sortierung-Richtung
}

/**
 * Generische Pagination-Parameter
 */
export interface PaginationParams {
  strategy: PaginationStrategy;
  cursor?: CursorPaginationParams;
  offset?: OffsetPaginationParams;
}

/**
 * Page Info für Cursor-Pagination
 */
export interface PageInfo {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startCursor?: string;
  endCursor?: string;
  totalCount?: number;
}

/**
 * Pagination-Metadaten für Offset-Pagination
 */
export interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  limit: number;
  offset: number;
}

/**
 * Paginiertes Ergebnis für Cursor-Pagination
 */
export interface CursorPaginatedResult<T> {
  edges: Array<{
    cursor: string;
    node: T;
  }>;
  pageInfo: PageInfo;
  totalCount?: number;
}

/**
 * Paginiertes Ergebnis für Offset-Pagination
 */
export interface OffsetPaginatedResult<T> {
  data: T[];
  meta: PaginationMeta;
}

/**
 * Drizzle Query Interface für Pagination
 */
export interface DrizzleQueryBuilder<T> {
  select: () => SQLiteSelectQueryBuilder<any, any, any>;
  where?: (condition: SQL) => SQLiteSelectQueryBuilder<any, any, any>;
  orderBy?: (...columns: any[]) => SQLiteSelectQueryBuilder<any, any, any>;
  limit?: (limit: number) => SQLiteSelectQueryBuilder<any, any, any>;
  offset?: (offset: number) => SQLiteSelectQueryBuilder<any, any, any>;
}

/**
 * Cursor-Info für interne Verarbeitung
 */
interface CursorInfo {
  field: string;
  value: any;
  id: string | number;
  direction: SortDirection;
}

/**
 * Drizzle ORM Pagination Service
 */
export class DrizzlePaginationService {
  private static instance: DrizzlePaginationService;
  
  private readonly DEFAULT_LIMIT = 50;
  private readonly MAX_LIMIT = 1000;
  private readonly DEFAULT_ORDER_BY = 'id';

  private constructor() {}

  static getInstance(): DrizzlePaginationService {
    if (!DrizzlePaginationService.instance) {
      DrizzlePaginationService.instance = new DrizzlePaginationService();
    }
    return DrizzlePaginationService.instance;
  }

  /**
   * Cursor-basierte Paginierung für Drizzle ORM implementieren
   */
  async applyCursorPagination<T extends Record<string, any>>(
    baseQuery: SQLiteSelectQueryBuilder<any, any, any>,
    table: any,
    params: CursorPaginationParams,
    defaultOrderBy: string = this.DEFAULT_ORDER_BY
  ): Promise<CursorPaginatedResult<T>> {
    const orderBy = params.orderBy || defaultOrderBy;
    const orderDirection = params.orderDirection || SortDirection.ASC;
    
    // Validiere und normalisiere Parameter
    const first = this.validateLimit(params.first);
    const last = this.validateLimit(params.last);
    
    if (first && last) {
      throw new Error('Cannot specify both "first" and "last" parameters');
    }
    
    if (params.after && params.before) {
      throw new Error('Cannot specify both "after" and "before" cursors');
    }

    let limit = first || last || this.DEFAULT_LIMIT;
    const isReversed = !!last;
    
    // Query-Builder vorbereiten
    let query = baseQuery;
    
    // Cursor-Bedingungen hinzufügen
    if (params.after) {
      const cursor = this.decodeCursor(params.after);
      const condition = this.buildDrizzleCursorCondition(table, cursor, false, orderDirection);
      query = query.where(condition);
    } else if (params.before) {
      const cursor = this.decodeCursor(params.before);
      const condition = this.buildDrizzleCursorCondition(table, cursor, true, orderDirection);
      query = query.where(condition);
    }

    // Sortierung hinzufügen
    const orderColumn = table[orderBy];
    const idColumn = table.id;
    
    if (isReversed) {
      const reversedDirection = this.reverseDirection(orderDirection);
      if (orderBy !== 'id') {
        query = query.orderBy(
          reversedDirection === SortDirection.ASC ? asc(orderColumn) : desc(orderColumn),
          reversedDirection === SortDirection.ASC ? asc(idColumn) : desc(idColumn)
        );
      } else {
        query = query.orderBy(
          reversedDirection === SortDirection.ASC ? asc(idColumn) : desc(idColumn)
        );
      }
    } else {
      if (orderBy !== 'id') {
        query = query.orderBy(
          orderDirection === SortDirection.ASC ? asc(orderColumn) : desc(orderColumn),
          orderDirection === SortDirection.ASC ? asc(idColumn) : desc(idColumn)
        );
      } else {
        query = query.orderBy(
          orderDirection === SortDirection.ASC ? asc(idColumn) : desc(idColumn)
        );
      }
    }

    // Limit hinzufügen (+1 um hasNextPage zu bestimmen)
    query = query.limit(limit + 1);

    // Query ausführen
    const results = await (query as any) as T[];
    
    // Prüfe ob mehr Daten verfügbar sind
    const hasMore = results.length > limit;
    if (hasMore) {
      results.pop(); // Entferne den extra Datensatz
    }

    // Bei Rückwärts-Paginierung die Reihenfolge umkehren
    if (isReversed) {
      results.reverse();
    }

    // Edges und Cursors erstellen
    const edges = results.map((node: T) => ({
      cursor: this.encodeCursor(orderBy, node[orderBy], node.id, orderDirection),
      node
    }));

    // PageInfo erstellen
    const pageInfo: PageInfo = {
      hasNextPage: isReversed ? false : hasMore,
      hasPreviousPage: isReversed ? hasMore : false,
      startCursor: edges.length > 0 ? edges[0].cursor : undefined,
      endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : undefined
    };

    // Bei Bedarf auch Rückwärts-Navigation prüfen
    if (!isReversed && params.after) {
      const cursor = this.decodeCursor(params.after);
      const previousCondition = this.buildDrizzleCursorCondition(table, cursor, true, orderDirection);
      
      const previousQuery = baseQuery.where(previousCondition).limit(1);
      const previousExists = await (previousQuery as any) as T[];
      pageInfo.hasPreviousPage = previousExists.length > 0;
    }

    return {
      edges,
      pageInfo
    };
  }

  /**
   * Offset-basierte Paginierung für Drizzle ORM implementieren
   */
  async applyOffsetPagination<T>(
    baseQuery: SQLiteSelectQueryBuilder<any, any, any>,
    table: any,
    params: OffsetPaginationParams,
    defaultOrderBy: string = this.DEFAULT_ORDER_BY
  ): Promise<OffsetPaginatedResult<T>> {
    const orderBy = params.orderBy || defaultOrderBy;
    const orderDirection = params.orderDirection || SortDirection.ASC;
    const limit = this.validateLimit(params.limit);
    
    // Berechne Offset
    let offset = 0;
    if (params.offset !== undefined) {
      offset = Math.max(0, params.offset);
    } else if (params.page !== undefined) {
      const page = Math.max(1, params.page);
      offset = (page - 1) * limit;
    }

    // Query für Daten
    let dataQuery = baseQuery;
    
    // Sortierung hinzufügen
    const orderColumn = table[orderBy];
    const idColumn = table.id;
    
    if (orderBy !== 'id') {
      dataQuery = dataQuery.orderBy(
        orderDirection === SortDirection.ASC ? asc(orderColumn) : desc(orderColumn),
        orderDirection === SortDirection.ASC ? asc(idColumn) : desc(idColumn)
      );
    } else {
      dataQuery = dataQuery.orderBy(
        orderDirection === SortDirection.ASC ? asc(idColumn) : desc(idColumn)
      );
    }

    // Limit und Offset hinzufügen
    dataQuery = dataQuery.limit(limit).offset(offset);

    // Query für Gesamtanzahl
    const countQuery = db.select({ count: count() }).from(table);
    
    // Beide Queries parallel ausführen
    const [data, countResult] = await Promise.all([
           (dataQuery as any) as Promise<T[]>,
           (countQuery as any) as Promise<Array<{ count: number }>>
         ]);

    const totalCount = countResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);
    const currentPage = Math.floor(offset / limit) + 1;

    const meta: PaginationMeta = {
      currentPage,
      totalPages,
      totalCount,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1,
      limit,
      offset
    };

    return {
      data,
      meta
    };
  }

  /**
   * Automatische Pagination basierend auf Parametern
   */
  async applyPagination<T extends Record<string, any>>(
    baseQuery: SQLiteSelectQueryBuilder<any, any, any>,
    table: any,
    params: PaginationParams,
    defaultOrderBy: string = this.DEFAULT_ORDER_BY
  ): Promise<CursorPaginatedResult<T> | OffsetPaginatedResult<T>> {
    if (params.strategy === PaginationStrategy.CURSOR && params.cursor) {
      return this.applyCursorPagination(baseQuery, table, params.cursor, defaultOrderBy);
    } else if (params.strategy === PaginationStrategy.OFFSET && params.offset) {
      return this.applyOffsetPagination(baseQuery, table, params.offset, defaultOrderBy);
    } else {
      throw new Error('Invalid pagination parameters');
    }
  }

  /**
   * Cursor kodieren
   */
  private encodeCursor(
    field: string,
    value: any,
    id: string | number,
    direction: SortDirection
  ): string {
    const cursorData = {
      field,
      value,
      id,
      direction
    };
    return Buffer.from(JSON.stringify(cursorData)).toString('base64');
  }

  /**
   * Cursor dekodieren
   */
  private decodeCursor(cursor: string): CursorInfo {
    try {
      const decoded = Buffer.from(cursor, 'base64').toString('utf-8');
      return JSON.parse(decoded);
    } catch (error) {
      throw new Error('Invalid cursor format');
    }
  }

  /**
   * Drizzle-spezifische Cursor-Bedingung erstellen
   */
  private buildDrizzleCursorCondition(
    table: any,
    cursor: CursorInfo,
    isBefore: boolean,
    orderDirection: SortDirection
  ): SQL {
    const { field, value, id } = cursor;
    
    // Bestimme Vergleichsoperator basierend auf Richtung
    const isAscending = orderDirection === SortDirection.ASC;
    const useGreaterThan = isBefore ? !isAscending : isAscending;

    const fieldColumn = table[field];
    const idColumn = table.id;

    if (field === 'id') {
      return useGreaterThan ? gt(idColumn, id) : lt(idColumn, id);
    }

    // Für Nicht-ID-Felder: Kombiniere Feld-Wert und ID für eindeutige Sortierung
    const fieldCondition = useGreaterThan ? gt(fieldColumn, value) : lt(fieldColumn, value);
    const idCondition = and(
      eq(fieldColumn, value),
      useGreaterThan ? gt(idColumn, id) : lt(idColumn, id)
    );
    
    return or(fieldCondition, idCondition)!;
  }

  /**
   * Hilfsfunktionen
   */

  private validateLimit(limit?: number): number {
    if (limit === undefined) {
      return this.DEFAULT_LIMIT;
    }
    
    if (limit < 1) {
      throw new Error('Limit must be positive');
    }
    
    if (limit > this.MAX_LIMIT) {
      throw new Error(`Limit cannot exceed ${this.MAX_LIMIT}`);
    }
    
    return limit;
  }

  private reverseDirection(direction: SortDirection): SortDirection {
    return direction === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC;
  }

  /**
   * Utility-Funktionen für API-Handler
   */

  /**
   * Parse Cursor-Parameter aus Request-Query
   */
  parseCursorParams(query: any): CursorPaginationParams {
    return {
      first: query.first ? parseInt(query.first) : undefined,
      after: query.after || undefined,
      last: query.last ? parseInt(query.last) : undefined,
      before: query.before || undefined,
      orderBy: query.orderBy || undefined,
      orderDirection: query.orderDirection as SortDirection || undefined
    };
  }

  /**
   * Parse Offset-Parameter aus Request-Query
   */
  parseOffsetParams(query: any): OffsetPaginationParams {
    return {
      page: query.page ? parseInt(query.page) : undefined,
      limit: query.limit ? parseInt(query.limit) : undefined,
      offset: query.offset ? parseInt(query.offset) : undefined,
      orderBy: query.orderBy || undefined,
      orderDirection: query.orderDirection as SortDirection || undefined
    };
  }

  /**
   * Automatische Strategie-Erkennung
   */
  detectPaginationStrategy(query: any): PaginationStrategy {
    const hasCursorParams = query.first || query.after || query.last || query.before;
    const hasOffsetParams = query.page || query.limit || query.offset;
    
    if (hasCursorParams && !hasOffsetParams) {
      return PaginationStrategy.CURSOR;
    } else if (hasOffsetParams && !hasCursorParams) {
      return PaginationStrategy.OFFSET;
    } else if (hasCursorParams && hasOffsetParams) {
      // Bei Konflikt bevorzuge Cursor-Pagination
      return PaginationStrategy.CURSOR;
    } else {
      // Default zu Offset-Pagination
      return PaginationStrategy.OFFSET;
    }
  }

  /**
   * Parse alle Request-Parameter
   */
  parseRequestParams(query: any): PaginationParams {
    const strategy = this.detectPaginationStrategy(query);
    
    return {
      strategy,
      cursor: strategy === PaginationStrategy.CURSOR ? this.parseCursorParams(query) : undefined,
      offset: strategy === PaginationStrategy.OFFSET ? this.parseOffsetParams(query) : undefined
    };
  }

  /**
   * Performance-Empfehlungen basierend auf Datenvolumen
   */
  getPerformanceRecommendations(totalCount: number, currentLimit: number): string[] {
    const recommendations: string[] = [];
    
    if (totalCount > 10000 && currentLimit > 100) {
      recommendations.push('Consider using cursor-based pagination for large datasets');
    }
    
    if (currentLimit > 500) {
      recommendations.push('Large page sizes may impact performance');
    }
    
    if (totalCount > 100000) {
      recommendations.push('Consider implementing search/filtering to reduce dataset size');
    }
    
    return recommendations;
  }
}

/**
 * Drizzle Repository Pagination Helper
 */
export class DrizzleRepositoryPaginationHelper {
  private paginationService: DrizzlePaginationService;

  constructor() {
    this.paginationService = DrizzlePaginationService.getInstance();
  }

  /**
   * Erweitere Repository findAll-Methode mit Drizzle Pagination
   */
  async paginatedFindAll<T extends Record<string, any>>(
    table: any,
    baseWhere?: SQL,
    paginationParams?: PaginationParams,
    defaultOrderBy: string = 'id'
  ): Promise<CursorPaginatedResult<T> | OffsetPaginatedResult<T>> {
    let baseQuery: any = db.select().from(table);
    
    if (baseWhere) {
      baseQuery = baseQuery.where(baseWhere);
    }

    const params = paginationParams || {
      strategy: PaginationStrategy.OFFSET,
      offset: { limit: 50 }
    };

    return this.paginationService.applyPagination(baseQuery as SQLiteSelectQueryBuilder<any, any, any>, table, params, defaultOrderBy);
  }

  /**
   * Cache-Key für paginierte Queries
   */
  generatePaginationCacheKey(
    baseKey: string,
    paginationParams: PaginationParams
  ): string {
    const strategy = paginationParams.strategy;
    let paramString = '';

    if (strategy === PaginationStrategy.CURSOR && paginationParams.cursor) {
      const { first, after, last, before, orderBy, orderDirection } = paginationParams.cursor;
      paramString = `cursor:${first || ''}:${after || ''}:${last || ''}:${before || ''}:${orderBy || ''}:${orderDirection || ''}`;
    } else if (strategy === PaginationStrategy.OFFSET && paginationParams.offset) {
      const { page, limit, offset, orderBy, orderDirection } = paginationParams.offset;
      paramString = `offset:${page || ''}:${limit || ''}:${offset || ''}:${orderBy || ''}:${orderDirection || ''}`;
    }

    return `${baseKey}:paginated:${paramString}`;
  }
}

// Singleton instances für Drizzle ORM
export const drizzlePaginationService = DrizzlePaginationService.getInstance();
export const drizzleRepositoryPaginationHelper = new DrizzleRepositoryPaginationHelper();