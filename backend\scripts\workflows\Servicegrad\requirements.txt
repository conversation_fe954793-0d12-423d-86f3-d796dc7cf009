# Python-Abhängigkeiten für Servicegrad Workflow
# 
# Installation: pip install -r requirements.txt

# Excel-Automatisierung
xlwings>=0.30.0

# Datenverarbeitung
pandas>=2.0.0

# Windows COM-Objekte (für SAP GUI und Outlook)
pywin32>=306

# E-Mail-Funktionalität
# (bereits in pywin32 enthalten, aber explizit für Klarheit)

# Zusätzliche Abhängigkeiten falls benötigt
openpyxl>=3.1.0  # Für Excel-Dateien ohne xlwings
python-dateutil>=2.8.0  # Für erweiterte Datums-Funktionen

psycopg2-binary