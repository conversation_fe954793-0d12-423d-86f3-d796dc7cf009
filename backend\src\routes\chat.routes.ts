/**
 * Chat-Routen für die direkte Kommunikation mit dem KI-Assistenten
 * 
 * Diese Routen sind unter /api/chat verfügbar und leiten direkt an die KI-Dienste weiter.
 * Implementiert mit Drizzle ORM - Migration von Prisma abgeschlossen.
 */

import express from 'express';
import { z } from 'zod';
import { createValidationMiddleware } from '../middleware/validation.middleware';
import { rateLimitConfig } from '../middleware/rate-limiting.middleware';
import OpenRouterService from '../services/openrouter.service';

const router = express.Router();

// Validierungsschema für Chat-Anfragen
const chatRequestSchema = z.object({
  message: z.string().min(1, "Nachricht darf nicht leer sein").max(1000, "Nachricht zu lang"),
  includeInsights: z.boolean().optional().default(false),
  includeAnomalies: z.boolean().optional().default(false)
});

/**
 * POST /api/chat
 * Einfacher Chat-Endpunkt für direkte KI-Kommunikation
 * Identisch mit /api/ai/chat für Kompatibilität
 */
router.post('/', rateLimitConfig.general, createValidationMiddleware({ body: chatRequestSchema }), async (req, res) => {
  try {
    const { message } = req.body;
    
    console.log(`📝 [CHAT] Anfrage erhalten: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`); 
    
    // Anfrage an OpenRouter-Service weiterleiten
    const response = await OpenRouterService.generateResponse({
      message,
      includeInsights: false,
      includeAnomalies: false
    });
    
    console.log(`✅ [CHAT] Antwort gesendet für: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`); 
    
    res.json(response);
  } catch (error) {
    console.error('❌ [CHAT] Fehler:', error);
    res.status(500).json({
      success: false,
      error: 'Interner Serverfehler',
      message: 'Bei der Verarbeitung der Chat-Anfrage ist ein Fehler aufgetreten.'
    });
  }
});

/**
 * POST /api/chat/enhanced
 * Erweiterter Chat-Endpunkt mit Insights und Anomalien
 * Identisch mit /api/ai/chat/enhanced für Kompatibilität
 */
router.post('/enhanced', rateLimitConfig.general, createValidationMiddleware({ body: chatRequestSchema }), async (req, res) => {
  try {
    const { message, includeInsights, includeAnomalies } = req.body;
    
    console.log(`📝 [CHAT-ENHANCED] Anfrage erhalten: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}" (Insights: ${includeInsights}, Anomalien: ${includeAnomalies})`);
    
    // Anfrage an OpenRouter-Service weiterleiten
    const response = await OpenRouterService.generateResponse({
      message,
      includeInsights,
      includeAnomalies
    });
    
    console.log(`✅ [CHAT-ENHANCED] Antwort gesendet für: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`); 
    
    res.json(response);
  } catch (error) {
    console.error('❌ [CHAT-ENHANCED] Fehler:', error);
    res.status(500).json({
      success: false,
      error: 'Interner Serverfehler',
      message: 'Bei der Verarbeitung der erweiterten Chat-Anfrage ist ein Fehler aufgetreten.'
    });
  }
});

/**
 * GET /api/chat/status
 * Status-Endpunkt zur Überprüfung der Chat-Dienstverfügbarkeit
 */
router.get('/status', (req, res) => {
  res.json({
    success: true,
    message: 'Chat-Dienst ist verfügbar',
    timestamp: new Date().toISOString(),
    endpoints: [
      'POST /api/chat - Einfacher Chat',
      'POST /api/chat/enhanced - Erweiterter Chat mit Insights'
    ]
  });
});

export default router;
