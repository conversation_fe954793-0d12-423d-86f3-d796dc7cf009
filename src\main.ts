import { app, BrowserWindow } from "electron";
import registerListeners from "./helpers/ipc/listeners-register";
import path from "path";
import { spawn, ChildProcess } from "child_process";
import * as http from 'http';
import { installExtension, REACT_DEVELOPER_TOOLS } from "electron-devtools-installer";
import apiService from "./services/api.service";
import { setupPortablePaths } from "./portable-paths";
import { initializeConfig } from "./helpers/ipc/config/config-handlers";

// Diese Konstanten werden vom Vite-Plugin zur Verfügung gestellt und zur Laufzeit deklariert.
declare const MAIN_WINDOW_VITE_DEV_SERVER_URL: string;
declare const MAIN_WINDOW_VITE_NAME: string;

// Backend-Server Prozess
let backendProcess: ChildProcess | null = null;
const inDevelopment = process.env.NODE_ENV === "development";

// Globale Referenz auf das Hauptfenster, um es vor der Garbage Collection zu schützen
let mainWindow: BrowserWindow | null = null;

// Konfiguriere portable Pfade, wenn die App nicht im Entwicklungsmodus läuft
if (!inDevelopment || process.env.PORTABLE_EXECUTABLE_DIR) {
  const paths = setupPortablePaths();
  console.log('Portable Pfade konfiguriert:', paths);
}

/**
 * Überprüft die Verfügbarkeit des Backend-Servers
 */
const checkBackendHealth = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3001/api/health', {
      timeout: 8000, // Increase timeout
      headers: {
        'User-Agent': 'Electron-Main-Process',
        'Accept': 'application/json'
      }
    }, (res: http.IncomingMessage) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          try {
            const response = JSON.parse(data);
            console.log(`[SUCCESS] Backend Health-Check erfolgreich - Status: ${response.status}`);
            resolve(true);
          } catch (parseError) {
            console.warn('[WARNING] Backend Health-Check Response konnte nicht geparst werden');
            resolve(res.statusCode === 200); // Still consider it successful if status is 200
          }
        } else {
          console.warn(`[WARNING] Backend Health-Check fehlgeschlagen mit Status: ${res.statusCode}`);
          resolve(false);
        }
      });
    });

    req.on('error', (error: any) => {
      // More specific error handling
      if (error.code === 'ECONNREFUSED') {
        console.warn('[WARNING] Backend Health-Check: Verbindung verweigert (Server noch nicht bereit)');
      } else if (error.code === 'ETIMEDOUT') {
        console.warn('[WARNING] Backend Health-Check: Timeout');
      } else {
        console.error('[ERROR] Fehler beim Backend Health-Check:', error.message || error);
      }
      resolve(false);
    });

    req.on('timeout', () => {
      console.warn('[WARNING] Timeout beim Backend Health-Check (8s)');
      req.destroy();
      resolve(false);
    });
  });
};

/**
 * Startet den Backend-Server
 */
const startBackendServer = async (maxRetries = 8, retryDelay = 2000): Promise<boolean> => {
  console.log('[INFO] Attempting to start the backend server...');

  if (backendProcess) {
    console.warn('[WARNING] Backend process already exists. Attempting to stop it first...');
    await stopBackendServer();
  }

  // SPAWN THE PROCESS ONCE
  if (inDevelopment) {
    console.log('[INFO] Spawning backend with nodemon for development...');
    backendProcess = spawn('pnpm dlx', ['nodemon'], {
      cwd: path.join(__dirname, '..', '..', 'backend'),
      stdio: 'pipe',
      shell: true,
      detached: true,
      env: { ...process.env, NODE_ENV: 'development', PORT: '3001' },
    });
  } else {
    console.log('[INFO] Spawning backend for production...');
    // Use compiled JavaScript files in production
    const backendPath = path.join(__dirname, '..', '..', 'backend', 'dist', 'server.js');
    console.log(`[INFO] Backend path: ${backendPath}`);
    
    // Set up production environment variables
    const productionEnv = {
      ...process.env,
      NODE_ENV: 'production',
      PORT: '3001',
      API_PORT: '3001',
      API_SECRET_KEY: 'production-secret-key-for-portable-build-12345678901234567890123456789012',
      // PostgreSQL-Konfiguration für portable Builds
      DB_HOST: 'localhost',
      DB_PORT: '5434',
      DB_USER: 'leitstand_dashboard',
      DB_PASSWORD: 'dashboard_password',
      DB_NAME: 'leitstand_dashboard',
      DB_SSL: 'false',
      OPENROUTER_API_KEY: '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',
      OPENROUTER_PRESET_MODEL: '@preset/lapp',
      // Disable strict validation in portable builds
      SKIP_ENV_VALIDATION: 'true'
    };
    
    // Try different approaches to find Node.js
    let nodeExecutable = 'node'; // Default fallback
    
    // Check if we can find node.exe in the Electron app directory
    const electronDir = path.dirname(process.execPath);
    const possibleNodePaths = [
      path.join(electronDir, 'node.exe'),
      path.join(electronDir, '..', 'node.exe'),
      path.join(electronDir, 'resources', 'node.exe'),
      'node' // System node as fallback
    ];
    
    for (const nodePath of possibleNodePaths) {
      try {
        if (nodePath !== 'node' && require('fs').existsSync(nodePath)) {
          nodeExecutable = nodePath;
          console.log(`[INFO] Found Node.js at: ${nodeExecutable}`);
          break;
        }
      } catch (e) {
        // Continue to next path
      }
    }
    
    console.log(`[INFO] Using Node executable: ${nodeExecutable}`);
    
    // Use Node.js to run the backend
    backendProcess = spawn(nodeExecutable, [backendPath], {
      cwd: path.join(__dirname, '..', '..', 'backend'),
      stdio: 'pipe',
      shell: true, // Use shell to help with path resolution
      env: productionEnv,
    });
  }

  // Track backend readiness
  let backendReady = false;
  
  // Attach listeners immediately
  backendProcess.stdout?.on('data', (data) => {
    const output = data.toString().trim();
    console.log(`[BACKEND] ${output}`);
    
    // Look for the server ready message
    if (output.includes('✅ Server läuft auf Port') || output.includes('Server läuft auf Port')) {
      backendReady = true;
      console.log('[INFO] Backend server ready signal detected');
    }
  });
  
  backendProcess.stderr?.on('data', (data) => console.error(`[BACKEND-ERROR] ${data.toString().trim()}`));
  backendProcess.on('error', (error) => console.error('[ERROR] Backend process spawn error:', error));
  backendProcess.on('exit', (code, signal) => {
    if (code !== 0 && signal !== 'SIGTERM') {
      console.error(`[ERROR] Backend process exited unexpectedly with code: ${code}, signal: ${signal}`);
    }
  });

  // Wait for initial backend startup (give it time to initialize)
  console.log('[INFO] Waiting for backend to initialize...');
  await new Promise(resolve => setTimeout(resolve, 5000));

  // RETRY LOOP FOR HEALTH CHECK
  for (let i = 0; i < maxRetries; i++) {
    console.log(`[INFO] Performing health check, attempt ${i + 1}/${maxRetries}...`);
    try {
      if (await checkBackendHealth()) {
        console.log('[SUCCESS] Backend server is healthy and running.');
        return true;
      }
    } catch (error) {
      console.warn(`[WARNING] Health check attempt ${i + 1} failed:`, error);
    }
    
    // Shorter delay between retries, but more attempts
    await new Promise(resolve => setTimeout(resolve, retryDelay));
  }

  console.error(`[FATAL] Backend server failed to start after ${maxRetries} attempts.`);
  return false;
};

/**
 * Stoppt den Backend-Server
 */
const stopBackendServer = async (): Promise<void> => {
  if (!backendProcess) {
    console.log('[WARNING] Kein aktiver Backend-Prozess zum Stoppen gefunden');
    return;
  }
  
  console.log('[INFO] Stoppe Backend-Server...');
  
  try {
    // Versuche, das Backend über eine API-Anfrage ordnungsgemäß herunterzufahren
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    try {
      await fetch('http://localhost:3001/api/shutdown', {
        method: 'POST',
        signal: controller.signal
      });
      console.log('[SUCCESS] Backend-Server wurde ordnungsgemäß heruntergefahren');
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.warn('[WARNING] Timeout beim ordnungsgemäßen Herunterfahren des Backend-Servers');
      } else {
        console.warn('[WARNING] Fehler beim ordnungsgemäßen Herunterfahren des Backend-Servers:', error);
      }
    } finally {
      clearTimeout(timeoutId);
    }
    
    // Falls der Prozess noch läuft, beende ihn gewaltsam
    if (backendProcess && !backendProcess.killed) {
      const pid = backendProcess.pid;
      if (pid) {
        console.log(`[INFO] Attempting to kill backend process with PID: ${pid}...`);
        
        if (process.platform === "win32") {
          spawn("taskkill", ["/pid", pid.toString(), "/f", "/t"]);
          console.log(`[SUCCESS] Sent taskkill command to process tree with PID: ${pid}`);
        } else {
          process.kill(-pid, 'SIGKILL');
          console.log(`[SUCCESS] Killed process group with PID: ${pid}`);
        }
      } else {
          console.warn('[WARNING] Backend process has no PID, cannot kill.');
      }
    }
    
  } catch (error) {
    console.error('[ERROR] Fehler beim Stoppen des Backend-Servers:', error);
  } finally {
    backendProcess = null;
  }
};

/**
 * Erstellt das Hauptfenster der Anwendung
 */
async function createWindow(): Promise<BrowserWindow> {
  console.log('[INFO] Erstelle Hauptfenster...');
  
  // Erstelle das Browser-Fenster.
  const window = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: !inDevelopment,
      preload: path.join(__dirname, 'preload.js'),
    },
    show: false, // Fenster erst nach dem Laden anzeigen
    fullscreen: false // Starte nicht im Vollbildmodus
  });
  
  // Lade die App im Entwicklungsmodus von der Vite-Entwicklungsumgebung
  // und im Produktionsmodus aus der Datei
  // Lade die App im Entwicklungsmodus von der Vite-Entwicklungsumgebung
  // und im Produktionsmodus aus der Datei.
  if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
    console.log(`[INFO] Lade Renderer von Dev-Server: ${MAIN_WINDOW_VITE_DEV_SERVER_URL}`);
    await window.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
    // DevTools können mit F12 oder Ctrl+Shift+I geöffnet werden
    // window.webContents.openDevTools();
  } else {
    const filePath = path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`);
    console.log(`[INFO] Lade Renderer aus Datei: ${filePath}`);
    // Anhängen eines Hashes, damit Hash-Routing (#/) im file:// Kontext korrekt startet
    await window.loadFile(filePath, { hash: '/' });
  }
  
  // Zeige das Fenster maximiert, sobald alles geladen ist
  window.once('ready-to-show', () => {
    window.show();
    window.maximize(); // Maximiere das Fenster (nicht Vollbild)
    window.focus();
    console.log('[SUCCESS] Hauptfenster ist bereit und maximiert sichtbar');
  });
  
  // Fallback: Zeige das Fenster nach 3 Sekunden, falls ready-to-show nicht ausgelöst wird
  setTimeout(() => {
    if (!window.isVisible()) {
      console.log('[INFO] Fallback: Zeige Fenster nach Timeout');
      window.show();
      window.focus();
    }
  }, 3000);
  
  return window;
};

/**
 * Installiert die Entwicklertools im Entwicklungsmodus
 */
async function installExtensions() {
  if (!inDevelopment) return;
  
  console.log('[INFO] Installiere Entwicklertools...');
  
  try {
    const extensions = [REACT_DEVELOPER_TOOLS];
    for (const extension of extensions) {
      try {
        const name = await installExtension(extension);
        console.log(`[SUCCESS] Erweiterung installiert: ${name}`);
      } catch (error) {
        console.warn(`[WARNING] Konnte Erweiterung nicht installieren:`, error);
      }
    }
  } catch (error) {
    console.error('[ERROR] Fehler beim Installieren der Entwicklertools:', error);
  }
}

/**
 * Hauptfunktion zur Initialisierung der Anwendung
 */
async function main() {
  try {
    console.log('[INFO] Starte Anwendung...');
    
    // Initialisiere sichere Konfiguration
    initializeConfig({
      apiKey: 'sfm_api_a7c4f2e8b9d6c3a1f5e9b2d7c4a8f6e3b1d9c5a7f2e8b4d6c9a3f7e1b5d2c8a4f6e9b3d7c1a5f8e2b6d4c',
      apiBaseUrl: 'http://localhost:3001/api',
      environment: inDevelopment ? 'development' : 'production',
      version: '1.0.0'
    });
    
    // Starte Backend Server
    const backendStarted = await startBackendServer();
    if (!backendStarted) {
      throw new Error('Backend konnte nicht gestartet werden');
    }
    
    // Initialisiere die Datenbank (nicht blockierend)
    console.log('[INFO] Starte Datenbankinitialisierung im Hintergrund...');
    
    const initDatabase = async () => {
      try {
        console.log('[INFO] Initialisiere Datenbankverbindung...');
        
        // Timeout für die Datenbankinitialisierung
        // API Service benötigt keine separate Initialisierung
        const initPromise = Promise.resolve();
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Datenbankinitialisierung Timeout nach 10 Sekunden')), 10000)
        );
        
        await Promise.race([initPromise, timeoutPromise]);
        console.log('[SUCCESS] Datenbank erfolgreich initialisiert');
        
        // Teste die API-Verbindung mit Timeout
        console.log('[INFO] Teste API-Verbindung...');
        try {
          const testPromise = apiService.getServiceLevelData();
          const testTimeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('API-Test Timeout nach 5 Sekunden')), 5000)
          );

          const data = await Promise.race([testPromise, testTimeoutPromise]);
          console.log(`[SUCCESS] Service-Level-Daten verfügbar: ${Array.isArray(data) ? data.length : 0} Einträge`);
          return data || [];
        } catch (testError) {
          console.warn('[WARNING] Konnte Service-Level-Daten nicht abrufen, fahre trotzdem fort:', testError instanceof Error ? testError.message : testError);
          return [];
        }
      } catch (error) {
        console.warn('[WARNING] Datenbankinitialisierung fehlgeschlagen, fahre trotzdem fort:', error instanceof Error ? error.message : error);
        return [];
      }
    };
    
    // Starte die Initialisierung im Hintergrund, blockiere aber nicht den Hauptthread
    initDatabase().catch(error => {
      console.error('[ERROR] Fehler bei der Hintergrundinitialisierung der Datenbank:', error instanceof Error ? error.message : error);
    });
    
    // Erstelle das Hauptfenster
    mainWindow = await createWindow();
    
    if (!mainWindow) {
      throw new Error('Fehler beim Erstellen des Hauptfensters');
    }
    
    // Installiere Entwicklertools im Entwicklungsmodus
    await installExtensions();
    
    // Registriere IPC-Handler
    registerListeners(mainWindow);
    
    console.log('[SUCCESS] Anwendung erfolgreich gestartet');
    
  } catch (error) {
    console.error('[ERROR] Kritischer Fehler beim Starten der Anwendung:', error);
    
    // Versuche, eine Fehlermeldung anzuzeigen, falls möglich
    try {
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('app:error', {
          message: 'Kritischer Fehler',
          details: error instanceof Error ? error.message : 'Unbekannter Fehler',
          stack: error instanceof Error ? error.stack : undefined
        });
      }
    } catch (e) {
      console.error('Konnte Fehlermeldung nicht anzeigen:', e);
    }
    
    // Warte kurz, damit die Meldung angezeigt werden kann
    setTimeout(() => {
      app.quit();
    }, 1000);
  }
}

// Event-Handler für das Beenden der Anwendung
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    console.log('[INFO] Alle Fenster geschlossen, beende Anwendung...');
    app.quit();
  }
});

// Event-Handler für das Beenden der Anwendung (vor dem eigentlichen Beenden)
app.on('will-quit', async (event) => {
  console.log('[INFO] Beende Anwendung...');
  try {
    await stopBackendServer();
    console.log('[SUCCESS] Backend-Server erfolgreich gestoppt');
  } catch (error) {
    console.error('[ERROR] Fehler beim Stoppen des Backend-Servers:', error);
  }
});

// macOS-spezifischer Event-Handler
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    console.log('[INFO] Erstelle neues Fenster (macOS)...');
    createWindow();
  }
});

// Behandele nicht abgefangene Ausnahmen
process.on('uncaughtException', (error) => {
  console.error('[ERROR] Nicht abgefangener Fehler:', error);
  if (error instanceof Error && error.stack) {
    console.error('Stack Trace:', error.stack);
  }
  if (!(error instanceof Error) || error.name !== 'AbortError') {
    console.error('[ERROR] Kritischer Fehler - Anwendung wird beendet');
    process.exit(1);
  }
});

// Behandele nicht behandelte Promise-Ablehnungen
process.on('unhandledRejection', (reason, promise) => {
  console.error('[ERROR] Nicht behandelte Promise-Ablehnung:');
  console.error('Grund:', reason);
  
  if (reason instanceof Error) {
    console.error('Fehlermeldung:', reason.message);
    if (reason.stack) {
      console.error('Stack Trace:', reason.stack);
    }
  }
  
  console.error('Promise:', promise);
  
  if (!(reason instanceof Error) || reason.name !== 'AbortError') {
    console.error('[ERROR] Kritischer Fehler - Anwendung wird beendet');
    process.exit(1);
  }
});

// Starte die Anwendung
app.whenReady().then(main).catch(error => {
  console.error('[ERROR] Fehler beim Starten der Anwendung:', error);
  process.exit(1);
});
