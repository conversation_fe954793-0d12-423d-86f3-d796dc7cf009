# BigDatePicker Komponente

Eine vollständig funktionsfähige TypeScript React-Komponente mit Tailwind CSS und shadcn/UI, die eine elegante Kalender-Auswahl mit charakteristischer Flip-Animation bietet.

## Features

✅ **1:1 Design-Nachbildung** der ursprünglichen HTML-Komponente  
✅ **3D Flip-Animation** be<PERSON> (von oben nach unten)  
✅ **Deutsche Lokalisierung** (Monate, Wochentage)  
✅ **Toggle-Funktion** für Kalender ein-/ausblenden  
✅ **Interaktive Datumsauswahl** mit visueller Hervorhebung  
✅ **Monatsnavigation** mit Pfeil-Buttons  
✅ **TypeScript Support** mit vollständiger Typisierung  
✅ **Tailwind CSS** für modernes, responsives Design  
✅ **shadcn/UI Integration** für konsistente UI-Komponenten  

## Technische Details

### Abhängigkeiten
- React 18+
- TypeScript
- Tailwind CSS
- shadcn/UI (Button Komponente)
- Lucide React (Icons)

### Animation
Die charakteristische Flip-Animation wurde entsprechend der Memory-Anforderungen implementiert:
- Realistische Simulation eines Kalender-Umblätterns von oben nach unten
- CSS 3D-Transformation mit `rotateX(-90deg)`
- Animationsdauer: 0.8s mit `cubic-bezier` Übergang
- Transform-Origin am unteren Rand für korrekte Rotationsrichtung

### Farbpalette
- Hintergrund: `bg-slate-100` (#F0F2F9)
- Hauptfarbe: `bg-indigo-500` (#6966FF)
- Dunkler Text: `text-slate-700` (#343D4F)
- Heller Text: `text-white` (#FFFFFF)
- Grauer Text: `text-slate-400` (#A0A5B1)

## Installation

```bash
# shadcn/UI Button Komponente installieren (falls noch nicht vorhanden)
npx shadcn-ui@latest add button

# Lucide React für Icons
npm install lucide-react
```

## Verwendung

```tsx
import BigDatePicker from './BigDatePicker'

function App() {
  const handleDateChange = (date: Date) => {
    console.log('Neues Datum:', date)
  }

  return (
    <BigDatePicker
      initialDate={new Date(2025, 8, 3)} // 3. September 2025
      onDateChange={handleDateChange}
      className="custom-styles"
    />
  )
}
```

## Props

| Prop | Typ | Standard | Beschreibung |
|------|-----|----------|--------------|
| `initialDate` | `Date` | `new Date(2025, 8, 3)` | Startdatum der Komponente |
| `onDateChange` | `(date: Date) => void` | `undefined` | Callback bei Datumsänderung |
| `className` | `string` | `undefined` | Zusätzliche CSS-Klassen |

## Besonderheiten

### Deutsche Lokalisierung
Entsprechend der Projektspezifikation werden alle Monate und Wochentage auf Deutsch angezeigt:
- Vollständige Monatsnamen: "JANUAR", "FEBRUAR", "MÄRZ", etc.
- Kurze Monatsnamen: "Jan", "Feb", "Mär", etc.  
- Wochentage: "So", "Mo", "Di", "Mi", "Do", "Fr", "Sa"

### Animation-Logik
Die Flip-Animation wird nur bei tatsächlichen Datumsänderungen ausgelöst und verhindert Doppel-Animationen durch einen `isAnimating` State.

### Responsive Design
Die Komponente ist vollständig responsiv und passt sich verschiedenen Bildschirmgrößen an.

## Struktur

```
DatePicker/
├── BigDatePicker.tsx      # Hauptkomponente
├── types.ts               # TypeScript Definitionen
├── DatePickerExample.tsx  # Verwendungsbeispiel
└── README.md             # Diese Dokumentation
```

## Browser-Unterstützung

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

Die CSS 3D-Transformationen werden von allen modernen Browsern unterstützt.