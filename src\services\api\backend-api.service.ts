/**
 * Backend API Service
 * 
 * Ersetzt den direkten Datenbankzugriff im Frontend durch API-Aufrufe an das Backend.
 * Alle Datenbankoperationen werden über das Backend-API abgewickelt.
 */

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

/**
 * Interface für System FTS Daten
 */
export interface SystemFTSDataPoint {
  id: number;
  Datum: string;
  verfuegbarkeitFTS: number;
}

/**
 * Interface für Knowledge Base Daten
 */
export interface KnowledgeBase {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface für Service Health
 */
export interface ServiceHealth {
  status: 'healthy' | 'unhealthy';
  responseTime?: number;
  cache?: any;
  databases?: string[];
  version?: string;
  error?: string;
}

/**
 * Backend API Service Klasse
 * 
 * Stellt alle Methoden bereit, die zuvor im Frontend-DatabaseService verfügbar waren,
 * aber jetzt über HTTP-API-Aufrufe an das Backend.
 */
export class BackendApiService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Generische API-Aufruf-Methode
   */
  private async apiCall<T>(endpoint: string, options?: RequestInit): Promise<T> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`API call failed for ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Testet die Verbindung zum Backend
   */
  async connect(): Promise<boolean> {
    try {
      const health = await this.getServiceHealth();
      return health.status === 'healthy';
    } catch (error) {
      console.error('Backend connection test failed:', error);
      return false;
    }
  }

  /**
   * System FTS Verfügbarkeitsdaten abrufen
   */
  async getSystemFTSData(dateRange?: { startDate?: string; endDate?: string }): Promise<SystemFTSDataPoint[]> {
    const params = new URLSearchParams();
    if (dateRange?.startDate) params.append('startDate', dateRange.startDate);
    if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
    
    const queryString = params.toString();
    const endpoint = `/system-fts-data${queryString ? `?${queryString}` : ''}`;
    
    return this.apiCall<SystemFTSDataPoint[]>(endpoint);
  }

  /**
   * Knowledge Bases abrufen (RAG Database)
   */
  async getKnowledgeBases(): Promise<KnowledgeBase[]> {
    return this.apiCall<KnowledgeBase[]>('/knowledge-bases');
  }

  /**
   * Service Health Status abrufen
   */
  async getServiceHealth(): Promise<ServiceHealth> {
    return this.apiCall<ServiceHealth>('/health');
  }

  /**
   * System-Statistiken abrufen
   */
  async getSystemStats(): Promise<any> {
    return this.apiCall<any>('/system-stats');
  }

  /**
   * Cache-Statistiken abrufen
   */
  async getCacheStats(): Promise<any> {
    return this.apiCall<any>('/cache-stats');
  }

  /**
   * Materialdaten abrufen
   */
  async getMaterialdaten(): Promise<any[]> {
    return this.apiCall<any[]>('/materialdaten');
  }

  /**
   * Trommeldaten abrufen
   */
  async getTrommeldaten(): Promise<any[]> {
    return this.apiCall<any[]>('/trommeldaten');
  }

  /**
   * Service Level Daten abrufen
   */
  async getServiceLevelData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const params = new URLSearchParams();
    if (dateRange?.startDate) params.append('startDate', dateRange.startDate);
    if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
    
    const queryString = params.toString();
    const endpoint = `/service-level${queryString ? `?${queryString}` : ''}`;
    
    return this.apiCall<any[]>(endpoint);
  }

  /**
   * Picking Daten abrufen
   */
  async getPickingData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const params = new URLSearchParams();
    if (dateRange?.startDate) params.append('startDate', dateRange.startDate);
    if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
    
    const queryString = params.toString();
    const endpoint = `/picking${queryString ? `?${queryString}` : ''}`;
    
    return this.apiCall<any[]>(endpoint);
  }

  /**
   * Returns Daten abrufen
   */
  async getReturnsData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const params = new URLSearchParams();
    if (dateRange?.startDate) params.append('startDate', dateRange.startDate);
    if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
    
    const queryString = params.toString();
    const endpoint = `/returns${queryString ? `?${queryString}` : ''}`;
    
    return this.apiCall<any[]>(endpoint);
  }

  /**
   * Delivery Positions Daten abrufen
   */
  async getDeliveryPositionsData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const params = new URLSearchParams();
    if (dateRange?.startDate) params.append('startDate', dateRange.startDate);
    if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
    
    const queryString = params.toString();
    const endpoint = `/delivery-positions${queryString ? `?${queryString}` : ''}`;
    
    return this.apiCall<any[]>(endpoint);
  }

  /**
   * Tagesleistung Daten abrufen
   */
  async getTagesleistungData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const params = new URLSearchParams();
    if (dateRange?.startDate) params.append('startDate', dateRange.startDate);
    if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
    
    const queryString = params.toString();
    const endpoint = `/tagesleistung${queryString ? `?${queryString}` : ''}`;
    
    return this.apiCall<any[]>(endpoint);
  }

  /**
   * Ablaengerei Daten abrufen
   */
  async getAblaengereiData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const params = new URLSearchParams();
    if (dateRange?.startDate) params.append('startDate', dateRange.startDate);
    if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
    
    const queryString = params.toString();
    const endpoint = `/ablaengerei${queryString ? `?${queryString}` : ''}`;
    
    return this.apiCall<any[]>(endpoint);
  }

  /**
   * WE Daten abrufen
   */
  async getWEData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const params = new URLSearchParams();
    if (dateRange?.startDate) params.append('startDate', dateRange.startDate);
    if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
    
    const queryString = params.toString();
    const endpoint = `/we-data${queryString ? `?${queryString}` : ''}`;
    
    return this.apiCall<any[]>(endpoint);
  }

  /**
   * Auslastung 200 Daten abrufen
   */
  async getAuslastung200Data(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const params = new URLSearchParams();
    if (dateRange?.startDate) params.append('startDate', dateRange.startDate);
    if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
    
    const queryString = params.toString();
    const endpoint = `/auslastung200${queryString ? `?${queryString}` : ''}`;
    
    return this.apiCall<any[]>(endpoint);
  }

  /**
   * Auslastung 240 Daten abrufen
   */
  async getAuslastung240Data(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const params = new URLSearchParams();
    if (dateRange?.startDate) params.append('startDate', dateRange.startDate);
    if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
    
    const queryString = params.toString();
    const endpoint = `/auslastung240${queryString ? `?${queryString}` : ''}`;
    
    return this.apiCall<any[]>(endpoint);
  }

  /**
   * ARiL Daten abrufen
   */
  async getArilData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const params = new URLSearchParams();
    if (dateRange?.startDate) params.append('startDate', dateRange.startDate);
    if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
    
    const queryString = params.toString();
    const endpoint = `/aril-data${queryString ? `?${queryString}` : ''}`;
    
    return this.apiCall<any[]>(endpoint);
  }

  /**
   * ATrL Daten abrufen
   */
  async getAtrlData(dateRange?: { startDate?: string; endDate?: string }): Promise<any[]> {
    const params = new URLSearchParams();
    if (dateRange?.startDate) params.append('startDate', dateRange.startDate);
    if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
    
    const queryString = params.toString();
    const endpoint = `/atrl-data${queryString ? `?${queryString}` : ''}`;
    
    return this.apiCall<any[]>(endpoint);
  }
}

// Export singleton instance
export const backendApiService = new BackendApiService();
export default backendApiService;
