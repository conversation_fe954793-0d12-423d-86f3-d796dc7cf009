/**
 * TypeScript-Definitionen für das erweiterte Drag-and-Drop-System
 * 
 * Diese Datei enthält alle Typen und Interfaces für:
 * - Kartengrößen
 * - KPI-Bibliothek
 * - Edit-Modus-Funktionalität
 */

/**
 * Interface für verfügbare KPI-Karten in der Bibliothek
 */
export interface AvailableKPICard {
  /** Eindeutige ID der KPI-Karte */
  id: string;
  /** Anzeigename der KPI-Karte */
  name: string;
  /** Titel der KPI-Karte (optional, falls abweichend vom Namen) */
  title?: string;
  /** Beschreibung der KPI-Karte */
  description: string;
  /** Kategorie der KPI-Karte für bessere Organisation */
  category: 'production' | 'logistics' | 'quality' | 'efficiency' | 'wareneingang' | 'dispatch' | 'ablaengerei';
  /** Ob die Karte aktuell aktiv/sichtbar ist */
  isActive: boolean;
}

/**
 * Konfiguration für den Edit-Toggle-Button
 */
export interface EditToggleConfig {
  /** Position des Toggle-Buttons */
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  /** Ob der Button sichtbar ist */
  visible: boolean;
  /** Custom Label für den Button */
  label?: string;
  /** Custom Icon für den Button */
  icon?: React.ComponentType;
}

/**
 * Typ für die addCard-Funktion
 */
export type AddCardFunction = (cardId: string, position?: number) => void;

/**
 * Kategorien für KPI-Karten mit deutschen Labels
 */
export const KPI_CATEGORIES = {
  production: 'Produktion',
  logistics: 'Logistik', 
  quality: 'Qualität',
  efficiency: 'Effizienz'
} as const;

/**
 * Standard-KPI-Karten die in der Bibliothek verfügbar sind
 */
export const DEFAULT_AVAILABLE_CARDS: AvailableKPICard[] = [
  {
    id: 'wareneingang',
    name: 'Wareneingang',
    description: 'Überwachung der eingehenden Waren und Materialien',
    category: 'logistics',
    isActive: true
  },
  {
    id: 'tonnage',
    name: 'Tonnage',
    description: 'Gesamtgewicht der verarbeiteten Materialien',
    category: 'production',
    isActive: true
  },
  {
    id: 'performance',
    name: 'Performance',
    description: 'Gesamtleistung der Produktionsanlagen',
    category: 'production',
    isActive: true
  },
  {
    id: 'efficiency',
    name: 'Effizienz',
    description: 'Effizienz der Produktionsprozesse',
    category: 'efficiency',
    isActive: true
  },
  {
    id: 'quality',
    name: 'Qualität',
    description: 'Qualitätsrate der produzierten Waren',
    category: 'quality',
    isActive: true
  },
  {
    id: 'delivery',
    name: 'Lieferleistung',
    description: 'Lieferperformance in Prozent',
    category: 'logistics',
    isActive: true
  },
  {
    id: 'picking',
    name: 'Picking',
    description: 'Picking-Performance Anzahl Positionen',
    category: 'logistics',
    isActive: true
  },
  {
    id: 'automatisierung',
    name: 'Automatisierung',
    description: 'Automatisierte Wareneingangspositionen',
    category: 'logistics',
    isActive: true
  },
  {
    id: 'produktion',
    name: 'Produktion',
    description: 'Anzahl Schnitte in der Ablängerei',
    category: 'production',
    isActive: true
  },
  {
    id: 'lager',
    name: 'Lager',
    description: 'Umschlag im Lagerbereich',
    category: 'logistics',
    isActive: true
  },
  {
    id: 'versand',
    name: 'Versand',
    description: 'Direktverladung Kiaa',
    category: 'logistics',
    isActive: true
  },
  {
    id: 'service',
    name: 'Service',
    description: 'Anzahl Elefanten',
    category: 'logistics',
    isActive: true
  }
];