import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { DatabaseService } from './services/database.service';
import databaseRoutes from './routes/database.routes';
import systemRoutes from './routes/system.routes';
import arilDebugRoutes from './routes/aril-debug.routes';
import performanceRoutes from './routes/performance.routes';
import performanceDbRoutes from './routes/performance-db.routes';
import errorMonitoringRoutes from './routes/error-monitoring.routes';
import paginatedDataRoutes from './routes/paginated-data.routes';
import workflowRoutes from './routes/workflow.routes';
import stoerungenRoutes from './routes/stoerungen.routes';
import aiRoutes from './routes/ai.routes';
import aiHealthRoutes from './routes/ai-health.routes';
import chatRoutes from './routes/chat.routes';
import authRoutes from './routes/auth.routes';
import bereitschaftsRoutes from './routes/bereitschaftsRoutes';
import dashboardRoutes from './routes/dashboardRoutes';
import ragRoutes from './routes/ragRoutes';
import inventoryRoutes from './routes/inventory.routes';
import availableDrumsRoutes from './routes/available-drums.routes';
import emailRoutes from './routes/email.routes';
import runbookRoutes from './routes/runbookRoutes';
import kpiRoutes from './routes/kpi.routes';
import deliveryRoutes from './routes/delivery.routes';
import productionRoutes from './routes/production.routes';
import supplierRoutes from './routes/supplier.routes';
import userRoutes from './routes/user.routes';
import { validateEnvironment, logEnvironmentInfo } from './utils/env.validation';
import { createAuthMiddleware, ipDetectionMiddleware } from './middleware/auth.middleware';
import { rateLimitConfig } from './middleware/rate-limiting.middleware';
import { performanceTrackingMiddleware } from './middleware/performance-tracking.middleware';
import { initializeRepositoryFactory } from './repositories/repository.factory';
import path from 'path';
// import warehouseRoutes from './routes/warehouse.routes';

// Validiere Umgebungsvariablen beim Start
const env = validateEnvironment();
logEnvironmentInfo(env);

// Create app instance for export
const app = express();

async function startServer() {
const port = env.API_PORT;

// Security-Middleware
app.use(helmet({
  // Konfiguriere Helmet für Electron-Kompatibilität
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"], // Notwendig für Vite dev
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws:", "wss:"] // WebSocket für Vite dev
    }
  },
  crossOriginEmbedderPolicy: false // Für Electron-Kompatibilität
}));

// CORS-Konfiguration mit produktionstauglichen Origins
const allowedOrigins = env.NODE_ENV === 'production'
  ? ['electron://localhost', 'https://localhost:3000'] // Produktions-Origins
  : true; // Entwicklung: alle Origins erlaubt

app.use(cors({
  origin: allowedOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
}));

// Request-Parsing mit Sicherheitsbegrenzungen
app.use(express.json({
  limit: '10mb',
  type: ['application/json', 'text/plain']
}));
app.use(express.urlencoded({
  extended: true,
  limit: '10mb',
  parameterLimit: 100 // Begrenze Parameter-Anzahl
}));

// Security Event Logging Middleware (Alternative Implementation)
app.use((req, res, next) => {
  const start = Date.now();

  // Log request
  console.log(`📝 [REQUEST] ${new Date().toISOString()} - ${req.method} ${req.path} - IP: ${req.ip || 'unknown'}`);

  // Hook into response finish event
  res.on('finish', () => {
    const duration = Date.now() - start;
    const statusCode = res.statusCode;

    // Log security-relevant events
    if (statusCode === 401 || statusCode === 403) {
      console.log(`🚨 [SECURITY] ${new Date().toISOString()} - ${req.method} ${req.path} - Status: ${statusCode} - IP: ${req.ip || 'unknown'} - Duration: ${duration}ms`);
    } else if (statusCode >= 400) {
      console.log(`⚠️  [ERROR] ${new Date().toISOString()} - ${req.method} ${req.path} - Status: ${statusCode} - IP: ${req.ip || 'unknown'} - Duration: ${duration}ms`);
    } else if (req.path.startsWith('/api/') && statusCode === 200) {
      console.log(`✅ [ACCESS] ${new Date().toISOString()} - ${req.method} ${req.path} - Status: ${statusCode} - IP: ${req.ip || 'unknown'} - Duration: ${duration}ms`);
    }
  });

  next();
});

// Statische Dateien für Uploads bereitstellen
// Uploads-Ordner für Störungsbilder, Avatare und andere Dateien
app.use('/api/uploads', express.static(path.join(process.cwd(), 'uploads')));
app.use('/api/uploads/stoerungen', express.static(path.join(process.cwd(), 'uploads', 'stoerungen')));
app.use('/api/uploads/avatars', express.static(path.join(process.cwd(), 'uploads', 'avatars')));
console.log('📁 Statische Dateien werden bereitgestellt unter /api/uploads, /api/uploads/stoerungen und /api/uploads/avatars');

// IP-Erkennung und Basis-Rate-Limiting
app.use(ipDetectionMiddleware);
app.use(rateLimitConfig.general); // Globales Rate-Limiting

// Performance-Tracking für alle API-Requests
app.use(performanceTrackingMiddleware);

// Authentifizierungs-Middleware für alle API-Routen
const authMiddleware = createAuthMiddleware(env.API_SECRET_KEY);

// Database-Service initialisieren
console.log('🏭 Initialisiere Database Service mit Drizzle...');
const dbService = new DatabaseService();
const isConnected = await dbService.connect();
if (!isConnected) {
  throw new Error('Database connection failed');
}
console.log('✅ Database Service initialisiert');

// Repository Factory initialisieren
console.log('🏭 Initialisiere Repository Factory...');
try {
  initializeRepositoryFactory();
  console.log('✅ Repository Factory erfolgreich initialisiert');
} catch (error) {
  console.error('❌ Fehler bei der Repository Factory Initialisierung:', error);
  throw error;
}

console.log('🚀 Server startet...');

// Öffentliche Routen (ohne Authentifizierung, aber mit Rate-Limiting)
app.get('/api/health', rateLimitConfig.healthCheck, (req, res) => {
    console.log('✅ Health-Check aufgerufen');
    res.json({
        status: 'ok',
        message: 'Backend läuft!',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: env.NODE_ENV,
        security: {
            authentication: 'enabled',
            rateLimit: 'enabled',
            validation: 'enabled'
        }
    });
});

// Erweiterte Health-Check mit Datenbankstatus
app.get('/api/health/detailed', rateLimitConfig.healthCheck, async (req, res) => {
    try {
        // Teste Datenbankverbindung
        const testResult = await dbService.connect();

        res.json({
            status: 'ok',
            message: 'Backend und Datenbank sind verfügbar',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            environment: env.NODE_ENV,
            services: {
                database: 'healthy',
                api: 'healthy'
            },
            security: {
                authentication: 'enabled',
                rateLimit: 'enabled',
                validation: 'enabled'
            }
        });
    } catch (error) {
        console.error('❌ Datenbankfehler im Health-Check:', error);
        res.status(503).json({
            status: 'degraded',
            message: 'Backend läuft, aber Datenbankverbindung fehlgeschlagen',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            environment: env.NODE_ENV,
            services: {
                database: 'unhealthy',
                api: 'healthy'
            },
            error: env.NODE_ENV === 'development' ? (error as Error).message : 'Datenbankfehler'
        });
    }
});

// System-Metriken Endpunkt für Monitoring
app.get('/api/metrics', rateLimitConfig.healthCheck, (req, res) => {
    const memUsage = process.memoryUsage();
    const uptime = process.uptime();

    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        system: {
            uptime: Math.floor(uptime),
            memory: {
                rss: Math.round(memUsage.rss / 1024 / 1024), // MB
                heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
                heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
                external: Math.round(memUsage.external / 1024 / 1024) // MB
            },
            nodejs: process.version,
            platform: process.platform,
            arch: process.arch
        }
    });
});

// API-Routen (temporär ohne Authentifizierung für Development)
// Auth routes are always public (for registration and login)
app.use('/api/auth', authRoutes);

if (env.NODE_ENV === 'development') {
  // Development: Keine Authentifizierung für einfachere Entwicklung
  app.use('/api/database', databaseRoutes);
  app.use('/api/system', systemRoutes);
  app.use('/api/aril', arilDebugRoutes);
  app.use('/api/performance', performanceRoutes);
  app.use('/api/performance-db', performanceDbRoutes);
  app.use('/api/errors', errorMonitoringRoutes);
  app.use('/api/paginated', paginatedDataRoutes);
  app.use('/api/workflows', workflowRoutes);
  app.use('/api/stoerungen', stoerungenRoutes);
  app.use('/api/ai', aiRoutes);
  app.use('/api/ai', aiHealthRoutes);
  app.use('/api/chat', chatRoutes);
  app.use('/api/bereitschafts', bereitschaftsRoutes);
  app.use('/api/dashboard', dashboardRoutes);
  app.use('/api/rag', ragRoutes);
  app.use('/api/inventory', inventoryRoutes);
  app.use('/api/available-drums', availableDrumsRoutes);
  app.use('/api/runbooks', runbookRoutes);
  app.use('/api/email', emailRoutes); // E-Mail-Service auch in Development verfügbar
  app.use('/api', kpiRoutes); // /api/servicegrad, /api/bestand
  app.use('/api/delivery', deliveryRoutes);
  app.use('/api/production', productionRoutes);
  app.use('/api/supplier', supplierRoutes);
} else {
  // Production: Mit Authentifizierung
  app.use('/api/database', authMiddleware, databaseRoutes);
  app.use('/api/system', authMiddleware, systemRoutes);
  app.use('/api/aril', authMiddleware, arilDebugRoutes);
  app.use('/api/performance', authMiddleware, performanceRoutes);
  app.use('/api/performance-db', authMiddleware, performanceDbRoutes);
  app.use('/api/errors', authMiddleware, errorMonitoringRoutes);
  app.use('/api/paginated', authMiddleware, paginatedDataRoutes);
  app.use('/api/workflows', authMiddleware, workflowRoutes);
  app.use('/api/stoerungen', authMiddleware, stoerungenRoutes);
  app.use('/api/ai', authMiddleware, aiRoutes);
  app.use('/api/ai', aiHealthRoutes); // Health routes are public
  app.use('/api/chat', authMiddleware, chatRoutes);
  app.use('/api/bereitschafts', authMiddleware, bereitschaftsRoutes);
  app.use('/api/dashboard', authMiddleware, dashboardRoutes);
  app.use('/api/rag', authMiddleware, ragRoutes);
  app.use('/api/inventory', authMiddleware, inventoryRoutes);
  app.use('/api/available-drums', authMiddleware, availableDrumsRoutes);
  app.use('/api/runbooks', authMiddleware, runbookRoutes);
  app.use('/api/email', authMiddleware, emailRoutes); // E-Mail-Service mit Authentifizierung
  app.use('/api', authMiddleware, kpiRoutes);
  app.use('/api/delivery', authMiddleware, deliveryRoutes);
  app.use('/api/production', authMiddleware, productionRoutes);
  app.use('/api/supplier', authMiddleware, supplierRoutes);
}
// app.use('/api/warehouse', authMiddleware, warehouseRoutes);

// Globaler Error-Handler für nicht abgefangene Fehler
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('[GLOBAL-ERROR]', error);

  // Keine sensiblen Informationen in Produktion preisgeben
  const isDevelopment = env.NODE_ENV === 'development';

  res.status(500).json({
    success: false,
    error: 'Interner Serverfehler',
    message: 'Es ist ein unerwarteter Fehler aufgetreten.',
    ...(isDevelopment && { details: error.message, stack: error.stack }),
    code: 'INTERNAL_SERVER_ERROR'
  });
});

// 404-Handler für nicht gefundene Routen
app.use('*', (req: express.Request, res: express.Response) => {
  const clientIP = (req as any).clientIP || req.ip || 'unbekannt';
  console.warn(`[404] Nicht gefundene Route: ${req.method} ${req.originalUrl} von IP: ${clientIP}`);

  res.status(404).json({
    success: false,
    error: 'Route nicht gefunden',
    message: `Die Route ${req.method} ${req.originalUrl} existiert nicht.`,
    code: 'ROUTE_NOT_FOUND'
  });
});

// Server starten
app.listen(port, () => {
  console.log(`✅ Server läuft auf Port ${port}`);
  console.log(`✅ Alle Warehouse-Service-Funktionen verfügbar!`);
  console.log("[DEBUG] Backend-Server vollständig initialisiert");
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  await dbService.disconnect();
  process.exit(0);
});

}

// Export für Tests
export { app };

// Starte Server
startServer().catch(error => {
  console.error('❌ Fehler beim Starten des Servers:', error);
  process.exit(1);
});

