import React, { createContext, useContext, useState, useCallback, ReactNode, useMemo } from 'react';
import type { AddCardFunction } from './types';
import {
  DndContext,
  closestCenter,
  closestCorners,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  UniqueIdentifier,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  rectSortingStrategy,
} from '@dnd-kit/sortable';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';
import { usePersistentCardPositions } from '../../hooks/usePersistentCardPositions';
import { Edit3, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import type { AvailableKPICard } from './types';
import { DragOverlayCard } from './DragOverlayCard';

// Interface für Kartenpositionen
export interface CardPosition {
  id: string;
  position: number;
  type?: string; // Optional: Typ der Karte (z.B. 'kpi-card', 'chart-card')
}


// AvailableKPICard wird aus types.ts importiert

// Props für den DragDropProvider
interface DragDropProviderProps {
  children: ReactNode;
  showEditToggle?: boolean; // Zeigt den Edit-Toggle-Button an
  editTogglePosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  initialPositions?: CardPosition[]; // Initiale Kartenpositionen
  editToggleConfig?: {
    position: string;
    visible: boolean;
    label: string;
  };
}

// Kontext für Drag-and-Drop-Funktionalität
interface DragDropContextType {
  cardPositions: CardPosition[];
  activeId: UniqueIdentifier | null;
  isEditMode: boolean;
  availableCards: AvailableKPICard[];
  activeCards: string[]; // IDs der aktuell aktiven Karten
  updateCardPosition: (cardId: string, newPosition: number) => void;
  resetPositions: () => void;
  toggleEditMode: () => void;
  addCard: AddCardFunction;
  removeCard: (cardId: string) => void;
}

// Erstelle den Kontext
const DragDropContext = createContext<DragDropContextType | undefined>(undefined);

/**
 * Custom Hook zum Zugriff auf den DragDropContext
 * Wirft einen Fehler, wenn außerhalb des DragDropProviders verwendet
 */
export const useDragDropContext = (): DragDropContextType => {
  const context = useContext(DragDropContext);
  if (context === undefined) {
    throw new Error('useDragDropContext must be used within a DragDropProvider');
  }
  return context;
};

/**
 * DragDropProvider - Haupt-Provider für Drag-and-Drop-Funktionalität
 * 
 * Dieser Provider umhüllt das gesamte Dashboard und stellt allen Kind-Komponenten
 * die Drag-and-Drop-Funktionalität zur Verfügung. Er verwaltet:
 * - Kartenpositionen und deren Persistierung
 * - Edit-Modus für Bearbeitung der Karten
 * - KPI-Card-Bibliothek mit Add/Remove-Funktionalität
 * - Drei vordefinierte Kartengrößen (klein, mittel, groß)
 * - Drag-Start/End-Events
 * - Visuelle Rückmeldung während des Ziehens
 * - Keyboard- und Touch-Unterstützung
 */
export const DragDropProvider: React.FC<DragDropProviderProps> = ({ 
  children, 
  showEditToggle = true,
  editTogglePosition = 'top-right'
}) => {
  // State für die aktuelle Drag-Operation
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  
  // State für Edit-Modus
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  
  // Verwende den Hook für persistente Kartenpositionen
  const { positions: cardPositions, updatePositions, resetPositions } = usePersistentCardPositions();
  
  // State für aktive Karten
  const [activeCards, setActiveCards] = useState<string[]>(['wareneingang', 'tonnage', 'performance', 'efficiency', 'quality', 'delivery', 'picking', 'automatisierung', 'produktion', 'lager', 'versand', 'service']);
  
  // Verfügbare KPI-Cards in der Bibliothek
  const availableCards: AvailableKPICard[] = [
    {
      id: 'wareneingang',
      name: 'Wareneingang',
      description: 'Anzahl eingehender Warenpositionen',
      category: 'wareneingang',
      isActive: false
    },
    {
      id: 'tonnage',
      name: 'Tonnage',
      description: 'Produzierte Tonnage pro Tag',
      category: 'dispatch',
      isActive: false
    },
    {
      id: 'performance',
      name: 'Performance',
      description: 'Service Level Performance',
      category: 'dispatch',
      isActive: false
    },
    {
      id: 'efficiency',
      name: 'Effizienz',
      description: 'Automatisierungsgrad',
      category: 'wareneingang',
      isActive: false
    },
    {
      id: 'quality',
      name: 'Qualität',
      description: 'Qualitätsrate über alle Bereiche',
      category: 'quality',
      isActive: false
    },
    {
      id: 'delivery',
      name: 'Lieferleistung',
      description: 'Lieferperformance in Prozent',
      category: 'dispatch',
      isActive: false
    },
    {
      id: 'picking',
      name: 'Kommissionierung',
      description: 'Picking-Performance Anzahl Positionen',
      category: 'dispatch',
      isActive: false
    },
    {
      id: 'automatisierung',
      name: 'Automatisierung',
      description: 'Automatisierte Wareneingangspositionen',
      category: 'wareneingang',
      isActive: false
    },
    {
      id: 'produktion',
      name: 'Produktion',
      description: 'Anzahl Schnitte in der Ablängerei',
      category: 'ablaengerei',
      isActive: false
    },
    {
      id: 'lager',
      name: 'Lager',
      description: 'Umschlag im Lagerbereich',
      category: 'dispatch',
      isActive: false
    },
    {
      id: 'versand',
      name: 'Versand',
      description: 'Direktverladung Kiaa',
      category: 'dispatch',
      isActive: false
    },
    {
      id: 'service',
      name: 'Service',
      description: 'Anzahl Elefanten',
      category: 'dispatch',
      isActive: false
    }
  ];

  // Funktionen für Edit-Modus und Kartenverwaltung
  const toggleEditMode = useCallback(() => {
    setIsEditMode(prev => !prev);
  }, []);
  
  const addCard = useCallback((cardId: string, position?: number) => {
    if (!activeCards.includes(cardId)) {
      setActiveCards(prev => [...prev, cardId]);
      
      // Wenn eine Position angegeben ist, aktualisiere die Kartenpositionen
      if (position !== undefined) {
        // Prüfe, ob die Position bereits belegt ist
        const existingPosition = cardPositions.find(pos => pos.position === position);
        
        if (existingPosition) {
          // Wenn die Position belegt ist, finde die nächste freie Position
          const newPosition = Math.max(...cardPositions.map(p => p.position), 0) + 1;
          updatePositions([...cardPositions, { id: cardId, position: newPosition }]);
        } else {
          // Wenn die Position frei ist, füge die Karte dort hinzu
          updatePositions([...cardPositions, { id: cardId, position }]);
        }
      } else {
        // Wenn keine Position angegeben ist, füge die Karte am Ende hinzu
        const newPosition = Math.max(...cardPositions.map(p => p.position), 0) + 1;
        updatePositions([...cardPositions, { id: cardId, position: newPosition }]);
      }
    }
  }, [activeCards, cardPositions, updatePositions]);
  
  const removeCard = useCallback((cardId: string) => {
    setActiveCards(prev => prev.filter(id => id !== cardId));
  }, []);
  
  
  // Sensoren für verschiedene Eingabemethoden konfigurieren
  // Optimiert für bessere Performance und Benutzerfreundlichkeit
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // Aktivierungsdistanz verhindert versehentliches Ziehen bei Klicks
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handler für den Start einer Drag-Operation
  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id);
  }, []);

  // Handler für das Ende einer Drag-Operation
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    
    if (active.id !== over?.id) {
      // Finde die Position der aktiven Karte
      const activeCard = cardPositions.find((pos: CardPosition) => pos.id === active.id);
      
      if (!activeCard) return;
      
      // Prüfe, ob over eine Position im Format "position-{index}" ist
      const overIdString = String(over?.id || '');
      const isOverEmptyPosition = overIdString.startsWith('position-');
      const targetPositionIndex = isOverEmptyPosition ? parseInt(overIdString.split('-')[1]) : -1;
      const overCard = isOverEmptyPosition ? null : cardPositions.find((pos: CardPosition) => pos.id === over?.id);
      
      if (targetPositionIndex !== -1) {
        // Finde die Karte an der Zielposition, falls vorhanden
        const targetPositionCard = cardPositions.find((pos: CardPosition) => pos.position === targetPositionIndex);
        
        if (targetPositionCard && targetPositionCard.id !== active.id) {
          // Zielposition ist belegt - tausche die Positionen
          const updatedPositions = cardPositions.map((pos: CardPosition) => {
            if (pos.id === active.id) {
              // Die aktive Karte erhält die Zielposition
              return { ...pos, position: targetPositionIndex };
            } else if (pos.id === targetPositionCard.id) {
              // Die Karte an der Zielposition erhält die Position der aktiven Karte
              return { ...pos, position: activeCard.position };
            }
            return pos;
          });
          
          updatePositions(updatedPositions);
        } else {
          // Zielposition ist frei - verschiebe die Karte dorthin
          const updatedPositions = cardPositions.map((pos: CardPosition) =>
            pos.id === active.id ? { ...pos, position: targetPositionIndex } : pos
          );
          
          updatePositions(updatedPositions);
        }
      } else if (overCard) {
        // Ziehen auf eine andere Karte - tausche die Positionen
        const updatedPositions = cardPositions.map((pos: CardPosition) => {
          if (pos.id === active.id) {
            // Die aktive Karte erhält die Position der Zielkarte
            return { ...pos, position: overCard.position };
          } else if (pos.id === overCard.id) {
            // Die Zielkarte erhält die Position der aktiven Karte
            return { ...pos, position: activeCard.position };
          }
          return pos;
        });
        
        updatePositions(updatedPositions);
      }
    }
    
    setActiveId(null);
  }, [cardPositions, updatePositions]);

  // Funktion zum Aktualisieren einer einzelnen Kartenposition
  const updateCardPosition = useCallback((cardId: string, newPosition: number) => {
    const updatedPositions = cardPositions.map((pos: CardPosition) => 
      pos.id === cardId ? { ...pos, position: newPosition } : pos
    );
    updatePositions(updatedPositions);
  }, [cardPositions, updatePositions]);

  // Sortierte IDs basierend auf den Positionen (nur aktive Karten)
  const sortedIds = cardPositions
    .filter((pos: CardPosition) => activeCards.includes(pos.id))
    .sort((a: CardPosition, b: CardPosition) => a.position - b.position)
    .map((pos: CardPosition) => pos.id);
    
  // Alle verfügbaren Positionen für den SortableContext (auch leere)
  const allPositionIds = Array.from({ length: 12 }, (_, i) => `position-${i}`);
    
  // Position für Edit-Toggle-Button
  const getToggleButtonPosition = useCallback((): string => {
    const positions: Record<string, string> = {
      'top-left': 'fixed top-4 left-4 z-50',
      'top-right': 'inline-flex ml-4', // Inline neben der Dashboard-Überschrift
      'bottom-left': 'fixed bottom-4 left-4 z-50',
      'bottom-right': 'fixed bottom-4 right-4 z-50'
    };
    return positions[editTogglePosition] || positions['top-right'];
  }, [editTogglePosition]);

  // Context Value memoization für Performance-Optimierung
  // Verhindert unnötige Re-Renders von Consumer-Komponenten
  const contextValue = useMemo(() => ({
    cardPositions,
    activeId,
    isEditMode,
    availableCards,
    activeCards,
    updateCardPosition,
    resetPositions,
    toggleEditMode,
    addCard,
    removeCard,
  }), [
    cardPositions,
    activeId,
    isEditMode,
    availableCards,
    activeCards,
    updateCardPosition,
    resetPositions,
    toggleEditMode,
    addCard,
    removeCard,
  ]);

  return (
    <DragDropContext.Provider value={contextValue}>
      <DndContext
        sensors={sensors}
        // closestCorners bietet bessere UX für Grid-Layouts als closestCenter
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        modifiers={[restrictToWindowEdges]}
        // Accessibility: Beschreibung für Screen Reader
        accessibility={{
          announcements: {
            onDragStart: ({ active }) => `Karte ${active.id} wird verschoben`,
            onDragOver: ({ active, over }) => 
              over ? `Karte ${active.id} über Position ${over.id}` : `Karte ${active.id} wird verschoben`,
            onDragEnd: ({ active, over }) => 
              over ? `Karte ${active.id} wurde zu Position ${over.id} verschoben` : `Karte ${active.id} wurde zurückgesetzt`,
            onDragCancel: ({ active }) => `Verschieben von Karte ${active.id} wurde abgebrochen`,
          },
        }}
      >
        <SortableContext items={isEditMode ? allPositionIds : sortedIds} strategy={rectSortingStrategy}>
          {/* Edit-Toggle-Button */}
          {showEditToggle && (
            <Button
              onClick={toggleEditMode}
              variant={isEditMode ? "default" : "outline"}
              className={cn(
                getToggleButtonPosition(),
                "flex items-center gap-2 transition-all duration-200",
                isEditMode 
                  ? "bg-blue-600 hover:bg-blue-700 text-white" 
                  : "bg-white/80 hover:bg-gray-50 text-gray-700 border-gray-200"
              )}
            >
              <Edit3 className="h-4 w-4" />
              {isEditMode ? 'Bearbeitung beenden' : 'Layout bearbeiten'}
            </Button>
          )}
          
          {children}
        </SortableContext>
        
        {/* DragOverlay für visuelle Rückmeldung während des Ziehens */}
        <DragOverlay>
          {activeId ? (
            <DragOverlayCard activeId={activeId} />
          ) : null}
        </DragOverlay>
      </DndContext>
    </DragDropContext.Provider>
  );
};

export default DragDropProvider;