import React, { useState } from "react";
import { DateRange } from "react-day-picker";
import { TruckIcon, Package, Clock, Target } from "lucide-react";
import { CSRChartPunktuality } from "../components/charts/CSRChartPunktuality";
import { CSRChartProcurement } from "../components/charts/CSRChartProcurement";
import { CSRChartIntralogistik } from "../components/charts/CSRChartIntralogistik";
import { CSRChartTransport } from "../components/charts/CSRChartTransport";
import { CSROverallChart } from "../components/charts/CSROverallChart";
import { csrData } from "../components/data/csr-data";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import { AskJaszButton } from "@/modules/ai/components";
import { createPageContext } from "@/contexts/AskJaszContext";

/**
 * CSR-Dashboard mit sauberen, wiederverwendbaren Komponenten
 * 
 * Zeigt Customer Service Rate Performance über die gesamte Supply Chain:
 * Beschaffung → Intralogistik → Transport → Overall CSR
 */
export default function CSRPage() {
  // Zustand für den ausgewählten Datumsbereich
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 4, 1), // 1. Juli 2025
    to: new Date(2025, 4, 30), // 8. Juli 2025 (alle verfügbaren Daten)
  });

  // Transform data for charts
  const overallData = csrData.overall as any;

  const punctualityData = csrData.overall.map(item => ({
    date: item.date,
    value: item.punctualityDays,
    label: `${item.punctualityDays} days`
  })) as any[];

  const procurementData = csrData.procurement.map(item => ({
    date: item.date,
    value: item.serviceLevelDays,
    label: `${item.serviceLevelDays} days`
  })) as any[];

  const intralogisticsData = csrData.intralogistics.map(item => ({
    date: item.date,
    value: item.serviceLevelHours,
    label: `${item.serviceLevelHours} hours`
  })) as any[];

  const transportData = csrData.transport.map(item => ({
    date: item.date,
    value: item.serviceLevelDays,
    label: `${item.serviceLevelDays} days`
  })) as any[];

  const procurementPercentData = csrData.procurement.map(item => ({
    date: item.date,
    value: item.serviceLevelPercent,
    label: `${item.serviceLevelPercent}%`
  })) as any[];

  const intralogisticsPercentData = csrData.intralogistics.map(item => ({
    date: item.date,
    value: item.serviceLevelPercent,
    label: `${item.serviceLevelPercent}%`
  })) as any[];

  const transportPercentData = csrData.transport.map(item => ({
    date: item.date,
    value: item.serviceLevelPercent,
    label: `${item.serviceLevelPercent}%`
  })) as any[];

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Target className="h-10 w-10 mr-2 text-blue-600" />
          <h1 className="text-4xl font-bold text-black">CUSTOMER SERVICE RATE</h1>
          <div className="-mt-7">
            <AskJaszButton
              context={createPageContext(
                "CSR Dashboard",
                [
                  "Customer Service Rate Analyse",
                  "Pünktlichkeitsmetriken über Supply Chain",
                  "Beschaffung, Intralogistik und Transport Performance",
                  "Service Level Monitoring und Trends"
                ],
                "Supply Chain Performance Dashboard"
              )}
              position="inline"
              size="md"
              variant="default"
              tooltip="Hilfe zum CSR Dashboard erhalten"
            />
          </div>
        </div>
        <DateRangePicker
          value={dateRange}
          onChange={setDateRange}
          label="Datumsauswahl"
        />
      </div>

      {/* Main Metrics - Overall Performance */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-2">
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <CSROverallChart data={overallData} />
            </ChartErrorBoundary>
          </div>

          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <CSRChartPunktuality
                title="Gesamte Pünktlichkeit"
                description="Tage voraus (+) oder verspätet (-) als geplant"
                data={punctualityData}
                dataKey="value"
                unit=" Tage"
                trend={{ value: 1.2, isPositive: true }}
              />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>

      {/* Supply Chain Stages */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-2">
          {/* Procurement */}
          <div className="xl:col-span-1 space-y-1">
            <div className="flex items-center mb-2">
              <Package className="h-7 w-7 mr-2 text-green-600" />
              <h2 className="text-2xl font-semibold text-black">Lieferant</h2>
            </div>

            <ChartErrorBoundary>
              <CSRChartProcurement
                title="Beschaffung Service Level"
                description="Tage voraus (+) oder verspätet (-)"
                data={procurementData}
                dataKey="value"
                unit=" Tage"
                trend={{ value: 2.1, isPositive: false }}
              />
            </ChartErrorBoundary>

            <ChartErrorBoundary>
              <CSRChartProcurement
                title="Beschaffung Performance"
                description="Service Level Prozentsatz"
                data={procurementPercentData}
                dataKey="value"
                unit="%"
                trend={{ value: 1.8, isPositive: true }}
              />
            </ChartErrorBoundary>
          </div>

          {/* Intralogistics */}
          <div className="xl:col-span-1 space-y-1">
            <div className="flex items-center mb-2">
              <Clock className="h-7 w-7 mr-2 text-yellow-600" />
              <h2 className="text-2xl font-semibold text-black">Intralogistik</h2>
            </div>

            <ChartErrorBoundary>
              <CSRChartIntralogistik
                title="Intralogistik Service Level"
                description="Stunden voraus (+) oder verspätet (-)"
                data={intralogisticsData}
                dataKey="value"
                unit=" Std"
                trend={{ value: 3.2, isPositive: true }}
              />
            </ChartErrorBoundary>

            <ChartErrorBoundary>
              <CSRChartIntralogistik
                title="Intralogistik Performance"
                description="Service Level Prozentsatz"
                data={intralogisticsPercentData}
                dataKey="value"
                unit="%"
                trend={{ value: 2.5, isPositive: true }}
              />
            </ChartErrorBoundary>
          </div>

          {/* Transport */}
          <div className="xl:col-span-1 space-y-1">
            <div className="flex items-center mb-2">
              <TruckIcon className="h-7 w-7 mr-2 text-blue-600" />
              <h2 className="text-2xl font-semibold text-black">Transport an Kunde</h2>
            </div>

            <ChartErrorBoundary>
              <CSRChartTransport
                title="Transport Service Level"
                description="Tage voraus (+) oder verspätet (-)"
                data={transportData}
                dataKey="value"
                unit=" Tage"
                trend={{ value: 1.5, isPositive: false }}
              />
            </ChartErrorBoundary>

            <ChartErrorBoundary>
              <CSRChartTransport
                title="Transport Performance"
                description="Service Level Prozentsatz"
                data={transportPercentData}
                dataKey="value"
                unit="%"
                trend={{ value: 0.8, isPositive: true }}
              />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
}