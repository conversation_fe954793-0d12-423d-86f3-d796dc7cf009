"use strict";
/**
 * Datenbankkonfiguration für portable Anwendung
 *
 * Diese Datei zentralisiert die Datenbankkonfiguration für den portablen Einsatz.
 * Sie unterstützt verschiedene Konfigurationsmethoden:
 * 1. Umgebungsvariablen (für Produktionsumgebungen)
 * 2. Konfigurationsdatei (für portablen Einsatz)
 * 3. Standardwerte (für Entwicklung)
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadDatabaseConfig = loadDatabaseConfig;
exports.loadRagDatabaseConfig = loadRagDatabaseConfig;
exports.createConnectionString = createConnectionString;
exports.validateDatabaseConfig = validateDatabaseConfig;
exports.createPoolConfig = createPoolConfig;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
/**
 * Standard-Datenbankkonfiguration
 */
const DEFAULT_CONFIG = {
    host: 'localhost',
    port: 5432,
    database: 'lapp_dashboard',
    username: 'postgres',
    password: 'password',
    ssl: false,
    connectionTimeout: 30000,
    maxConnections: 10
};
/**
 * Lädt die Datenbankkonfiguration aus verschiedenen Quellen
 */
function loadDatabaseConfig() {
    // 1. Zuerst versuchen, Konfiguration aus portabler Konfigurationsdatei zu laden
    const portableConfig = loadPortableConfig();
    if (portableConfig) {
        console.log('✅ Verwende portable Datenbankkonfiguration');
        return portableConfig;
    }
    // 2. Dann Umgebungsvariablen prüfen
    const envConfig = loadEnvironmentConfig();
    if (envConfig) {
        console.log('✅ Verwende Umgebungsvariablen für Datenbankkonfiguration');
        return envConfig;
    }
    // 3. Standardkonfiguration verwenden
    console.log('⚠️  Verwende Standard-Datenbankkonfiguration');
    return DEFAULT_CONFIG;
}
/**
 * Lädt die Konfiguration aus einer portablen Konfigurationsdatei
 */
function loadPortableConfig() {
    try {
        // Verschiedene mögliche Pfade für die portable Konfiguration
        const possiblePaths = [];
        // Pfad im portablen Build (nur in Electron-Umgebung)
        if (process.versions && process.versions.electron) {
            possiblePaths.push(
            // @ts-ignore - resourcesPath existiert nur in Electron
            path_1.default.join(process.resourcesPath, '..', 'portable-config.json'));
        }
        // Pfade im Entwicklungsmodus
        possiblePaths.push(path_1.default.join(process.cwd(), 'portable-config.json'), path_1.default.join(process.cwd(), 'backend', 'portable-config.json'));
        for (const configPath of possiblePaths) {
            if (fs_1.default.existsSync(configPath)) {
                const configContent = fs_1.default.readFileSync(configPath, 'utf8');
                const portableConfig = JSON.parse(configContent);
                if (portableConfig.database) {
                    return {
                        host: portableConfig.database.host || DEFAULT_CONFIG.host,
                        port: portableConfig.database.port || DEFAULT_CONFIG.port,
                        database: portableConfig.database.database || DEFAULT_CONFIG.database,
                        username: portableConfig.database.username || DEFAULT_CONFIG.username,
                        password: portableConfig.database.password || DEFAULT_CONFIG.password,
                        ssl: portableConfig.database.ssl || DEFAULT_CONFIG.ssl,
                        connectionTimeout: portableConfig.database.connectionTimeout || DEFAULT_CONFIG.connectionTimeout,
                        maxConnections: portableConfig.database.maxConnections || DEFAULT_CONFIG.maxConnections
                    };
                }
            }
        }
    }
    catch (error) {
        console.warn('⚠️  Konnte portable Konfiguration nicht laden:', error);
    }
    return null;
}
/**
 * Lädt die Konfiguration aus Umgebungsvariablen
 */
function loadEnvironmentConfig() {
    const envVars = {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        database: process.env.DB_NAME,
        username: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        ssl: process.env.DB_SSL,
        connectionTimeout: process.env.DB_CONNECTION_TIMEOUT,
        maxConnections: process.env.DB_MAX_CONNECTIONS
    };
    // Prüfen, ob mindestens die erforderlichen Variablen gesetzt sind
    if (envVars.host && envVars.database && envVars.username) {
        return {
            host: envVars.host,
            port: envVars.port ? parseInt(envVars.port, 10) : DEFAULT_CONFIG.port,
            database: envVars.database,
            username: envVars.username,
            password: envVars.password || DEFAULT_CONFIG.password,
            ssl: envVars.ssl === 'true',
            connectionTimeout: envVars.connectionTimeout ? parseInt(envVars.connectionTimeout, 10) : DEFAULT_CONFIG.connectionTimeout,
            maxConnections: envVars.maxConnections ? parseInt(envVars.maxConnections, 10) : DEFAULT_CONFIG.maxConnections
        };
    }
    return null;
}
/**
 * Lädt die RAG-Datenbankkonfiguration aus Umgebungsvariablen
 */
function loadRagDatabaseConfig() {
    const envVars = {
        host: process.env.RAG_DB_HOST,
        port: process.env.RAG_DB_PORT,
        database: process.env.RAG_DB_NAME,
        username: process.env.RAG_DB_USER,
        password: process.env.RAG_DB_PASSWORD,
        ssl: process.env.RAG_DB_SSL,
        connectionTimeout: process.env.RAG_DB_CONNECTION_TIMEOUT,
        maxConnections: process.env.RAG_DB_MAX_CONNECTIONS
    };
    // Prüfen, ob mindestens die erforderlichen Variablen gesetzt sind
    if (envVars.host && envVars.database && envVars.username) {
        return {
            host: envVars.host,
            port: envVars.port ? parseInt(envVars.port, 10) : DEFAULT_CONFIG.port,
            database: envVars.database,
            username: envVars.username,
            password: envVars.password || DEFAULT_CONFIG.password,
            ssl: envVars.ssl === 'true',
            connectionTimeout: envVars.connectionTimeout ? parseInt(envVars.connectionTimeout, 10) : DEFAULT_CONFIG.connectionTimeout,
            maxConnections: envVars.maxConnections ? parseInt(envVars.maxConnections, 10) : DEFAULT_CONFIG.maxConnections
        };
    }
    return null;
}
/**
 * Erstellt eine PostgreSQL-Verbindungszeichenfolge aus der Konfiguration
 */
function createConnectionString(config) {
    const { host, port, database, username, password, ssl } = config;
    const sslParam = ssl ? '?sslmode=require' : '';
    return `postgresql://${username}:${password}@${host}:${port}/${database}${sslParam}`;
}
/**
 * Validiert die Datenbankkonfiguration
 */
function validateDatabaseConfig(config) {
    const requiredFields = ['host', 'database', 'username'];
    for (const field of requiredFields) {
        if (!config[field]) {
            console.error(`❌ Fehlendes erforderliches Feld in der Datenbankkonfiguration: ${field}`);
            return false;
        }
    }
    if (config.port < 1 || config.port > 65535) {
        console.error('❌ Ungültiger Port in der Datenbankkonfiguration');
        return false;
    }
    return true;
}
/**
 * Erstellt die Konfiguration für den pg-Pool
 */
function createPoolConfig(config) {
    return {
        host: config.host,
        port: config.port,
        user: config.username,
        password: config.password,
        database: config.database,
        ssl: config.ssl ? { rejectUnauthorized: false } : undefined,
        connectionTimeoutMillis: config.connectionTimeout,
        max: config.maxConnections,
        idleTimeoutMillis: 30000,
        // Zusätzliche Optionen für Stabilität
        application_name: 'lapp_dashboard_portable',
        // Retry-Strategie für Verbindungsprobleme
        retryDelayMin: 100,
        retryDelayMax: 2000,
        // Logging für Debugging
        log: (msg) => {
            if (process.env.NODE_ENV === 'development') {
                console.log(`[PostgreSQL] ${msg}`);
            }
        }
    };
}
