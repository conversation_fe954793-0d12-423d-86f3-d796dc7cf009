# 🪝 Hooks Directory

> **Custom React Hooks für die Leitstand App**  
> Wiederverwendbare Hooks für State Management, UI-Interaktionen und Business Logic

---

## 📋 Übersicht

Dieser Ordner enthält alle benutzerdefinierten React Hooks der Leitstand App. Die Hooks sind modular aufgebaut und folgen den React Best Practices für State Management, Side Effects und UI-Logik.

### 🎯 Hauptfunktionen
- **Authentifizierung & Autorisierung** - Benutzer-Session Management
- **Accessibility Support** - Barrierefreiheit und Benutzerfreundlichkeit
- **Database Integration** - IPC-basierte Datenbankoperationen
- **UI State Management** - Mobile Detection, Motion Preferences, Page Visibility
- **Business Logic** - Workflow Management, ChatBot Integration

---

## 📁 Dateistruktur

```
src/hooks/
├── 📄 index.ts                    # Hook-Exports (zentrale Schnittstelle)
├── 🔐 useAuth.ts                  # Authentifizierung & Session Management
├── ♿ useAccessibility.ts          # Barrierefreiheit & Screen Reader Support
├── 🤖 useChatBot.ts               # ChatBot State Management
├── 🗄️ useDatabase.ts              # Database IPC Operations
├── 📱 use-mobile.ts               # Mobile Breakpoint Detection
├── 🎭 useMotionPreference.ts      # Motion & Animation Preferences
├── 👁️ usePageVisibility.ts        # Page Visibility API Integration
└── ⚙️ useWorkflows.ts             # SAP Workflow Management
```

---

## 🔍 Detaillierte Beschreibungen

### 🔐 `useAuth.ts`
**Authentifizierung & Session Management**

```typescript
interface AuthHook {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<{success: boolean; message: string}>;
  logout: () => void;
  trackActivity: () => void;
}
```

**Features:**
- ✅ JWT Token Management
- ✅ User Session Persistence
- ✅ Activity Tracking
- ✅ Automatic Logout
- ✅ Role-based Access Control

**Verwendung:**
```typescript
const { user, isAuthenticated, login, logout } = useAuth();
```

---

### ♿ `useAccessibility.ts`
**Umfassende Barrierefreiheits-Unterstützung**

```typescript
interface AccessibilityPreferences {
  prefersReducedMotion: boolean;
  prefersHighContrast: boolean;
  prefersReducedTransparency: boolean;
  screenReaderActive: boolean;
  keyboardNavigationActive: boolean;
}
```

**Features:**
- ✅ Screen Reader Detection
- ✅ Keyboard Navigation Support
- ✅ Focus Management
- ✅ ARIA Announcements
- ✅ High Contrast Mode
- ✅ Reduced Motion Support

**Compliance:** WCAG 2.1 AA Standards

---

### 🗄️ `useDatabase.ts`
**IPC-basierte Datenbankoperationen**

```typescript
interface DatabaseHook {
  isLoading: boolean;
  error: string | null;
  isConnected: boolean;
  connect: (customPath?: string) => Promise<boolean>;
  getTables: () => Promise<DatabaseTable[]>;
  getTableData: (tableName: string) => Promise<TableData>;
  executeQuery: (query: string) => Promise<any[]>;
}
```

**Features:**
- ✅ SQLite Connection Management
- ✅ Table Schema Inspection
- ✅ Query Execution
- ✅ Error Handling
- ✅ Connection Status Tracking

---

### 📱 `use-mobile.ts`
**Responsive Design Support**

```typescript
const MOBILE_BREAKPOINT = 768; // px

function useIsMobile(): boolean
```

**Features:**
- ✅ Media Query Integration
- ✅ Real-time Breakpoint Detection
- ✅ SSR-safe Implementation
- ✅ Performance Optimized

---

### 🎭 `useMotionPreference.ts`
**Motion & Animation Preferences**

```typescript
interface MotionPreferences {
  prefersReducedMotion: boolean;
  allowTransitions: boolean;
  fallbackMode: 'static' | 'minimal-animation';
}
```

**Features:**
- ✅ System Motion Preference Detection
- ✅ Fallback Animation Modes
- ✅ Real-time Preference Updates
- ✅ Accessibility Compliance

---

### 👁️ `usePageVisibility.ts`
**Performance Optimization durch Page Visibility API**

```typescript
interface VisibilityState {
  isVisible: boolean;
  visibilityState: DocumentVisibilityState;
  lastVisibilityChange: number;
}
```

**Features:**
- ✅ Resource Cleanup bei Hidden State
- ✅ Performance Monitoring
- ✅ Configurable Cleanup Delays
- ✅ Event Callbacks

---

### ⚙️ `useWorkflows.ts`
**SAP Workflow Management**

```typescript
interface WorkflowHook {
  processes: ProcessWithConfig[];
  executions: WorkflowExecution[];
  logs: WorkflowLog[];
  stats: WorkflowStats | null;
  loading: boolean;
  error: string | null;
}
```

**Features:**
- ✅ SAP Workflow Integration
- ✅ Process Configuration Management
- ✅ Execution Monitoring
- ✅ Logging & Statistics
- ✅ Real-time Updates

---

### 🤖 `useChatBot.ts`
**ChatBot State Management**

```typescript
interface UseChatBotReturn {
  isChatOpen: boolean;
  chatMessage: string | undefined;
  openChatWithMessage: (message: string) => void;
  closeChatBot: () => void;
  setChatOpen: (isOpen: boolean) => void;
}
```

**Features:**
- ✅ Chat Modal State
- ✅ Pre-filled Messages
- ✅ Clean State Management

---

## 🔗 Dependencies

### Core Dependencies
```json
{
  "react": "^18.x",
  "@tanstack/react-query": "^5.x"
}
```

### Internal Dependencies
- `@/services/auth.service` - Authentifizierungs-Service
- `@/services/workflowService` - Workflow-Service
- `@/types/workflow` - Workflow Type Definitions

### Browser APIs
- **Media Query API** - Responsive & Motion Detection
- **Page Visibility API** - Performance Optimization
- **Electron IPC** - Database Communication

---

## 🛠️ Technische Details

### Hook Patterns
- **State Management:** `useState`, `useReducer`
- **Side Effects:** `useEffect`, `useLayoutEffect`
- **Performance:** `useCallback`, `useMemo`, `useRef`
- **Custom Logic:** Composition Pattern

### Error Handling
```typescript
// Standardisiertes Error Handling
const [error, setError] = useState<string | null>(null);

try {
  // Hook Logic
} catch (err) {
  const errorMessage = err instanceof Error ? err.message : 'Unbekannter Fehler';
  setError(errorMessage);
}
```

### Performance Optimizations
- ✅ `useCallback` für Event Handlers
- ✅ `useMemo` für berechnete Werte
- ✅ `useRef` für DOM-Referenzen
- ✅ Cleanup Functions in `useEffect`

---

## 💡 Verwendungsbeispiele

### Authentifizierung
```typescript
function LoginComponent() {
  const { login, isLoading, error } = useAuth();
  
  const handleLogin = async (credentials: LoginCredentials) => {
    const result = await login(credentials.username, credentials.password);
    if (result.success) {
      // Redirect to dashboard
    }
  };
}
```

### Responsive Design
```typescript
function ResponsiveComponent() {
  const isMobile = useIsMobile();
  
  return (
    <div className={isMobile ? 'mobile-layout' : 'desktop-layout'}>
      {/* Content */}
    </div>
  );
}
```

### Accessibility
```typescript
function AccessibleComponent() {
  const { 
    accessibilityPreferences, 
    announceToScreenReader,
    manageFocus 
  } = useAccessibility();
  
  const handleAction = () => {
    // Perform action
    announceToScreenReader('Aktion erfolgreich ausgeführt', 'polite');
  };
}
```

---

## 🧪 Testing

### Unit Tests (Vitest)
```typescript
// Beispiel Test Structure
describe('useAuth', () => {
  it('should handle login successfully', async () => {
    const { result } = renderHook(() => useAuth());
    // Test implementation
  });
});
```

### Test Coverage
- ✅ State Transitions
- ✅ Error Scenarios
- ✅ Cleanup Functions
- ✅ Event Handlers
- ✅ API Integration

---

## 🚀 Entwicklungshinweise

### Best Practices
1. **Separation of Concerns** - Ein Hook pro Verantwortlichkeit
2. **Error Boundaries** - Graceful Error Handling
3. **Performance** - Memoization wo sinnvoll
4. **Accessibility** - WCAG 2.1 AA Compliance
5. **TypeScript** - Vollständige Typisierung

### Code Style
```typescript
// ✅ Guter Hook
export function useFeature(config: FeatureConfig = {}) {
  const [state, setState] = useState(initialState);
  
  const handleAction = useCallback((param: string) => {
    // Implementation
  }, []);
  
  useEffect(() => {
    // Side effect
    return () => {
      // Cleanup
    };
  }, []);
  
  return { state, handleAction };
}
```

### Performance Monitoring
- 🔍 React DevTools Profiler
- 📊 Custom Performance Metrics
- ⚡ Bundle Size Optimization

---

## 🔄 Migration & Updates

### Legacy Code
- 🏷️ **Deprecated:** Keine aktuell
- 🔄 **Migration Path:** React 18 Concurrent Features
- 📈 **Roadmap:** React Query v5 Integration

### Breaking Changes
- Version 2.0: TypeScript Strict Mode
- Version 1.5: React 18 Migration

---

**📝 Letzte Aktualisierung:** $(date)  
**👥 Maintainer:** Leitstand Development Team  
**📚 Dokumentation:** [Internal Wiki](link-to-wiki)

---

> 💡 **Tipp:** Verwende die `index.ts` für saubere Imports: `import { useAuth, useDatabase } from '@/hooks'`