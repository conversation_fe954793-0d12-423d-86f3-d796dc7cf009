"use client";

import React, { useEffect, useRef, useState, useMemo } from "react";
import styles from "./BentoGrid.module.css";
import { DraggableCard } from "../DragDrop/DraggableCard";
import { DroppableContainer } from "../DragDrop/DroppableContainer";
import { useDragDropContext } from "../DragDrop/DragDropProvider";
import type { CardPosition } from "../DragDrop/DragDropProvider";
import { KPILibrary } from "../DragDrop/KPILibrary";
import { Button } from "@/components/ui/button";
import { Library, X } from "lucide-react";
import { cn } from "@/lib/utils";

// Interface für Department KPIs - erweitert für bessere Typisierung
interface DepartmentKPIs {
  incomingGoods: {
    current: number;
    target: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
  };
  tonnage: {
    current: number;
    target: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
  };
  performance: {
    current: number;
    target: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
  };
  efficiency: {
    current: number;
    target: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
  };
}

export interface DraggableBentoGridProps {
  className?: string;
  departmentKPIs?: DepartmentKPIs;
}

/**
 * Mapping von Kartengrößen zu CSS-Klassen für das Bento-Grid
 * Definiert die drei Standardgrößen für KPI-Karten
 */
const getCardSizeClasses = (size: string) => {
  // Direkte CSS-Klassen-Namen (t1, t2, etc.) werden direkt verwendet
  if (typeof size === 'string' && size.startsWith('t') && styles[size]) {
    return styles[size as keyof typeof styles];
  }
  
  // Mapping für die drei Standardgrößen
  const sizeMap: Record<string, string> = {
    small: styles.t2,   // Kleine Karte (2x1) - entspricht t2
    medium: styles.t4,  // Mittlere Karte (2x1) - entspricht t4
    large: styles.t1    // Große Karte (2x2) - entspricht t1
  };
  
  return sizeMap[size] || sizeMap.medium;
};

interface CardTiltProps {
  children: React.ReactNode;
  className?: string;
  onMouseMove?: (e: React.MouseEvent) => void;
  onTouchMove?: (e: React.TouchEvent) => void;
  onMouseLeave?: () => void;
  onTouchEnd?: () => void;
}

// Basis BentoCard-Komponente (ohne Drag-Funktionalität)
const BentoCard: React.FC<CardTiltProps> = ({
  children,
  className = "",
  onMouseMove,
  onTouchMove,
  onMouseLeave,
  onTouchEnd,
}) => {
  const cardRef = useRef<HTMLElement>(null);

  const clamp = (v: number, min: number, max: number) => Math.min(Math.max(v, min), max);

  const handleMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    let x, y;
    
    if ('clientX' in e) {
      x = e.clientX;
      y = e.clientY;
    } else if (e.touches && e.touches[0]) {
      x = e.touches[0].clientX;
      y = e.touches[0].clientY;
    } else {
      return;
    }
    
    const px = (x - rect.left) / rect.width;
    const py = (y - rect.top) / rect.height;
    const r = 12; // degrees
    const rx = clamp((0.5 - py) * (r * 2), -r, r);
    const ry = clamp((px - 0.5) * (r * 2), -r, r);
    
    cardRef.current.style.setProperty('--rx', `${rx.toFixed(2)}deg`);
    cardRef.current.style.setProperty('--ry', `${ry.toFixed(2)}deg`);
    cardRef.current.style.setProperty('--mx', `${px * 100}%`);
  };

  const handleReset = () => {
    if (!cardRef.current) return;
    cardRef.current.style.setProperty('--rx', '0deg');
    cardRef.current.style.setProperty('--ry', '0deg');
    cardRef.current.style.removeProperty('--mx');
  };

  return (
    <section
      ref={cardRef}
      className={`${styles.card} ${className}`}
      onMouseMove={handleMove}
      onTouchMove={handleMove}
      onMouseLeave={handleReset}
      onTouchEnd={handleReset}
    >
      {children}
    </section>
  );
};

/**
 * DraggableBentoGrid - Erweiterte BentoGrid mit Drag-and-Drop-Funktionalität
 * 
 * Diese Komponente erweitert die ursprüngliche BentoGrid um die Möglichkeit,
 * KPI-Karten per Drag-and-Drop zu verschieben. Die Positionen werden automatisch
 * gespeichert und beim nächsten Laden wiederhergestellt.
 * 
 * Features:
 * - Alle ursprünglichen BentoGrid-Features
 * - Drag-and-Drop für KPI-Karten (T1-T4)
 * - Persistente Speicherung der Positionen
 * - Visuelle Rückmeldung während des Ziehens
 * - Barrierefreie Bedienung
 */
export const DraggableBentoGrid: React.FC<DraggableBentoGridProps> = ({ className = "", departmentKPIs }) => {
  const [sliderValue, setSliderValue] = useState(55);
  const [performanceValue, setPerformanceValue] = useState(0);
  
  // State für KPI-Bibliothek
  const [isLibraryOpen, setIsLibraryOpen] = useState(false);
  
  // Kontext für Drag-and-Drop-Positionen und Edit-Modus
  const {
    cardPositions,
    isEditMode,
    activeCards,
    removeCard
  } = useDragDropContext();

  useEffect(() => {
    // Progressive number animation for performance
    let n = 0;
    const target = departmentKPIs?.performance?.current || 20;
    const step = () => {
      n += 1;
      if (n > target) n = target;
      setPerformanceValue(n);
      if (n < target) requestAnimationFrame(step);
    };
    requestAnimationFrame(step);
  }, [departmentKPIs]);

  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    setSliderValue(value);
    e.target.style.setProperty('--val', `${value}%`);
  };

  // Berechne KPI-Werte mit Fallback-Daten für Demo-Zwecke
  const kpiData = useMemo(() => {
    if (!departmentKPIs) {
      // Fallback Demo-Daten wenn keine KPIs übergeben werden
      return {
        incomingGoods: { current: 1247, target: 1200, unit: 'Stk', trend: 'up' as const },
        tonnage: { current: 89.2, target: 85.0, unit: 't', trend: 'up' as const },
        performance: { current: 94.7, target: 90.0, unit: '%', trend: 'up' as const },
        efficiency: { current: 87.3, target: 85.0, unit: '%', trend: 'up' as const }
      };
    }
    return departmentKPIs;
  }, [departmentKPIs]);

  // Berechne Prozentsätze für visuelle Darstellung
  const getPercentage = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  // Bestimme Farbe basierend auf Performance
  const getPerformanceColor = (current: number, target: number, trend: string) => {
    const percentage = (current / target) * 100;
    if (percentage >= 100) return 'var(--accent-green)';
    if (percentage >= 80) return 'var(--accent-cyan)';
    if (trend === 'up') return 'var(--accent-lime)';
    return 'var(--accent-orange)';
  };

  // Funktion zum Erstellen einer KPI-Karte mit anpassbarer Größe
  const createKPICard = (id: string, size: string = 'medium') => {
    const cardClass = getCardSizeClasses(size);
    
    const kpiCards = {
      wareneingang: (
        <BentoCard className={cardClass}>
          <div className={styles.splash} aria-hidden="true"></div>
          <div className={styles.dots} aria-hidden="true"></div>
          <div className={styles.content}>
            <div className={`${styles.labelTop} ${styles.eyebrow}`}>Wareneingang</div>
            <div style={{ marginTop: 'auto' }}>
              <div className={styles.headline} style={{ fontSize: 'clamp(18px,2.6vw,22px)', fontWeight: 600, opacity: 0.95 }}>
                {kpiData.incomingGoods.current.toLocaleString()}
                <span style={{ fontSize: '0.4em', opacity: 0.7, marginLeft: '8px' }}>
                  {kpiData.incomingGoods.unit}
                </span>
              </div>
              <div style={{ 
                fontSize: '12px', 
                opacity: 0.8, 
                color: getPerformanceColor(kpiData.incomingGoods.current, kpiData.incomingGoods.target, kpiData.incomingGoods.trend)
              }}>
                Ziel: {kpiData.incomingGoods.target.toLocaleString()} {kpiData.incomingGoods.unit}
              </div>
            </div>
          </div>
        </BentoCard>
      ),
      tonnage: (
        <BentoCard className={cardClass}>
          <div className={styles.content}>
            <div className={`${styles.labelTop} ${styles.eyebrow}`}>Tonnage</div>
            <div className={styles.headline} style={{ fontSize: 'clamp(18px,2.6vw,22px)', fontWeight: 600, opacity: 0.95 }}>
              {kpiData.tonnage.current.toFixed(1)}
              <span style={{ fontSize: '0.5em', opacity: 0.7, marginLeft: '4px' }}>
                {kpiData.tonnage.unit}
              </span>
            </div>
          </div>
          <svg className={styles.chain} viewBox="0 0 64 64" fill="none" aria-hidden="true">
            <defs>
              <linearGradient id="gradChain" x1="0" y1="0" x2="64" y2="64" gradientUnits="userSpaceOnUse">
                <stop stopColor={getPerformanceColor(kpiData.tonnage.current, kpiData.tonnage.target, kpiData.tonnage.trend)} />
                <stop offset="1" stopColor="#22d3ee" />
              </linearGradient>
            </defs>
            <path d="M36 14h10a8 8 0 1 1 0 16h-8M28 50H18a8 8 0 1 1 0-16h8M24 34h16M24 30h16" />
          </svg>
        </BentoCard>
      ),
      performance: (
        <BentoCard className={cardClass}>
          <div className={styles.content}>
            <div className={`${styles.labelTop} ${styles.eyebrow}`}>Performance</div>
            <div className={styles.headline} style={{ fontSize: 'clamp(18px,2.6vw,22px)', fontWeight: 600, opacity: 0.95 }}>
              {kpiData.performance.current.toFixed(1)}%
            </div>
          </div>
          <div className={styles.beam} aria-hidden="true" style={{
            background: `linear-gradient(180deg, rgba(212, 91, 255, 0), ${getPerformanceColor(kpiData.performance.current, kpiData.performance.target, kpiData.performance.trend)}, rgba(34, 211, 238, 0))`
          }}></div>
        </BentoCard>
      ),
      efficiency: (
        <BentoCard className={cardClass}>
          <div className={styles.content}>
            <div className={`${styles.labelTop} ${styles.eyebrow}`}>Effizienz</div>
            <div className={styles.headline} style={{ fontSize: 'clamp(18px,2.6vw,22px)', fontWeight: 600, opacity: 0.95 }}>
              {kpiData.efficiency.current.toFixed(1)}%
            </div>
            <div className={styles.bars} aria-hidden="true">
              {[...Array(5)].map((_, i) => {
                const height = Math.max(35, Math.min(100, getPercentage(kpiData.efficiency.current, kpiData.efficiency.target) - (i * 15)));
                return (
                  <div 
                    key={i}
                    className={styles.bar} 
                    style={{ 
                      height: `${height}%`,
                      background: `linear-gradient(180deg, ${getPerformanceColor(kpiData.efficiency.current, kpiData.efficiency.target, kpiData.efficiency.trend)}, rgba(20, 198, 198, 0.22))`
                    }}
                  ></div>
                );
              })}
            </div>
          </div>
        </BentoCard>
      ),
      // Neue KPI-Karten
      schnitte: (
        <BentoCard className={cardClass}>
          <div className={styles.content}>
            <div className={`${styles.labelTop} ${styles.eyebrow}`}>Schnitte</div>
            <div className={styles.headline} style={{ fontSize: 'clamp(18px,2.6vw,22px)', fontWeight: 600, opacity: 0.95 }}>
              1,247
              <span style={{ fontSize: '0.4em', opacity: 0.7, marginLeft: '8px' }}>Stk</span>
            </div>
          </div>
        </BentoCard>
      ),
      quality: (
        <BentoCard className={cardClass}>
          <div className={styles.content}>
            <div className={`${styles.labelTop} ${styles.eyebrow}`}>Qualitätsrate</div>
            <div className={styles.headline} style={{ fontSize: 'clamp(18px,2.6vw,22px)', fontWeight: 600, opacity: 0.95 }}>
              98.7%
            </div>
          </div>
        </BentoCard>
      )
    };
    
    return kpiCards[id as keyof typeof kpiCards] || null;
  };

  // Definiere die ursprünglichen Größenklassen für jede KPI-Karte
  const originalCardSizes: Record<string, string> = {
    wareneingang: 'large',    // Große Karte (2x2) - entspricht t1
    tonnage: 'medium',        // Mittlere Karte (2x1) - entspricht t4
    performance: 'small',     // Kleine Karte (2x1) - entspricht t2
    efficiency: 'small'       // Kleine Karte (2x1) - entspricht t2
  };

  // Sortiere die KPI-Karten basierend auf den gespeicherten Positionen (nur aktive Karten)
  const sortedKpiCards = useMemo(() => {
    return cardPositions
      .filter((pos: CardPosition) => activeCards.includes(pos.id))
      .sort((a: CardPosition, b: CardPosition) => a.position - b.position)
      .map((pos: CardPosition) => ({
        id: pos.id,
        card: createKPICard(pos.id, originalCardSizes[pos.id] || 'medium')
      }));
  }, [cardPositions, activeCards, departmentKPIs]);

  return (
    <div className={`${styles.wrap} ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h2 className={styles.title}>Bento Grid – Tech Showcase mit Drag & Drop</h2>
        
        {/* KPI-Bibliothek Button (nur im Edit-Modus) */}
        {isEditMode && (
          <Button
            onClick={() => setIsLibraryOpen(true)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Library className="h-4 w-4" />
            KPI-Bibliothek
          </Button>
        )}
      </div>

      <div className={styles.bento}>
        {/* Ziehbare KPI-Karten in Droppable-Containern */}
        {sortedKpiCards.map(({ id, card }: { id: string; card: React.ReactNode }, index: number) => (
          <DroppableContainer
            key={`drop-${id}`}
            id={id} // Verwende die Karten-ID direkt als Container-ID
            className={cn(
              "kpi-drop-zone relative group",
              isEditMode && "ring-2 ring-blue-200 ring-opacity-50"
            )}
          >
            <DraggableCard key={id} id={id} className="relative">
              {/* Entfernen-Button (nur im Edit-Modus) */}
              {isEditMode && (
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeCard(id);
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
              {card}
            </DraggableCard>
          </DroppableContainer>
        ))}

        {/* Statische Karten (T5-T10) - nicht ziehbar */}
        
        {/* T5 – Performance Dial mit echten Daten */}
        <BentoCard className={styles.t5}>
          <div className={styles.dial} style={{
            '--performance-angle': `${(kpiData.performance.current / 100) * 360}deg`
          } as React.CSSProperties} aria-hidden="true"></div>
          <div className={styles.content} style={{ justifyContent: 'center', alignItems: 'flex-start' }}>
            <div className={styles.eyebrow} style={{ marginBottom: '8px' }}>Gesamtperformance</div>
            <div className={styles.big}>{performanceValue.toFixed(1)}%</div>
            <div style={{ 
              fontSize: '11px', 
              opacity: 0.7, 
              color: getPerformanceColor(kpiData.performance.current, kpiData.performance.target, kpiData.performance.trend)
            }}>
              Ziel: {kpiData.performance.target}%
            </div>
          </div>
        </BentoCard>

        {/* T6 – Automation Control */}
        <BentoCard className={styles.t6}>
          <div className={styles.glow} aria-hidden="true"></div>
          <div className={styles.content}>
            <div className={`${styles.labelTop} ${styles.eyebrow}`} style={{ color: '#b8ffe1' }}>Automatisierung</div>
            <div style={{ marginTop: 'auto' }}>
              <div className={styles.headline}>STEUERUNG</div>
              <div className={styles.sliderWrap}>
                <span className={styles.eyebrow} style={{ color: '#b8ffe1', letterSpacing: '.12em' }}>Level</span>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={sliderValue}
                  onChange={handleSliderChange}
                  style={{ '--val': `${sliderValue}%` } as React.CSSProperties}
                  aria-label="Automatisierungsgrad einstellen"
                />
                <span style={{ fontSize: '12px', color: 'var(--accent-lime)', fontWeight: '600', marginTop: '4px' }}>
                  {sliderValue}%
                </span>
              </div>
            </div>
          </div>
        </BentoCard>

        {/* T7 Security Vault */}
        <BentoCard className={styles.t7}>
          <div className={styles.content}>
            <div className={styles.eyebrow}>Security</div>
            <div className={styles.headline}>VAULT</div>
          </div>
          <svg className={styles.lock} viewBox="0 0 64 64" fill="none" aria-hidden="true">
            <defs>
              <linearGradient id="gradLock" x1="0" y1="0" x2="64" y2="64">
                <stop stopColor="#d45bff" />
                <stop offset="1" stopColor="#22d3ee" />
              </linearGradient>
            </defs>
            <path d="M20 30h24a6 6 0 0 1 6 6v14a6 6 0 0 1-6 6H20a6 6 0 0 1-6-6V36a6 6 0 0 1 6-6z" />
            <path d="M24 30v-6a8 8 0 1 1 16 0v6" />
            <path d="M32 40v6" />
          </svg>
        </BentoCard>

        {/* T8 Rotating ring */}
        <BentoCard className={styles.t8}>
          <div className={styles.ring} aria-hidden="true"></div>
        </BentoCard>

        {/* T9 Slim neon line */}
        <BentoCard className={styles.t9}>
          <div className={styles.line} aria-hidden="true"></div>
        </BentoCard>

        {/* T10 Subtle cube */}
        <BentoCard className={styles.t10}>
          <div className={styles.cube} aria-hidden="true"></div>
        </BentoCard>
      </div>
      
      {/* KPI-Bibliothek Modal */}
      <KPILibrary
        isVisible={isLibraryOpen}
        onClose={() => setIsLibraryOpen(false)}
      />
    </div>
  );
};

export default DraggableBentoGrid;