# Backend Scripts

This directory contains utility scripts for database management and setup.

## RAG Database Management

### Database Initialization
The RAG database uses SQLite with Drizzle ORM. Categories are automatically initialized when the database is created using the `init-rag-database.sql` schema.

### Categories Available
- **dispatch** (Versand) - Versand- und Lieferoperationen
- **cutting** (Ablängerei) - Schneid- und Ablängoperationen  
- **incoming-goods** (Wareneingang) - Wareneingang und Annahme
- **system** (System) - Systemdokumentation und -konfiguration
- **quality** (Qualität) - Qualitätssicherung und -kontrolle
- **maintenance** (Wartung) - Wartung und Instandhaltung
- **safety** (Sicherheit) - Sicherheitsrichtlinien und Verfahren
- **app** (App Leitfaden) - Anwendungsleitfäden und Benutzerhandbücher
- **procedures** (Verfahren) - Betriebsverfahren und Arbeitsanweisungen
- **kpi** (KPI & Metriken) - Leistungskennzahlen und Metriken

### Manual Category Management
To add or modify categories manually, use SQLite commands:

```bash
# Add a new category
sqlite3 "backend/database/rag_knowledge.db" "INSERT OR IGNORE INTO categories (id, name, description) VALUES ('new-category', 'New Category', 'Description');"  # TODO: PostgreSQL-Befehle verwenden

# List all categories
sqlite3 "backend/database/rag_knowledge.db" "SELECT * FROM categories;"  # TODO: PostgreSQL-Befehle verwenden
```

### Requirements
- SQLite3 installed  # TODO: PostgreSQL installiert
- RAG database file exists at `backend/database/rag_knowledge.db`
- Database schema initialized with `init-rag-database.sql`

### Error Handling
- Use `INSERT OR IGNORE` to prevent duplicate category errors
- Check foreign key constraints when deleting categories
- Backup database before making manual changes