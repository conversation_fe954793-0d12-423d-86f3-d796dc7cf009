/**
 * Authentifizierungs-Middleware
 * 
 * Überprüft API-Schlüssel für alle geschützten Routen und
 * stellt sicher, dass nur autorisierte Anfragen verarbeitet werden.
 */

import { Request, Response, NextFunction } from 'express';

/**
 * Erweitert das Express Request Interface um Benutzerinformationen
 */
export interface AuthenticatedRequest extends Request {
  user?: {
    apiKey: string;
    authenticated: boolean;
  };
}

/**
 * Authentifizierungs-Middleware für API-Routen
 * 
 * Überprüft den Authorization Header auf einen gültigen Bearer Token
 * oder einen X-API-Key Header für API-Schlüssel-Authentifizierung.
 * 
 * @param apiSecretKey Der gültige API-Schlüssel aus den Umgebungsvariablen
 * @returns Express Middleware-Funktion
 */
export function createAuthMiddleware(apiSecretKey: string) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      // Health-Check und Test-Routen sind öffentlich zugänglich
      // RAG-Test-Route ist ebenfalls öffentlich für Frontend-Initialisierung
      if (req.path === '/health' || req.path === '/test' || req.path === '/rag/test') {
        return next();
      }

      // Extrahiere API-Schlüssel aus verschiedenen Header-Formaten
      let providedKey: string | undefined;

      // Option 1: Authorization Bearer Token
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        providedKey = authHeader.substring(7);
      }

      // Option 2: X-API-Key Header (Alternative)
      if (!providedKey) {
        providedKey = req.headers['x-api-key'] as string;
      }

      // Option 3: Query Parameter (nur für Entwicklung)
      if (!providedKey && process.env.NODE_ENV === 'development') {
        providedKey = req.query.apiKey as string;
      }

      // Überprüfe ob API-Schlüssel vorhanden ist
      if (!providedKey) {
        const clientIP = (req as any).clientIP || req.ip || 'unbekannt';
        console.warn(`[AUTH] Keine Authentifizierung für ${req.method} ${req.originalUrl} von IP: ${clientIP}`);
        
        res.status(401).json({
          success: false,
          error: 'Authentifizierung erforderlich',
          message: 'Bitte geben Sie einen gültigen API-Schlüssel im Authorization Header oder X-API-Key Header an.',
          code: 'MISSING_API_KEY'
        });
        return;
      }

      // Validiere API-Schlüssel
      if (providedKey !== apiSecretKey) {
        const clientIP = (req as any).clientIP || req.ip || 'unbekannt';
        console.warn(`[AUTH] Ungültiger API-Schlüssel von IP: ${clientIP}, bereitgestellt: ${providedKey.substring(0, 8)}...`);
        
        res.status(403).json({
          success: false,
          error: 'Ungültiger API-Schlüssel',
          message: 'Der bereitgestellte API-Schlüssel ist nicht gültig.',
          code: 'INVALID_API_KEY'
        });
        return;
      }

      // Authentifizierung erfolgreich - erweitere Request-Objekt
      req.user = {
        apiKey: providedKey,
        authenticated: true
      };

      // Erfolgreiche Authentifizierung loggen (nur in Entwicklung)
      if (process.env.NODE_ENV === 'development') {
        const clientIP = (req as any).clientIP || req.ip || 'unbekannt';
        console.log(`[AUTH] ✅ Authentifiziert: ${req.method} ${req.originalUrl} von IP: ${clientIP}`);
      }

      next();
    } catch (error) {
      console.error('[AUTH] Fehler bei der Authentifizierung:', error);
      
      res.status(500).json({
        success: false,
        error: 'Interner Authentifizierungsfehler',
        message: 'Es ist ein Fehler bei der Authentifizierung aufgetreten.',
        code: 'AUTH_ERROR'
      });
      return;
    }
  };
}

/**
 * Middleware für optionale Authentifizierung
 * 
 * Überprüft den API-Schlüssel falls vorhanden, blockiert aber nicht
 * bei fehlender Authentifizierung. Nützlich für öffentliche APIs mit
 * erhöhten Limits für authentifizierte Benutzer.
 * 
 * @param apiSecretKey Der gültige API-Schlüssel aus den Umgebungsvariablen
 * @returns Express Middleware-Funktion
 */
export function createOptionalAuthMiddleware(apiSecretKey: string) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      const authHeader = req.headers.authorization;
      const apiKeyHeader = req.headers['x-api-key'] as string;

      let providedKey: string | undefined;
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        providedKey = authHeader.substring(7);
      } else if (apiKeyHeader) {
        providedKey = apiKeyHeader;
      }

      // Falls kein Schlüssel bereitgestellt wurde, als nicht-authentifiziert behandeln
      if (!providedKey) {
        req.user = { apiKey: '', authenticated: false };
        return next();
      }

      // Falls ungültiger Schlüssel, trotzdem weiterleiten aber als nicht-authentifiziert markieren
      if (providedKey !== apiSecretKey) {
        const clientIP = (req as any).clientIP || req.ip || 'unbekannt';
        console.warn(`[AUTH-OPTIONAL] Ungültiger API-Schlüssel ignoriert von IP: ${clientIP}`);
        req.user = { apiKey: '', authenticated: false };
        return next();
      }

      // Gültiger Schlüssel
      req.user = {
        apiKey: providedKey,
        authenticated: true
      };

      next();
    } catch (error) {
      console.error('[AUTH-OPTIONAL] Fehler bei der optionalen Authentifizierung:', error);
      // Bei Fehlern als nicht-authentifiziert behandeln und weiterleiten
      req.user = { apiKey: '', authenticated: false };
      next();
    }
  };
}

/**
 * Hilfsfunktion zur Extraktion der Client-IP-Adresse
 * Berücksichtigt Proxy-Header für korrekte IP-Erkennung
 * 
 * @param req Express Request-Objekt
 * @returns Client-IP-Adresse
 */
export function getClientIP(req: Request): string {
  return (
    (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
    req.headers['x-real-ip'] as string ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    'unbekannt'
  );
}

/**
 * Middleware zur IP-Adress-Erkennung
 * Fügt die Client-IP zur Request hinzu für besseres Logging
 */
export function ipDetectionMiddleware(req: Request, res: Response, next: NextFunction): void {
  // Express setzt req.ip automatisch, aber wir können es mit unserer Logik überschreiben
  // durch erweitern des Request-Objekts
  (req as any).clientIP = getClientIP(req);
  next();
}