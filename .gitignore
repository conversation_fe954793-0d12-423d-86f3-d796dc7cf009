# Ignore database files
/database/
/database/**/*
**/*.db
**/*.db-shm
**/*.db-wal
**/*.sqlite
**/*.sqlite3
**/*.sql
**/sfm_dashboard.db
**/sfm_dashboard.db.backup-20250821-040903 (1939.80 MB)

# Ignore build outputs
/portable-*/
/portable-real/
**/LappDashboard.exe
**/*.exe
**/*.dll
**/*.lib
**/*.a
**/*.pdb
**/*.ilk

# Ignore large files (über 50MB)
**/*.zip
**/*.tar
**/*.tar.gz
**/*.rar
**/*.7z
**/*.iso

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock
.DS_Store

# Build directories
/dist
/build
/out
/out-*
/out-min
/out-min/**/*
/node_modules
/backend/node_modules

# Prisma engine files (large binaries)
**/query_engine-*
**/libquery_engine-*
**/schema-engine*
**/.prisma/client/*.node
**/.prisma/client/*.dll.node*
**/prisma/engines/**/*
**/@prisma/engines/**/*

# Environment variables
.env
.env.local
.env.*.local
.env.teams

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# CLI working dir
.crush/
.roo/
.claude/
.windsurf/



*.backup-20250821-040903
.clinerules/byterover-rules.md
.kilocode/rules/byterover-rules.md
.roo/rules/byterover-rules.md
.windsurf/rules/byterover-rules.md
.cursor/rules/byterover-rules.mdc
.kiro/steering/byterover-rules.md
.qoder/rules/byterover-rules.md
.augment/rules/byterover-rules.md