#!/usr/bin/env python3
"""
Workflow Logger für SAP-Automatisierung
Strukturiertes Logging-System mit PostgreSQL-Persistierung
"""

import json
import psycopg2  # Auf PostgreSQL migriert
import uuid
import os
from datetime import datetime
from typing import Literal, Optional, Dict, Any
from pathlib import Path

LogLevel = Literal['info', 'warning', 'error', 'debug']

class WorkflowLogger:
    """
    Strukturiertes Logging-System für SAP Workflow-Prozesse
    Speichert Logs sowohl in PostgreSQL als auch in JSON-Format für Frontend-Zugriff
    """
    
    def __init__(self, workflow_id: str, db_config: Optional[Dict[str, Any]] = None):
        self.workflow_id = workflow_id
        
        # PostgreSQL-Konfiguration aus Umgebungsvariablen oder übergebenen Parametern
        if db_config is None:
            self.db_config = {
                'host': os.getenv("DB_HOST", "localhost"),
                'database': os.getenv("DB_NAME", "lapp_dashboard"),
                'user': os.getenv("DB_USER", "postgres"),
                'password': os.getenv("DB_PASSWORD", ""),
                'port': os.getenv("DB_PORT", "5432")
            }
        else:
            self.db_config = db_config
            
        self.execution_id = str(uuid.uuid4())
        self.start_time = datetime.now()
        
        # Erstelle Logs-Tabelle falls nicht vorhanden
        self._init_logs_table()
        
    def _get_connection(self):
        """Stellt eine Verbindung zur PostgreSQL-Datenbank her"""
        try:
            conn = psycopg2.connect(
                host=self.db_config['host'],
                database=self.db_config['database'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                port=self.db_config['port']
            )
            return conn
        except Exception as e:
            print(f"[WorkflowLogger] Fehler beim Herstellen der Datenbankverbindung: {e}")
            raise
    
    def _init_logs_table(self):
        """Erstellt die workflow_logs Tabelle falls sie nicht existiert"""
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS workflow_logs (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    level TEXT NOT NULL,
                    message TEXT NOT NULL,
                    workflow_id TEXT NOT NULL,
                    execution_id TEXT,
                    details TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Index für bessere Performance
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_workflow_logs_workflow_id 
                ON workflow_logs(workflow_id)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_workflow_logs_timestamp 
                ON workflow_logs(timestamp)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_workflow_logs_level 
                ON workflow_logs(level)
            ''')
            
            conn.commit()
            print(f"[WorkflowLogger] PostgreSQL-Tabelle workflow_logs initialisiert")
            
        except Exception as e:
            print(f"[WorkflowLogger] Fehler beim Initialisieren der Logs-Tabelle: {e}")
        finally:
            if conn:
                conn.close()
    
    def log(self, level: LogLevel, message: str, details: Optional[Dict[str, Any]] = None):
        """
        Schreibt einen Log-Eintrag in die Datenbank
        
        Args:
            level: Log-Level (info, warning, error, debug)
            message: Log-Nachricht
            details: Zusätzliche Details als Dictionary
        """
        log_entry = {
            'id': str(uuid.uuid4()),
            'timestamp': datetime.now().isoformat(),
            'level': level,
            'message': message,
            'workflow_id': self.workflow_id,
            'execution_id': self.execution_id,
            'details': json.dumps(details) if details else None
        }
        
        conn = None
        try:
            # In Datenbank speichern
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO workflow_logs 
                (id, timestamp, level, message, workflow_id, execution_id, details)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            ''', (
                log_entry['id'],
                log_entry['timestamp'],
                log_entry['level'],
                log_entry['message'],
                log_entry['workflow_id'],
                log_entry['execution_id'],
                log_entry['details']
            ))
            
            conn.commit()
            
            # Auch in Konsole ausgeben für Debugging
            timestamp_str = datetime.now().strftime("%H:%M:%S")
            level_str = level.upper().ljust(7)
            print(f"[{timestamp_str}] {level_str} [{self.workflow_id}] {message}")
            
        except Exception as e:
            print(f"[WorkflowLogger] Fehler beim Schreiben des Log-Eintrags: {e}")
            # Fallback: Nur Konsolen-Output
            timestamp_str = datetime.now().strftime("%H:%M:%S")
            level_str = level.upper().ljust(7)
            print(f"[{timestamp_str}] {level_str} [{self.workflow_id}] {message}")
        finally:
            if conn:
                conn.close()
    
    def info(self, message: str, details: Optional[Dict[str, Any]] = None):
        """Log Info-Nachricht"""
        self.log('info', message, details)
    
    def warning(self, message: str, details: Optional[Dict[str, Any]] = None):
        """Log Warning-Nachricht"""
        self.log('warning', message, details)
    
    def error(self, message: str, details: Optional[Dict[str, Any]] = None):
        """Log Error-Nachricht"""
        self.log('error', message, details)
    
    def debug(self, message: str, details: Optional[Dict[str, Any]] = None):
        """Log Debug-Nachricht"""
        self.log('debug', message, details)
    
    def log_process_start(self, process_name: str):
        """Loggt den Start eines Prozesses"""
        self.info(f"Starte Prozess: {process_name}", {
            'process_name': process_name,
            'execution_id': self.execution_id,
            'start_time': self.start_time.isoformat(),
            'event_type': 'process_start'
        })
    
    def log_process_complete(self, process_name: str, export_path: Optional[str] = None):
        """Loggt den erfolgreichen Abschluss eines Prozesses"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        self.info(f"Prozess abgeschlossen: {process_name}", {
            'process_name': process_name,
            'execution_id': self.execution_id,
            'duration_seconds': duration,
            'export_path': export_path,
            'end_time': end_time.isoformat(),
            'event_type': 'process_complete'
        })
    
    def log_process_error(self, process_name: str, error_message: str):
        """Loggt einen Prozess-Fehler"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        self.error(f"Prozess fehlgeschlagen: {process_name} - {error_message}", {
            'process_name': process_name,
            'execution_id': self.execution_id,
            'duration_seconds': duration,
            'error_message': error_message,
            'end_time': end_time.isoformat(),
            'event_type': 'process_error'
        })
    
    def log_sap_action(self, action: str, element_id: Optional[str] = None, success: bool = True):
        """Loggt SAP GUI-Aktionen"""
        level = 'info' if success else 'warning'
        message = f"SAP Aktion: {action}"
        if element_id:
            message += f" (Element: {element_id})"
        
        self.log(level, message, {
            'action': action,
            'element_id': element_id,
            'success': success,
            'event_type': 'sap_action'
        })
    
    def log_file_operation(self, operation: str, file_path: str, success: bool = True):
        """Loggt Datei-Operationen"""
        level = 'info' if success else 'error'
        message = f"Datei {operation}: {file_path}"
        
        self.log(level, message, {
            'operation': operation,
            'file_path': file_path,
            'success': success,
            'event_type': 'file_operation'
        })
    
    def log_database_operation(self, operation: str, table_name: str, record_count: Optional[int] = None, success: bool = True):
        """Loggt Datenbank-Operationen"""
        level = 'info' if success else 'error'
        message = f"Datenbank {operation}: {table_name}"
        if record_count is not None:
            message += f" ({record_count} Datensätze)"
        
        self.log(level, message, {
            'operation': operation,
            'table_name': table_name,
            'record_count': record_count,
            'success': success,
            'event_type': 'database_operation'
        })

    @classmethod
    def get_logs_for_workflow(cls, workflow_id: str, db_config: Optional[Dict[str, Any]] = None, limit: int = 100):
        """
        Lädt Logs für einen bestimmten Workflow aus der Datenbank
        """
        # Standardkonfiguration aus Umgebungsvariablen
        if db_config is None:
            db_config = {
                'host': os.getenv("DB_HOST", "localhost"),
                'database': os.getenv("DB_NAME", "lapp_dashboard"),
                'user': os.getenv("DB_USER", "postgres"),
                'password': os.getenv("DB_PASSWORD", ""),
                'port': os.getenv("DB_PORT", "5432")
            }
        
        conn = None
        try:
            # Verbindung herstellen
            conn = psycopg2.connect(
                host=db_config['host'],
                database=db_config['database'],
                user=db_config['user'],
                password=db_config['password'],
                port=db_config['port']
            )
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, timestamp, level, message, workflow_id, execution_id, details
                FROM workflow_logs
                WHERE workflow_id = %s
                ORDER BY timestamp DESC
                LIMIT %s
            ''', (workflow_id, limit))
            
            rows = cursor.fetchall()
            
            logs = []
            for row in rows:
                log_entry = {
                    'id': row[0],
                    'timestamp': row[1],
                    'level': row[2],
                    'message': row[3],
                    'workflow_id': row[4],
                    'execution_id': row[5],
                    'details': json.loads(row[6]) if row[6] else None
                }
                logs.append(log_entry)
            
            return logs
            
        except Exception as e:
            print(f"[WorkflowLogger] Fehler beim Laden der Logs für Workflow {workflow_id}: {e}")
            return []
        finally:
            if conn:
                conn.close()
    
    @classmethod
    def get_all_logs(cls, db_config: Optional[Dict[str, Any]] = None, limit: int = 200):
        """
        Lädt alle Workflow-Logs aus der Datenbank
        """
        # Standardkonfiguration aus Umgebungsvariablen
        if db_config is None:
            db_config = {
                'host': os.getenv("DB_HOST", "localhost"),
                'database': os.getenv("DB_NAME", "lapp_dashboard"),
                'user': os.getenv("DB_USER", "postgres"),
                'password': os.getenv("DB_PASSWORD", ""),
                'port': os.getenv("DB_PORT", "5432")
            }
        
        conn = None
        try:
            # Verbindung herstellen
            conn = psycopg2.connect(
                host=db_config['host'],
                database=db_config['database'],
                user=db_config['user'],
                password=db_config['password'],
                port=db_config['port']
            )
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, timestamp, level, message, workflow_id, execution_id, details
                FROM workflow_logs
                ORDER BY timestamp DESC
                LIMIT %s
            ''', (limit,))
            
            rows = cursor.fetchall()
            
            logs = []
            for row in rows:
                log_entry = {
                    'id': row[0],
                    'timestamp': row[1],
                    'level': row[2],
                    'message': row[3],
                    'workflow_id': row[4],
                    'execution_id': row[5],
                    'details': json.loads(row[6]) if row[6] else None
                }
                logs.append(log_entry)
            
            return logs
            
        except Exception as e:
            print(f"[WorkflowLogger] Fehler beim Laden aller Logs: {e}")
            return []
        finally:
            if conn:
                conn.close()