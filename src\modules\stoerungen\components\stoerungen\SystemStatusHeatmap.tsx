import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DateRange } from 'react-day-picker';
import { Activity, AlertTriangle, CheckCircle, XCircle, Power } from 'lucide-react';
import { SystemStatus, SYSTEM_STATUS_COLORS } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';
import { SystemStatusCard } from '@/components/stoerungen/SystemStatusCard';
import { cn } from '@/lib/utils';
import terminalBg from '@/assets/terminalOB.png';
import wlanBg from '@/assets/WlanOB.png';
import foerdertechnikBg from '@/assets/FTechnikOB.png';
import staplerBg from '@/assets/StaplerOB.png';
import sapBg from '@/assets/SAPOB.png';
import itmBg from '@/assets/witron.webp';
import atrlBg from '@/assets/iconATRL.png';
import databaseBg from '@/assets/DatabaseOB.png';
import drumBg from '@/assets/DrumOB.png';
import ringBg from '@/assets/RingOB.png';

// Helper function to get machine-specific background images
const getMachineBackgroundImage = (systemName: string): { backgroundImage: string; backgroundSize: string; backgroundPosition: string; backgroundRepeat: string } | null => {
  // Check if system is a machine with specific identifier pattern
  const machineMatch = systemName.match(/^M\d+-([TR]+)-H\d+$/);
  if (!machineMatch) return null;
  
  const machineType = machineMatch[1];
  
  if (machineType === 'T') {
    // Terminal machines get drumBg
    return {
      backgroundImage: `url(${drumBg})`,
      backgroundSize: '70%',
      backgroundPosition: 'right center',
      backgroundRepeat: 'no-repeat'
    };
  } else if (machineType === 'R' || machineType === 'RR') {
    // Ring machines get ringBg
    return {
      backgroundImage: `url(${ringBg})`,
      backgroundSize: '70%',
      backgroundPosition: 'right center',
      backgroundRepeat: 'no-repeat'
    };
  }
  
  return null;
};

interface SystemStatusHeatmapProps {
  dateRange?: DateRange;
  refreshKey?: number;
}

export const SystemStatusHeatmap: React.FC<SystemStatusHeatmapProps> = React.memo(({ 
  dateRange, 
  refreshKey = 0 
}) => {
  const [systemStatuses, setSystemStatuses] = useState<SystemStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSystemStatus = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await stoerungenService.getSystemStatus();
        setSystemStatuses(data);
      } catch (err) {
        console.error('Error fetching system status:', err);
        setError('Fehler beim Laden der Systemdaten');
        
        // Fallback: Empty array - real data should come from backend
        console.warn('SystemStatus API failed, showing empty state. Please ensure the SystemStatus table is populated.');
        setSystemStatuses([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSystemStatus();
  }, [refreshKey]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OK':
        return 'bg-green-500';
      case 'WARNING':
        return 'bg-yellow-500';
      case 'ERROR':
        return 'bg-red-500';
      case 'OFF':
        return 'bg-gray-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusCount = (status: string) => {
    return systemStatuses.filter(system => system.status === status).length;
  };

  const formatLastCheck = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Gerade eben';
    if (diffMinutes < 60) return `${diffMinutes}m`;
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h`;
    return `${Math.floor(diffMinutes / 1440)}d`;
  };

  if (loading) {
    return (
      <Card className="h-full border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Status Heatmap
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          System Status Heatmap
        </CardTitle>
        
        {/* Status Summary */}
        <div className="flex items-center gap-4 text-sm">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>OK ({getStatusCount('OK')})</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span>Warnung ({getStatusCount('WARNING')})</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span>Fehler ({getStatusCount('ERROR')})</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
            <span>Aus ({getStatusCount('OFF')})</span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {error && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">{error}</p>
            <p className="text-xs text-yellow-600 mt-1">Zeige Beispieldaten zur Demonstration</p>
          </div>
        )}
        
        {/* Group systems by category with specific rules for 'Maschinen' */}
        {(() => {
          // Define categories in order of precedence for primary assignment
          const primaryCategoryDefinitions: [string, (system: SystemStatus) => boolean][] = [
            ['Datenbanken', (system) => system.system_name.includes('Datenbank')],
            ['Terminals', (system) => system.system_name.includes('Terminal')],
            ['Fördertechnik', (system) => system.system_name.includes('Fördertechnik')],
            ['Anlagen', (system) => system.system_name.includes('Schrumpfanlage')],
            ['Netzwerk', (system) => system.system_name.includes('Wlan')],
            ['Läger', (system) => 
              system.system_name.includes('Automatisches Trommellager') ||
              system.system_name.includes('Automatisches Ringlager')
            ],
            ['Flurförderzeuge', (system) => 
              system.system_name.includes('Stapler') ||
              system.system_name.includes('FTS')
            ],
            ['SAP', (system) => system.system_name.includes('SAP')],
            ['ITM', (system) => system.system_name.includes('ITM')],
          ];

          // Categorize systems ensuring each system is primarily in one category (first match)
          const primaryCategorizedSystems: Record<string, SystemStatus[]> = {};
          const alreadyCategorized = new Set<number>(); // Track system IDs for primary assignment

          primaryCategoryDefinitions.forEach(([categoryName, filterFn]) => {
            primaryCategorizedSystems[categoryName] = systemStatuses.filter(system => {
              if (alreadyCategorized.has(system.id)) {
                return false; // Already assigned to a previous primary category
              }
              const matches = filterFn(system);
              if (matches) {
                alreadyCategorized.add(system.id); // Mark as assigned for primary categorization
              }
              return matches;
            });
          });

          // Handle 'Maschinen' category: Include all systems with specific machine identifiers
          // regardless of other categorizations
          const machineIdentifiers = [
            'M1-T-H3', 'M2-T-H3', 'M3-R-H3', 'M4-T-H3', 'M5-R-H3',
            'M6-T-H1', 'M7-R-H1', 'M8-T-H1', 'M9-R-H1', 'M10-T-H1',
            'M11-R-H1', 'M12-T-H1', 'M13-R-H1', 'M14-T-H1', 'M15-R-H1',
            'M16-T-H1', 'M17-R-H1', 'M18-T-H1', 'M19-T-H1', 'M20-T-H1',
            'M21-R-H1', 'M22-T-H3', 'M23-T-H1', 'M24-T-H3', 'M25-RR-H1',
            'M26-T-H1', 'M27-R-H3', 'M28-T-H1'
          ];
          
          // Debug: Log system names and check for matches
          console.log("SystemStatusHeatmap: All system names:", systemStatuses.map(s => s.system_name));
          const machines = systemStatuses.filter(system => 
            machineIdentifiers.some(id => system.system_name.includes(id))
          );
          console.log("SystemStatusHeatmap: Systems matching machine identifiers:", machines);
          console.log("SystemStatusHeatmap: Machine identifiers list:", machineIdentifiers);
          
          primaryCategorizedSystems['Maschinen'] = machines;

          // Filter out empty categories
          const nonEmptyCategories = Object.entries(primaryCategorizedSystems).filter(([_, systems]) => systems.length > 0);
          
          return (
            <div className="space-y-2">
              {nonEmptyCategories.map(([category, systems]) => (
                <div key={category}>
                  <h3 className="text-lg font-semibold mb-1">{category} ({systems.length})</h3>
                  <div className="grid grid-cols-4 md:grid-cols-5 lg:grid-cols-6" style={{ gap: '5px' }}>
                    {systems.map((system) => {
                      // Get background style for this system
                      const backgroundStyle = (() => {
                        // Check for machine-specific backgrounds first
                        const machineBackground = getMachineBackgroundImage(system.system_name);
                        if (machineBackground) return machineBackground;

                        // Fall back to other system-specific backgrounds
                        if (system.system_name.includes('Terminal')) {
                          return {
                            backgroundImage: `url(${terminalBg})`,
                            backgroundSize: '70%',
                            backgroundPosition: 'right center',
                            backgroundRepeat: 'no-repeat'
                          };
                        } else if (system.system_name.includes('Wlan')) {
                          return {
                            backgroundImage: `url(${wlanBg})`,
                            backgroundSize: '40%',
                            backgroundPosition: 'right center',
                            backgroundRepeat: 'no-repeat'
                          };
                        } else if (system.system_name.includes('Automatisches Trommellager') ||
                          system.system_name.includes('Automatisches Ringlager')) {
                          return {
                            backgroundImage: `url(${atrlBg})`,
                            backgroundSize: '30%',
                            backgroundPosition: 'right center',
                            backgroundRepeat: 'no-repeat'
                          };
                        } else if (system.system_name.includes('Stapler') || system.system_name.includes('FTS')) {
                          return {
                            backgroundImage: `url(${staplerBg})`,
                            backgroundSize: '70%',
                            backgroundPosition: 'right center',
                            backgroundRepeat: 'no-repeat'
                          };
                        } else if (system.system_name.includes('SAP')) {
                          return {
                            backgroundImage: `url(${sapBg})`,
                            backgroundSize: '70%',
                            backgroundPosition: 'right center',
                            backgroundRepeat: 'no-repeat'
                          };
                        } else if (system.system_name.includes('ITM')) {
                          return {
                            backgroundImage: `url(${itmBg})`,
                            backgroundSize: '70%',
                            backgroundPosition: 'right center',
                            backgroundRepeat: 'no-repeat'
                          };
                        } else if (system.system_name.includes('Datenbank')) {
                          return {
                            backgroundImage: `url(${databaseBg})`,
                            backgroundSize: '50%',
                            backgroundPosition: 'right center',
                            backgroundRepeat: 'no-repeat'
                          };
                        } else if (system.system_name.includes('Schrumpfanlagen')) {
                          return {
                            backgroundImage: `url(${databaseBg})`,
                            backgroundSize: '80%',
                            backgroundPosition: 'right center',
                            backgroundRepeat: 'no-repeat'
                          };
                        } else if (system.system_name.includes('Fördertechnik')) {
                          return {
                            backgroundImage: `url(${foerdertechnikBg})`,
                            backgroundSize: '60%',
                            backgroundPosition: 'right center',
                            backgroundRepeat: 'no-repeat'
                          };
                        }
                        return {};
                      })();

                      return (
                        <SystemStatusCard
                          key={system.id}
                          system={system}
                          backgroundStyle={backgroundStyle}
                          formatLastCheck={formatLastCheck}
                          getStatusColor={getStatusColor}
                        />
                      );
                    })}
                  </div>
                </div>
              ))}
              
              {systemStatuses.length === 0 && !loading && (
                <div className="text-center py-8 text-gray-500">
                  <Activity className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p>Keine Systemdaten verfügbar</p>
                </div>
              )}
            </div>
          );
        })()}
        
        {/* Legend */}
        <div className="mt-6 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Legende:</h4>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-2 text-xs text-gray-600">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span><strong>OK:</strong> System funktioniert normal</span>
            </div>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <span><strong>Warnung:</strong> Leichte Probleme erkannt</span>
            </div>
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <span><strong>Fehler:</strong> System nicht verfügbar</span>
            </div>
            <div className="flex items-center gap-2">
              <Power className="h-4 w-4 text-gray-600" />
              <span><strong>Aus:</strong> System ist ausgeschaltet</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

SystemStatusHeatmap.displayName = 'SystemStatusHeatmap';