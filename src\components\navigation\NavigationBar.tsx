import React from "react";
import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@tanstack/react-router";
import { Warehouse, ChevronRight } from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import {
    NavigationMenu,
    NavigationMenuContent,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
    NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
    SheetTrigger,
} from "@/components/ui/sheet";
import { MenuItem } from "@/config/navigation/types";

interface NavigationBarProps {
    menu: MenuItem[];
}

export const NavigationBar = ({ menu }: NavigationBarProps) => {
    return (
        <div className="py-2">
            {/* Desktop Menu */}
            <nav className="hidden lg:flex">
                <NavigationMenu>
                    <NavigationMenuList>
                        {menu.map((item, index) => (
                            <NavigationMenuItem key={index}>
                                {item.items ? (
                                    <DropdownMenuItem item={item} />
                                ) : (
                                    <NavMenuItem item={item} />
                                )}
                            </NavigationMenuItem>
                        ))}
                    </NavigationMenuList>
                </NavigationMenu>
            </nav>

            {/* Mobile Menu */}
            <div className="block lg:hidden">
                <Sheet>
                    <SheetTrigger asChild>
                        <Button variant="ghost" size="icon">
                            <Warehouse className="size-4" />
                        </Button>
                    </SheetTrigger>
                    <SheetContent className="overflow-y-auto bg-transparent border-none shadow-none">
                        <SheetHeader>
                            <SheetTitle>Navigation</SheetTitle>
                        </SheetHeader>
                        <div className="flex flex-col gap-6 p-4">
                            {/* Unser UI-Accordion stellt nur einen Default-Export bereit (Accordion mit items[]).
                                Die zuvor genutzten Unterkomponenten (AccordionItem/Trigger/Content) existieren nicht.
                                Wir rendern das Mobile-Menü daher ohne diese Unterkomponenten. */}
                            <div className="flex w-full flex-col gap-4">
                                {menu.map((item) => renderMobileMenuItemNode(item))}
                            </div>
                        </div>
                    </SheetContent>
                </Sheet>
            </div>
        </div>
    );
};// Einfaches Navigationsmenü-Element

// Mobile-Menüeintrag-Renderer (ohne shadcn-Unterkomponenten)
const renderMobileMenuItemNode = (item: MenuItem) => {
  if (!item.items || item.items.length === 0) {
    return (
      <Link
        key={item.title}
        to={item.to}
        className="flex items-center justify-between rounded-md px-4 py-2 text-sm hover:bg-muted/20"
      >
        <div className="flex items-center gap-2">
          {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
          <span>{item.title}</span>
        </div>
        <ChevronRight className="size-4 text-muted-foreground" />
      </Link>
    );
  }

  // Gruppe mit Unterpunkten
  return (
    <div key={item.title} className="rounded-lg border border-muted/20">
      <div className="px-4 py-2 font-semibold flex items-center gap-2">
        {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
        <span>{item.title}</span>
      </div>
      <div className="mt-2 pl-4 border-t border-muted/20">
        {item.items.map((sub) => (
          <Link
            key={sub.title}
            to={sub.to}
            className="block px-4 py-2 text-sm hover:bg-muted/15"
          >
            <div className="flex items-center gap-2">
              {sub.icon && <span className="flex-shrink-0">{sub.icon}</span>}
              <span>{sub.title}</span>
            </div>
            {sub.description && (
              <p className="text-xs text-muted-foreground mt-1">{sub.description}</p>
            )}
          </Link>
        ))}
      </div>
    </div>
  );
};

const NavMenuItem = ({ item }: { item: MenuItem }) => {
    const matchRoute = useMatchRoute();
    const isActive = matchRoute({ to: item.to });

    return (
        <NavigationMenuLink asChild>
            <Link
                to={item.to}
                className={`group inline-flex h-10 w-max items-center justify-start rounded-md px-4 py-2 text-sm font-medium transition-colors ${isActive
                        ? 'bg-white text-foreground'
                        : 'bg-transparent text-foreground hover:bg-foreground/10 hover:text-accent-foreground'
                    }`}
            >
                <div className="flex items-center gap-2">
                    {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                    <span className="whitespace-nowrap">{item.title}</span>
                </div>
            </Link>
        </NavigationMenuLink>
    );
};

// Dropdown-Menü-Element mit Untermenüs
const DropdownMenuItem = ({ item }: { item: MenuItem }) => {
    const matchRoute = useMatchRoute();

    // Prüfe, ob eines der Untermenüs aktiv ist
    const hasActiveSubItem = item.items?.some(subItem => matchRoute({ to: subItem.to }));

    return (
        <>
            <NavigationMenuTrigger
                className={`${hasActiveSubItem
                        ? 'bg-white text-foreground'
                        : 'bg-transparent text-foreground hover:bg-foreground/10'
                    }`}
            >
                <div className="flex items-center gap-2">
                    {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                    <span className="whitespace-nowrap">{item.title}</span>
                </div>
            </NavigationMenuTrigger>
            <NavigationMenuContent className="bg-white/95 backdrop-blur-md border border-gray-200/50 shadow-2xl rounded-xl overflow-hidden z-50">
                <motion.div
                    className="grid grid-cols-1 p-2 w-[420px]"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                >
                    {item.items?.map((subItem, index) => {
                        const isSubItemActive = matchRoute({ to: subItem.to });
                        return (
                            <motion.div
                                key={subItem.title}
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.15, delay: index * 0.05 }}
                            >
                                <NavigationMenuLink asChild>
                                    <Link
                                        to={subItem.to}
                                        className={cn(
                                            "group flex flex-row gap-4 rounded-lg p-4 leading-none no-underline transition-all duration-200 outline-none select-none relative overflow-hidden",
                                            isSubItemActive
                                                ? 'bg-gradient-to-r from-[#ff7a05]/10 to-[#ff7a05]/5 text-[#ff7a05] border-l-4 border-[#ff7a05]'
                                                : 'hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100/50 hover:shadow-md hover:scale-[1.02] transform'
                                        )}
                                    >
                                        {/* Background Pattern */}
                                        <div className="absolute inset-0 bg-[linear-gradient(to_right,#f0f0f0_1px,transparent_1px),linear-gradient(to_bottom,#f0f0f0_1px,transparent_1px)] bg-[size:20px_20px] opacity-30 group-hover:opacity-50 transition-opacity duration-200"></div>

                                        {/* Icon Container */}
                                        <motion.div
                                            className={cn(
                                                "relative z-10 flex h-12 w-12 items-center justify-center rounded-lg shadow-sm transition-all duration-200",
                                                isSubItemActive
                                                    ? 'bg-[#ff7a05]/10 text-[#ff7a05] shadow-[#ff7a05]/20'
                                                    : 'bg-gray-100 text-gray-600 group-hover:bg-white group-hover:shadow-md group-hover:text-[#ff7a05]'
                                            )}
                                            whileHover={{ scale: 1.1, rotate: 5 }}
                                            transition={{ duration: 0.15 }}
                                        >
                                            {subItem.icon}
                                        </motion.div>

                                        {/* Content */}
                                        <div className="relative z-10 flex-1">
                                            <div className={cn(
                                                "text-base font-semibold mb-1 transition-colors duration-200",
                                                isSubItemActive ? 'text-[#ff7a05]' : 'text-gray-900 group-hover:text-gray-900'
                                            )}>
                                                {subItem.title}
                                            </div>
                                            {subItem.description && (
                                                <p className="text-sm leading-relaxed text-gray-600 group-hover:text-gray-700 transition-colors duration-200">
                                                    {subItem.description}
                                                </p>
                                            )}
                                        </div>

                                        {/* Arrow Icon */}
                                        <motion.div
                                            className="relative z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                                            initial={{ x: -10 }}
                                            whileHover={{ x: 0 }}
                                        >
                                            <ChevronRight className="h-5 w-5 text-[#ff7a05]" />
                                        </motion.div>

                                        {/* Active Indicator */}
                                        {isSubItemActive && (
                                            <motion.div
                                                className="absolute right-0 top-0 bottom-0 w-1 bg-gradient-to-b from-[#ff7a05] to-[#ff7a05]/60"
                                                initial={{ scaleY: 0 }}
                                                animate={{ scaleY: 1 }}
                                                transition={{ duration: 0.3, ease: "easeOut" }}
                                            />
                                        )}
                                    </Link>
                                </NavigationMenuLink>

                                {/* Separator */}
                                {item.items && index < item.items.length - 1 && (
                                    <motion.div
                                        className="mx-4 my-2 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"
                                        initial={{ scaleX: 0 }}
                                        animate={{ scaleX: 1 }}
                                        transition={{ duration: 0.3, delay: index * 0.05 + 0.1 }}
                                    />
                                )}
                            </motion.div>
                        );
                    })}
                </motion.div>
            </NavigationMenuContent>
        </>
    );
};

const renderMobileMenuItem = (item: MenuItem) => {
    if (item.items) {
        return (
            <AccordionItem key={item.title} value={item.title} className="border-b-0">
                <AccordionTrigger className="text-md py-2 font-semibold hover:no-underline hover:bg-muted/20 rounded-md px-4 -mx-2">
                    {item.title}
                </AccordionTrigger>
                <AccordionContent className="mt-2 pl-4 border-l-2 border-muted/20 ml-2">
                    {item.items.map((subItem) => (
                        <div key={subItem.title} className="py-2">
                            <Link
                                to={subItem.to}
                                className="flex items-center gap-3 px-2 py-1.5 rounded-md hover:bg-muted/20 text-sm"
                            >
                                {subItem.icon && <span className="text-foreground/70">{subItem.icon}</span>}
                                <div>
                                    <div className="font-medium">{subItem.title}</div>
                                    {subItem.description && (
                                        <div className="text-xs text-muted-foreground">{subItem.description}</div>
                                    )}
                                </div>
                            </Link>
                        </div>
                    ))}
                </AccordionContent>
            </AccordionItem>
        );
    }

    return (
        <Link
            key={item.title}
            to={item.to}
            className="text-md font-medium block py-2 px-4 -mx-4 hover:bg-muted/20 rounded-md"
        >
            {item.title}
        </Link>
    );
};