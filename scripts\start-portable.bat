@echo off
REM Portables Startskript für die Lapp Dashboard Anwendung
REM Dieses Skript startet die Anwendung ohne Installation

echo ====================================
echo Lapp Dashboard - Portable Anwendung
echo ====================================
echo.

REM Prüfe, ob die Anwendung bereits läuft
tasklist /FI "IMAGENAME eq LappDashboard.exe" 2>NUL | find /I "LappDashboard.exe" >NUL
if %ERRORLEVEL% equ 0 (
    echo Anwendung läuft bereits. Beenden Sie zuerst die laufende Instanz.
    pause
    exit /b 1
)

REM Finde das Installationsverzeichnis
set APP_DIR=%~dp0
set APP_EXE=%APP_DIR%LappDashboard.exe

REM Prüfe, ob die ausführbare Datei existiert
if not exist "%APP_EXE%" (
    echo Fehler: Anwendung nicht gefunden unter %APP_EXE%
    echo Bitte stellen Sie sicher, dass Sie das Skript aus dem richtigen Verzeichnis starten.
    pause
    exit /b 1
)

REM Prüfe, ob die portale Konfigurationsdatei existiert
set CONFIG_FILE=%APP_DIR%portable-config.json
if exist "%CONFIG_FILE%" (
    echo Konfigurationsdatei gefunden: %CONFIG_FILE%
    
    REM Lese Datenbankkonfiguration (falls vorhanden)
    echo Lese Datenbankkonfiguration...
    
    REM Versuche, die Konfiguration zu lesen und zu validieren
    powershell -Command "try { $config = Get-Content '%CONFIG_FILE%' | ConvertFrom-Json; if ($config.database) { Write-Host 'Datenbank-Host:' $config.database.host; Write-Host 'Datenbank-Name:' $config.database.database } else { Write-Host 'Keine Datenbankkonfiguration gefunden' } } catch { Write-Host 'Fehler beim Lesen der Konfiguration' }"
) else (
    echo Keine Konfigurationsdatei gefunden. Verwende Standardeinstellungen.
)

echo.
echo Starte Anwendung...
echo.

REM Starte die Anwendung
start "" "%APP_EXE%"

REM Warte kurz, damit die Anwendung starten kann
timeout /t 3 /nobreak >NUL

REM Prüfe, ob die Anwendung erfolgreich gestartet wurde
tasklist /FI "IMAGENAME eq LappDashboard.exe" 2>NUL | find /I "LappDashboard.exe" >NUL
if %ERRORLEVEL% equ 0 (
    echo Anwendung wurde erfolgreich gestartet.
    echo.
    echo Hinweis: Die Anwendung läuft im Hintergrund. Schließen Sie dieses Fenster nicht.
    echo Um die Anwendung zu beenden, klicken Sie auf das X im Anwendungsfenster.
    echo.
    echo Wenn die Anwendung nicht sichtbar ist, überprüfen Sie die Taskleiste.
) else (
    echo Fehler beim Starten der Anwendung.
    echo Bitte überprüfen Sie die Log-Dateien im logs-Verzeichnis.
)

echo.
pause