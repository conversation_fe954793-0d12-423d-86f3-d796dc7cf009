/**
 * Workflow-Typdefinitionen für automatisierte Python-Skript-Überwachung
 * 
 * Diese Typen definieren die Struktur für das Monitoring von automatisierten
 * Workflows, die kontinuierlich Daten aus SAP und BI-Systemen importieren.
 */

export type WorkflowStatus = 'running' | 'completed' | 'error' | 'disabled' | 'scheduled';
export type WorkflowSourceType = 'SAP' | 'BI_Excel' | 'Database' | 'API';
export type WorkflowFrequency = 'hourly' | 'daily' | 'weekly' | 'manual';

export interface Workflow {
  id: string;
  name: string;
  description: string;
  sourceType: WorkflowSourceType;
  frequency: WorkflowFrequency;
  targetTables: string[];
  scriptPath: string;
  isActive: boolean;
  status: WorkflowStatus;
  lastExecution: Date | null;
  nextExecution: Date | null;
  duration: number | null; // in seconds
  successRate: number; // percentage
  errorCount: number;
  totalRuns: number;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  startTime: Date | null; // Kann null sein wenn noch nicht gestartet
  endTime: Date | null;
  duration: number | null; // in seconds
  status: 'running' | 'completed' | 'error';
  errorMessage: string | null;
  recordsProcessed: number | null;
  dataSize: number | null; // in bytes
}

export interface WorkflowLogEntry {
  timestamp: Date;
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  workflowId: string;
  executionId?: string;
  details?: Record<string, any>;
}

export interface WorkflowMetrics {
  workflowId: string;
  averageDuration: number; // in seconds
  successRate: number; // percentage
  lastSuccessfulRun: Date | null;
  totalExecutions: number;
  errorCount: number;
  dataFreshness: number; // minutes since last update
}

export interface WorkflowConfig {
  id: string;
  name: string;
  description: string;
  sourceType: WorkflowSourceType;
  frequency: WorkflowFrequency;
  targetTables: string[];
  scriptPath: string;
  isActive: boolean;
  cronExpression?: string;
  timeout: number; // in seconds
  retryCount: number;
  notificationEmails: string[];
  environmentVariables: Record<string, string>;
}

export interface WorkflowStatusFile {
  workflowId: string;
  status: WorkflowStatus;
  lastExecution: string | null;
  nextExecution: string | null;
  duration: number | null;
  errorMessage: string | null;
  recordsProcessed: number | null;
  updatedAt: string;
}

export interface WorkflowPerformance {
  workflowId: string;
  executionTimes: Array<{
    date: string;
    duration: number;
  }>;
  successRates: Array<{
    date: string;
    rate: number;
  }>;
  dataVolumes: Array<{
    date: string;
    records: number;
  }>;
}

// API Response Types
export interface WorkflowListResponse {
  workflows: Workflow[];
  totalCount: number;
  lastUpdated: Date;
}

export interface WorkflowDetailResponse {
  workflow: Workflow;
  metrics: WorkflowMetrics;
  recentExecutions: WorkflowExecution[];
  recentLogs: WorkflowLogEntry[];
}

export interface WorkflowHistoryResponse {
  executions: WorkflowExecution[];
  totalCount: number;
  pageSize: number;
  currentPage: number;
}