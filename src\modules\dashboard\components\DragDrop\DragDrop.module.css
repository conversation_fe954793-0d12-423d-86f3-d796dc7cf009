/* Drag-and-Drop Styling für KPI-Karten */

/* Basis-Styling für ziehbare <PERSON> */
.draggable-card {
  cursor: grab;
  transition: all 0.2s ease-in-out;
  position: relative;
  z-index: 1;
}

.draggable-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.draggable-card:active {
  cursor: grabbing;
}

/* Styling während des Ziehens */
.draggable-card--dragging {
  opacity: 0.8;
  transform: rotate(5deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  cursor: grabbing;
}

/* Drag-Handle (Griff-Bereich) */
.drag-handle {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  cursor: grab;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
}

.draggable-card:hover .drag-handle {
  opacity: 1;
}

.drag-handle:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.drag-handle:active {
  cursor: grabbing;
  transform: scale(0.95);
}

/* Drag-Handle Icon (6 Punkte) */
.drag-handle::before {
  content: '';
  width: 12px;
  height: 12px;
  background-image: 
    radial-gradient(circle at 2px 2px, currentColor 1px, transparent 1px),
    radial-gradient(circle at 2px 6px, currentColor 1px, transparent 1px),
    radial-gradient(circle at 2px 10px, currentColor 1px, transparent 1px),
    radial-gradient(circle at 6px 2px, currentColor 1px, transparent 1px),
    radial-gradient(circle at 6px 6px, currentColor 1px, transparent 1px),
    radial-gradient(circle at 6px 10px, currentColor 1px, transparent 1px);
  background-size: 8px 12px;
  background-repeat: no-repeat;
  color: rgba(255, 255, 255, 0.7);
}

/* Droppable Container Styling */
.droppable-container {
  transition: all 0.2s ease-in-out;
  border-radius: 12px;
  position: relative;
  min-height: 120px;
}

/* Container wenn ein Element darüber schwebt */
.droppable-container--over {
  background-color: rgba(34, 211, 238, 0.1) !important;
  border: 2px dashed var(--accent-cyan, #22d3ee) !important;
  transform: scale(1.02);
  box-shadow: 0 0 20px rgba(34, 211, 238, 0.3);
}

/* Container wenn ein Drag-Vorgang aktiv ist */
.droppable-container--active {
  background-color: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Drop-Hinweis */
.drop-hint {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  background-color: rgba(34, 211, 238, 0.1);
  border-radius: 12px;
  z-index: 10;
  animation: pulse 1.5s ease-in-out infinite;
}

.drop-hint-text {
  color: var(--accent-cyan, #22d3ee);
  font-size: 14px;
  font-weight: 600;
  text-shadow: 0 0 8px rgba(34, 211, 238, 0.5);
  animation: glow 2s ease-in-out infinite alternate;
}

/* KPI Drop Zone spezifisches Styling */
.kpi-drop-zone {
  border-radius: 16px;
  padding: 4px;
}

.kpi-drop-zone--over {
  background: linear-gradient(135deg, 
    rgba(34, 211, 238, 0.1) 0%, 
    rgba(212, 91, 255, 0.1) 100%);
  border: 2px dashed transparent;
  background-clip: padding-box;
  position: relative;
}

.kpi-drop-zone--over::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(135deg, #22d3ee, #d45bff);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
}

/* Animationen */
@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

@keyframes glow {
  from {
    text-shadow: 0 0 8px rgba(34, 211, 238, 0.5);
  }
  to {
    text-shadow: 0 0 16px rgba(34, 211, 238, 0.8), 0 0 24px rgba(34, 211, 238, 0.4);
  }
}

@keyframes dragStart {
  from {
    transform: scale(1) rotate(0deg);
  }
  to {
    transform: scale(1.05) rotate(5deg);
  }
}

/* Responsive Anpassungen */
@media (max-width: 768px) {
  .drag-handle {
    width: 32px;
    height: 32px;
    opacity: 1; /* Auf mobilen Geräten immer sichtbar */
  }
  
  .drag-handle::before {
    width: 16px;
    height: 16px;
    background-size: 12px 16px;
  }
  
  .draggable-card--dragging {
    transform: rotate(3deg) scale(1.03);
  }
}

/* Accessibility: Fokus-Styling */
.draggable-card:focus-visible {
  outline: 2px solid var(--accent-cyan, #22d3ee);
  outline-offset: 2px;
}

.drag-handle:focus-visible {
  outline: 2px solid var(--accent-lime, #84cc16);
  outline-offset: 2px;
}

/* Reduzierte Bewegung für Benutzer mit entsprechenden Einstellungen */
@media (prefers-reduced-motion: reduce) {
  .draggable-card,
  .droppable-container,
  .drag-handle {
    transition: none;
    animation: none;
  }
  
  .draggable-card:hover {
    transform: none;
  }
  
  .draggable-card--dragging {
    transform: scale(1.02);
  }
  
  .droppable-container--over {
    transform: none;
  }
}

/* Dark Mode Anpassungen */
@media (prefers-color-scheme: dark) {
  .drag-handle {
    background: rgba(0, 0, 0, 0.3);
  }
  
  .drag-handle:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .droppable-container--over {
    border-color: #ffffff !important;
    background-color: rgba(255, 255, 255, 0.2) !important;
  }
  
  .drop-hint-text {
    color: #ffffff;
    text-shadow: none;
  }
}