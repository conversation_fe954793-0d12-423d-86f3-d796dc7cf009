/**
 * Debug-Skript zur Diagnose des Startfehlers
 */

import { config } from 'dotenv';

console.log('🔍 Starte Debugging des Backend-Problems...');

try {
  // 1. Umgebungsvariablen prüfen
  console.log('\n1️⃣ Prüfe Umgebungsvariablen...');
  config({ path: '.env' });
  
  const requiredEnvVars = [
    'DB_HOST',
    'DB_PORT', 
    'DB_USER',
    'DB_PASSWORD',
    'DB_NAME',
    'OPENROUTER_API_KEY',
    'API_SECRET_KEY',
    'NODE_ENV',
    'API_PORT'
  ];
  
  let envVarsOk = true;
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      console.error(`❌ Fehlende Umgebungsvariable: ${envVar}`);
      envVarsOk = false;
    } else {
      console.log(`✅ ${envVar}: ${envVar.includes('KEY') ? process.env[envVar].substring(0, 12) + '...' : process.env[envVar]}`);
    }
  }
  
  if (!envVarsOk) {
    throw new Error('Ungültige Umgebungsvariablen');
  }
  
  // 2. PostgreSQL-Datenbankverbindung prüfen
  console.log('\n2️⃣ Prüfe PostgreSQL-Datenbankverbindung...');
  try {
    const { Pool } = await import('pg');
    
    const pool = new Pool({
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      ssl: process.env.DB_SSL === 'true'
    });
    
    console.log(`📁 Versuche Verbindung zu PostgreSQL: ${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}`);
    
    // Einfache Testabfrage
    const result = await pool.query('SELECT table_name FROM information_schema.tables WHERE table_schema = \'public\' LIMIT 5');
    console.log('✅ PostgreSQL-Datenbankverbindung erfolgreich');
    console.log(`📊 Gefundene Tabellen: ${result.rows.length}`);
    
    await pool.end();
  } catch (dbError) {
    console.error('❌ PostgreSQL-Datenbankfehler:', dbError.message);
    throw dbError;
  }
  
  // 3. Drizzle ORM mit PostgreSQL prüfen
  console.log('\n3️⃣ Prüfe Drizzle ORM mit PostgreSQL...');
  try {
    const { drizzle } = await import('drizzle-orm/node-postgres');
    const { Pool } = await import('pg');
    
    const pool = new Pool({
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      ssl: process.env.DB_SSL === 'true'
    });
    
    const db = drizzle(pool);
    
    console.log('✅ Drizzle ORM mit PostgreSQL erfolgreich initialisiert');
    
    await pool.end();
  } catch (drizzleError) {
    console.error('❌ Drizzle Fehler:', drizzleError.message);
    throw drizzleError;
  }
  
  // 4. Module-Importe prüfen
  console.log('\n4️⃣ Prüfe kritische Module...');
  const modulesToCheck = [
    'express',
    'cors',
    'helmet',
    '@libsql/client',
    'drizzle-orm',
    'dotenv'
  ];
  
  for (const module of modulesToCheck) {
    try {
      await import(module);
      console.log(`✅ ${module}`);
    } catch (moduleError) {
      console.error(`❌ ${module}: ${moduleError.message}`);
    }
  }
  
  console.log('\n✅ Alle Checks bestanden! Das Backend sollte starten können.');
  
} catch (error) {
  console.error('\n💥 Fehler beim Debugging:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}