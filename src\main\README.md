# Main Modul

## Übersicht

Das Main-Modul enthält die Electron-Hauptprozess-Implementierung der Leitstand-App. Es verwaltet das Anwendungsfenster, Backend-Integration, IPC-Kommunikation und Workflow-Management.

## Dateistruktur

```
src/main/
├── electron-main.ts     # Hauptprozess-Entry-Point
└── preload.ts          # Preload-Script für sichere IPC-Kommunikation
```

## Architektur-Übersicht

### Electron-Hauptprozess-Pattern

Das Main-Modul implementiert eine sichere Electron-Architektur mit:

- **Hauptprozess-Management**: Fenster-Lifecycle und App-Events
- **Backend-Integration**: Sowohl integriertes als auch separates Backend
- **IPC-Sicherheit**: Context-Isolation und sichere Kommunikation
- **Workflow-System**: Python-Script-Ausführung und Monitoring

### Sicherheitsarchitektur

```typescript
// Sichere Preload-Konfiguration
contextBridge.exposeInMainWorld('api', {
  workflows: workflowsAPI,
  config: configAPI
});
```

- **Context Isolation**: Vollständige Trennung zwischen Main und Renderer
- **Preload-Bridge**: Sichere API-Exposition ohne Node.js-Zugriff
- **URL-Validierung**: Sicherheitsprüfungen für externe Links

### Backend-Management

Drei Backend-Modi werden unterstützt:

1. **Portable Backend**: Gebautes Backend für Produktion
2. **Development Backend**: pnpm dev für Entwicklung
3. **Integriertes Backend**: Express direkt in Electron

## Detaillierte Dateibeschreibungen

### electron-main.ts

**Zweck**: Hauptprozess-Entry-Point und Anwendungslogik

**Kernfunktionalitäten**:

```typescript
// Logging-System für Crash-Diagnose
const LOG_FILE = (() => {
  const base = app?.isPackaged ? 
    path.join(process.resourcesPath, '..') : 
    process.cwd();
  return path.join(base, 'electron-main.log');
})();

// Workflow-Registry für Python-Scripts
const registry: Record<WorkflowId, WorkflowDefinition> = {
  bestand: {
    id: 'bestand',
    name: 'Bestand',
    scriptPath: path.join(process.cwd(), 'backend', 'scripts', 'workflows', 'Bestand', 'Bestand-Workflow.py'),
    env: {},
    logGlob: path.join(process.cwd(), 'backend', 'workflows', 'logs')
  }
};
```

**Backend-Modi**:

- `startPortableBackend()`: Für gebaute Anwendungen
- `startOriginalBackend()`: Für Development mit pnpm
- `startIntegratedBackend()`: Express direkt in Electron

**Fenster-Management**:

```typescript
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });
}
```

### preload.ts

**Zweck**: Sichere Brücke zwischen Main- und Renderer-Prozess

**API-Exposition**:

```typescript
// Konfiguration und Health-Checks
const configAPI = {
  getApiBaseUrl: () => apiBase,
  healthCheck: async () => checkBackendHealth(apiBase),
  getConfig: async (): Promise<AppConfig> => 
    ipcRenderer.invoke(CONFIG_CHANNELS.GET_CONFIG)
};

// Workflow-Management
export const workflowsAPI = {
  list: async () => ipcRenderer.invoke('workflows:list'),
  start: async (id: WorkflowId, params?: Record<string, string>) => 
    ipcRenderer.invoke('workflows:start', { id, params }),
  status: async (id: WorkflowId) => 
    ipcRenderer.invoke('workflows:status', { id })
};
```

**Sicherheitsfeatures**:

- URL-Validierung für externe Links
- Sichere IPC-Kanäle ohne direkten Node.js-Zugriff
- Typisierte API-Interfaces

## Technische Details

### Abhängigkeiten

```json
{
  "electron": "^latest",
  "express": "^4.x",
  "cors": "^2.x",
  "bcryptjs": "^2.x",
  "jsonwebtoken": "^9.x"
}
```

### IPC-Kanäle

**Workflow-Management**:
- `workflows:list` - Verfügbare Workflows abrufen
- `workflows:start` - Workflow starten
- `workflows:status` - Workflow-Status abfragen
- `workflows:log` - Log-Events empfangen
- `workflows:finished` - Completion-Events

**Konfiguration**:
- `config:get` - Konfiguration abrufen
- `config:updated` - Konfigurationsänderungen

**Backend-Events**:
- `backend:log` - Backend-Logs
- `backend:exit` - Backend-Exit-Events

### Workflow-System

```typescript
type WorkflowDefinition = {
  id: WorkflowId;
  name: string;
  scriptPath: string;
  env?: Record<string, string>;
  logGlob?: string;
};

type WorkflowRuntime = {
  proc: ChildProcessWithoutNullStreams | null;
  status: WorkflowStatus;
  lastLogFilePath: string | null;
  startedAt: number | null;
  finishedAt: number | null;
  buffer: string[];
};
```

### Logging-System

```typescript
function logLine(msg: string, data?: unknown) {
  const line = `[${new Date().toISOString()}] ${msg}${data !== undefined ? ' ' + JSON.stringify(data) : ''}\n`;
  fs.appendFileSync(LOG_FILE, line);
}
```

## Verwendungsbeispiele

### Workflow starten

```typescript
// Im Renderer-Prozess
const result = await window.api.workflows.start('bestand', {
  param1: 'value1'
});

if (result.ok) {
  console.log('Workflow gestartet');
} else {
  console.error('Fehler:', result.message);
}
```

### Backend Health-Check

```typescript
// Health-Check durchführen
const health = await window.config.healthCheck();
if (health.ok) {
  console.log('Backend erreichbar:', health.url);
} else {
  console.error('Backend-Fehler:', health.error);
}
```

### Konfiguration abrufen

```typescript
// Aktuelle Konfiguration laden
const config = await window.api.config.getConfig();
console.log('API Base URL:', config.apiBaseUrl);
console.log('Environment:', config.environment);
```

## Performance-Optimierung

### Backend-Startup

- **Lazy Loading**: Backend wird erst bei Bedarf gestartet
- **Health Checks**: Automatische Verfügbarkeitsprüfung
- **Fallback-Modi**: Mehrere Backend-Strategien

### Workflow-Performance

```typescript
// Effizientes Log-Tailing
function startTailLogs(id: WorkflowId) {
  const rt = runtimeState[id];
  if (!rt.lastLogFilePath) return;
  
  tailIntervals[id] = setInterval(() => {
    // Nur neue Zeilen lesen
    const newContent = readNewLines(rt.lastLogFilePath!, tailPositions[id]);
    if (newContent) {
      mainWindow?.webContents.send('workflows:log', { id, line: newContent });
    }
  }, 500);
}
```

### Memory Management

- **Buffer-Limits**: Begrenzte Log-Buffer pro Workflow
- **Process Cleanup**: Automatische Prozess-Bereinigung
- **Event Listener Cleanup**: Proper Event-Entfernung

## Testing

### Unit Tests

```typescript
// Workflow-System testen
describe('Workflow Management', () => {
  test('should start workflow successfully', async () => {
    const result = startWorkflow('bestand');
    expect(result.ok).toBe(true);
  });
  
  test('should handle invalid workflow', async () => {
    const result = startWorkflow('invalid' as WorkflowId);
    expect(result.ok).toBe(false);
    expect(result.message).toContain('nicht gefunden');
  });
});
```

### Integration Tests

```typescript
// IPC-Kommunikation testen
describe('IPC Communication', () => {
  test('should expose secure API', () => {
    expect(window.api).toBeDefined();
    expect(window.api.workflows).toBeDefined();
    expect(window.config).toBeDefined();
  });
});
```

## Migration & Wartung

### Electron-Updates

```typescript
// Kompatibilität mit neuen Electron-Versionen
const webPreferences = {
  nodeIntegration: false,        // Sicherheit
  contextIsolation: true,        // Isolation
  enableRemoteModule: false,     // Deprecated
  sandbox: true                  // Zusätzliche Sicherheit
};
```

### Backend-Migration

- **Versionierung**: Kompatible API-Versionen
- **Fallback-Strategien**: Mehrere Backend-Modi
- **Graceful Degradation**: Funktionalität ohne Backend

## Entwicklungsrichtlinien

### Sicherheit

1. **Niemals Node.js im Renderer**: Nur über Preload-Bridge
2. **Input-Validierung**: Alle IPC-Parameter validieren
3. **URL-Sanitization**: Externe Links prüfen
4. **Process-Isolation**: Separate Prozesse für Backend

### Code-Qualität

```typescript
// Typisierte IPC-Handler
ipcMain.handle('workflows:start', async (
  _e, 
  payload: { id: WorkflowId; params?: Record<string, string> }
) => {
  return startWorkflow(payload.id, payload.params);
});
```

### Error Handling

```typescript
// Robuste Fehlerbehandlung
try {
  const result = await dangerousOperation();
  return { success: true, data: result };
} catch (error) {
  logLine('Operation failed', { error: error.message });
  return { success: false, error: error.message };
}
```

## Integration mit anderen Modulen

### Frontend-Integration

- **React Components**: Nutzen window.api für Backend-Calls
- **State Management**: React Query für IPC-Caching
- **Error Boundaries**: Behandlung von IPC-Fehlern

### Backend-Integration

- **Express Routes**: Direkte Integration in Electron
- **Database Access**: Drizzle ORM über IPC
- **File System**: Sichere Dateizugriffe

### Workflow-Integration

- **Python Scripts**: Externe Prozess-Ausführung
- **Log Management**: Real-time Log-Streaming
- **Status Tracking**: Workflow-Lifecycle-Management

---

*Diese Dokumentation beschreibt die Electron-Hauptprozess-Architektur der Leitstand-App. Für weitere Details siehe die entsprechenden TypeScript-Dateien und IPC-Handler-Implementierungen.*