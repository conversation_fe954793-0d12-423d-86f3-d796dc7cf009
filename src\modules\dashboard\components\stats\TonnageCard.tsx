import React from 'react';
import { BaseKPICardProps } from './types';

/**
 * KPI-Karte für Tonnage
 * 
 * Zeigt die produzierte Tonnage aus den Dispatch-Daten an.
 * Verwendet grünes Farbschema mit Gewichts-Icon.
 */
export const TonnageCard: React.FC<BaseKPICardProps> = ({ 
  departmentKPIs, 
  className = '' 
}) => {
  return (
    <>
      <div className="flex items-center mb-2">
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className="h-6 w-6 text-green-600 mr-3" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" 
          />
        </svg>
        <h3 className="text-xl text-gray-800">TONNAGE</h3>
      </div>
      
      <div className="text-2xl font-bold text-gray-900">
        {departmentKPIs?.dispatch.producedTonnage.toFixed(1) || '0.0'}
        <span className="text-sm font-normal text-gray-500 ml-1">t</span>
      </div>
      
      <div className="text-sm text-gray-600 mt-1">
        Ziel: 85.0 t
      </div>
    </>
  );
};

export default TonnageCard;