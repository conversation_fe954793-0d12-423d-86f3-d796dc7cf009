@import "tailwindcss";
@import "tw-animate-css";
@import "../assets/fonts/jetbrains-mono.css";

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.65rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.705 0.213 47.604);
  --primary-foreground: oklch(0.98 0.016 73.684);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.213 47.604);
  --chart-1: oklch(0.685 0.169 237.323);
  --chart-2: oklch(0.66 0.17 301);
  --chart-3: oklch(0.77 0.10 55);
  --chart-4: oklch(0.442 0.017 285.786);
  --chart-5: oklch(0.704 0.14 182.503);
  --chart-6: oklch(0.869 0.022 252.894);
  --chart-7: oklch(0.553 0.013 58.071);
  --chart-8: oklch(0.437 0.078 188.216);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.705 0.213 47.604);
  --sidebar-primary-foreground: oklch(0.98 0.016 73.684);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.705 0.213 47.604);
}

.dark {
  /* Hauptfarben - Exakte Inversion mit Lesbarkeits-Anpassungen */
  --background: oklch(0 0 0);
  /* Perfekte Inversion von oklch(1 0 0) */
  --foreground: oklch(0.859 0.005 285.823);
  /* Inversion von oklch(0.141 0.005 285.823) */

  /* Karten und Popover - Inversion der Light Mode Werte */
  --card: oklch(0 0 0);
  /* Inversion von oklch(1 0 0) */
  --card-foreground: oklch(0.859 0.005 285.823);
  /* Inversion von oklch(0.141 0.005 285.823) */
  --popover: oklch(0 0 0);
  /* Inversion von oklch(1 0 0) */
  --popover-foreground: oklch(0.859 0.005 285.823);
  /* Inversion von oklch(0.141 0.005 285.823) */

  /* Primärfarbe - Inversion mit leichter Helligkeitsanpassung für bessere Sichtbarkeit */
  --primary: oklch(0.295 0.213 47.604);
  /* Inversion von oklch(0.705 0.213 47.604) */
  --primary-foreground: oklch(0.02 0.016 73.684);
  /* Inversion von oklch(0.98 0.016 73.684) */

  /* Sekundäre Farben - Exakte Inversionen */
  --secondary: oklch(0.033 0.001 286.375);
  /* Inversion von oklch(0.967 0.001 286.375) */
  --secondary-foreground: oklch(0.79 0.006 285.885);
  /* Inversion von oklch(0.21 0.006 285.885) */

  /* Gedämpfte Farben - Exakte Inversionen */
  --muted: oklch(0.033 0.001 286.375);
  /* Inversion von oklch(0.967 0.001 286.375) */
  --muted-foreground: oklch(0.448 0.016 285.938);
  /* Inversion von oklch(0.552 0.016 285.938) */

  /* Akzentfarben - Exakte Inversionen */
  --accent: oklch(0.033 0.001 286.375);
  /* Inversion von oklch(0.967 0.001 286.375) */
  --accent-foreground: oklch(0.79 0.006 285.885);
  /* Inversion von oklch(0.21 0.006 285.885) */

  /* Destruktive Farben - Inversion */
  --destructive: oklch(0.423 0.245 27.325);
  /* Inversion von oklch(0.577 0.245 27.325) */

  /* Rahmen und Eingabefelder - Exakte Inversionen */
  --border: oklch(0.08 0.004 286.32);
  /* Inversion von oklch(0.92 0.004 286.32) */
  --input: oklch(0.08 0.004 286.32);
  /* Inversion von oklch(0.92 0.004 286.32) */
  --ring: oklch(0.295 0.213 47.604);
  /* Inversion von oklch(0.705 0.213 47.604) */

  /* Chart-Farben - Inversionen der Light Mode Werte */
  --chart-1: oklch(0.315 0.169 237.323);
  /* Inversion von oklch(0.685 0.169 237.323) */
  --chart-2: oklch(0.34 0.17 301);
  /* Inversion von oklch(0.66 0.17 301) */
  --chart-3: oklch(0.23 0.10 55);
  /* Inversion von oklch(0.77 0.10 55) */
  --chart-4: oklch(0.558 0.017 285.786);
  /* Inversion von oklch(0.442 0.017 285.786) */
  --chart-5: oklch(0.296 0.14 182.503);
  /* Inversion von oklch(0.704 0.14 182.503) */
  --chart-6: oklch(0.131 0.022 252.894);
  /* Inversion von oklch(0.869 0.022 252.894) */
  --chart-7: oklch(0.447 0.013 58.071);
  /* Inversion von oklch(0.553 0.013 58.071) */
  --chart-8: oklch(0.563 0.078 188.216);
  /* Inversion von oklch(0.437 0.078 188.216) */

  /* Sidebar - Exakte Inversionen */
  --sidebar: oklch(0.015 0 0);
  /* Inversion von oklch(0.985 0 0) */
  --sidebar-foreground: oklch(0.859 0.005 285.823);
  /* Inversion von oklch(0.141 0.005 285.823) */
  --sidebar-primary: oklch(0.295 0.213 47.604);
  /* Inversion von oklch(0.705 0.213 47.604) */
  --sidebar-primary-foreground: oklch(0.02 0.016 73.684);
  /* Inversion von oklch(0.98 0.016 73.684) */
  --sidebar-accent: oklch(0.033 0.001 286.375);
  /* Inversion von oklch(0.967 0.001 286.375) */
  --sidebar-accent-foreground: oklch(0.79 0.006 285.885);
  /* Inversion von oklch(0.21 0.006 285.885) */
  --sidebar-border: oklch(0.08 0.004 286.32);
  /* Inversion von oklch(0.92 0.004 286.32) */
  --sidebar-ring: oklch(0.295 0.213 47.604);
  /* Inversion von oklch(0.705 0.213 47.604) */
}

@theme inline {
  --color-main: var(--main);
  --color-background: var(--background);
  --color-secondary-background: var(--secondary-background);
  --color-foreground: var(--foreground);
  --color-main-foreground: var(--main-foreground);
  --color-border: var(--border);
  --color-overlay: var(--overlay);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --spacing-boxShadowX: 4px;
  --spacing-boxShadowY: 4px;
  --spacing-reverseBoxShadowX: -4px;
  --spacing-reverseBoxShadowY: -4px;
  --radius-base: 5px;
  --shadow-shadow: var(--shadow);
  --font-weight-base: 500;
  --font-weight-heading: 700;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --animate-rippling: rippling var(--duration) ease-out;
  @keyframes rippling {
  0% {
    opacity: 1;
    }
  100% {
    transform: scale(2);
    opacity: 0;
    }
  }
  --animate-shimmer-slide: shimmer-slide var(--speed) ease-in-out infinite alternate;
  --animate-spin-around: spin-around calc(var(--speed) * 2) infinite linear
;
  @keyframes shimmer-slide {
  to {
    transform: translate(calc(100cqw - 100%), 0);
    }
  }
  @keyframes spin-around {
  0% {
    transform: translateZ(0) rotate(0);
    }
  15%, 35% {
    transform: translateZ(0) rotate(90deg);
    }
  65%, 85% {
    transform: translateZ(0) rotate(270deg);
    }
  100% {
    transform: translateZ(0) rotate(360deg);
    }
  }}

.container {
  width: 7em;
  height: 7em;
  position: relative;
}

.button {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid #090909;
  background-color: transparent;
  background-image: linear-gradient(145deg, #171717, #444245);
  box-sizing: border-box;
  box-shadow: inset 2px 2px 0 #7d7c7e, inset -2px -2px 0px #1c1c1c;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container input {
  display: none;
}

.button::before {
  position: absolute;
  content: "";
  width: 7.25em;
  height: 7.25em;
  border-radius: inherit;
  background-color: transparent;
  background-image: linear-gradient(145deg, #262626, #606060);
  z-index: -1;
  box-shadow: 11px 11px 22px #141414, -11px -11px 22px #525252;
}

.button .icon {
  width: 60px;
  height: 60px;
  display: inline-block;
}

.button .icon svg {
  height: 100%;
  width: 100%;
  fill: #a5a5a5;
}

/* The switch - the box around the slider */
#theme-toggle-button {
  font-size: 17px;
  position: relative;
  display: inline-block;
  width: 7em;
  cursor: pointer;
}

/* Hide default HTML checkbox */
#toggle {
  opacity: 0;
  width: 0;
  height: 0;
}

#container,
#patches,
#stars,
#button,
#sun,
#moon,
#cloud {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.25s;
}

/* night sky background */
#toggle:checked+svg #container {
  fill: #2b4360;
}

/* move button to right when checked */
#toggle:checked+svg #button {
  transform: translate(28px, 2.333px);
}

/* show/hide sun and moon based on checkbox state */
#sun {
  opacity: 1;
}

#toggle:checked+svg #sun {
  opacity: 0;
}

#moon {
  opacity: 0;
}

#toggle:checked+svg #moon {
  opacity: 1;
}

/* show or hide background items on checkbox state */
#cloud {
  opacity: 1;
}

#toggle:checked+svg #cloud {
  opacity: 0;
}

#stars {
  opacity: 0;
}

#toggle:checked+svg #stars {
  opacity: 1;
}

@layer base {
  body {
    @apply text-foreground font-base bg-background;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading;
  }
}

.font-jetbrains-mono {
  font-family: 'JetBrains Mono', monospace;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Power Switch Component Styles */
.power-switch-input {
  display: none;
}

.power-switch {
  position: relative;
  width: 70px;
  height: 70px;
  background-color: rgb(255, 255, 255);
  border-radius: 50%;
  z-index: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgb(126, 126, 126);
  box-shadow: 0px 0px 3px rgb(2, 2, 2) inset;
  transition: all 0.3s ease;
}

.power-switch svg {
  width: 1.2em;
  transition: all 0.3s ease;
}

.power-switch svg path {
  fill: rgb(48, 48, 48);
  transition: fill 0.3s ease;
}

.power-switch-input:checked+.power-switch {
  box-shadow:
    0px 0px 1px #4ade80 inset,
    0px 0px 3px #4ade80 inset,
    0px 0px 8px rgba(74, 222, 128, 0.4),
    0px 0px 15px rgba(74, 222, 128, 0.2);
  border: 2px solid #4ade80;
  background-color: rgb(146, 180, 184);
}

.power-switch-input:checked+.power-switch svg {
  filter: drop-shadow(0px 0px 2px rgba(74, 222, 128, 0.6));
}

.power-switch-input:checked+.power-switch svg path {
  fill: #4ade80;
}

.power-switch-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.power-switch-disabled:hover {
  transform: none;
}

/* WiFi Button Component Styles */
.wifi-button-container {
  width: 7em;
  height: 7em;
  position: relative;
}

.wifi-button {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid #090909;
  background-color: transparent;
  background-image: linear-gradient(145deg, #171717, #444245);
  box-sizing: border-box;
  box-shadow: inset 2px 2px 0 #7d7c7e, inset -2px -2px 0px #1c1c1c;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.wifi-button::before {
  position: absolute;
  content: "";
  width: 7.25em;
  height: 7.25em;
  border-radius: inherit;
  background-color: transparent;
  background-image: linear-gradient(145deg, #262626, #606060);
  z-index: -1;
  box-shadow: 11px 11px 22px #141414, -11px -11px 22px #525252;
}

.wifi-button .wifi-icon {
  width: 60px;
  height: 60px;
  display: inline-block;
}

.wifi-button .wifi-icon svg {
  height: 100%;
  width: 100%;
  fill: #a5a5a5;
  transition: fill 0.3s ease;
}

.wifi-button-container input:checked+.wifi-button {
  box-shadow: inset -2px -2px 0 #5e5e5e, inset 2px 2px 0 #1c1c1c;
  border: 4px solid rgba(77, 124, 255, 0.281);
  animation: animeBorder 0.3s linear alternate-reverse infinite;
}

.wifi-button-container input:checked+.wifi-button .wifi-icon svg {
  fill: rgb(77, 124, 255);
  animation: animeFill 0.3s linear alternate-reverse infinite;
}

@keyframes animeFill {
  to {
    fill: rgba(77, 124, 255, 0.642);
  }
}

@keyframes animeBorder {
  to {
    border-color: rgba(77, 124, 255, 0.137);
  }
}

/* Power Button Component Styles */
.power-button-container {
  width: 7em;
  height: 7em;
  position: relative;
}

.power-button {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid #090909;
  background-color: transparent;
  background-image: linear-gradient(145deg, #171717, #444245);
  box-sizing: border-box;
  box-shadow: inset 2px 2px 0 #7d7c7e, inset -2px -2px 0px #1c1c1c;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.power-button::before {
  position: absolute;
  content: "";
  width: 7.25em;
  height: 7.25em;
  border-radius: inherit;
  background-color: transparent;
  background-image: linear-gradient(145deg, #262626, #606060);
  z-index: -1;
  box-shadow: 11px 11px 22px #141414, -11px -11px 22px #525252;
}

.power-button .power-icon {
  width: 60px;
  height: 60px;
  display: inline-block;
}

.power-button .power-icon svg {
  height: 100%;
  width: 100%;
  fill: #a5a5a5;
  transition: fill 0.3s ease;
}

.power-button-container input:checked+.power-button {
  box-shadow: inset -2px -2px 0 #5e5e5e, inset 2px 2px 0 #1c1c1c;
  border: 4px solid rgba(255, 77, 77, 0.281);
  animation: animePowerBorder 0.3s linear alternate-reverse infinite;
}

.power-button-container input:checked+.power-button .power-icon svg {
  fill: rgb(255, 77, 77);
  animation: animePowerFill 0.3s linear alternate-reverse infinite;
}

@keyframes animePowerFill {
  to {
    fill: rgba(255, 77, 77, 0.642);
  }
}

@keyframes animePowerBorder {
  to {
    border-color: rgba(255, 77, 77, 0.137);
  }
}

/* Switch Button Component Styles */
.switch-button-container {
  width: 7em;
  height: 7em;
  position: relative;
}

.switch-button {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid #e5e5e5;
  background-color: white;
  background-image: linear-gradient(145deg, #ffffff, #f5f5f5);
  box-sizing: border-box;
  box-shadow: inset 2px 2px 0 #ffffff, inset -2px -2px 0px #d0d0d0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.switch-button::before {
  position: absolute;
  content: "";
  width: 7.25em;
  height: 7.25em;
  border-radius: inherit;
  background-color: transparent;
  background-image: linear-gradient(145deg, #f8f8f8, #e0e0e0);
  z-index: -1;
  box-shadow: 11px 11px 22px #c0c0c0, -11px -11px 22px #ffffff;
}

.switch-button .switch-icon {
  width: 60px;
  height: 60px;
  display: inline-block;
}

.switch-button .switch-icon svg {
  height: 100%;
  width: 100%;
  fill: #666666;
  transition: fill 0.3s ease;
}

.switch-button-container input:checked+.switch-button {
  background-color: #22c55e;
  background-image: linear-gradient(145deg, #22c55e, #16a34a);
  border: 4px solid #15803d;
  box-shadow:
    inset 2px 2px 0 #34d399,
    inset -2px -2px 0px #166534,
    0px 0px 20px rgba(34, 197, 94, 0.5);
}

.switch-button-container input:checked+.switch-button::before {
  background-image: linear-gradient(145deg, #22c55e, #16a34a);
  box-shadow: 11px 11px 22px rgba(34, 197, 94, 0.3), -11px -11px 22px rgba(34, 197, 94, 0.1);
}

.switch-button-container input:checked+.switch-button .switch-icon svg {
  fill: white;
}

.light-rays-container {
  width: 100%;
  height: 100%;
  position: relative;
  pointer-events: none;
  z-index: 3;
  overflow: hidden;
}

.text-type {
  display: inline-block;
  white-space: pre-wrap;
}

.text-type__cursor {
  margin-left: 0.25rem;
  display: inline-block;
  opacity: 1;
}

.text-type__cursor--hidden {
  display: none;
}

/* Dark Mode Scrollbar Anpassungen */
.dark ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.dark ::-webkit-scrollbar-track {
  background: #0f0f0f; /* Inversion von #f1f1f1 */
  border-radius: 10px;
}

.dark ::-webkit-scrollbar-thumb {
  background-color: #0c63ed; /* Inversion von #f39c12 */
  border-radius: 10px;
  border: 1px solid #0f0f0f; /* Inversion von #f1f1f1 */
}

.dark ::-webkit-scrollbar-thumb:hover {
  background-color: #1981dd; /* Inversion von #e67e22 */
}

.dark ::-webkit-scrollbar-corner {
  background: transparent;
}

/* Light Mode Scrollbar (Originale Werte) */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: #f39c12;
  border-radius: 10px;
  border: 1px solid #f1f1f1;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #e67e22;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* KPI Cards Scrollbar */
.kpi-cards-container {
  scrollbar-width: thin;
  scrollbar-color: #f39c12 #f1f1f1;
}

.dark .kpi-cards-container {
  scrollbar-color: #0c63ed #0f0f0f; /* Dark Mode Inversion */
}

.kpi-cards-container::-webkit-scrollbar {
  height: 6px;
}

.kpi-cards-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dark .kpi-cards-container::-webkit-scrollbar-track {
  background: #0f0f0f; /* Dark Mode Inversion */
}

.kpi-cards-container::-webkit-scrollbar-thumb {
  background-color: #f39c12;
  border-radius: 3px;
  border: 1px solid #f1f1f1;
}

.dark .kpi-cards-container::-webkit-scrollbar-thumb {
  background-color: #0c63ed; /* Dark Mode Inversion */
  border: 1px solid #0f0f0f; /* Dark Mode Inversion */
}

.kpi-cards-container::-webkit-scrollbar-thumb:hover {
  background-color: #e67e22;
}

.dark .kpi-cards-container::-webkit-scrollbar-thumb:hover {
  background-color: #1981dd; /* Dark Mode Inversion */
}