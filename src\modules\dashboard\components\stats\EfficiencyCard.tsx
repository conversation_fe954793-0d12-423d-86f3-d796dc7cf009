import React from 'react';
import { BaseKPICardProps } from './types';

/**
 * KPI-Karte für Effizienz (Automatisierungsgrad)
 * 
 * <PERSON>eigt den Automatisierungsgrad des Wareneingangs an.
 * Verwendet gelbes Farbschema mit Blitz-Icon.
 */
export const EfficiencyCard: React.FC<BaseKPICardProps> = ({ 
  departmentKPIs, 
  className = '' 
}) => {
  return (
    <>
      <div className="flex items-center mb-2">
        <div className="p-2 bg-yellow-100 rounded-lg mr-3">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6 text-yellow-600" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M13 10V3L4 14h7v7l9-11h-7z" 
            />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Effizienz</h3>
      </div>
      
      <div className="text-2xl font-bold text-gray-900">
        {departmentKPIs?.wareneingang.automatisierungsgrad.toFixed(1) || '0.0'}
        <span className="text-sm font-normal text-gray-500 ml-1">%</span>
      </div>
      
      <div className="text-sm text-gray-600 mt-1">
        Ziel: 85.0%
      </div>
    </>
  );
};

export default EfficiencyCard;