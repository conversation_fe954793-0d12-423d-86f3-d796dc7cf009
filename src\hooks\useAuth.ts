import { useState, useEffect, useCallback } from "react";
import { authService } from "@/services/auth.service";

export interface User {
  id: number;
  username: string;
  email: string;
  roles: string[];
  name?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

interface AuthHook extends AuthState {
  login: (
    username: string,
    password: string,
  ) => Promise<{ success: boolean; message: string }>;
  logout: () => void;
  trackActivity: () => void;
  updateUser: (updates: Partial<User>) => void;
  validateCurrentAuth: () => Promise<boolean>;
  // Note: isAuthenticated is now managed by the hook itself, not computed
}

interface ValidationState {
  isValidating: boolean;
  lastValidation: number;
  validationPromise: Promise<boolean> | null;
}

export const useAuth = (): AuthHook => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [validationState, setValidationState] = useState<ValidationState>({
    isValidating: false,
    lastValidation: 0,
    validationPromise: null
  });

  const logout = useCallback(() => {
    console.log("🚪 Logging out user");
    authService.logout();
    setUser(null);
    setToken(null);
    setIsAuthenticated(false);
  }, []);

  const login = async (
    username: string,
    password: string,
  ): Promise<{ success: boolean; message: string }> => {
    try {
      setIsLoading(true);
      const response = await authService.login({ username, password });

      if (response.success && response.data) {
        setUser(response.data.user);
        setToken(response.data.token);
        setIsAuthenticated(true);
        return { success: true, message: response.message };
      } else {
        setIsAuthenticated(false);
        return { success: false, message: response.message };
      }
    } catch (error) {
      console.error("Login error:", error);
      setIsAuthenticated(false);
      return { success: false, message: "Ein Fehler ist aufgetreten" };
    } finally {
      setIsLoading(false);
    }
  };

  const trackActivity = useCallback(() => {
    // Simplified activity tracking - just a placeholder for now
    console.log("Activity tracked");
  }, []);

  const updateUser = useCallback((updates: Partial<User>) => {
    if (user) {
      setUser(prevUser => prevUser ? { ...prevUser, ...updates } : null);
      // Keep authentication state as is
    }
  }, [user]);

  // Initialize auth state - very simple version
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log("🔄 Initializing auth state...");
        const storedToken = authService.getToken();
        const storedUser = authService.getUser();

        console.log("🔍 Stored token:", storedToken ? "Present" : "Not found");
        console.log("🔍 Stored user:", storedUser ? "Present" : "Not found");

        if (storedToken && storedUser) {
          console.log("✅ Found stored auth data");

          // Validate token before setting as authenticated
          const isValidToken = await authService.isLoggedIn();
          console.log("🔐 Token validation result:", isValidToken);

          if (isValidToken) {
            console.log("✅ Token is valid");
            setToken(storedToken);
            setUser(storedUser);
            setIsAuthenticated(true);
          } else {
            console.log("❌ Token is invalid, clearing stored data");
            authService.logout();
            setToken(null);
            setUser(null);
            setIsAuthenticated(false);
          }
        } else {
          console.log("ℹ️ No stored auth data found");
          setToken(null);
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error("❌ Auth initialization error:", error);
        setToken(null);
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
        console.log("✅ Auth initialization complete");
      }
    };

    initializeAuth();
  }, []);

  // Add a manual token validation function with improved error handling
  const validateCurrentAuth = useCallback(async () => {
    if (!token) {
      console.log("❌ validateCurrentAuth: No token found");
      setIsAuthenticated(false);
      return false;
    }

    // Prevent multiple simultaneous validation calls
    if (validationState.isValidating) {
      console.log("🔐 validateCurrentAuth: Validation already in progress, waiting...");
      if (validationState.validationPromise) {
        return await validationState.validationPromise;
      }
      return false;
    }

    // Prevent too frequent validation calls (throttle to max once per 2 seconds)
    const now = Date.now();
    if (now - validationState.lastValidation < 2000) {
      console.log("🔐 validateCurrentAuth: Too frequent, using cached result");
      return isAuthenticated;
    }

    const validationPromise = (async () => {
      try {
        setValidationState(prev => ({ ...prev, isValidating: true }));

        console.log("🔐 validateCurrentAuth: Validating token...");
        const isValid = await authService.isLoggedIn();
        console.log("🔐 validateCurrentAuth: Token validation result:", isValid);

        setIsAuthenticated(isValid);
        setValidationState(prev => ({
          ...prev,
          isValidating: false,
          lastValidation: Date.now(),
          validationPromise: null
        }));

        if (!isValid) {
          console.log("❌ validateCurrentAuth: Token is invalid, clearing auth state");
          authService.logout();
          setToken(null);
          setUser(null);
        }

        return isValid;
      } catch (error) {
        console.error("❌ validateCurrentAuth: Token validation error:", error);
        setIsAuthenticated(false);
        setValidationState(prev => ({
          ...prev,
          isValidating: false,
          lastValidation: Date.now(),
          validationPromise: null
        }));

        // Don't automatically logout on validation errors, might be network issues
        // Let the user decide or retry
        console.log("⚠️ validateCurrentAuth: Keeping current auth state due to error");

        return false;
      }
    })();

    setValidationState(prev => ({
      ...prev,
      validationPromise,
      lastValidation: now
    }));

    return await validationPromise;
  }, [token, isAuthenticated, validationState]);

  return {
    user,
    token,
    isAuthenticated,
    isLoading,
    login,
    logout,
    trackActivity,
    updateUser,
    validateCurrentAuth,
  };
};
