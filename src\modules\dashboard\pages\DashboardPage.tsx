import { useEffect, useState, useMemo } from "react";
import type { AddCardFunction } from "@/modules/dashboard/components/DragDrop/types";
import { useTranslation } from "react-i18next";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Scissors, Info, Edit3, Check } from "lucide-react";
import dashboardIcon from "@/assets/iconDashboard.png";
import { Clock } from "lucide-react";
import { cn } from "@/lib/utils";
import apiService from "@/services/api.service";
import { AskJaszButton } from "@/modules/ai/components";
import { createPageContext } from "@/contexts/AskJaszContext";
import BigDatePicker from "@/modules/dashboard/components/Calendar/BigDatePicker";
import { DragDropProvider, useDragDropContext } from "@/modules/dashboard/components/DragDrop/DragDropProvider";
import { KPILibrary } from "@/modules/dashboard/components/DragDrop/KPILibrary";
import { KPIGrid } from "@/modules/dashboard/components/KPIGrid";
// Drawer-Komponenten gemäß Dokumentation
import {
  Drawer,
  DrawerOverlay,
  DrawerContent,
  DrawerHeader,
  DrawerFooter,
  DrawerTitle,
  DrawerDescription,
} from '@/components/ui/drawer';

// Interface für echte KPI-Daten aus der Datenbank
interface DepartmentKPIs {
  dispatch: {
    serviceLevel: number;
    targetServiceLevel: number;
    pickingPerformance: number; // Summe von ATRL + ARIL (keine Prozent)
    pickingArilPerformance: number; // ATRL
    pickingAtrlPerformance: number; // ARIL
    deliveryPerformance: number;
    qualityRate: number;
    producedTonnage: number; // Aus TagesleistungData
    directLoading: number; // Aus TagesleistungData
    umschlag: number; // Aus TagesleistungData
    kgPerColli: number; // Aus TagesleistungData
    elefanten: number; // Aus TagesleistungData
    lastUpdated: string;
  };
  ablaengerei: {
    schnitte: number; // Summe von pickCut + cutRR + cutTR + cutTT
    schnitteTT: number; // cutTT
    schnitteTR: number; // cutTR
    schnitteRR: number; // cutRR
    schnitteCut2Pick: number; // pickCut
    qualityRate: number; // Gleiche Berechnung wie bei Dispatch
    lastUpdated: string;
  };
  wareneingang: {
    weAtrlPos: number; // weAtrl - Automatische WE-Positionen
    weManuellePos: number; // weManl - Manuelle WE-Positionen
    gesamtWE: number; // Summe von weAtrl + weManl
    automatisierungsgrad: number; // Prozentsatz automatischer WE
    qualityRate: number; // Gleiche Berechnung wie bei Dispatch
    lastUpdated: string;
  };
}



/**
 * Dashboard-Seite des Shopfloor-Management-Dashboards
 *
 * Zeigt eine Übersicht über alle verfügbaren Abteilungen mit echten KPI-Daten
 * aus der Datenbank für den gestrigen Tag.
 * Das Design folgt dem Neobrutalism-Stil mit kräftigen Farben und starken Konturen.
 */
interface DashboardContentProps {
  departmentKPIs: DepartmentKPIs | null;
  selectedDate: Date;
  handleDateChange: (newDate: Date) => Promise<void>;
  actualDataDate: string;
  getTargetDate: () => string;
  loadKPIData: () => Promise<void>;
  error: string | null;
  t: (key: string) => string;
}

const DashboardContent: React.FC<DashboardContentProps> = ({
  departmentKPIs,
  selectedDate,
  handleDateChange,
  actualDataDate,
  getTargetDate,
  loadKPIData,
  error,
  t
}) => {
  const { isEditMode, toggleEditMode } = useDragDropContext();
  const [isLibraryOpen, setIsLibraryOpen] = useState(false);
  const [hasPendingChanges, setHasPendingChanges] = useState(false);
  const [applyChangesFunction, setApplyChangesFunction] = useState<(() => void) | null>(null);

  // Error State
  if (error || !departmentKPIs) {
    return (
      <div className="w-full bg-bg min-h-screen p-8 flex items-center justify-center">
        <div className="flex flex-col items-center text-red-500">
          <div className="text-4xl mb-4">⚠️</div>
          <p className="font-heading text-xl">{error || 'Fehler beim Laden der Daten'}</p>
          <Button
            onClick={loadKPIData}
            className="mt-4 bg-black text-white hover:bg-gray-800"
          >
            Erneut versuchen
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-bg min-h-screen p-8">
      {/* Container für Header und Card-Bereich mit Kalender rechts */}
      <div className="relative">
        {/* Kalender - absolut positioniert rechts, überspannt Header und Card-Bereich */}
        <div className="absolute top-0 right-0 z-50">
          <BigDatePicker
            initialDate={selectedDate}
            onDateChange={handleDateChange}
          />
        </div>

        {/* Header mit korrektem Neobrutalism Styling */}
        <div className="flex items-center justify-between w-full mb-8 pr-80"> {/* Platz für Kalender rechts */}
          <div className="flex-1">
            <div className="flex items-center gap-4">
              <img
                src={dashboardIcon}
                alt="Dashboard"
                className="h-8 w-8 mr-2 object-contain"
              />
              <h1 className="text-4xl font-heading tracking-tight text-text">{t("DASHBOARD")}</h1>
              <div className="-mt-7">
              <AskJaszButton
                context={createPageContext(
                  "Dashboard Hauptseite",
                  [
                    "Übersicht über alle Abteilungen",
                    "KPI-Anzeige für Versand, Ablängerei und Wareneingang",
                    "Echtzeit-Daten und Leistungsmetriken",
                    "Navigation zu Detailansichten"
                  ],
                  "Tagesübersicht mit aktuellen Kennzahlen"
                )}
                position="inline"
                size="md"
                variant="default"
                tooltip="Hilfe zur Dashboard-Hauptseite erhalten"
              />
            </div>
            </div>
            <p className="text-lg text-text font-base mt-2 opacity-70">{t("Tagesübersicht mit KPI-Karten")}</p>
            
            {/* Status Badge mit Edit-Button */}
            <div className="flex items-center justify-start mt-4 mb-4 gap-4">
              <Badge
                variant="outline"
                className={cn(
                  "bg-white/80 border-gray-200 text-gray-600 font-base text-sm px-3 py-1.5 gap-2 w-fit",
                  "hover:bg-gray-50 transition-colors duration-200 shadow-sm"
                )}
              >
                <Clock className="h-4 w-4 opacity-60" />
                <span>
                  Letzte Aktualisierung: {new Date().toLocaleTimeString('de-DE')}
                  {actualDataDate && actualDataDate !== getTargetDate() && (
                    <span className="ml-2 opacity-50 text-xs">
                      (Neueste verfügbare Daten)
                    </span>
                  )}
                </span>
              </Badge>
              
              {/* Edit-Button */}
              <Button
                onClick={toggleEditMode}
                variant={isEditMode ? "default" : "outline"}
                className={cn(
                  "flex items-center gap-2 transition-all duration-200",
                  isEditMode
                    ? "bg-blue-600 hover:bg-blue-700 text-white"
                    : "bg-white/80 hover:bg-gray-50 text-gray-700 border-gray-200"
                )}
              >
                <Edit3 className="h-4 w-4" />
                {isEditMode ? 'Bearbeitung beenden' : 'Layout bearbeiten'}
              </Button>
              
              {/* KPI-Bibliothek Button (nur im Edit-Modus) */}
              {isEditMode && (
                <Button
                  onClick={() => setIsLibraryOpen(true)}
                  variant="outline"
                  className="flex items-center gap-2 bg-white/80 hover:bg-gray-50 text-gray-700 border-gray-200"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  KPI-Bibliothek
                </Button>
              )}
            </div>
          </div>
          <div className="mb-8 flex"> {/* Platz für Kalender rechts */}
             
          </div>
        </div>
      </div>

      {/* KPI-Grid-Komponente mit Drag-and-Drop-Funktionalität */}
      <KPIGrid 
        departmentKPIs={departmentKPIs} 
        isEditMode={isEditMode} 
      />
      
      {/* KPI-Bibliothek Drawer von links */}
      <Drawer
        open={isLibraryOpen}
        onOpenChange={(open) => {
          if (!open) {
            setIsLibraryOpen(false);
          }
        }}
        side="left"
      >
        <DrawerOverlay />
        <DrawerContent className="w-[520px] max-w-[95vw]">
          <DrawerHeader>
            <DrawerTitle>KPI-Bibliothek</DrawerTitle>
            <DrawerDescription>
              Wähle die KPI-Karten für dein Dashboard aus
            </DrawerDescription>
          </DrawerHeader>
          
          <div className="p-4 overflow-auto">
            <KPILibrary
              isVisible={true}
              onClose={() => setIsLibraryOpen(false)}
              onPendingChangesUpdate={(hasPending, applyFn) => {
                setHasPendingChanges(hasPending);
                setApplyChangesFunction(() => applyFn);
              }}
            />
          </div>
          
          <DrawerFooter>
            <div className="flex gap-2 w-full">
              {/* Änderungen anwenden Button */}
              {hasPendingChanges && (
                <Button
                  onClick={() => applyChangesFunction?.()}
                  variant="accept"
                >
                  <Check className="h-4 w-4" />
                  Änderungen anwenden
                </Button>
              )}
              
              {/* Schließen Button */}
              <Button variant="outline" onClick={() => setIsLibraryOpen(false)}>
                Schließen
              </Button>
            </div>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </div>
  );
};

export default function DashboardPage() {
  const { t } = useTranslation();
  const [departmentKPIs, setDepartmentKPIs] = useState<DepartmentKPIs | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [actualDataDate, setActualDataDate] = useState<string>('');
  
  // State für das ausgewählte Datum im Kalender
  const [selectedDate, setSelectedDate] = useState<Date>(() => {
    // Standarddatum: Gestern
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return yesterday;
  });

  // Funktion zur Rückgabe des ausgewählten Datums für die KPI-Anzeige
  const getTargetDate = (): string => {
    const day = String(selectedDate.getDate()).padStart(2, '0');
    const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
    const year = selectedDate.getFullYear();
    return `${year}-${month}-${day}`; // Format: YYYY-MM-DD (passend zur Datenbank)
  };

  // Handler für Datumsänderungen im Kalender
  const handleDateChange = async (newDate: Date) => {
    setSelectedDate(newDate);
    // KPI-Daten für das neue Datum laden ohne Loading-State zu setzen
    // um das Flackern und Schließen des Kalenders zu verhindern
    try {
      setError(null);
      
      const day = String(newDate.getDate()).padStart(2, '0');
      const month = String(newDate.getMonth() + 1).padStart(2, '0');
      const year = newDate.getFullYear();
      const targetDate = `${year}-${month}-${day}`;

      // Parallel alle Datenquellen laden über die API mit Datumsfilter
      const [
        serviceLevelResponse,
        pickingResponse,
        deliveryResponse,
        qmResponse,
        tagesleistungResponse,
        ablaengereiResponse,
        weResponse
      ] = await Promise.all([
        apiService.getServiceLevelData(targetDate, targetDate),
        apiService.getPickingData(targetDate, targetDate),
        apiService.getDeliveryPositionsData(targetDate, targetDate),
        apiService.getReturnsData(),
        apiService.getTagesleistungData(targetDate, targetDate),
        apiService.getAblaengereiData(targetDate, targetDate),
        apiService.getWEData(targetDate, targetDate)
      ]);

      // Verwende die API-Responses direkt, da sie bereits die Datenarrays enthalten
      const serviceLevelResult = serviceLevelResponse || [];
      const pickingResult = pickingResponse || [];
      const deliveryResult = deliveryResponse || [];
      const qmResult = qmResponse || [];
      const tagesleistungResult = tagesleistungResponse || [];
      const ablaengereiResult = ablaengereiResponse || [];
      const weResult = weResponse || [];

      // Berechne KPIs basierend auf den geladenen Daten
      // targetDate muss als erster Parameter übergeben werden
      const kpis = calculateKPIs(
        targetDate,
        serviceLevelResult,
        pickingResult,
        deliveryResult,
        qmResult,
        tagesleistungResult,
        ablaengereiResult,
        weResult
      );

      setDepartmentKPIs(kpis);
      setActualDataDate(targetDate);
    } catch (err) {
      console.error('Fehler beim Laden der KPI-Daten:', err);
      setError('Fehler beim Laden der KPI-Daten');
    }
  };

  // Funktion zum Laden aller KPI-Daten für den gestrigen Tag
  const loadKPIData = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const targetDate = getTargetDate();


      // Parallel alle Datenquellen laden über die API mit Datumsfilter
      const [
        serviceLevelResponse,
        pickingResponse,
        deliveryResponse,
        qmResponse,
        tagesleistungResponse,
        ablaengereiResponse,
        weResponse
      ] = await Promise.all([
        apiService.getServiceLevelData(targetDate, targetDate),
        apiService.getPickingData(targetDate, targetDate),
        apiService.getDeliveryPositionsData(targetDate, targetDate),
        apiService.getReturnsData(),
        apiService.getTagesleistungData(targetDate, targetDate),
        apiService.getAblaengereiData(targetDate, targetDate),
        apiService.getWEData(targetDate, targetDate)
      ]);

      // Verwende die API-Responses direkt, da sie bereits die Datenarrays enthalten
      const serviceLevelResult = serviceLevelResponse || [];
      const pickingResult = pickingResponse || [];
      const deliveryResult = deliveryResponse || [];
      const qmResult = qmResponse || [];
      const tagesleistungResult = tagesleistungResponse || [];
      const ablaengereiResult = ablaengereiResponse || [];
      const weResult = weResponse || [];



      // Filtere Daten für das Zieldatum und berechne KPIs
      const kpis = calculateKPIs(
        targetDate,
        serviceLevelResult,
        pickingResult,
        deliveryResult,
        qmResult,
        tagesleistungResult,
        ablaengereiResult,
        weResult
      );

      setDepartmentKPIs(kpis);
    } catch (err) {
      console.error('Fehler beim Laden der KPI-Daten:', err);
      setError('Fehler beim Laden der Dashboard-Daten');
    } finally {
      setLoading(false);
    }
  };

  // Funktion zur Berechnung der KPIs aus den Rohdaten
  const calculateKPIs = (
    targetDate: string,
    serviceLevelData: any[],
    pickingData: any[],
    deliveryData: any[],
    qmData: any[],
    tagesleistungData: any[],
    ablaengereiData: any[],
    weData: any[]
  ): DepartmentKPIs => {
    const lastUpdated = new Date().toLocaleTimeString('de-DE');

    // Hilfsfunktion: Verwende neueste verfügbare Daten als Fallback
    const getLatestOrTargetData = (dataArray: any[], targetDate: string, dateField: string = 'datum') => {
      // Versuche zuerst das Zieldatum zu finden
      let item = dataArray?.find(item => item[dateField] === targetDate);

      // Falls nicht gefunden, verwende den neuesten verfügbaren Datensatz
      if (!item && dataArray && dataArray.length > 0) {
        item = dataArray[0]; // Daten sind bereits nach Datum sortiert (neueste zuerst)

      }

      return item;
    };

    // Servicegrad - verwende neueste verfügbare Daten
    const serviceLevelItem = getLatestOrTargetData(serviceLevelData, targetDate);
    let serviceLevel = serviceLevelItem?.servicegrad || 0;
    // Konvertiere Dezimalwerte zu Prozent
    if (serviceLevel > 0 && serviceLevel <= 1) {
      serviceLevel = serviceLevel * 100;
    }

    // Picking-Performance - verwende neueste verfügbare Daten
    const pickingItem = getLatestOrTargetData(pickingData, targetDate, 'date');
    const pickingPerformance = pickingItem
      ? pickingItem.atrl + pickingItem.aril
      : 0;

    // Picking-Performance - verwende neueste verfügbare Daten
    const pickingAtrlItem = getLatestOrTargetData(pickingData, targetDate, 'date');
    const pickingAtrlPerformance = pickingAtrlItem
      ? pickingAtrlItem.atrl
      : 0;

    // Picking-Performance - verwende neueste verfügbare Daten
    const pickingArilItem = getLatestOrTargetData(pickingData, targetDate, 'date');
    const pickingArilPerformance = pickingArilItem
      ? pickingArilItem.aril
      : 0;

    // Delivery-Performance - verwende neueste verfügbare Daten
    const deliveryItem = getLatestOrTargetData(deliveryData, targetDate, 'date');
    const deliveryPerformance = deliveryItem && (deliveryItem.ausgeliefert_lup + deliveryItem.rueckstaendig) > 0
      ? (deliveryItem.ausgeliefert_lup / (deliveryItem.ausgeliefert_lup + deliveryItem.rueckstaendig)) * 100
      : 0;

    // Qualitätsrate - verwende neueste verfügbare Daten
    const qmLatestData = qmData && qmData.length > 0 ? qmData : [];
    const totalQM = qmLatestData?.reduce((sum, item) => sum + item.value, 0) || 0;
    const acceptedQM = qmLatestData?.find(item => item.name.toLowerCase().includes('angenommen'))?.value || 0;
    const qualityRate = totalQM > 0 ? (acceptedQM / totalQM) * 100 : 85; // Fallback-Wert

    // Tagesleistung - verwende neueste verfügbare Daten
    const tagesleistungItem = getLatestOrTargetData(tagesleistungData, targetDate, 'date');
    const producedTonnage = tagesleistungItem?.produzierte_tonnagen || 0;
    const kgPerColli = tagesleistungItem?.kg_pro_colli || 0;
    const umschlag = tagesleistungItem?.umschlag || 0;
    const directLoading = tagesleistungItem?.direktverladung_kiaa || 0;
    const elefanten = tagesleistungItem?.elefanten || 0;

    // Ablängerei-Daten - verwende neueste verfügbare Daten
    const ablaengereiItem = getLatestOrTargetData(ablaengereiData, targetDate);

    const cutTT = ablaengereiItem?.cutTT || 0;
    const cutTR = ablaengereiItem?.cutTR || 0;
    const cutRR = ablaengereiItem?.cutRR || 0;
    const pickCut = ablaengereiItem?.pickCut || 0;
    const schnitte = cutTT + cutTR + cutRR + pickCut; // Summe aller Schnitte



    // Qualitätsrate für Ablängerei (gleiche Berechnung wie bei Dispatch)
    const ablaengereiQualityRate = qualityRate;

    // Wareneingang-Daten - verwende neueste verfügbare Daten
    // getWEData() liefert Objekte der Form:
    // { datum: "YYYY-MM-DD", weAtrl: number, weManl: number }
    const weItem = getLatestOrTargetData(weData, targetDate, 'datum');
    const weAtrl = Number(weItem?.weAtrl ?? 0);
    const weManl = Number(weItem?.weManl ?? 0);
    const gesamtWE = weAtrl + weManl;
    const automatisierungsgrad = gesamtWE > 0 ? (weAtrl / gesamtWE) * 100 : 0;



    // Qualitätsrate für Wareneingang (gleiche Berechnung wie bei Dispatch)
    const wareneingangQualityRate = qualityRate;

    // Setze das tatsächlich verwendete Datum für die Anzeige
    const actualDate = weItem?.datum || ablaengereiItem?.datum || serviceLevelItem?.datum || targetDate;
    setActualDataDate(actualDate);

    return {
      dispatch: {
        serviceLevel,
        targetServiceLevel: 98.5,
        pickingPerformance,
        pickingArilPerformance,
        pickingAtrlPerformance,
        deliveryPerformance,
        qualityRate,
        producedTonnage,
        directLoading,
        umschlag,
        kgPerColli,
        elefanten,
        lastUpdated
      },
      ablaengerei: {
        schnitte,
        schnitteTT: cutTT,
        schnitteTR: cutTR,
        schnitteRR: cutRR,
        schnitteCut2Pick: pickCut,
        qualityRate: ablaengereiQualityRate,
        lastUpdated
      },
      wareneingang: {
        weAtrlPos: weAtrl,
        weManuellePos: weManl,
        gesamtWE,
        automatisierungsgrad,
        qualityRate: wareneingangQualityRate,
        lastUpdated
      }
    };
  };

  // Lade KPI-Daten nur beim Component Mount
  // selectedDate Dependency entfernt um Flackern zu verhindern
  useEffect(() => {
    loadKPIData();
  }, []); // Keine Abhängigkeit von selectedDate mehr



  // Loading State
  if (loading) {
    return (
      <div className="w-full bg-bg min-h-screen p-8 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mb-4"></div>
          <p className="font-heading text-xl">Dashboard wird geladen...</p>
        </div>
      </div>
    );
  }

  return (
    <DragDropProvider
      initialPositions={[
        { id: 'wareneingang', position: 0 },
        { id: 'tonnage', position: 1 },
        { id: 'performance', position: 2 },
        { id: 'efficiency', position: 3 },
        { id: 'quality', position: 4 },
        { id: 'delivery', position: 5 },
        { id: 'picking', position: 6 },
        { id: 'automatisierung', position: 7 },
        { id: 'produktion', position: 8 },
        { id: 'lager', position: 9 },
        { id: 'versand', position: 10 },
        { id: 'service', position: 11 }
      ]}
      showEditToggle={false}
    >
      <DashboardContent
        departmentKPIs={departmentKPIs}
        selectedDate={selectedDate}
        handleDateChange={handleDateChange}
        actualDataDate={actualDataDate}
        getTargetDate={getTargetDate}
        loadKPIData={loadKPIData}
        error={error}
        t={t}
      />
    </DragDropProvider>
  );
}
