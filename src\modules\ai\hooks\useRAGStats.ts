/**
 * useRAGStats Hook
 * 
 * Custom hook for fetching and managing RAG statistics
 */

import { useState, useEffect, useCallback } from 'react';

export interface RAGStats {
    totalDocuments: number;
    totalChunks: number;
    totalEmbeddings: number;
    totalKnowledgeBases: number;
    averageChunkSize: number;
    storageSize: number;
    averageResponseTime: number;
    averageSimilarity: number;
}

export interface UseRAGStatsReturn {
    stats: RAGStats | null;
    isLoading: boolean;
    error: string | null;
    refreshStats: () => Promise<void>;
}

export const useRAGStats = (): UseRAGStatsReturn => {
    const [stats, setStats] = useState<RAGStats | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Utility function for API calls with fallback
    const fetchWithFallback = async (endpoint: string, options?: RequestInit) => {
        // Use correct API base URL for development
        const API_BASE_URL = import.meta.env.DEV
            ? 'http://localhost:3001'
            : '';

        const baseUrl = endpoint.startsWith('http') ? '' : API_BASE_URL;
        const fullUrl = `${baseUrl}${endpoint}`;

        console.log(`[RAG Stats Debug] Attempting to fetch: ${fullUrl}`);

        try {
            const response = await fetch(fullUrl, {
                ...options,
                headers: {
                    'Content-Type': 'application/json',
                    ...options?.headers,
                },
            });

            console.log(`[RAG Stats Debug] Response status: ${response.status} for ${fullUrl}`);

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`[RAG Stats Debug] Error response:`, errorText);
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }

            return response;
        } catch (error) {
            console.error(`[RAG Stats Debug] Fetch failed for ${fullUrl}:`, error);
            throw error;
        }
    };

    const loadStats = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);
            
            const response = await fetchWithFallback('/api/rag/statistics');

            if (response.ok) {
                const data = await response.json();
                console.log('RAG Statistics loaded:', data);
                setStats(data.data);
            } else {
                const errorText = await response.text();
                console.error('Failed to load RAG statistics:', response.status, errorText);
                setError(`Fehler beim Laden der Statistiken (${response.status})`);
            }
        } catch (error) {
            console.error('Error loading RAG statistics:', error);
            setError('Verbindungsfehler beim Laden der Statistiken');
        } finally {
            setIsLoading(false);
        }
    }, []);

    const refreshStats = useCallback(async () => {
        await loadStats();
    }, [loadStats]);

    // Load stats on mount
    useEffect(() => {
        loadStats();
    }, [loadStats]);

    return {
        stats,
        isLoading,
        error,
        refreshStats
    };
};