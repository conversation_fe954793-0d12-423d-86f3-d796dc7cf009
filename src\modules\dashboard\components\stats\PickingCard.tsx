import React from 'react';
import { Package } from 'lucide-react';
import { BaseKPICardProps } from './types';

/**
 * KPI-Karte für Kommissionierung (Picking)
 * 
 * Zeigt die ATrL und ARiL Kommissionierungsleistung an.
 * Verwendet türkises Farbschema mit Package-Icon aus Lucide.
 * Diese Karte hat ein spezielles Layout mit zwei Werten.
 */
export const PickingCard: React.FC<BaseKPICardProps> = ({ 
  departmentKPIs, 
  className = '' 
}) => {
  return (
    <>
      <div className="flex items-center mb-1">
        <Package className="h-6 w-6 text-teal-600 mr-3" />
        <h1 className="text-xl text-gray-800">KOMMISSIONIERUNG</h1>
      </div>
      
      <div className="flex items-center">
        <h2 className="text-lg font-semibold text-gray-800">ATrL:</h2>
      </div>
      <div className="text-xl font-bold text-gray-900">
        {departmentKPIs?.dispatch.pickingAtrlPerformance.toLocaleString() || '0'}
        <span className="text-sm font-normal text-gray-500 ml-1">Pos</span>
      </div>
      
      <div className="flex items-center mt-1">
        <h2 className="text-lg font-semibold text-gray-800">ARiL:</h2>
      </div>
      <div className="text-xl font-bold text-gray-900">
        {departmentKPIs?.dispatch.pickingArilPerformance.toLocaleString() || '0'}
        <span className="text-sm font-normal text-gray-500 ml-1">Pos</span>
      </div>
    </>
  );
};

export default PickingCard;