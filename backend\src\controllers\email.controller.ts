/**
 * E-Mail Controller
 * 
 * Controller für E-Mail-Versand mit Anhängen.
 */

import { Request, Response } from 'express';
import { z } from 'zod';
import { getEmailService } from '../services/email.service';
import * as path from 'path';
import * as fs from 'fs';

// Temporäre Typen-Definitionen
interface EmailOptions {
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  text?: string;
  html?: string;
  attachments?: EmailAttachment[];
  replyTo?: string;
  priority?: 'high' | 'normal' | 'low';
}

interface EmailAttachment {
  filename: string;
  content?: Buffer | string;
  path?: string;
  contentType?: string;
  cid?: string;
}

interface StoerungData {
  titel: string;
  schweregrad: 'niedrig' | 'mittel' | 'hoch' | 'kritisch';
  system: string;
  standort: string;
  melder: string;
  beschreibung: string;
  anhaenge?: EmailAttachment[];
}

// Validation Schemas
const sendEmailSchema = z.object({
  to: z.union([z.string().email(), z.array(z.string().email())]),
  cc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  bcc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  subject: z.string().min(1, 'Betreff ist erforderlich'),
  text: z.string().optional(),
  html: z.string().optional(),
  attachmentPaths: z.array(z.string()).optional()
});

const sendStoerungEmailSchema = z.object({
  to: z.union([z.string().email(), z.array(z.string().email())]),
  stoerungId: z.string().min(1, 'Störungs-ID ist erforderlich'),
  stoerungData: z.object({
    title: z.string().min(1),
    description: z.string().min(1),
    severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
    affected_system: z.string().min(1),
    location: z.string().optional(),
    reported_by: z.string().optional()
  }),
  includeAttachments: z.boolean().default(true)
});

export class EmailController {
  private emailService = getEmailService();

  /**
   * Sendet eine allgemeine E-Mail
   */
  async sendEmail(req: Request, res: Response) {
    try {
      const validatedData = sendEmailSchema.parse(req.body);
      
      // Überprüfe E-Mail-Service-Konfiguration
      const isConfigured = await this.emailService.verifyConnection();
      if (!isConfigured) {
        return res.status(500).json({
          success: false,
          error: 'E-Mail-Service ist nicht konfiguriert oder nicht erreichbar'
        });
      }

      // Bereite Anhänge vor
      let attachments: EmailAttachment[] = [];
      if (validatedData.attachmentPaths && validatedData.attachmentPaths.length > 0) {
        for (const filePath of validatedData.attachmentPaths) {
          if (fs.existsSync(filePath)) {
            attachments.push({
              filename: path.basename(filePath),
              path: filePath,
              contentType: this.getContentType(filePath)
            });
          } else {
            console.warn(`Anhang nicht gefunden: ${filePath}`);
          }
        }
      }

      // Verarbeite Anhänge aus der Anfrage
      if (req.files && Array.isArray(req.files)) {
        for (const file of req.files) {
          attachments.push({
            filename: file.originalname,
            path: file.path,
            contentType: file.mimetype
          });
        }
      }

      // Sende E-Mail
      const success = await this.emailService.sendEmail({
        to: validatedData.to,
        cc: validatedData.cc,
        bcc: validatedData.bcc,
        subject: validatedData.subject,
        text: validatedData.text,
        html: validatedData.html,
        attachments: attachments.length > 0 ? attachments : undefined
      });

      if (success) {
        res.json({
          success: true,
          message: 'E-Mail erfolgreich gesendet',
          attachmentCount: attachments.length
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'E-Mail konnte nicht gesendet werden'
        });
      }
    } catch (error) {
      console.error('Fehler beim E-Mail-Versand:', error);
      
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Validierungsfehler',
          details: error.errors
        });
      }
      
      res.status(500).json({
        success: false,
        error: 'Interner Server-Fehler beim E-Mail-Versand'
      });
    }
  }

  /**
   * Sendet eine Störungs-E-Mail mit Anhängen
   */
  async sendStoerungEmail(req: Request, res: Response) {
    try {
      const validatedData = sendStoerungEmailSchema.parse(req.body);
      
      // Überprüfe E-Mail-Service-Konfiguration
      const isConfigured = await this.emailService.verifyConnection();
      if (!isConfigured) {
        return res.status(500).json({
          success: false,
          error: 'E-Mail-Service ist nicht konfiguriert oder nicht erreichbar'
        });
      }

      // Lade Anhänge der Störung, falls gewünscht
      let attachments: EmailAttachment[] = [];
      if (validatedData.includeAttachments) {
        try {
          // Hier würden wir die Anhänge aus der Datenbank laden
          const uploadDir = path.join(process.cwd(), 'uploads', 'stoerungen');
          const stoerungAttachments = await this.getStoerungAttachments(validatedData.stoerungId);
          
          for (const attachment of stoerungAttachments) {
            const filePath = path.join(uploadDir, attachment.stored_name || attachment.filename);
            if (fs.existsSync(filePath)) {
              attachments.push({
                filename: attachment.filename,
                path: filePath,
                contentType: attachment.mime_type || 'application/octet-stream'
              });
            }
          }
        } catch (attachmentError) {
          console.warn('Fehler beim Laden der Störungs-Anhänge:', attachmentError);
        }
      }

      // Sende Störungs-E-Mail
      const success = await this.emailService.sendStoerungEmail(
        validatedData.to,
        validatedData.stoerungData,
        attachments.length > 0 ? attachments : undefined
      );

      if (success) {
        res.json({
          success: true,
          message: 'Störungs-E-Mail erfolgreich gesendet',
          attachmentCount: attachments.length
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Störungs-E-Mail konnte nicht gesendet werden'
        });
      }
    } catch (error) {
      console.error('Fehler beim Störungs-E-Mail-Versand:', error);
      
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Validierungsfehler',
          details: error.errors
        });
      }
      
      res.status(500).json({
        success: false,
        error: 'Interner Server-Fehler beim Störungs-E-Mail-Versand'
      });
    }
  }

  /**
   * Überprüft die E-Mail-Konfiguration
   */
  async checkConfig(req: Request, res: Response) {
    try {
      const isConfigured = await this.emailService.verifyConnection();
      
      res.json({
        success: true,
        configured: isConfigured,
        message: isConfigured 
          ? 'E-Mail-Service ist konfiguriert und erreichbar'
          : 'E-Mail-Service ist nicht konfiguriert oder nicht erreichbar'
      });
    } catch (error) {
      console.error('Fehler bei der Konfigurationsprüfung:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler bei der Konfigurationsprüfung'
      });
    }
  }

  /**
   * Hilfsmethode: Lädt Anhänge einer Störung aus der Datenbank
   */
  private async getStoerungAttachments(stoerungId: string): Promise<any[]> {
    // Hier würde normalerweise eine Datenbankabfrage stehen
    // Für jetzt geben wir ein leeres Array zurück
    try {
      // TODO: Implementiere Datenbankabfrage für Störungs-Anhänge
      // TODO: Implementierung mit Drizzle ORM nach vollständiger Migration
// return await db.select().from(stoerungsAttachment).where({
      //   where: { stoerung_id: stoerungId }
      // });
      return [];
    } catch (error) {
      console.error('Fehler beim Laden der Störungs-Anhänge:', error);
      return [];
    }
  }

  /**
   * Hilfsmethode: Bestimmt den Content-Type basierend auf der Dateiendung
   */
  private getContentType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const contentTypes: { [key: string]: string } = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.txt': 'text/plain'
    };
    
    return contentTypes[ext] || 'application/octet-stream';
  }
}