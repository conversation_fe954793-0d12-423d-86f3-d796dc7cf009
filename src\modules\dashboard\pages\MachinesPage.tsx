import { useState } from "react";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { MaschinenEfficiencyChart } from "@/modules/dashboard/components/charts/MaschinenEfficiencyChart";
import { MaschinenDataTable } from "@/modules/dashboard/components/charts/MaschinenDataTable";
import { MaschinenHeatmapChart } from "@/modules/dashboard/components/charts/MaschinenHeatmapChart";
import { Scissors, FileSpreadsheet, FileText } from "lucide-react";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import { AskJaszButton } from "@/modules/ai/components";
import { createPageContext } from "@/contexts/AskJaszContext";
import { ExportService } from "@/services/export.service";
import { collectMachinesChartData } from "@/services/chart-data-collector.service";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

// Import für das Maschinen-Icon
import machineIcon from "../../../assets/cableMachine.png";

/**
 * Maschinen-Bereich Dashboard
 * 
 * Zeigt verschiedene Kennzahlen für den Maschinen-Bereich an:
 * - Maschinen-Effizienz als Balkendiagramm
 */
export default function MachinesPage() {
  // Zustand für den ausgewählten Datumsbereich - Standard: letzten 30 Tage (!NICHT ÄNDERN!)
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 3, 15), // 15. April 2025
    to: new Date(2025, 4, 7), // 7. Mai 2025 (alle verfügbaren Daten)
  });

  // Zustand für die Heatmap-Anzeige
  const [showHeatmap, setShowHeatmap] = useState<boolean>(false);

  // Zustand für Export-Loading
  const [isExporting, setIsExporting] = useState<{ excel: boolean; powerpoint: boolean }>({
    excel: false,
    powerpoint: false
  });

  // Export-Funktionen
  const handleExcelExport = async () => {
    if (!dateRange?.from || !dateRange?.to) {
      toast.error('Bitte wählen Sie einen Datumsbereich aus');
      return;
    }

    setIsExporting(prev => ({ ...prev, excel: true }));

    try {
      const chartData = await collectMachinesChartData(dateRange);

      if (chartData.length === 0) {
        toast.error('Keine Daten für den Export verfügbar');
        return;
      }

      await ExportService.exportToExcel(chartData, {
        dateRange,
        includeSummary: true,
        dashboardType: 'machines'
      });

      toast.success('Excel-Datei wurde erfolgreich erstellt');
    } catch (error) {
      console.error('Excel-Export Fehler:', error);
      toast.error('Fehler beim Erstellen der Excel-Datei');
    } finally {
      setIsExporting(prev => ({ ...prev, excel: false }));
    }
  };

  const handlePowerPointExport = async () => {
    if (!dateRange?.from || !dateRange?.to) {
      toast.error('Bitte wählen Sie einen Datumsbereich aus');
      return;
    }

    setIsExporting(prev => ({ ...prev, powerpoint: true }));

    try {
      const chartData = await collectMachinesChartData(dateRange);

      if (chartData.length === 0) {
        toast.error('Keine Daten für den Export verfügbar');
        return;
      }

      await ExportService.exportToPowerPoint(chartData, {
        dateRange,
        includeSummary: true,
        dashboardType: 'machines'
      });

      toast.success('PowerPoint-Datei wurde erfolgreich erstellt');
    } catch (error) {
      console.error('PowerPoint-Export Fehler:', error);
      toast.error('Fehler beim Erstellen der PowerPoint-Datei');
    } finally {
      setIsExporting(prev => ({ ...prev, powerpoint: false }));
    }
  };

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <img src={machineIcon} alt="Maschinen" className="h-10 w-10" />
          <h1 className="text-4xl font-bold text-black">MACHINEN-EFFIZIENZ</h1>
          <div className="-mt-7">
            <AskJaszButton
              context={createPageContext(
                "Maschinen-Effizienz Dashboard",
                [
                  "Effizienz-Monitoring von H1- und H3-Maschinen",
                  "Soll-/Ist-Vergleich der Schnittleistungen",
                  "Tagesschnitte vs. Soll-Schnitte Analyse",
                  "Detailansicht in Tabellenform"
                ],
                "Maschinenleistung und Effizienz-Übersicht"
              )}
              position="inline"
              size="md"
              variant="default"
              tooltip="Hilfe zum Maschinen-Dashboard erhalten"
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex gap-2">
            <Button
              onClick={handleExcelExport}
              disabled={isExporting.excel}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <img src="/src/assets/excel-pn.png" alt="Excel Export" className="h-8 w-8" />
            </Button>
            <Button
              onClick={handlePowerPointExport}
              disabled={isExporting.powerpoint}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <img src="/src/assets/pp.png" alt="PowerPoint Export" className="h-8 w-8" />
            </Button>
          </div>
          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
            label="Datumsauswahl"
          />
        </div>
      </div>

      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 gap-6">
          <div className="col-span-2">
            <ChartErrorBoundary>
              <MaschinenEfficiencyChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>

          {/* Maschinendatentabelle oder Heatmap mit Swipe-Animation */}
          <div className="col-span-2">
            <ChartErrorBoundary>
              <div className="relative overflow-hidden">
                {/* Tabellen-Ansicht */}
                <div 
                  className={`transition-transform duration-500 ease-in-out ${
                    showHeatmap ? '-translate-x-full opacity-0' : 'translate-x-0 opacity-100'
                  }`}
                  style={{ 
                    transform: showHeatmap ? 'translateX(-100%)' : 'translateX(0)',
                    display: showHeatmap ? 'none' : 'block'
                  }}
                >
                  <MaschinenDataTable 
                    dateRange={dateRange} 
                    onHeatmapClick={() => setShowHeatmap(true)}
                  />
                </div>
                
                {/* Heatmap-Ansicht */}
                <div 
                  className={`transition-transform duration-500 ease-in-out ${
                    showHeatmap ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
                  }`}
                  style={{ 
                    transform: showHeatmap ? 'translateX(0)' : 'translateX(100%)',
                    display: showHeatmap ? 'block' : 'none'
                  }}
                >
                  <MaschinenHeatmapChart 
                    dateRange={dateRange} 
                    onBackClick={() => setShowHeatmap(false)}
                  />
                </div>
              </div>
            </ChartErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
}
