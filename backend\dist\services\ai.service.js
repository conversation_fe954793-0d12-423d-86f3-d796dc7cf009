"use strict";
/**
 * AIService - Erweiterte AI-Funktionen für SFM Dashboard
 *
 * Bietet intelligente Datenanalyse, Anomalie-Erkennung,
 * Predictive Analytics und Prozessoptimierung
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const db_1 = require("../db");
const drizzle_orm_1 = require("drizzle-orm");
const schema_1 = require("../db/schema");
class AIService {
    constructor(database = db_1.db) {
        this.database = database;
    }
    /**
     * Anomalie-Erkennung in Produktionsdaten
     */
    async detectAnomalies(timeframe = 7) {
        const anomalies = [];
        const now = new Date();
        const startDate = new Date(now.getTime() - timeframe * 24 * 60 * 60 * 1000);
        try {
            // 1. Lagerauslastung Anomalien
            const warehouseAnomalies = await this.detectWarehouseAnomalies();
            anomalies.push(...warehouseAnomalies);
            // 2. Schnitt-Performance Anomalien
            const cuttingAnomalies = await this.detectCuttingAnomalies(startDate);
            anomalies.push(...cuttingAnomalies);
            // 3. Dispatch/Servicegrad Anomalien
            const dispatchAnomalies = await this.detectDispatchAnomalies(startDate);
            anomalies.push(...dispatchAnomalies);
            // 4. System-Performance Anomalien
            const systemAnomalies = await this.detectSystemAnomalies();
            anomalies.push(...systemAnomalies);
            return anomalies.sort((a, b) => {
                const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
                return severityOrder[b.severity] - severityOrder[a.severity];
            });
        }
        catch (error) {
            console.error('Fehler bei Anomalie-Erkennung:', error);
            return [];
        }
    }
    /**
     * Lagerauslastung Anomalien
     */
    async detectWarehouseAnomalies() {
        const anomalies = [];
        try {
            // Aktuelle Auslastung abrufen
            const currentStock = await this.database.select({
                auslastung: schema_1.bestand200.auslastung,
                auslastungA: schema_1.bestand200.auslastungA,
                auslastungB: schema_1.bestand200.auslastungB,
                auslastungC: schema_1.bestand200.auslastungC
            }).from(schema_1.bestand200).limit(1);
            if (currentStock.length > 0) {
                const stockData = currentStock[0];
                const auslastung = parseFloat(stockData.auslastung || '0');
                const auslastungA = parseFloat(stockData.auslastungA || '0');
                // Kritische Überauslastung (>95%)
                if (auslastung > 0.95) {
                    anomalies.push({
                        type: 'warehouse',
                        severity: 'critical',
                        description: 'Lager kritisch überausgelastet',
                        value: auslastung,
                        threshold: 0.95,
                        timestamp: new Date(),
                        recommendation: 'Sofortige Umlagerung oder Auslagerung erforderlich'
                    });
                }
                // A-Artikel Überauslastung (>98%)
                if (auslastungA > 0.98) {
                    anomalies.push({
                        type: 'warehouse',
                        severity: 'high',
                        description: 'A-Artikel Bereich überausgelastet',
                        value: auslastungA,
                        threshold: 0.98,
                        timestamp: new Date(),
                        recommendation: 'Prioritäre A-Artikel umlagern oder auslagern'
                    });
                }
                // Unterauslastung (<20%)
                if (auslastung < 0.20) {
                    anomalies.push({
                        type: 'warehouse',
                        severity: 'medium',
                        description: 'Lager stark unterausgelastet',
                        value: auslastung,
                        threshold: 0.20,
                        timestamp: new Date(),
                        recommendation: 'Lagerplatz-Konsolidierung prüfen'
                    });
                }
            }
        }
        catch (error) {
            console.error('Fehler bei Lager-Anomalie-Erkennung:', error);
        }
        return anomalies;
    }
    /**
     * Schnitt-Performance Anomalien
     */
    async detectCuttingAnomalies(startDate) {
        const anomalies = [];
        try {
            // Aktuelle Schnittdaten der letzten 7 Tage - alle Maschinen-Spalten abfragen
            const recentCuttings = await this.database.select({
                datum: schema_1.schnitte.datum, // Spalte 'datum' (kleingeschrieben im Schema, aber 'Datum' in DB)
                m5RH1: schema_1.schnitte.m5RH1,
                m6TH1: schema_1.schnitte.m6TH1,
                m7RH1: schema_1.schnitte.m7RH1,
                m8TH1: schema_1.schnitte.m8TH1,
                m9RH1: schema_1.schnitte.m9RH1,
                m10TH1: schema_1.schnitte.m10TH1,
                m11RH1: schema_1.schnitte.m11RH1,
                m12TH1: schema_1.schnitte.m12TH1,
                m13RH1: schema_1.schnitte.m13RH1,
                m14TH1: schema_1.schnitte.m14TH1,
                m15RH1: schema_1.schnitte.m15RH1
            }).from(schema_1.schnitte)
                .where((0, drizzle_orm_1.gte)(schema_1.schnitte.datum, startDate.toISOString().split('T')[0]));
            if (recentCuttings.length > 0) {
                // Durchschnittliche tägliche Schnitte berechnen - Summen zur Laufzeit berechnen
                const totalCuts = recentCuttings.reduce((sum, cutting) => {
                    // Sum_H1 entspricht allen R-Maschinen (m5RH1, m7RH1, m9RH1, m11RH1, m13RH1, m15RH1)
                    const sumH1 = (cutting.m5RH1 || 0) + (cutting.m7RH1 || 0) + (cutting.m9RH1 || 0) +
                        (cutting.m11RH1 || 0) + (cutting.m13RH1 || 0) + (cutting.m15RH1 || 0);
                    // Sum_H3 entspricht allen T-Maschinen (m6TH1, m8TH1, m10TH1, m12TH1, m14TH1)
                    const sumH3 = (cutting.m6TH1 || 0) + (cutting.m8TH1 || 0) + (cutting.m10TH1 || 0) +
                        (cutting.m12TH1 || 0) + (cutting.m14TH1 || 0);
                    return sum + sumH1 + sumH3;
                }, 0);
                const avgDailyCuts = totalCuts / recentCuttings.length;
                // Letzte Schnittdaten - Summen zur Laufzeit berechnen
                const latestCutting = recentCuttings[recentCuttings.length - 1];
                const latestSumH1 = (latestCutting.m5RH1 || 0) + (latestCutting.m7RH1 || 0) + (latestCutting.m9RH1 || 0) +
                    (latestCutting.m11RH1 || 0) + (latestCutting.m13RH1 || 0) + (latestCutting.m15RH1 || 0);
                const latestSumH3 = (latestCutting.m6TH1 || 0) + (latestCutting.m8TH1 || 0) + (latestCutting.m10TH1 || 0) +
                    (latestCutting.m12TH1 || 0) + (latestCutting.m14TH1 || 0);
                const latestTotal = latestSumH1 + latestSumH3;
                // Performance Drop (>30% unter Durchschnitt)
                if (latestTotal < avgDailyCuts * 0.7) {
                    anomalies.push({
                        type: 'cutting',
                        severity: 'high',
                        description: 'Schnittleistung deutlich unter Durchschnitt',
                        value: latestTotal,
                        threshold: avgDailyCuts * 0.7,
                        timestamp: new Date(),
                        recommendation: 'Maschinenauslastung und -wartung prüfen'
                    });
                }
                // Außergewöhnlich hohe Performance (>150% über Durchschnitt)
                if (latestTotal > avgDailyCuts * 1.5) {
                    anomalies.push({
                        type: 'cutting',
                        severity: 'medium',
                        description: 'Außergewöhnlich hohe Schnittleistung',
                        value: latestTotal,
                        threshold: avgDailyCuts * 1.5,
                        timestamp: new Date(),
                        recommendation: 'Qualitätskontrolle verstärken, Verschleiß prüfen'
                    });
                }
            }
        }
        catch (error) {
            console.error('Fehler bei Schnitt-Anomalie-Erkennung:', error);
        }
        return anomalies;
    }
    /**
     * Dispatch/Servicegrad Anomalien
     */
    async detectDispatchAnomalies(startDate) {
        const anomalies = [];
        try {
            const recentDispatch = await this.database.select({
                servicegrad: schema_1.dispatchData.servicegrad,
                datum: schema_1.dispatchData.datum
            }).from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.gte)(schema_1.dispatchData.datum, startDate.toISOString().split('T')[0]))
                .orderBy((0, drizzle_orm_1.desc)(schema_1.dispatchData.datum))
                .limit(7);
            if (recentDispatch.length > 0) {
                const latestServicegrad = recentDispatch[0].servicegrad;
                // Kritischer Servicegrad (<70%)
                if (latestServicegrad && latestServicegrad < 0.70) {
                    anomalies.push({
                        type: 'dispatch',
                        severity: 'critical',
                        description: 'Servicegrad kritisch niedrig',
                        value: latestServicegrad || 0,
                        threshold: 0.70,
                        timestamp: new Date(),
                        recommendation: 'Sofortige Kapazitätserhöhung oder Prozessoptimierung'
                    });
                }
                // Niederer Servicegrad (<85%)
                if (latestServicegrad && latestServicegrad < 0.85 && latestServicegrad >= 0.70) {
                    anomalies.push({
                        type: 'dispatch',
                        severity: 'medium',
                        description: 'Servicegrad unter Zielwert',
                        value: latestServicegrad || 0,
                        threshold: 0.85,
                        timestamp: new Date(),
                        recommendation: 'Engpässe in der Kommissionierung prüfen'
                    });
                }
            }
        }
        catch (error) {
            console.error('Fehler bei Dispatch-Anomalie-Erkennung:', error);
        }
        return anomalies;
    }
    /**
     * System-Performance Anomalien
     */
    async detectSystemAnomalies() {
        const anomalies = [];
        try {
            // Prüfe auf System-Störungen oder kritische Verfügbarkeit
            const systemDataResult = await this.database.select()
                .from(schema_1.system)
                .orderBy((0, drizzle_orm_1.desc)(schema_1.system.datum))
                .limit(1);
            const systemData = systemDataResult.length > 0 ? systemDataResult[0] : null;
            if (systemData) {
                // FTS Verfügbarkeit (einzige verfügbare Spalte in System-Tabelle)
                const ftsVerfuegbarkeit = systemData.verfuegbarkeitFts || 0;
                if (ftsVerfuegbarkeit < 0.90) {
                    anomalies.push({
                        type: 'system',
                        severity: 'high',
                        description: 'FTS Systemverfügbarkeit niedrig',
                        value: ftsVerfuegbarkeit,
                        threshold: 0.90,
                        timestamp: new Date(),
                        recommendation: 'FTS System-Check und Wartung einplanen'
                    });
                }
                // Hinweis: Gesamtverfügbarkeit nicht verfügbar in System-Tabelle
                // Nur FTS-Verfügbarkeit wird überwacht
                // Weitere Verfügbarkeitsdaten müssten aus anderen Tabellen aggregiert werden
            }
        }
        catch (error) {
            console.error('Fehler bei System-Anomalie-Erkennung:', error);
        }
        return anomalies;
    }
    /**
     * Predictive Analytics für Bestandsengpässe
     */
    async predictInventoryShortage(days = 14) {
        var _a;
        const predictions = [];
        try {
            // Analysiere historische Bewegungsdaten
            const historicalMovements = await this.database.select()
                .from(schema_1.ablaengerei)
                .orderBy((0, drizzle_orm_1.desc)(schema_1.ablaengerei.datum))
                .limit(30);
            // Berechne durchschnittliche tägliche Auslagerungen
            const avgDailyOutbound = historicalMovements.reduce((sum, movement) => sum + (movement.cutLagerK200 || 0) + (movement.cutLagerK220 || 0) + (movement.cutLagerK240 || 0), 0) / historicalMovements.length;
            // Aktuelle Lagerbestände
            const currentStockResult = await this.database.select({
                gesamtbestand: schema_1.bestand200.gesamtbestand
            }).from(schema_1.bestand200);
            const totalStock = currentStockResult.reduce((sum, item) => sum + (item.gesamtbestand || 0), 0);
            const projectedDaysUntilEmpty = totalStock / avgDailyOutbound;
            if (projectedDaysUntilEmpty < days) {
                predictions.push({
                    type: 'inventory_shortage',
                    probability: Math.min(0.95, (days - projectedDaysUntilEmpty) / days),
                    daysUntil: Math.ceil(projectedDaysUntilEmpty),
                    description: `Bestandsengpass in ${Math.ceil(projectedDaysUntilEmpty)} Tagen erwartet`,
                    impact: projectedDaysUntilEmpty < 7 ? 'high' : 'medium',
                    preventionActions: [
                        'Eilbestellungen einleiten',
                        'Verbrauch reduzieren',
                        'Alternative Lieferanten kontaktieren',
                        'Lagerumschlag optimieren'
                    ]
                });
            }
            // Kapazitätsüberlastung vorhersagen
            const avgDailyInbound = historicalMovements.reduce((sum, movement) => sum + (movement.lagerCut200 || 0) + (movement.lagerCut220 || 0) + (movement.lagerCut240 || 0), 0) / historicalMovements.length;
            const currentUtilizationResult = await this.database.select({
                auslastung: schema_1.bestand200.auslastung
            }).from(schema_1.bestand200).limit(1);
            const currentUtilization = parseFloat(((_a = currentUtilizationResult[0]) === null || _a === void 0 ? void 0 : _a.auslastung) || '0');
            const netInflow = avgDailyInbound - avgDailyOutbound;
            if (netInflow > 0 && currentUtilization > 0.80) {
                const daysUntilOverload = (0.95 - currentUtilization) / (netInflow / totalStock);
                if (daysUntilOverload < days) {
                    predictions.push({
                        type: 'capacity_overload',
                        probability: Math.min(0.90, (days - daysUntilOverload) / days),
                        daysUntil: Math.ceil(daysUntilOverload),
                        description: `Lagerkapazität überlastet in ${Math.ceil(daysUntilOverload)} Tagen`,
                        impact: 'high',
                        preventionActions: [
                            'Auslagerungen verstärken',
                            'Umlagerungen einplanen',
                            'Zusätzliche Lagerkapazität schaffen',
                            'Eingangskontrolle verschärfen'
                        ]
                    });
                }
            }
        }
        catch (error) {
            console.error('Fehler bei Predictive Analytics:', error);
        }
        return predictions.sort((a, b) => b.probability - a.probability);
    }
    /**
     * Schnittoptimierung mit einfachem Bin-Packing
     */
    async suggestCuttingOptimization() {
        try {
            // Hole aktuelle Schnittdaten
            const recentCuttings = await this.database.select()
                .from(schema_1.schnitte)
                .orderBy((0, drizzle_orm_1.desc)(schema_1.schnitte.datum)) // Schema verwendet kleingeschriebenes 'datum'
                .limit(7);
            // Berechne aktuelle Effizienz
            const totalCuts = recentCuttings.reduce((sum, cutting) => {
                // Berechne Sum_H1 und Sum_H3 zur Laufzeit aus einzelnen Maschinenspalten
                const sumH1 = (cutting.m5RH1 || 0) + (cutting.m6RH1 || 0) + (cutting.m7RH1 || 0) +
                    (cutting.m8RH1 || 0) + (cutting.m9RH1 || 0) + (cutting.m10RH1 || 0) +
                    (cutting.m11RH1 || 0) + (cutting.m12RH1 || 0) + (cutting.m13RH1 || 0) +
                    (cutting.m14RH1 || 0) + (cutting.m15RH1 || 0);
                const sumH3 = (cutting.m5TH1 || 0) + (cutting.m6TH1 || 0) + (cutting.m7TH1 || 0) +
                    (cutting.m8TH1 || 0) + (cutting.m9TH1 || 0) + (cutting.m10TH1 || 0) +
                    (cutting.m11TH1 || 0) + (cutting.m12TH1 || 0) + (cutting.m13TH1 || 0) +
                    (cutting.m14TH1 || 0) + (cutting.m15TH1 || 0);
                return sum + sumH1 + sumH3;
            }, 0);
            const avgDailyCuts = totalCuts / recentCuttings.length;
            // Hole Materialverbrauchsdaten
            const materialMovements = await this.database.select()
                .from(schema_1.ablaengerei)
                .orderBy((0, drizzle_orm_1.desc)(schema_1.ablaengerei.datum)) // Schema verwendet kleingeschriebenes 'datum'
                .limit(7);
            const avgMaterialUsage = materialMovements.reduce((sum, movement) => sum + (movement.cutGesamt || 0), 0) / materialMovements.length;
            // Berechne aktuelle Effizienz (Schnitte pro Material)
            const currentEfficiency = avgMaterialUsage > 0 ? (avgDailyCuts / avgMaterialUsage) * 100 : 0;
            // Optimierungsvorschläge basierend auf Analyse
            const actions = [];
            // Bin-Packing Optimierung
            if (currentEfficiency < 75) {
                actions.push({
                    priority: 'high',
                    action: 'Schnittmuster-Optimierung',
                    description: 'Implementierung von Bin-Packing Algorithmus für bessere Materialausnutzung',
                    effort: 'medium',
                    impact: 'high'
                });
            }
            // Maschinenauslastung
            // Berechne H1 und H3 Verhältnisse zur Laufzeit
            const h1Total = recentCuttings.reduce((sum, c) => {
                const sumH1 = (c.m5RH1 || 0) + (c.m6RH1 || 0) + (c.m7RH1 || 0) +
                    (c.m8RH1 || 0) + (c.m9RH1 || 0) + (c.m10RH1 || 0) +
                    (c.m11RH1 || 0) + (c.m12RH1 || 0) + (c.m13RH1 || 0) +
                    (c.m14RH1 || 0) + (c.m15RH1 || 0);
                return sum + sumH1;
            }, 0);
            const h3Total = recentCuttings.reduce((sum, c) => {
                const sumH3 = (c.m5TH1 || 0) + (c.m6TH1 || 0) + (c.m7TH1 || 0) +
                    (c.m8TH1 || 0) + (c.m9TH1 || 0) + (c.m10TH1 || 0) +
                    (c.m11TH1 || 0) + (c.m12TH1 || 0) + (c.m13TH1 || 0) +
                    (c.m14TH1 || 0) + (c.m15TH1 || 0);
                return sum + sumH3;
            }, 0);
            const h1Ratio = h1Total / totalCuts;
            const h3Ratio = h3Total / totalCuts;
            if (Math.abs(h1Ratio - h3Ratio) > 0.3) {
                actions.push({
                    priority: 'medium',
                    action: 'Maschinenauslastung ausgleichen',
                    description: `H1: ${(h1Ratio * 100).toFixed(1)}%, H3: ${(h3Ratio * 100).toFixed(1)}% - Umverteilung optimieren`,
                    effort: 'low',
                    impact: 'medium'
                });
            }
            // Restlängen-Management
            actions.push({
                priority: 'medium',
                action: 'Restlängen-Datenbank',
                description: 'Systematische Erfassung und Wiederverwendung von Restlängen',
                effort: 'high',
                impact: 'high'
            });
            const potentialEfficiency = Math.min(95, currentEfficiency + 15); // Realistisches Optimierungspotential
            return {
                type: 'cutting',
                currentEfficiency: Math.round(currentEfficiency),
                potentialEfficiency: Math.round(potentialEfficiency),
                improvementPercent: Math.round(potentialEfficiency - currentEfficiency),
                actions,
                estimatedSavings: {
                    time: Math.round((potentialEfficiency - currentEfficiency) * 2), // Minuten pro Tag
                    material: Math.round((potentialEfficiency - currentEfficiency) * 0.5), // Prozent Materialeinsparung
                    cost: Math.round((potentialEfficiency - currentEfficiency) * 50) // Euro pro Tag
                }
            };
        }
        catch (error) {
            console.error('Fehler bei Schnittoptimierung:', error);
            return {
                type: 'cutting',
                currentEfficiency: 0,
                potentialEfficiency: 0,
                improvementPercent: 0,
                actions: [],
                estimatedSavings: { time: 0, material: 0, cost: 0 }
            };
        }
    }
    /**
     * Generiere intelligente Insights für Dashboard
     */
    async generateInsights(timeframe = 30) {
        const insights = [];
        try {
            // Performance Insights
            const cuttingInsight = await this.generateCuttingInsights(timeframe);
            if (cuttingInsight)
                insights.push(cuttingInsight);
            // Effizienz Insights
            const warehouseInsight = await this.generateWarehouseInsights();
            if (warehouseInsight)
                insights.push(warehouseInsight);
            // Qualitäts Insights
            const qualityInsight = await this.generateQualityInsights(timeframe);
            if (qualityInsight)
                insights.push(qualityInsight);
            // Kapazitäts Insights
            const capacityInsight = await this.generateCapacityInsights();
            if (capacityInsight)
                insights.push(capacityInsight);
        }
        catch (error) {
            console.error('Fehler bei Insight-Generierung:', error);
        }
        return insights.sort((a, b) => {
            const urgencyOrder = { high: 3, medium: 2, low: 1 };
            return urgencyOrder[b.urgency] - urgencyOrder[a.urgency];
        });
    }
    async generateCuttingInsights(timeframe) {
        try {
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - timeframe);
            // Hole alle Maschinenspalten für die Berechnung
            const cuttingData = await this.database.select()
                .from(schema_1.schnitte)
                .where((0, drizzle_orm_1.gte)(schema_1.schnitte.datum, startDate.toISOString().split('T')[0])); // Schema verwendet kleingeschriebenes 'datum'
            if (cuttingData.length < 7)
                return null;
            // Berechne Summen zur Laufzeit
            const calculateSums = (cutting) => {
                const sumH1 = (cutting.m5RH1 || 0) + (cutting.m6RH1 || 0) + (cutting.m7RH1 || 0) +
                    (cutting.m8RH1 || 0) + (cutting.m9RH1 || 0) + (cutting.m10RH1 || 0) +
                    (cutting.m11RH1 || 0) + (cutting.m12RH1 || 0) + (cutting.m13RH1 || 0) +
                    (cutting.m14RH1 || 0) + (cutting.m15RH1 || 0);
                const sumH3 = (cutting.m5TH1 || 0) + (cutting.m6TH1 || 0) + (cutting.m7TH1 || 0) +
                    (cutting.m8TH1 || 0) + (cutting.m9TH1 || 0) + (cutting.m10TH1 || 0) +
                    (cutting.m11TH1 || 0) + (cutting.m12TH1 || 0) + (cutting.m13TH1 || 0) +
                    (cutting.m14TH1 || 0) + (cutting.m15TH1 || 0);
                return sumH1 + sumH3;
            };
            const totalCuts = cuttingData.reduce((sum, c) => sum + calculateSums(c), 0);
            const avgDaily = totalCuts / cuttingData.length;
            // Trend-Analyse
            const recent = cuttingData.slice(-7);
            const older = cuttingData.slice(0, Math.min(7, cuttingData.length - 7));
            const recentAvg = recent.reduce((sum, c) => sum + calculateSums(c), 0) / recent.length;
            const olderAvg = older.length > 0 ?
                older.reduce((sum, c) => sum + calculateSums(c), 0) / older.length : recentAvg;
            const trendPercent = ((recentAvg - olderAvg) / olderAvg) * 100;
            return {
                category: 'performance',
                title: 'Schnittleistung Trend',
                description: `Durchschnittlich ${Math.round(avgDaily)} Schnitte/Tag. Trend: ${trendPercent > 0 ? '+' : ''}${trendPercent.toFixed(1)}%`,
                trend: trendPercent > 5 ? 'positive' : trendPercent < -5 ? 'negative' : 'stable',
                urgency: Math.abs(trendPercent) > 15 ? 'high' : 'medium',
                actionRequired: Math.abs(trendPercent) > 20,
                recommendations: trendPercent < -10 ?
                    ['Maschinenauslastung prüfen', 'Wartungsplan überarbeiten', 'Personalplanung optimieren'] :
                    ['Aktuelle Performance beibehalten', 'Effizienzsteigerungen dokumentieren']
            };
        }
        catch (error) {
            console.error('Fehler bei Cutting Insights:', error);
            return null;
        }
    }
    async generateWarehouseInsights() {
        try {
            const warehouseDataResult = await this.database.select({
                auslastung: schema_1.bestand200.auslastung,
                auslastungA: schema_1.bestand200.auslastungA,
                auslastungB: schema_1.bestand200.auslastungB,
                auslastungC: schema_1.bestand200.auslastungC
            }).from(schema_1.bestand200).limit(1);
            if (warehouseDataResult.length === 0)
                return null;
            const warehouseData = warehouseDataResult[0];
            const utilization = parseFloat(warehouseData.auslastung || '0');
            const utilizationA = parseFloat(warehouseData.auslastungA || '0');
            let urgency = 'low';
            let recommendations = [];
            if (utilization > 0.90) {
                urgency = 'high';
                recommendations = ['Sofortige Auslagerung einplanen', 'Umlagerungen priorisieren', 'Zusätzliche Kapazität schaffen'];
            }
            else if (utilization > 0.80) {
                urgency = 'medium';
                recommendations = ['Auslagerungen verstärken', 'Bestandsoptimierung prüfen'];
            }
            else {
                recommendations = ['Aktuelle Auslastung optimal', 'Kontinuierliches Monitoring'];
            }
            return {
                category: 'efficiency',
                title: 'Lagerauslastung Status',
                description: `Gesamtauslastung: ${(utilization * 100).toFixed(1)}%, A-Artikel: ${(utilizationA * 100).toFixed(1)}%`,
                trend: utilization > 0.85 ? 'negative' : utilization < 0.60 ? 'positive' : 'stable',
                urgency,
                actionRequired: utilization > 0.90,
                recommendations
            };
        }
        catch (error) {
            console.error('Fehler bei Warehouse Insights:', error);
            return null;
        }
    }
    async generateQualityInsights(timeframe) {
        try {
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - timeframe);
            const dispatchDataResults = await this.database.select({
                servicegrad: schema_1.dispatchData.servicegrad,
                qm_angenommen: schema_1.dispatchData.qm_angenommen,
                qm_abgelehnt: schema_1.dispatchData.qm_abgelehnt
            }).from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.gte)(schema_1.dispatchData.datum, startDate.toISOString().split('T')[0]));
            if (dispatchDataResults.length === 0)
                return null;
            const avgServicegrad = dispatchDataResults.reduce((sum, d) => sum + (d.servicegrad || 0), 0) / dispatchDataResults.length;
            const totalQMAngenommen = dispatchDataResults.reduce((sum, d) => sum + (d.qm_angenommen || 0), 0);
            const totalQMAbgelehnt = dispatchDataResults.reduce((sum, d) => sum + (d.qm_abgelehnt || 0), 0);
            const qmRate = totalQMAngenommen + totalQMAbgelehnt > 0 ?
                totalQMAngenommen / (totalQMAngenommen + totalQMAbgelehnt) : 1;
            return {
                category: 'quality',
                title: 'Qualitäts-Performance',
                description: `Servicegrad: ${(avgServicegrad * 100).toFixed(1)}%, QM-Rate: ${(qmRate * 100).toFixed(1)}%`,
                trend: avgServicegrad > 0.90 ? 'positive' : avgServicegrad < 0.80 ? 'negative' : 'stable',
                urgency: avgServicegrad < 0.75 ? 'high' : 'medium',
                actionRequired: avgServicegrad < 0.80 || qmRate < 0.95,
                recommendations: avgServicegrad < 0.85 ?
                    ['Kommissionierungsprozess optimieren', 'Kapazitäten erhöhen', 'Qualitätskontrolle verstärken'] :
                    ['Aktuelle Qualität beibehalten', 'Kontinuierliche Verbesserung']
            };
        }
        catch (error) {
            console.error('Fehler bei Quality Insights:', error);
            return null;
        }
    }
    async generateCapacityInsights() {
        try {
            // Aktuelle Systemverfügbarkeit
            const systemDataResult = await this.database.select()
                .from(schema_1.system)
                .orderBy((0, drizzle_orm_1.desc)(schema_1.system.datum)) // Schema verwendet kleingeschriebenes 'datum'
                .limit(1);
            if (systemDataResult.length === 0)
                return null;
            const systemData = systemDataResult[0];
            // Nur FTS-Verfügbarkeit ist in der System-Tabelle verfügbar
            const ftsVerfuegbarkeit = systemData.verfuegbarkeitFts || 0;
            return {
                category: 'capacity',
                title: 'System-Kapazität',
                description: `FTS-Verfügbarkeit: ${(ftsVerfuegbarkeit * 100).toFixed(1)}%`,
                trend: ftsVerfuegbarkeit > 0.95 ? 'positive' : ftsVerfuegbarkeit < 0.85 ? 'negative' : 'stable',
                urgency: ftsVerfuegbarkeit < 0.80 ? 'high' : ftsVerfuegbarkeit < 0.90 ? 'medium' : 'low',
                actionRequired: ftsVerfuegbarkeit < 0.85,
                recommendations: ftsVerfuegbarkeit < 0.90 ?
                    ['Systemwartung priorisieren', 'Ausfallursachen analysieren', 'Backup-Systeme aktivieren'] :
                    ['Präventive Wartung beibehalten', 'Performance monitoring']
            };
        }
        catch (error) {
            console.error('Fehler bei Capacity Insights:', error);
            return null;
        }
    }
    /**
     * Cleanup Ressourcen
     * Drizzle benötigt keine explizite Disconnect-Methode
     */
    async disconnect() {
        // Drizzle mit better-sqlite3 benötigt keine explizite Disconnect-Methode
        // Die Datenbankverbindung wird automatisch verwaltet
    }
}
exports.AIService = AIService;
exports.default = AIService;
