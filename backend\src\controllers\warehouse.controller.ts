import { Request, Response } from 'express';
import { db } from '../db';
import { auslastung200, auslastung240 } from '../db/schema';
import { gte, lte, and, asc } from 'drizzle-orm';

// Typdefinitionen für die Antworten
interface LagerauslastungDataPoint {
  date: string;
  auslastungA: number;
  auslastungB: number;
  auslastungC: number;
  gesamt: number;
}

interface WarehouseStats {
  date: string;
  avgAuslastungA: number | null;
  avgAuslastungB: number | null;
  avgAuslastungC: number | null;
}

export class WarehouseController {
  constructor() {
    // Drizzle DB wird direkt importiert
  }

  /**
   * Holt die Lagerauslastungsdaten für das Lager 200
   */
  async getLagerauslastung200(req: Request, res: Response) {
    try {
      const { startDate, endDate } = req.query;
      
      // Baue die WHERE-Bedingungen für Drizzle auf
      const conditions = [];
      
      if (startDate) {
        conditions.push(gte(auslastung200.aufnahmeDatum, startDate as string));
      }
      if (endDate) {
        conditions.push(lte(auslastung200.aufnahmeDatum, endDate as string));
      }

      // Hole die Rohdaten mit Drizzle
      let rawData;
      if (conditions.length > 0) {
        rawData = await db.select({
          aufnahmeDatum: auslastung200.aufnahmeDatum,
          auslastungA: auslastung200.auslastungA,
          auslastungB: auslastung200.auslastungB,
          auslastungC: auslastung200.auslastungC,
        }).from(auslastung200)
          .where(and(...conditions))
          .orderBy(asc(auslastung200.aufnahmeDatum));
      } else {
        rawData = await db.select({
          aufnahmeDatum: auslastung200.aufnahmeDatum,
          auslastungA: auslastung200.auslastungA,
          auslastungB: auslastung200.auslastungB,
          auslastungC: auslastung200.auslastungC,
        }).from(auslastung200)
          .orderBy(asc(auslastung200.aufnahmeDatum));
      }

      // Gruppiere nach Datum und berechne Durchschnitte
      const groupedByDate = rawData.reduce<Record<string, number[]>>((acc, item) => {
        if (!item.aufnahmeDatum) return acc;
        
        // Konvertiere das Datum zu 'YYYY-MM-DD' Format
        const date = new Date(item.aufnahmeDatum);
        const dateStr = date.toISOString().split('T')[0];
        if (!acc[dateStr]) {
          acc[dateStr] = [0, 0, 0, 0]; // [sumA, sumB, sumC, count]
        }
        
        // Konvertiere alle Werte zu Zahlen, um Typenkonflikte zu vermeiden
        const a = Number(item.auslastungA) || 0;
        const b = Number(item.auslastungB) || 0;
        const c = Number(item.auslastungC) || 0;
        
        acc[dateStr][0] += a;
        acc[dateStr][1] += b;
        acc[dateStr][2] += c;
        acc[dateStr][3] += 1;
        
        return acc;
      }, {});

      // Transformiere in das gewünschte Format
      const result: LagerauslastungDataPoint[] = Object.entries(groupedByDate).map(([date, values]) => {
        const [sumA, sumB, sumC, count] = values;
        const avgA = count > 0 ? sumA / count : 0;
        const avgB = count > 0 ? sumB / count : 0;
        const avgC = count > 0 ? sumC / count : 0;
        
        return {
          date,
          auslastungA: parseFloat(avgA.toFixed(2)),
          auslastungB: parseFloat(avgB.toFixed(2)),
          auslastungC: parseFloat(avgC.toFixed(2)),
          gesamt: parseFloat(((avgA + avgB + avgC) / 3).toFixed(2))
        };
      });

      res.json(result);
    } catch (error) {
      console.error('Fehler beim Abrufen der Lagerauslastung 200:', error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  }

  /**
   * Holt die Lagerauslastungsdaten für das Lager 240
   */
  async getLagerauslastung240(req: Request, res: Response) {
    try {
      const { startDate, endDate } = req.query;
      
      // Baue die WHERE-Bedingungen für Drizzle auf
      const conditions = [];
      
      if (startDate) {
        conditions.push(gte(auslastung240.aufnahmeDatum, String(startDate)));
      }
      if (endDate) {
        conditions.push(lte(auslastung240.aufnahmeDatum, String(endDate)));
      }

      // Hole die Rohdaten mit Drizzle aus auslastung240 Tabelle
      let rawData;
      if (conditions.length > 0) {
        rawData = await db.select({
          aufnahmeDatum: auslastung240.aufnahmeDatum,
          auslastungA: auslastung240.auslastungA,
          auslastungB: auslastung240.auslastungB,
          auslastungC: auslastung240.auslastungC,
        }).from(auslastung240)
          .where(and(...conditions))
          .orderBy(asc(auslastung240.aufnahmeDatum));
      } else {
        rawData = await db.select({
          aufnahmeDatum: auslastung240.aufnahmeDatum,
          auslastungA: auslastung240.auslastungA,
          auslastungB: auslastung240.auslastungB,
          auslastungC: auslastung240.auslastungC,
        }).from(auslastung240)
          .orderBy(asc(auslastung240.aufnahmeDatum));
      }

      // Gruppiere nach Datum und berechne Durchschnitte
      const groupedByDate = rawData.reduce<Record<string, number[]>>((acc, item) => {
        if (!item.aufnahmeDatum) return acc;
        
        // Konvertiere das Datum zu 'YYYY-MM-DD' Format
        const date = new Date(item.aufnahmeDatum);
        const dateStr = date.toISOString().split('T')[0];
        if (!acc[dateStr]) {
          acc[dateStr] = [0, 0, 0, 0]; // [sumA, sumB, sumC, count]
        }
        
        // Konvertiere alle Werte zu Zahlen und multipliziere mit 0.8 für Lager 240
        const a = (Number(item.auslastungA) || 0) * 0.8;
        const b = (Number(item.auslastungB) || 0) * 0.8;
        const c = (Number(item.auslastungC) || 0) * 0.8;
        
        acc[dateStr][0] += a;
        acc[dateStr][1] += b;
        acc[dateStr][2] += c;
        acc[dateStr][3] += 1;
        
        return acc;
      }, {});

      // Transformiere in das gewünschte Format
      const result: LagerauslastungDataPoint[] = Object.entries(groupedByDate).map(([date, values]) => {
        const [sumA, sumB, sumC, count] = values;
        const avgA = count > 0 ? sumA / count : 0;
        const avgB = count > 0 ? sumB / count : 0;
        const avgC = count > 0 ? sumC / count : 0;
        
        return {
          date,
          auslastungA: parseFloat(avgA.toFixed(2)),
          auslastungB: parseFloat(avgB.toFixed(2)),
          auslastungC: parseFloat(avgC.toFixed(2)),
          gesamt: parseFloat(((avgA + avgB + avgC) / 3).toFixed(2))
        };
      });

      res.json(result);
    } catch (error) {
      console.error('Fehler beim Abrufen der Lagerauslastung 240:', error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  }
}
