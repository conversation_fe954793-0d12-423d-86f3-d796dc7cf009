:root {
  --bg: #0b0e13;
  --panel: #0f1319;
  --panel-2: #10151c;
  --text: #e9eef6;
  --muted: #9aa3b2;
  --border: #171d27;
  --accent-pink: #d45bff;
  --accent-purple: #8b5cf6;
  --accent-violet: #6d28d9;
  --accent-cyan: #22d3ee;
  --accent-teal: #14c6c6;
  --accent-green: #16f089;
  --accent-lime: #12ff8c;
  --accent-orange: #ff7a18;
  --shadow: 0 0 0 1px var(--border), 0 10px 30px rgba(0, 0, 0, 0.45), inset 0 0 0 1px rgba(255, 255, 255, 0.02);
  --radius: 0px;
  --gap: 0px;
}

/* Base */
.wrap {
  max-width: 1120px;
  margin: min(5vh, 48px) auto;
  padding: 16px;
}

.title {
  letter-spacing: 0.04em;
  color: #cfd6e3;
  font-size: clamp(14px, 2.2vw, 16px);
  text-transform: uppercase;
  opacity: 0.8;
  margin: 0 0 14px 6px;
}

/* Bento Grid */
.bento {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-auto-rows: 120px;
  gap: var(--gap);
}

.card {
  position: relative;
  background: linear-gradient(180deg, var(--panel), var(--panel-2));
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  isolation: isolate;
  transform-style: preserve-3d;
  transition: transform 0.35s cubic-bezier(0.2, 0.7, 0.2, 1), box-shadow 0.35s ease, border-color 0.35s ease;
  border: 1px solid var(--border);
  will-change: transform;
}

.card::after {
  /* subtle gloss */
  content: "";
  position: absolute;
  inset: 0;
  background: radial-gradient(400px 160px at var(--mx, 50%) -80px, rgba(255, 255, 255, 0.06), transparent 50%);
  opacity: 0.4;
  pointer-events: none;
  mix-blend-mode: screen;
  transition: 0.25s ease;
}

.card:hover {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.04), 0 18px 50px rgba(0, 0, 0, 0.55), 0 0 60px rgba(130, 87, 230, 0.08);
}

.content {
  position: relative;
  z-index: 2;
  height: 100%;
  padding: 18px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.eyebrow {
  font-size: 11px;
  letter-spacing: 0.16em;
  color: var(--muted);
  text-transform: uppercase;
}

.headline {
  font-weight: 700;
  line-height: 1;
  letter-spacing: 0.01em;
  font-size: clamp(24px, 3.6vw, 42px);
  color: var(--text);
}

/* Layout mapping (6 columns) */
.t1 {
  grid-column: 1 / span 2;
  grid-row: 1 / span 2;
}

.t2 {
  grid-column: 3 / span 2;
  grid-row: 1 / span 1;
}

.t3 {
  grid-column: 5 / span 2;
  grid-row: 1 / span 1;
}

.t4 {
  grid-column: 3 / span 3;
  grid-row: 2 / span 1;
}

.t5 {
  grid-column: 3 / span 3;
  grid-row: 3 / span 1;
}

.t6 {
  grid-column: 5 / span 2;
  grid-row: 2 / span 2;
}

.t7 {
  grid-column: 1 / span 3;
  grid-row: 3 / span 2;
}

.t8 {
  grid-column: 4 / span 1;
  grid-row: 4 / span 1;
}

.t9 {
  grid-column: 5 / span 1;
  grid-row: 4 / span 1;
}

.t10 {
  grid-column: 6 / span 1;
  grid-row: 4 / span 1;
}

/* Card specifics and effects */

/* T1 – Distributed Systems */
.t1 {
  overflow: hidden;
}

.t1 .content {
  align-items: flex-start;
}

.t1 .splash {
  position: absolute;
  inset: -40% auto auto -40%;
  width: 110%;
  height: 110%;
  background: radial-gradient(450px 340px at 35% 30%, rgba(212, 91, 255, 0.35), transparent 55%), radial-gradient(480px 340px at 30% 20%, rgba(255, 122, 24, 0.28), transparent 55%);
  filter: blur(6px);
  z-index: 1;
  pointer-events: none;
  transition: transform 0.6s ease, opacity 0.6s ease;
}

.t1:hover .splash {
  transform: translate3d(6px, -6px, 0) scale(1.03);
  opacity: 0.95;
}

.t1 .dots {
  position: absolute;
  right: -40px;
  bottom: -40px;
  width: 220px;
  height: 220px;
  opacity: 0.12;
  background-image: radial-gradient(#fff 1px, transparent 1px);
  background-size: 10px 10px;
  filter: contrast(120%);
  transform: rotate(15deg);
}

.t1 .eyebrow {
  color: #e7e7f6;
  opacity: 0.9;
}

/* T2 – Blockchain Powered */
.t2 .chain {
  width: 64px;
  height: 64px;
  position: absolute;
  right: 16px;
  top: 14px;
  opacity: 0.85;
  transform: translateZ(30px);
}

.t2 .chain path {
  stroke: url(#gradChain);
  stroke-width: 2;
  fill: none;
  filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.4));
}

.t2:hover {
  transform: translateZ(0) rotateX(2deg) rotateY(-2.5deg);
}

.t2:hover .chain {
  animation: bob 1.2s ease-in-out infinite alternate;
}

@keyframes bob {
  to {
    transform: translateY(4px) translateZ(30px);
  }
}

/* T3 – Quantum Encryption */
.t3 .beam {
  position: absolute;
  inset: 12px 18px 12px auto;
  width: 2px;
  border-radius: 2px;
  background: linear-gradient(180deg, rgba(212, 91, 255, 0), rgba(212, 91, 255, 0.9), rgba(34, 211, 238, 0));
  box-shadow: 0 0 22px rgba(212, 91, 255, 0.65), 0 0 44px rgba(34, 211, 238, 0.35) inset;
  animation: beamPulse 2.2s ease-in-out infinite;
}

@keyframes beamPulse {
  50% {
    transform: scaleY(1.25);
  }
}

.t3:hover .beam {
  filter: hue-rotate(40deg) brightness(1.25);
}

/* T4 – AI-powered Insights */
.t4 .bars {
  display: flex;
  gap: 6px;
  align-items: flex-end;
  height: 54px;
}

.t4 .bar {
  flex: 1;
  background: linear-gradient(180deg, rgba(34, 211, 238, 0.8), rgba(20, 198, 198, 0.22));
  border-radius: 6px;
  box-shadow: 0 0 18px rgba(34, 211, 238, 0.22);
  transform-origin: bottom;
  animation: grow 1.6s ease-in-out infinite;
}

.t4 .bar:nth-child(2) {
  height: 80%;
  animation-delay: 0.15s;
}

.t4 .bar:nth-child(3) {
  height: 48%;
  animation-delay: 0.3s;
}

.t4 .bar:nth-child(4) {
  height: 65%;
  animation-delay: 0.45s;
}

.t4 .bar:nth-child(5) {
  height: 35%;
  animation-delay: 0.6s;
}

@keyframes grow {
  50% {
    transform: scaleY(0.7);
  }
}

.t4:hover .bar {
  filter: brightness(1.2) saturate(1.2);
}

/* T5 – Performance 20% */
.t5 {
  background: radial-gradient(220px 220px at 50% 50%, rgba(140, 150, 170, 0.08), transparent 60%), linear-gradient(180deg, var(--panel), var(--panel-2));
}

.t5 .dial {
  position: absolute;
  inset: 0;
  display: grid;
  place-items: center;
  pointer-events: none;
  opacity: 0.7;
}

.t5 .dial:before {
  content: "";
  width: 220px;
  height: 220px;
  border-radius: 50%;
  background: conic-gradient(from 0deg, rgba(255, 255, 255, 0) 0 80%, rgba(22, 240, 137, 0.55) 80% 100%);
  -webkit-mask: radial-gradient(circle 86px, transparent 74px, #000 75px);
  mask: radial-gradient(circle 86px, transparent 74px, #000 75px);
  filter: blur(6px);
  animation: sweep 4s linear infinite;
}

@keyframes sweep {
  to {
    transform: rotate(360deg);
  }
}

.t5 .big {
  font-size: clamp(28px, 5.4vw, 44px);
  font-weight: 800;
  letter-spacing: 0.02em;
}

.t5 .eyebrow {
  opacity: 0.8;
}

.t5:hover {
  transform: translateZ(0) rotateX(2deg) rotateY(2deg);
}

/* T6 – Advanced Automation (green block with slider) */
.t6 {
  background: linear-gradient(135deg, rgba(18, 255, 140, 0.15), rgba(18, 255, 140, 0) 55%), linear-gradient(180deg, var(--panel), var(--panel-2));
  border-color: #1b2b22;
}

.t6 .headline {
  font-size: clamp(22px, 3.2vw, 34px);
  color: #c9ffe7;
}

.t6 .sliderWrap {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 8px;
}

.t6 input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 10px;
  border-radius: 10px;
  background: linear-gradient(90deg, var(--accent-lime) var(--val, 55%), rgba(255, 255, 255, 0.12) var(--val, 55%));
  outline: none;
  transition: background 0.15s linear;
}

.t6 input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: radial-gradient(circle at 35% 35%, #fff, #b4ffd9 35%, #42ffa6 36% 70%, #0e7348 90%);
  box-shadow: 0 0 0 3px rgba(22, 240, 137, 0.25), 0 0 30px rgba(22, 240, 137, 0.55);
  cursor: pointer;
  border: none;
}

.t6 .glow {
  position: absolute;
  right: -30px;
  top: -30px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(closest-side, rgba(18, 255, 140, 0.25), transparent 70%);
  filter: blur(10px);
  opacity: 0.75;
  pointer-events: none;
  animation: breathe 4s ease-in-out infinite;
}

@keyframes breathe {
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

/* T7 – Security Vault */
.t7 {
  background: radial-gradient(700px 400px at 0% 100%, rgba(212, 91, 255, 0.15), transparent 60%), linear-gradient(180deg, var(--panel), var(--panel-2));
}

.t7 .headline {
  font-size: clamp(40px, 8vw, 64px);
  letter-spacing: 0.04em;
}

.t7 .lock {
  position: absolute;
  right: 22px;
  bottom: 22px;
  width: 72px;
  height: 72px;
  opacity: 0.9;
}

.t7 .lock path {
  stroke: url(#gradLock);
  stroke-width: 2;
  fill: none;
  stroke-linecap: round;
}

.t7:hover .lock path {
  stroke-dasharray: 180;
  stroke-dashoffset: 180;
  animation: dash 1.4s ease forwards;
  filter: drop-shadow(0 0 12px rgba(212, 91, 255, 0.35));
}

@keyframes dash {
  to {
    stroke-dashoffset: 0;
  }
}

/* T8 – Rotating ring (bottom center) */
.t8 {
  display: grid;
  place-items: center;
}

.t8 .ring {
  width: 62px;
  height: 62px;
  border-radius: 50%;
  background: conic-gradient(from 0deg, rgba(212, 91, 255, 0), rgba(212, 91, 255, 0.8), rgba(34, 211, 238, 0.9), rgba(212, 91, 255, 0));
  -webkit-mask: radial-gradient(circle 28px, transparent 26px, #000 27px);
  mask: radial-gradient(circle 28px, transparent 26px, #000 27px);
  filter: blur(0.3px);
  animation: rotate 8s linear infinite;
}

@keyframes rotate {
  to {
    transform: rotate(360deg);
  }
}

.t8:hover .ring {
  animation-duration: 3.5s;
}

/* T9 – Slim neon line */
.t9 .line {
  position: absolute;
  left: 50%;
  top: 18px;
  bottom: 18px;
  width: 2px;
  transform: translateX(-50%);
  background: linear-gradient(180deg, rgba(34, 211, 238, 0), rgba(34, 211, 238, 0.85), rgba(212, 91, 255, 0.85), rgba(34, 211, 238, 0));
  box-shadow: 0 0 18px rgba(34, 211, 238, 0.55);
  animation: shimmer 2.6s ease-in-out infinite;
}

@keyframes shimmer {
  50% {
    filter: hue-rotate(40deg) brightness(1.2);
  }
}

/* T10 – Subtle cube */
.t10 .cube {
  position: absolute;
  inset: 18px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(212, 91, 255, 0.15), rgba(34, 211, 238, 0) 50%), radial-gradient(180px 180px at 80% 80%, rgba(34, 211, 238, 0.08), transparent 60%);
  border: 1px solid rgba(180, 190, 210, 0.08);
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.03);
}

.t10:hover .cube {
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.07), 0 0 24px rgba(212, 91, 255, 0.12);
}

/* Shared micro-label placement */
.labelTop {
  position: absolute;
  top: 16px;
  left: 18px;
}

/* Hover tilt (JS sets --rx/--ry) */
.card {
  --rx: 0;
  --ry: 0;
  --tx: 0px;
  --ty: 0px;
  transform: perspective(900px) rotateX(var(--rx)) rotateY(var(--ry)) translateZ(0);
}

/* Responsive */
@media (max-width: 980px) {
  .bento {
    grid-template-columns: repeat(4, 1fr);
    grid-auto-rows: 120px;
  }

  .t1 {
    grid-column: 1 / span 2;
    grid-row: 1 / span 2;
  }

  .t2 {
    grid-column: 3 / span 2;
    grid-row: 1 / span 1;
  }

  .t3 {
    grid-column: 3 / span 2;
    grid-row: 2 / span 1;
  }

  .t4 {
    grid-column: 1 / span 2;
    grid-row: 3 / span 1;
  }

  .t5 {
    grid-column: 3 / span 2;
    grid-row: 3 / span 1;
  }

  .t6 {
    grid-column: 3 / span 2;
    grid-row: 4 / span 2;
  }

  .t7 {
    grid-column: 1 / span 4;
    grid-row: 5 / span 2;
  }

  .t8 {
    grid-column: 1 / span 1;
    grid-row: 7 / span 1;
  }

  .t9 {
    grid-column: 2 / span 1;
    grid-row: 7 / span 1;
  }

  .t10 {
    grid-column: 3 / span 2;
    grid-row: 7 / span 1;
  }
}

@media (max-width: 640px) {
  .bento {
    grid-template-columns: repeat(2, 1fr);
    grid-auto-rows: 120px;
  }

  .t1 {
    grid-column: 1 / span 2;
    grid-row: 1 / span 2;
  }

  .t2 {
    grid-column: 1 / span 2;
    grid-row: auto;
  }

  .t3 {
    grid-column: 1 / span 2;
    grid-row: auto;
  }

  .t4 {
    grid-column: 1 / span 2;
    grid-row: auto;
  }

  .t5 {
    grid-column: 1 / span 2;
    grid-row: auto;
  }

  .t6 {
    grid-column: 1 / span 2;
    grid-row: auto;
  }

  .t7 {
    grid-column: 1 / span 2;
    grid-row: auto;
  }

  .t8 {
    grid-column: 1 / span 1;
    grid-row: auto;
  }

  .t9 {
    grid-column: 2 / span 1;
    grid-row: auto;
  }

  .t10 {
    grid-column: 1 / span 2;
    grid-row: auto;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .card,
  .t2 .chain,
  .t4 .bar,
  .t5 .dial:before,
  .t8 .ring,
  .t6 .glow {
    animation: none !important;
  }

  .card {
    transition: none;
  }
}