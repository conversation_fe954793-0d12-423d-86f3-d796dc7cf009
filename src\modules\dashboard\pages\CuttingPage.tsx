import React, { useState, useRef } from "react";
import { DateRange } from "react-day-picker";
import { CuttingsChart } from "@/modules/dashboard/components/charts/CuttingsChart";
import { ReturnsChart } from "@/modules/dashboard/components/charts/QMeldungenChart";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { LagerCutsChart } from "@/modules/dashboard/components/charts/LagerCutsChart";
import { SchnitteDataChart } from "@/modules/dashboard/components/charts/SchnitteDataChart";
import { MaschinenEfficiencyChart } from "@/modules/dashboard/components/charts/MaschinenEfficiencyChart";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import cutterIcon from "@/assets/iconCutter.png";
import { AskJaszButton } from "@/modules/ai/components";
import { createPageContext } from "@/contexts/AskJaszContext";
import { ExportService } from "@/services/export.service";
import { collectCuttingChartData } from "@/services/chart-data-collector.service";
import { Button } from "@/components/ui/button";
import { FileSpreadsheet, FileText, Image } from "lucide-react";
import { toast } from "sonner";

/**
 * Cutting-Bereich Dashboard
 *
 * Zeigt verschiedene Kennzahlen für den Cutting-Bereich an:
 * - Cuttings nach Maschinen als Balkendiagramm
 * - Retouren als Kreisdiagramm
 * - Lagerbestand Schnitte als Balkendiagramm
 */
export default function CuttingPage() {
  // Zustand für den ausgewählten Datumsbereich - Standard: letzten 30 Tage (!NICHT ÄNDERN!)
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 4, 1), // 1. Juni 2025
    to: new Date(2025, 4, 30), // 30. Juni 2025 (alle verfügbaren Daten)
  });

  // Zustand für Export-Loading
  const [isExporting, setIsExporting] = useState<{ excel: boolean; powerpoint: boolean }>({
    excel: false,
    powerpoint: false
  });

  // Refs für Chart-Komponenten (für Screenshot-Export)
  const cuttingsChartRef = useRef<HTMLDivElement>(null);
  const returnsChartRef = useRef<HTMLDivElement>(null);
  const lagerCutsChartRef = useRef<HTMLDivElement>(null);
  const schnitteDataChartRef = useRef<HTMLDivElement>(null);

  // Export-Funktionen
  const handleExcelExport = async () => {
    if (!dateRange?.from || !dateRange?.to) {
      toast.error('Bitte wählen Sie einen Datumsbereich aus');
      return;
    }

    setIsExporting(prev => ({ ...prev, excel: true }));

    try {
      const chartData = await collectCuttingChartData(dateRange);

      if (chartData.length === 0) {
        toast.error('Keine Daten für den Export verfügbar');
        return;
      }

      await ExportService.exportToExcel(chartData, {
        dateRange,
        includeSummary: true
      });

      toast.success('Excel-Datei wurde erfolgreich erstellt');
    } catch (error) {
      console.error('Excel-Export Fehler:', error);
      toast.error('Fehler beim Erstellen der Excel-Datei');
    } finally {
      setIsExporting(prev => ({ ...prev, excel: false }));
    }
  };

  const handlePowerPointExport = async () => {
    if (!dateRange?.from || !dateRange?.to) {
      toast.error('Bitte wählen Sie einen Datumsbereich aus');
      return;
    }

    setIsExporting(prev => ({ ...prev, powerpoint: true }));

    try {
      const chartData = await collectCuttingChartData(dateRange);

      if (chartData.length === 0) {
        toast.error('Keine Daten für den Export verfügbar');
        return;
      }

      console.log('Starte PowerPoint Export mit', chartData.length, 'Charts...');
      await ExportService.exportToPowerPoint(chartData, {
        dateRange,
        includeSummary: true
      });

      toast.success('PowerPoint-Datei wurde erfolgreich erstellt');
    } catch (error) {
      console.error('PowerPoint-Export Fehler:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler';
      toast.error(`Fehler beim Erstellen der PowerPoint-Datei: ${errorMessage}`);
    } finally {
      setIsExporting(prev => ({ ...prev, powerpoint: false }));
    }
  };

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift, Export-Buttons und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <img
            src={cutterIcon}
            alt="Ablängerei"
            className="h-10 w-10 mr-2 object-contain"
          />
          <h1 className="text-4xl font-bold text-black">ABLÄNGEREI</h1>
          <div className="-mt-7">
            <AskJaszButton
              context={createPageContext(
                "Ablängerei Dashboard",
                [
                  "Cutting-Bereich Performance Übersicht",
                  "Cuttings nach Maschinen und Zeitraum",
                  "Retouren und Q-Meldungen Analyse",
                  "Lagerbestand Schnitte und Effizienz",
                  "Maschinen-Effizienz Monitoring"
                ],
                "Ablängerei Bereich Dashboard"
              )}
              position="inline"
              size="md"
              variant="default"
              tooltip="Hilfe zum Ablängerei Dashboard erhalten"
            />
          </div>
        </div>

        {/* Export-Buttons und Date Range Picker */}
        <div className="flex items-center gap-4">
          <div className="flex gap-2">
            <Button
              onClick={handleExcelExport}
              disabled={isExporting.excel}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <img src="/src/assets/excel-pn.png" alt="Excel Export" className="h-8 w-8" />
            </Button>
            <Button
              onClick={handlePowerPointExport}
              disabled={isExporting.powerpoint}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <img src="/src/assets/pp.png" alt="PowerPoint Export" className="h-8 w-8" />
            </Button>
          </div>
          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
            label="Datumsauswahl"
          />
        </div>
      </div>

      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-6">
          <div className="xl:col-span-2">
            <ChartErrorBoundary>
              <div ref={lagerCutsChartRef}>
                <LagerCutsChart dateRange={dateRange} />
              </div>
            </ChartErrorBoundary>
          </div>

          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <div ref={returnsChartRef}>
                <ReturnsChart dateRange={dateRange} />
              </div>
            </ChartErrorBoundary>
          </div>
        </div>
      </div>

      {/* Secondary Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <div ref={cuttingsChartRef}>
                <CuttingsChart dateRange={dateRange} />
              </div>
            </ChartErrorBoundary>
          </div>
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <div ref={schnitteDataChartRef}>
                <SchnitteDataChart dateRange={dateRange} />
              </div>
            </ChartErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
}