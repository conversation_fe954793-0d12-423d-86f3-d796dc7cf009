/**
 * DocumentList - RAG Document Management Component
 * 
 * Displays uploaded documents with delete functionality
 */

import React, { useState, useEffect } from 'react';
import { FileText, Trash2, Calendar, Tag, AlertCircle, Loader2, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface Document {
  id: string;
  title: string;
  category: string;
  description?: string;
  language: string;
  filename: string;
  fileSize: number;
  chunksCount: number;
  embeddingsCount: number;
  createdAt: string;
  updatedAt: string;
}

interface DocumentListProps {
  onDocumentDeleted?: () => void;
  refreshTrigger?: number;
  className?: string;
}

const CATEGORY_LABELS: Record<string, string> = {
  'dispatch': 'Versand',
  'cutting': 'Ablängerei',
  'incoming-goods': 'Wareneingang',
  'system': 'System',
  'quality': 'Qualität',
  'maintenance': 'Wartung',
  'app': 'App Leitfaden',
  'procedures': 'Verfahren',
  'kpi': 'KPI & Metriken'
};

const CATEGORY_COLORS: Record<string, string> = {
  'dispatch': 'bg-blue-100 text-blue-800',
  'cutting': 'bg-green-100 text-green-800',
  'incoming-goods': 'bg-purple-100 text-purple-800',
  'system': 'bg-gray-100 text-gray-800',
  'quality': 'bg-yellow-100 text-yellow-800',
  'maintenance': 'bg-red-100 text-red-800',
  'app': 'bg-indigo-100 text-indigo-800',
  'procedures': 'bg-orange-100 text-orange-800',
  'kpi': 'bg-pink-100 text-pink-800'
};

export const DocumentList: React.FC<DocumentListProps> = ({
  onDocumentDeleted,
  refreshTrigger,
  className = ''
}) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Utility function for API calls with fallback
  const fetchWithFallback = async (endpoint: string, options?: RequestInit) => {
    // Use correct API base URL for development
    const API_BASE_URL = import.meta.env.DEV
      ? 'http://localhost:3001'
      : '';

    const baseUrl = endpoint.startsWith('http') ? '' : API_BASE_URL;
    const fullUrl = `${baseUrl}${endpoint}`;

    console.log(`[DocumentList Debug] Attempting to fetch: ${fullUrl}`);

    try {
      const response = await fetch(fullUrl, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
      });

      console.log(`[DocumentList Debug] Response status: ${response.status} for ${fullUrl}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[DocumentList Debug] Error response:`, errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return response;

    } catch (error) {
      console.error(`[DocumentList Debug] Fetch failed for ${fullUrl}:`, error);
      throw error;
    }
  };

  // Load documents
  const loadDocuments = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('[DocumentList Debug] Loading documents...');
      const response = await fetchWithFallback('/api/rag/documents', {
        headers: {
          ...(localStorage.getItem('token') && {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          })
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('[DocumentList Debug] Documents loaded successfully:', data);
        setDocuments(data.data || []);
      } else {
        const errorText = await response.text();
        console.error('[DocumentList Debug] Failed to load documents:', response.status, errorText);
        setError(`Fehler beim Laden der Dokumente (${response.status}): ${errorText}`);
      }
    } catch (error) {
      console.error('[DocumentList Debug] Error loading documents:', error);
      setError(`Netzwerkfehler beim Laden der Dokumente: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Load documents on mount and when refresh is triggered
  useEffect(() => {
    loadDocuments();
  }, [refreshTrigger]);

  // Delete document
  const handleDeleteDocument = async (documentId: string) => {
    setDeletingId(documentId);

    try {
      console.log(`[DocumentList Debug] Deleting document: ${documentId}`);
      const response = await fetchWithFallback(`/api/rag/documents/${documentId}`, {
        method: 'DELETE',
        headers: {
          ...(localStorage.getItem('token') && {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          })
        }
      });

      if (response.ok) {
        console.log(`[DocumentList Debug] Document deleted successfully: ${documentId}`);
        // Remove document from local state
        setDocuments(prev => prev.filter(doc => doc.id !== documentId));
        onDocumentDeleted?.();
      } else {
        const errorText = await response.text();
        console.error(`[DocumentList Debug] Failed to delete document:`, response.status, errorText);
        setError(`Fehler beim Löschen des Dokuments (${response.status}): ${errorText}`);
      }
    } catch (error) {
      console.error('[DocumentList Debug] Error deleting document:', error);
      setError(`Netzwerkfehler beim Löschen des Dokuments: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`);
    } finally {
      setDeletingId(null);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString('de-DE');
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Dokumente werden geladen...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`w-full ${className} border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200`} >
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 py-2">
              <FileText className="h-5 w-5" />
              Hochgeladene Dokumente
            </CardTitle>
            <CardDescription className="text-gray-600">
              {documents.length} {documents.length === 1 ? 'Dokument' : 'Dokumente'} in der Wissensdatenbank
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={loadDocuments}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Aktualisieren
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {error && (
          <Alert className="mb-4 border-red-500">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {documents.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Keine Dokumente vorhanden
            </h3>
            <p className="text-gray-500">
              Laden Sie Ihr erstes Dokument hoch, um zu beginnen.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {documents.map((document) => (
              <Card key={document.id} className="border border-gray-200 bg-white">
                <CardContent className="px-4 py-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-lg font-medium text-gray-900 truncate">
                          {document.title}
                        </h3>
                        <Badge 
                          className={CATEGORY_COLORS[document.category] || 'bg-gray-100 text-gray-800'}
                        >
                          {CATEGORY_LABELS[document.category] || document.category}
                        </Badge>
                        {document.language === 'en' && (
                          <Badge variant="outline">EN</Badge>
                        )}
                      </div>

                      {document.description && (
                        <p className="text-sm text-gray-600 mb-3">
                          {document.description}
                        </p>
                      )}

                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <FileText className="h-4 w-4" />
                          {document.filename}
                        </span>
                        <span>{formatFileSize(document.fileSize)}</span>
                        <span>{document.chunksCount} Abschnitte</span>
                        <span>{document.embeddingsCount} Embeddings</span>
                      </div>

                      <div className="flex items-center gap-1 mt-2 text-xs text-gray-400">
                        <Calendar className="h-3 w-3" />
                        Hochgeladen: {formatDate(document.createdAt)}
                      </div>
                    </div>

                    <div className="ml-4 flex-shrink-0">
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={deletingId === document.id}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            {deletingId === document.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Dokument löschen</AlertDialogTitle>
                            <AlertDialogDescription>
                              Sind Sie sicher, dass Sie das Dokument "{document.title}" löschen möchten?
                              Diese Aktion kann nicht rückgängig gemacht werden. Alle zugehörigen 
                              Textabschnitte und Embeddings werden ebenfalls gelöscht.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteDocument(document.id)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Löschen
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DocumentList;