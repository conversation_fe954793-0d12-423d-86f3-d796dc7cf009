"use strict";
/**
 * Drizzle ORM Pagination Service
 *
 * Implementiert cursor-basierte und offset-basierte Paginierung
 * für große Datasets mit Performance-Optimierungen.
 * Vollständig kompatibel mit Drizzle ORM.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.drizzleRepositoryPaginationHelper = exports.drizzlePaginationService = exports.DrizzleRepositoryPaginationHelper = exports.DrizzlePaginationService = exports.SortDirection = exports.PaginationStrategy = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../db");
/**
 * Pagination-Strategien
 */
var PaginationStrategy;
(function (PaginationStrategy) {
    PaginationStrategy["CURSOR"] = "cursor";
    PaginationStrategy["OFFSET"] = "offset";
})(PaginationStrategy || (exports.PaginationStrategy = PaginationStrategy = {}));
/**
 * Sortierungs-Richtungen
 */
var SortDirection;
(function (SortDirection) {
    SortDirection["ASC"] = "asc";
    SortDirection["DESC"] = "desc";
})(SortDirection || (exports.SortDirection = SortDirection = {}));
/**
 * Drizzle ORM Pagination Service
 */
class DrizzlePaginationService {
    constructor() {
        this.DEFAULT_LIMIT = 50;
        this.MAX_LIMIT = 1000;
        this.DEFAULT_ORDER_BY = 'id';
    }
    static getInstance() {
        if (!DrizzlePaginationService.instance) {
            DrizzlePaginationService.instance = new DrizzlePaginationService();
        }
        return DrizzlePaginationService.instance;
    }
    /**
     * Cursor-basierte Paginierung für Drizzle ORM implementieren
     */
    async applyCursorPagination(baseQuery, table, params, defaultOrderBy = this.DEFAULT_ORDER_BY) {
        const orderBy = params.orderBy || defaultOrderBy;
        const orderDirection = params.orderDirection || SortDirection.ASC;
        // Validiere und normalisiere Parameter
        const first = this.validateLimit(params.first);
        const last = this.validateLimit(params.last);
        if (first && last) {
            throw new Error('Cannot specify both "first" and "last" parameters');
        }
        if (params.after && params.before) {
            throw new Error('Cannot specify both "after" and "before" cursors');
        }
        let limit = first || last || this.DEFAULT_LIMIT;
        const isReversed = !!last;
        // Query-Builder vorbereiten
        let query = baseQuery;
        // Cursor-Bedingungen hinzufügen
        if (params.after) {
            const cursor = this.decodeCursor(params.after);
            const condition = this.buildDrizzleCursorCondition(table, cursor, false, orderDirection);
            query = query.where(condition);
        }
        else if (params.before) {
            const cursor = this.decodeCursor(params.before);
            const condition = this.buildDrizzleCursorCondition(table, cursor, true, orderDirection);
            query = query.where(condition);
        }
        // Sortierung hinzufügen
        const orderColumn = table[orderBy];
        const idColumn = table.id;
        if (isReversed) {
            const reversedDirection = this.reverseDirection(orderDirection);
            if (orderBy !== 'id') {
                query = query.orderBy(reversedDirection === SortDirection.ASC ? (0, drizzle_orm_1.asc)(orderColumn) : (0, drizzle_orm_1.desc)(orderColumn), reversedDirection === SortDirection.ASC ? (0, drizzle_orm_1.asc)(idColumn) : (0, drizzle_orm_1.desc)(idColumn));
            }
            else {
                query = query.orderBy(reversedDirection === SortDirection.ASC ? (0, drizzle_orm_1.asc)(idColumn) : (0, drizzle_orm_1.desc)(idColumn));
            }
        }
        else {
            if (orderBy !== 'id') {
                query = query.orderBy(orderDirection === SortDirection.ASC ? (0, drizzle_orm_1.asc)(orderColumn) : (0, drizzle_orm_1.desc)(orderColumn), orderDirection === SortDirection.ASC ? (0, drizzle_orm_1.asc)(idColumn) : (0, drizzle_orm_1.desc)(idColumn));
            }
            else {
                query = query.orderBy(orderDirection === SortDirection.ASC ? (0, drizzle_orm_1.asc)(idColumn) : (0, drizzle_orm_1.desc)(idColumn));
            }
        }
        // Limit hinzufügen (+1 um hasNextPage zu bestimmen)
        query = query.limit(limit + 1);
        // Query ausführen
        const results = await query;
        // Prüfe ob mehr Daten verfügbar sind
        const hasMore = results.length > limit;
        if (hasMore) {
            results.pop(); // Entferne den extra Datensatz
        }
        // Bei Rückwärts-Paginierung die Reihenfolge umkehren
        if (isReversed) {
            results.reverse();
        }
        // Edges und Cursors erstellen
        const edges = results.map((node) => ({
            cursor: this.encodeCursor(orderBy, node[orderBy], node.id, orderDirection),
            node
        }));
        // PageInfo erstellen
        const pageInfo = {
            hasNextPage: isReversed ? false : hasMore,
            hasPreviousPage: isReversed ? hasMore : false,
            startCursor: edges.length > 0 ? edges[0].cursor : undefined,
            endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : undefined
        };
        // Bei Bedarf auch Rückwärts-Navigation prüfen
        if (!isReversed && params.after) {
            const cursor = this.decodeCursor(params.after);
            const previousCondition = this.buildDrizzleCursorCondition(table, cursor, true, orderDirection);
            const previousQuery = baseQuery.where(previousCondition).limit(1);
            const previousExists = await previousQuery;
            pageInfo.hasPreviousPage = previousExists.length > 0;
        }
        return {
            edges,
            pageInfo
        };
    }
    /**
     * Offset-basierte Paginierung für Drizzle ORM implementieren
     */
    async applyOffsetPagination(baseQuery, table, params, defaultOrderBy = this.DEFAULT_ORDER_BY) {
        var _a;
        const orderBy = params.orderBy || defaultOrderBy;
        const orderDirection = params.orderDirection || SortDirection.ASC;
        const limit = this.validateLimit(params.limit);
        // Berechne Offset
        let offset = 0;
        if (params.offset !== undefined) {
            offset = Math.max(0, params.offset);
        }
        else if (params.page !== undefined) {
            const page = Math.max(1, params.page);
            offset = (page - 1) * limit;
        }
        // Query für Daten
        let dataQuery = baseQuery;
        // Sortierung hinzufügen
        const orderColumn = table[orderBy];
        const idColumn = table.id;
        if (orderBy !== 'id') {
            dataQuery = dataQuery.orderBy(orderDirection === SortDirection.ASC ? (0, drizzle_orm_1.asc)(orderColumn) : (0, drizzle_orm_1.desc)(orderColumn), orderDirection === SortDirection.ASC ? (0, drizzle_orm_1.asc)(idColumn) : (0, drizzle_orm_1.desc)(idColumn));
        }
        else {
            dataQuery = dataQuery.orderBy(orderDirection === SortDirection.ASC ? (0, drizzle_orm_1.asc)(idColumn) : (0, drizzle_orm_1.desc)(idColumn));
        }
        // Limit und Offset hinzufügen
        dataQuery = dataQuery.limit(limit).offset(offset);
        // Query für Gesamtanzahl
        const countQuery = db_1.db.select({ count: (0, drizzle_orm_1.count)() }).from(table);
        // Beide Queries parallel ausführen
        const [data, countResult] = await Promise.all([
            dataQuery,
            countQuery
        ]);
        const totalCount = ((_a = countResult[0]) === null || _a === void 0 ? void 0 : _a.count) || 0;
        const totalPages = Math.ceil(totalCount / limit);
        const currentPage = Math.floor(offset / limit) + 1;
        const meta = {
            currentPage,
            totalPages,
            totalCount,
            hasNextPage: currentPage < totalPages,
            hasPreviousPage: currentPage > 1,
            limit,
            offset
        };
        return {
            data,
            meta
        };
    }
    /**
     * Automatische Pagination basierend auf Parametern
     */
    async applyPagination(baseQuery, table, params, defaultOrderBy = this.DEFAULT_ORDER_BY) {
        if (params.strategy === PaginationStrategy.CURSOR && params.cursor) {
            return this.applyCursorPagination(baseQuery, table, params.cursor, defaultOrderBy);
        }
        else if (params.strategy === PaginationStrategy.OFFSET && params.offset) {
            return this.applyOffsetPagination(baseQuery, table, params.offset, defaultOrderBy);
        }
        else {
            throw new Error('Invalid pagination parameters');
        }
    }
    /**
     * Cursor kodieren
     */
    encodeCursor(field, value, id, direction) {
        const cursorData = {
            field,
            value,
            id,
            direction
        };
        return Buffer.from(JSON.stringify(cursorData)).toString('base64');
    }
    /**
     * Cursor dekodieren
     */
    decodeCursor(cursor) {
        try {
            const decoded = Buffer.from(cursor, 'base64').toString('utf-8');
            return JSON.parse(decoded);
        }
        catch (error) {
            throw new Error('Invalid cursor format');
        }
    }
    /**
     * Drizzle-spezifische Cursor-Bedingung erstellen
     */
    buildDrizzleCursorCondition(table, cursor, isBefore, orderDirection) {
        const { field, value, id } = cursor;
        // Bestimme Vergleichsoperator basierend auf Richtung
        const isAscending = orderDirection === SortDirection.ASC;
        const useGreaterThan = isBefore ? !isAscending : isAscending;
        const fieldColumn = table[field];
        const idColumn = table.id;
        if (field === 'id') {
            return useGreaterThan ? (0, drizzle_orm_1.gt)(idColumn, id) : (0, drizzle_orm_1.lt)(idColumn, id);
        }
        // Für Nicht-ID-Felder: Kombiniere Feld-Wert und ID für eindeutige Sortierung
        const fieldCondition = useGreaterThan ? (0, drizzle_orm_1.gt)(fieldColumn, value) : (0, drizzle_orm_1.lt)(fieldColumn, value);
        const idCondition = (0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(fieldColumn, value), useGreaterThan ? (0, drizzle_orm_1.gt)(idColumn, id) : (0, drizzle_orm_1.lt)(idColumn, id));
        return (0, drizzle_orm_1.or)(fieldCondition, idCondition);
    }
    /**
     * Hilfsfunktionen
     */
    validateLimit(limit) {
        if (limit === undefined) {
            return this.DEFAULT_LIMIT;
        }
        if (limit < 1) {
            throw new Error('Limit must be positive');
        }
        if (limit > this.MAX_LIMIT) {
            throw new Error(`Limit cannot exceed ${this.MAX_LIMIT}`);
        }
        return limit;
    }
    reverseDirection(direction) {
        return direction === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC;
    }
    /**
     * Utility-Funktionen für API-Handler
     */
    /**
     * Parse Cursor-Parameter aus Request-Query
     */
    parseCursorParams(query) {
        return {
            first: query.first ? parseInt(query.first) : undefined,
            after: query.after || undefined,
            last: query.last ? parseInt(query.last) : undefined,
            before: query.before || undefined,
            orderBy: query.orderBy || undefined,
            orderDirection: query.orderDirection || undefined
        };
    }
    /**
     * Parse Offset-Parameter aus Request-Query
     */
    parseOffsetParams(query) {
        return {
            page: query.page ? parseInt(query.page) : undefined,
            limit: query.limit ? parseInt(query.limit) : undefined,
            offset: query.offset ? parseInt(query.offset) : undefined,
            orderBy: query.orderBy || undefined,
            orderDirection: query.orderDirection || undefined
        };
    }
    /**
     * Automatische Strategie-Erkennung
     */
    detectPaginationStrategy(query) {
        const hasCursorParams = query.first || query.after || query.last || query.before;
        const hasOffsetParams = query.page || query.limit || query.offset;
        if (hasCursorParams && !hasOffsetParams) {
            return PaginationStrategy.CURSOR;
        }
        else if (hasOffsetParams && !hasCursorParams) {
            return PaginationStrategy.OFFSET;
        }
        else if (hasCursorParams && hasOffsetParams) {
            // Bei Konflikt bevorzuge Cursor-Pagination
            return PaginationStrategy.CURSOR;
        }
        else {
            // Default zu Offset-Pagination
            return PaginationStrategy.OFFSET;
        }
    }
    /**
     * Parse alle Request-Parameter
     */
    parseRequestParams(query) {
        const strategy = this.detectPaginationStrategy(query);
        return {
            strategy,
            cursor: strategy === PaginationStrategy.CURSOR ? this.parseCursorParams(query) : undefined,
            offset: strategy === PaginationStrategy.OFFSET ? this.parseOffsetParams(query) : undefined
        };
    }
    /**
     * Performance-Empfehlungen basierend auf Datenvolumen
     */
    getPerformanceRecommendations(totalCount, currentLimit) {
        const recommendations = [];
        if (totalCount > 10000 && currentLimit > 100) {
            recommendations.push('Consider using cursor-based pagination for large datasets');
        }
        if (currentLimit > 500) {
            recommendations.push('Large page sizes may impact performance');
        }
        if (totalCount > 100000) {
            recommendations.push('Consider implementing search/filtering to reduce dataset size');
        }
        return recommendations;
    }
}
exports.DrizzlePaginationService = DrizzlePaginationService;
/**
 * Drizzle Repository Pagination Helper
 */
class DrizzleRepositoryPaginationHelper {
    constructor() {
        this.paginationService = DrizzlePaginationService.getInstance();
    }
    /**
     * Erweitere Repository findAll-Methode mit Drizzle Pagination
     */
    async paginatedFindAll(table, baseWhere, paginationParams, defaultOrderBy = 'id') {
        let baseQuery = db_1.db.select().from(table);
        if (baseWhere) {
            baseQuery = baseQuery.where(baseWhere);
        }
        const params = paginationParams || {
            strategy: PaginationStrategy.OFFSET,
            offset: { limit: 50 }
        };
        return this.paginationService.applyPagination(baseQuery, table, params, defaultOrderBy);
    }
    /**
     * Cache-Key für paginierte Queries
     */
    generatePaginationCacheKey(baseKey, paginationParams) {
        const strategy = paginationParams.strategy;
        let paramString = '';
        if (strategy === PaginationStrategy.CURSOR && paginationParams.cursor) {
            const { first, after, last, before, orderBy, orderDirection } = paginationParams.cursor;
            paramString = `cursor:${first || ''}:${after || ''}:${last || ''}:${before || ''}:${orderBy || ''}:${orderDirection || ''}`;
        }
        else if (strategy === PaginationStrategy.OFFSET && paginationParams.offset) {
            const { page, limit, offset, orderBy, orderDirection } = paginationParams.offset;
            paramString = `offset:${page || ''}:${limit || ''}:${offset || ''}:${orderBy || ''}:${orderDirection || ''}`;
        }
        return `${baseKey}:paginated:${paramString}`;
    }
}
exports.DrizzleRepositoryPaginationHelper = DrizzleRepositoryPaginationHelper;
// Singleton instances für Drizzle ORM
exports.drizzlePaginationService = DrizzlePaginationService.getInstance();
exports.drizzleRepositoryPaginationHelper = new DrizzleRepositoryPaginationHelper();
