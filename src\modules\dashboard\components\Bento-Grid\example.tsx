import React from "react";
import { BentoGrid } from "./index";

// Beispiel für die Verwendung der BentoGrid-Komponente
export const BentoGridExample: React.FC = () => {
  return (
    <div style={{ 
      fontFamily: 'Inter, system-ui, -apple-system, Segoe UI, Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji","Segoe UI Emoji"',
      color: '#e9eef6',
      background: `
        radial-gradient(1200px 600px at 50% 45%, rgba(56,64,82,.18), transparent 60%),
        radial-gradient(500px 500px at 95% 95%, rgba(34,211,238,.08), transparent 60%),
        radial-gradient(500px 500px at 5% 5%, rgba(212,91,255,.08), transparent 60%),
        #0b0e13
      `,
      minHeight: '100vh',
      overflowX: 'hidden'
    }}>
      <BentoGrid />
    </div>
  );
};

export default BentoGridExample;