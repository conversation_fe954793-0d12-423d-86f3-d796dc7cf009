import React from 'react';
import { Truck } from 'lucide-react';
import { BaseKPICardProps } from './types';

/**
 * KPI-Karte für Lieferleistung
 * 
 * Zeigt die Lieferleistung aus den Dispatch-Daten an.
 * Verwendet indigoblaues Farbschema mit Truck-Icon aus Lucide.
 */
export const DeliveryCard: React.FC<BaseKPICardProps> = ({ 
  departmentKPIs, 
  className = '' 
}) => {
  return (
    <>
      <div className="flex items-center mb-2">
        <div className="p-2 bg-indigo-100 rounded-lg mr-3">
          <Truck className="h-6 w-6 text-indigo-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-800">Lieferleistung</h3>
      </div>
      
      <div className="text-2xl font-bold text-gray-900">
        {departmentKPIs?.dispatch.deliveryPerformance.toFixed(1) || '0.0'}
        <span className="text-sm font-normal text-gray-500 ml-1">%</span>
      </div>
      
      <div className="text-sm text-gray-600 mt-1">
        Ziel: 97.0%
      </div>
    </>
  );
};

export default DeliveryCard;