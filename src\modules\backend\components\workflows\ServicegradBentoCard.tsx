import React, { useState, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import {
  Database,
  FileSpreadsheet,
  Mail,
  Clock,
  CheckCircle,
  AlertTriangle,
  Play,
  Settings,
  Calendar,
  Save,
  X
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { workflowsClient } from '@/helpers/ipc/workflows.ipc';
import { PowerSwitch } from '@/components/ui/power-switch';
import { workflowService } from '@/services/workflowService';
import { SAPWorkflowProcess, WorkflowConfig } from '@/types/workflow';
// Drawer-Komponenten gemäß Dokumentation
import {
  Drawer,
  DrawerOverlay,
  DrawerContent,
  DrawerHeader,
  DrawerFooter,
  DrawerTitle,
  DrawerDescription,
} from '@/components/ui/drawer';
// Inhalt, der vorher im Popup war
import { ServicegradWorkflowCard } from './ServicegradWorkflowCard';

interface ServicegradBentoCardProps {
  process: SAPWorkflowProcess;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  onExecute: (processId: string) => Promise<void>;
  isExecuting: boolean;
  initialTab?: 'overview' | 'settings';
}
interface PowerSwitchProps {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
  id?: string;
}
// Default-Konfiguration für Servicegrad - static to prevent re-renders
const defaultConfig: WorkflowConfig = {
  id: 'servicegrad',
  name: 'Servicegrad Automatisierung',
  description: 'SAP Servicegrad Export mit automatischer Excel-Verarbeitung',
  // PostgreSQL wird über Backend-Konfiguration verwendet - keine lokalen DB-Pfade mehr nötig
  databasePath: '', // Entfernt - PostgreSQL wird verwendet
  exportPath: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\SG',
  emailRecipients: ['<EMAIL>'],
  // SAP-Konfiguration
  sapExecutablePath: 'C:\\Program Files (x86)\\SAP\\FrontEnd\\SapGui\\sapshcut.exe',
  sapSystemId: 'PS4',
  sapClient: '009',
  sapLanguage: 'DE',
  dbPath: '', // Entfernt - PostgreSQL wird verwendet
  schedule: {
    enabled: false,
    frequency: 'daily',
    time: '08:00',
    dayOfWeek: 1,
    interval: 1
  },
  isActive: true,
  lastModified: new Date('2025-01-01') // Static date to prevent re-renders
};

export function ServicegradBentoCard({
  process,
  className,
  size = 'large',
  onExecute,
  isExecuting,
  initialTab = 'overview'
}: ServicegradBentoCardProps) {
  const [config, setConfig] = useState<WorkflowConfig>(defaultConfig);
  const [showSettings, setShowSettings] = useState(initialTab === 'settings');
  const [showConfigDialog, setShowConfigDialog] = useState(false);
  const [executionProgress, setExecutionProgress] = useState(0);
  const [tempConfig, setTempConfig] = useState<WorkflowConfig>(defaultConfig);

  const loadConfig = useCallback(async () => {
    try {
      const loadedConfig = await workflowService.getWorkflowConfig(process.id);
      if (loadedConfig) {
        setConfig({ ...defaultConfig, ...loadedConfig });
      }
    } catch (error) {
      console.error('Fehler beim Laden der Workflow-Konfiguration:', error);
    }
  }, [process.id]);

  const saveConfig = async () => {
    try {
      await workflowService.updateWorkflowConfig(process.id, tempConfig);
      setConfig(tempConfig);
      setShowSettings(false);
      console.log('Workflow-Konfiguration gespeichert:', tempConfig);
    } catch (error) {
      console.error('Fehler beim Speichern der Workflow-Konfiguration:', error);
    }
  };

  const handleConfigDialogClose = useCallback(() => {
    setShowConfigDialog(false);
    // Reload config after dialog closes to get updated values
    loadConfig();
  }, [loadConfig]);

  const handlePowerSwitchChange = useCallback((checked: boolean) => {
    const newConfig = { ...config, isActive: checked };
    setConfig(newConfig);
    // Asynchron speichern ohne await
    workflowService.updateWorkflowConfig(process.id, newConfig).catch(error => {
      console.error('Fehler beim Speichern der Workflow-Aktivierung:', error);
      // Bei Fehler zurücksetzen
      setConfig(prev => ({ ...prev, isActive: !checked }));
    });
  }, [config, process.id]);

  useEffect(() => {
    loadConfig();
  }, [loadConfig]);

  const getStatusIcon = () => {
    switch (process.status) {
      case 'running':
        return <Clock className="h-5 w-5 text-blue-600 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Database className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (process.status) {
      case 'running':
        return 'text-blue-600';
      case 'completed':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusText = () => {
    switch (process.status) {
      case 'running':
        return 'Läuft';
      case 'completed':
        return 'Abgeschlossen';
      case 'error':
        return 'Fehler';
      default:
        return 'Bereit';
    }
  };

  const handleExecute = async () => {
    // Prüfen ob der Workflow aktiv ist
    if (!config.isActive) {
      console.warn('Workflow kann nicht gestartet werden - PowerSwitch ist ausgeschaltet');
      return;
    }

    setExecutionProgress(0);

    const progressInterval = setInterval(() => {
      setExecutionProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + Math.random() * 10;
      });
    }, 2000);

    try {
      await onExecute(process.id);
      setExecutionProgress(100);
    } catch (error) {
      console.error('Execution failed:', error);
    } finally {
      clearInterval(progressInterval);
      setTimeout(() => setExecutionProgress(0), 2000);
    }
  };



  const variants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: 'spring' as const, damping: 25 },
    },
  };

  return (
    <motion.div
      variants={variants}
      className={cn(
        'group border-blue-300/60 bg-white hover:border-blue-400/80 relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border-2 px-6 pt-6 pb-6 shadow-lg transition-all duration-500',
        className,
      )}
    >
      {/* Background Pattern */}
      <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3b82f660_1px,transparent_1px),linear-gradient(to_bottom,#3b82f660_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>

      {/* Large Background Icon */}
      <div className="text-blue-200/50 group-hover:text-blue-300/70 absolute right-1 bottom-3 scale-[4] transition-all duration-700 group-hover:scale-[4.2]">
        <Database className="h-8 w-8" />
      </div>

      <div className="relative z-10 flex h-full flex-col justify-between">
        {/* Header */}
        <div>
          <div className="flex items-start justify-between mb-2">
            <div className="flex flex-col items-center gap-1">
              <div className="bg-blue-100/70 text-blue-600 shadow-blue-200/50 group-hover:bg-blue-200/80 group-hover:shadow-blue-300/60 flex h-10 w-10 items-center justify-center rounded-full shadow-lg transition-all duration-500">
                {getStatusIcon()}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {config.schedule.enabled && (
                <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                  <Calendar className="h-3 w-3 mr-1" />
                  Geplant
                </Badge>
              )}
              <Badge
                className={cn(
                  "text-xs font-medium shadow-sm border-0",
                  process.status === 'running' && "bg-blue-500 text-white",
                  process.status === 'completed' && "bg-green-500 text-white",
                  process.status === 'error' && "bg-red-500 text-white",
                  process.status === 'idle' && "bg-gray-500 text-white"
                )}
              >
                {getStatusText()}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowConfigDialog(true)}
                className="h-8 w-8 p-0 border-gray-300 text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 shadow-sm"
              >
                <Settings className="h-3 w-3" />
              </Button>
            </div>
          </div>

          <h3 className="mb-1 text-lg font-semibold tracking-tight">{process.name}</h3>
          <p className="text-muted-foreground text-sm mb-2">{process.description}</p>
          {/* Workflow Steps */}
          {size === 'large' && (
            <div className="space-y-1 mb-3">
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <Database className="h-3 w-3 text-blue-500" />
                <span>SAP-Export ({process.tcode})</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <FileSpreadsheet className="h-3 w-3 text-green-500" />
                <span>Excel-Verarbeitung</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <Mail className="h-3 w-3 text-orange-500" />
                <span>E-Mail-Versand</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <Database className="h-3 w-3 text-purple-500" />
                <span>Datenbank-Speicherung</span>
              </div>
            </div>
          )}

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-3 mb-3 text-xs">
            <div>
              <div className="text-muted-foreground">Ziel-Tabelle</div>
              <div className="font-mono text-blue-600">{process.dbTable}</div>
            </div>
            {process.lastRun && (
              <div>
                <div className="text-muted-foreground">Letzter Lauf</div>
                <div className="text-green-600">{process.lastRun.toLocaleDateString('de-DE')}</div>
              </div>
            )}
            {config.schedule.enabled && (
              <div className="col-span-2">
                <div className="text-muted-foreground">Zeitplan</div>
                <div className="text-green-600 text-xs">
                  {config.schedule.frequency === 'hourly' && `Alle ${config.schedule.interval}h`}
                  {config.schedule.frequency === 'daily' && `Täglich um ${config.schedule.time}`}
                  {config.schedule.frequency === 'weekly' && `Wöchentlich ${['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'][config.schedule.dayOfWeek || 1]} um ${config.schedule.time}`}
                </div>
              </div>
            )}
          </div>

          {/* Progress Bar (when running) */}
          {(process.status === 'running' || executionProgress > 0) && (
            <div className="mb-3">
              <div className="flex justify-between text-xs mb-1">
                <span className="text-muted-foreground">Fortschritt</span>
                <span className="text-blue-600">{Math.round(executionProgress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1.5">
                <div
                  className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                  style={{ width: `${executionProgress}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>

        {/* Action Button and Power Switch */}
        <div className="relative">
          <Button
            onClick={handleExecute}
            disabled={process.status === 'running' || isExecuting || !config.isActive}
            className={cn(
              "w-fit px-4 text-white text-sm shadow-md",
              config.isActive
                ? "bg-blue-600 hover:bg-blue-700"
                : "bg-gray-400 cursor-not-allowed"
            )}
            size="sm"
          >
            {process.status === 'running' || isExecuting ? (
              <>
                <Clock className="h-3 w-3 mr-2 animate-spin" />
                Servicegrad-Automatisierung läuft...
              </>
            ) : !config.isActive ? (
              <>
                <Play className="h-3 w-3 mr-2" />
                Workflow deaktiviert
              </>
            ) : (
              <>
                <Play className="h-3 w-3 mr-2" />
                Prozess starten
              </>
            )}
          </Button>

          <div className="absolute -bottom-2 -right-2">
            <PowerSwitch
              id="workflow-active-footer"
              checked={config.isActive}
              onChange={handlePowerSwitchChange}
              className="scale-75"
            />
          </div>
        </div>
      </div>

      {/* Bottom Gradient */}
      <div className="from-blue-400 to-blue-300 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />

      {/* Slide-Out Drawer (ersetzt den bisherigen Dialog) */}
      <Drawer
        open={showConfigDialog}
        onOpenChange={(open) => {
          if (!open) {
            handleConfigDialogClose();
          } else {
            setShowConfigDialog(true);
          }
        }}
        side="left"
      >
        <DrawerOverlay />
        <DrawerContent className="w-[520px] max-w-[95vw]">
          <DrawerHeader>
            <DrawerTitle>Workflow-Einstellungen</DrawerTitle>
            <DrawerDescription>
              Konfiguration und Planung für {process.name}
            </DrawerDescription>
          </DrawerHeader>

          <div className="p-4 overflow-auto">
            {/* Nutzung der bestehenden Konfigurations-/Info-Komponente */}
            <ServicegradWorkflowCard
              process={process}
              onExecute={onExecute}
              isExecuting={isExecuting}
              initialTab="settings"
            />
          </div>

          <DrawerFooter>
            <Button variant="accept" size="sm" onClick={saveConfig}>
              <Save className="h-4 w-4 mr-1" />
                Speichern
            </Button>
            <Button variant="outline" onClick={() => handleConfigDialogClose()}>
              Schließen
            </Button>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </motion.div>
  );
}