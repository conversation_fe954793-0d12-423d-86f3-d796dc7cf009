# AI-Modul - Umfassende Dokumentation

![AI Module](https://img.shields.io/badge/AI-Module-blue?style=for-the-badge&logo=brain&logoColor=white)
![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=flat-square&logo=typescript&logoColor=white)
![React](https://img.shields.io/badge/React-61DAFB?style=flat-square&logo=react&logoColor=black)
![Electron](https://img.shields.io/badge/Electron-191970?style=flat-square&logo=electron&logoColor=white)

## 1. Übersicht & Zweck

Das AI-Modul ist das Herzstück der intelligenten Funktionen im **JOZI1 Lapp Dashboard**. Es implementiert eine vollständige KI-Infrastruktur für die Kabelindustrie mit fortschrittlichen Optimierungsalgorithmen, Retrieval-Augmented Generation (RAG) und intelligenten Analysen.

### Hauptfunktionen

🎯 **Schnittoptimierung**: Genetische Algorithmen für optimale Kabelzuschnitte  
🤖 **JASZ AI-Chatbot**: RAG-basierter Wissensdatenbank-Assistent  
📊 **Lager-Intelligenz**: Vorhersagemodelle für Bestandsoptimierung  
⚡ **Prozessoptimierung**: Bottleneck-Analyse und Effizienzsteigerung  
📈 **Supply Chain Analytics**: Lieferkettenoptimierung mit KI  
📋 **Automatisierte Berichte**: Intelligente Berichtsgenerierung  

### Geschäftslogik & Anwendungsbereich

Das Modul implementiert domänenspezifische KI-Lösungen für:
- **Produktionsplanung**: Optimierung von Schnittmustern und Materialverbrauch
- **Lagerverwaltung**: ABC-Analyse, Demand-Forecasting, Anomalieerkennung  
- **Qualitätssicherung**: AI-gestützte Qualitätsanalyse
- **Wissensverwaltung**: Intelligente Dokumentation und Hilfsysteme

### Architekturrolle

Das Modul fungiert als **Application Service Layer** mit klarer Trennung zwischen:
- **Domain Layer**: Geschäftslogik und Domänenmodelle
- **Application Layer**: Use Cases und Orchestrierung
- **Infrastructure Layer**: External APIs, Vector Databases, Caching

## 2. Dateistruktur-Analyse

```
src/modules/ai/
├── 📁 components/           # React UI-Komponenten
│   ├── 📁 ai-elements/      # Wiederverwendbare AI UI-Bausteine
│   │   ├── actions.tsx      # Aktions-Buttons mit Tooltips
│   │   ├── code-block.tsx   # Syntax-Highlighting Code-Blöcke
│   │   ├── conversation.tsx # Chat-Container mit Auto-Scroll
│   │   ├── loader.tsx       # Animierte Ladeindikator
│   │   ├── message.tsx      # Nachrichten-Komponente
│   │   ├── prompt-input.tsx # Erweiterte Eingabe-Komponente
│   │   ├── response.tsx     # Markdown-Renderer für AI-Antworten
│   │   ├── source.tsx       # Quellen-Anzeige
│   │   ├── suggestion.tsx   # Interaktive Vorschläge
│   │   ├── task.tsx         # Task-Anzeige mit Status
│   │   └── tool.tsx         # Tool-Ausführung Visualisierung
│   ├── 📁 ai/              # Core AI-Komponenten
│   │   └── AIInsights.tsx   # KI-Einblicke Dashboard
│   ├── 📁 ask-jasz/        # JASZ Chatbot-Integration
│   │   └── AskJaszButton.tsx # Floating Chat-Button
│   ├── 📁 chat/            # Chat-System
│   │   └── ChatBot.tsx      # Hauptchat-Komponente
│   ├── 📁 cutting/         # Schnittoptimierung UI
│   │   ├── AIQualityAnalysis.tsx        # Qualitätsanalyse
│   │   ├── CuttingConfigurationSection.tsx # Konfiguration
│   │   ├── CuttingPlanVisualization.tsx    # Visualisierung
│   │   └── TrommelrechnungComponent.tsx    # Trommelberechnung
│   ├── 📁 documentation/   # Dokumentations-Komponenten
│   ├── 📁 error-handling/  # Fehlerbehandlung UI
│   ├── 📁 inventory/       # Lager-Intelligenz UI
│   ├── 📁 navigation/      # AI-Navigation
│   ├── 📁 performance/     # Performance-Monitoring UI
│   ├── 📁 reporting/       # Berichts-UI
│   ├── 📁 security/        # Sicherheits-Dashboard
│   ├── 📁 settings/        # AI-Einstellungen
│   ├── 📁 supply-chain/    # Lieferketten-Analytics
│   └── 📁 warehouse/       # Lager-Optimierung UI
├── 📁 decorators/          # TypeScript Decorators
├── 📁 demo/               # Demo-Komponenten
├── 📁 deployment/         # Deployment-Validierung
├── 📁 docs/               # Detaillierte Dokumentation
│   └── schneidoptimierung-dokumentation.md
├── 📁 hooks/              # React Custom Hooks
│   ├── useAIErrorHandler.ts    # Fehlerbehandlung
│   ├── useAIServiceHealth.ts   # Service-Gesundheit
│   └── useMaterialData.ts      # Material-Daten
├── 📁 monitoring/         # System-Monitoring
├── 📁 pages/              # AI-Seiten
│   ├── AIDashboardPage.tsx         # Haupt-Dashboard
│   ├── CuttingOptimizationPage.tsx # Schnittoptimierung
│   ├── RAGManagementPage.tsx       # RAG-Verwaltung
│   └── WarehouseOptimizationPage.tsx # Lageroptimierung
├── 📁 services/           # Backend-Services
│   ├── 📁 base/            # Basis-Services
│   ├── 📁 caching/         # Caching-Layer
│   ├── 📁 chat/            # Chat-Services
│   ├── 📁 cutting/         # Schnittoptimierung
│   │   ├── 📁 algorithms/   # Optimierungsalgorithmen
│   │   │   ├── AlternativeSolutionGenerator.ts
│   │   │   ├── GeneticAlgorithm.ts
│   │   │   └── MultiObjectiveOptimizer.ts
│   │   └── CuttingOptimizerService.ts
│   ├── 📁 embedding/       # Embedding-Services
│   ├── 📁 error-handling/  # Fehlerbehandlung
│   ├── 📁 inventory/       # Lager-Services
│   ├── 📁 knowledge/       # Wissensbasis
│   ├── 📁 performance/     # Performance-Monitoring
│   ├── 📁 process/         # Prozessoptimierung
│   ├── 📁 rag/            # RAG-Services
│   ├── 📁 reporting/       # Berichts-Services
│   ├── 📁 security/        # Sicherheits-Services
│   ├── 📁 supply-chain/    # Lieferketten-Services
│   ├── 📁 types/           # Service-Interfaces
│   ├── 📁 vector/          # Vector Database
│   └── 📁 warehouse/       # Lager-Services
├── 📁 types/              # TypeScript-Typdefinitionen
│   ├── cutting.ts          # Schnittoptimierung-Types
│   ├── errors.ts           # Fehler-Types
│   └── security.ts         # Sicherheits-Types
├── 📁 utils/              # Utility-Funktionen
├── index.ts               # Haupt-Export
└── module.config.ts       # Modul-Konfiguration
```

### Kategorisierung nach Funktionen

| Kategorie | Komponenten | Zweck |
|-----------|-------------|-------|
| **Core AI** | ai/, chat/, rag/ | Grundlegende AI-Funktionen |
| **Optimization** | cutting/, process/, warehouse/ | Optimierungsalgorithmen |
| **Analytics** | inventory/, supply-chain/, performance/ | Datenanalyse und Vorhersagen |
| **UI/UX** | ai-elements/, navigation/ | Benutzeroberfläche |
| **Infrastructure** | error-handling/, security/, monitoring/ | System-Services |

## 3. Detaillierte Dateibeschreibungen

### Core Services

#### `services/base/AIBaseService.ts`
**Zweck**: Basis-Klasse für alle AI-Services mit einheitlicher Fehlerbehandlung und Performance-Monitoring.

**Hauptfunktionalitäten**:
- Erweiterte Gesundheitsüberprüfung mit AI-spezifischen Metriken
- Integriertes Caching für Vector-Operations
- Performance-Tracking (Response-Zeit, Erfolgsrate, Cache-Hit-Rate)
- Fallback-Strategien für Service-Ausfälle

```typescript path=/src/modules/ai/services/base/AIBaseService.ts start=42
export abstract class AIBaseService extends BaseService {
  protected async handleAIError<T>(
    operation: () => Promise<T>,
    fallback: () => Promise<T>,
    errorType: AIServiceError
  ): Promise<T>
}
```

#### `services/rag/RAGService.ts`
**Zweck**: Implementierung von Retrieval-Augmented Generation für kontextbewusste AI-Antworten.

**Kernmethoden**:
- `createEmbedding(text: string)`: Generiert Vektor-Embeddings
- `searchSimilar(query: string)`: Semantische Ähnlichkeitssuche
- `enhanceQuery(query: string)`: Erweitert Queries mit Kontext

**Design Pattern**: Repository Pattern mit Vector Database Abstraktion

#### `services/cutting/CuttingOptimizerService.ts`
**Zweck**: Hochentwickelte Schnittoptimierung mit genetischen Algorithmen.

**Algorithmen**:
- **First-Fit**: O(n×m) - Schnell, suboptimal
- **Best-Fit**: O(n×m×log(m)) - Bessere Raumnutzung
- **Genetischer Algorithmus**: O(g×p×f) - Optimal, zeitintensiv
- **Multi-Objective NSGA-II**: Pareto-optimale Lösungen

**Performance**: 
- Kleine Probleme (5 Aufträge, 3 Trommeln): < 5s
- Mittlere Probleme (20 Aufträge, 8 Trommeln): < 15s
- Große Probleme (50 Aufträge, 15 Trommeln): < 60s

### UI Components

#### `components/ai-elements/`
**Zweck**: Wiederverwendbare AI UI-Komponenten mit konsistentem Neobrutalism Design.

**Komponenten-Highlights**:
- `PromptInput`: Auto-Resize Textarea mit Tool-Integration
- `Response`: Markdown-Renderer mit Syntax-Highlighting (Shiki)
- `Conversation`: Auto-Scroll Chat mit Virtualisierung
- `CodeBlock`: Copy-Button, Sprach-Detection, Zeilennummern

**Accessibility**: Vollständige Keyboard-Navigation und Screen-Reader Support

#### `components/cutting/TrommelrechnungComponent.tsx`
**Zweck**: Komplexe UI für Trommel-Konfiguration und Schnitt-Visualisierung.

**Features**:
- Interaktive Trommel-Auswahl
- Real-time Berechnungen
- 3D-Visualisierung der Schnittmuster
- Export zu PDF/Excel

### Advanced Algorithms

#### `services/cutting/algorithms/GeneticAlgorithm.ts`
**Zweck**: Evolutionäre Optimierung für komplexe Schnittprobleme.

**Konfiguration**:
```typescript path=null start=null
interface GeneticAlgorithmConfig {
  populationSize: number;      // 50-100
  generations: number;         // 100-200  
  mutationRate: number;        // 0.05-0.15
  crossoverRate: number;       // 0.7-0.9
  convergenceThreshold: number; // Frühes Stoppen
}
```

**Fitness-Funktion**: Multi-objektive Bewertung (Verschnitt, Effizienz, Zeit)

#### `services/cutting/algorithms/MultiObjectiveOptimizer.ts`
**Zweck**: NSGA-II Implementierung für Pareto-optimale Lösungen.

**Objektive**:
1. Verschnittminimierung (40% Gewichtung)
2. Effizienzmaximierung (40% Gewichtung)  
3. Zeitoptimierung (20% Gewichtung)

## 4. Abhängigkeiten & Verbindungen

### Externe Dependencies

| Package | Version | Zweck | Health Score |
|---------|---------|--------|-------------|
| `@ai-sdk/react` | ^2.0.9 | React AI Integration | ⭐⭐⭐⭐⭐ |
| `openai` | ^5.8.2 | OpenAI API Client | ⭐⭐⭐⭐⭐ |
| `react-syntax-highlighter` | ^15.6.1 | Code-Highlighting | ⭐⭐⭐⭐ |
| `recharts` | ^2.15.3 | Datenvisualisierung | ⭐⭐⭐⭐⭐ |
| `katex` | ^0.16.22 | Mathematische Formeln | ⭐⭐⭐⭐ |
| `better-sqlite3` | latest | Vector Database | ⭐⭐⭐⭐ |

### Interne Module-Abhängigkeiten

```mermaid
graph TD
    A[AI Module] --> B[Base Services]
    A --> C[Database Layer]
    A --> D[UI Components]
    
    E[Chat Service] --> F[RAG Service]
    F --> G[Vector DB]
    F --> H[Embedding Service]
    
    I[Cutting Service] --> J[Genetic Algorithm]
    I --> K[Multi-Objective Optimizer]
    
    L[Inventory Service] --> M[Predictive Analytics]
    L --> N[ABC Analysis]
```

### API-Endpunkte

#### Chat API
```http
POST /api/ai/chat
Content-Type: application/json
Authorization: Bearer <token>

{
  "message": "Optimiere Schnittmuster für Kabel XYZ",
  "context": {
    "module": "cutting",
    "material": "copper"
  }
}
```

#### Optimization API
```http
POST /api/ai/cutting/optimize
Content-Type: application/json

{
  "orders": [...],
  "availableDrums": [...],
  "algorithm": "genetic"
}
```

### Datenfluss-Architektur

1. **Input Layer**: User Input → Validation → Sanitization
2. **Processing Layer**: RAG Enhancement → Service Orchestration
3. **Algorithm Layer**: Genetic/Multi-Objective Optimization
4. **Output Layer**: Result Formatting → UI Rendering

## 5. Technische Details

### Verwendete Technologien

#### Frontend Stack
- **React 18.2** mit Concurrent Features
- **TypeScript 5.8** für Type Safety
- **Tailwind CSS 4.1** für Responsive Design
- **Framer Motion 12.23** für Animationen
- **Radix UI** für Accessibility

#### AI & ML Stack
- **Vector Database**: SQLite mit Embedding-Support
- **Embeddings**: OpenAI text-embedding-3-small (1536d)
- **LLM Integration**: OpenRouter API mit Multiple Models
- **Optimization**: Custom Genetic Algorithms

#### Performance Optimierungen
- **Vector Caching**: In-Memory LRU Cache (1h TTL)
- **Component Virtualization**: Für große Chat-Historien
- **Lazy Loading**: Algorithmische Module bei Bedarf
- **Web Workers**: Für CPU-intensive Berechnungen

### Konfigurationsdateien

#### `module.config.ts`
Definiert Modul-Routen, Berechtigungen und Sicherheitsrichtlinien:

```typescript path=/src/modules/ai/module.config.ts start=3
export const aiModuleConfig: ModuleConfig = {
  id: 'ai',
  name: 'ai',
  displayName: 'KI-Assistent',
  description: 'KI-gestützte Analyse und Optimierung',
  security: {
    requiresAuthentication: true,
    enableInputValidation: true,
    rateLimiting: {
      enabled: true,
      defaultLimit: 50,
      windowMs: 15 * 60 * 1000
    }
  }
}
```

#### Umgebungsvariablen

```bash
# OpenRouter API Configuration
OPENROUTER_API_KEY=sk-or-v1-...
OPENROUTER_MODEL=openai/gpt-4-turbo

# Vector Database
VECTOR_DB_PATH=./data/vectors.db
VECTOR_CACHE_TTL=3600000

# Performance Tuning
MAX_CONTEXT_LENGTH=8000
SIMILARITY_THRESHOLD=0.7
GENETIC_POPULATION_SIZE=80
```

### Performance-kritische Bereiche

#### 1. Vector Similarity Search
**Optimierung**: 
- Hierarchical Navigable Small World (HNSW) Index
- Quantized Embeddings für Memory-Effizienz
- Batch-Processing für Multiple Queries

#### 2. Genetic Algorithm
**Optimierung**:
- Parallelisierung über Web Workers
- Early Stopping bei Konvergenz  
- Elitismus für schnellere Konvergenz

```typescript path=null start=null
// Performance Monitoring
const performanceTracker = {
  trackAlgorithmPerformance(algorithm: string, duration: number): void,
  getAverageResponseTime(): number,
  getCacheHitRate(): number
};
```

## 6. Verwendungsbeispiele

### JASZ Chatbot Integration

```typescript path=null start=null
import { ChatBot, AskJaszButton } from '@/modules/ai/components';

// Floating Chat Button für alle Seiten
<AskJaszButton 
  position="bottom-right"
  variant="neobrutalism"
  hotkey="Ctrl+K"
/>

// Vollständiger Chat in eigenständiger Seite
<ChatBot
  mode="ai-plus"
  enableRAG={true}
  enableTools={['cutting-optimization', 'inventory-analysis']}
  customPrompts={[
    "Optimiere meine Schnittmuster",
    "Analysiere Lagerbestände"
  ]}
/>
```

### Schnittoptimierung Service

```typescript path=null start=null
import { CuttingOptimizerService } from '@/modules/ai/services';

const optimizer = new CuttingOptimizerService({
  defaultAlgorithm: 'genetic',
  enableAdvancedOptimization: true,
  maxOptimizationTime: 30000
});

await optimizer.initialize();

const cuttingPlan = await optimizer.optimizeCuttingPlan({
  orders: [
    { id: '001', requiredLength: 150, quantity: 5, priority: 'high' },
    { id: '002', requiredLength: 200, quantity: 3, priority: 'medium' }
  ],
  availableDrums: [
    { id: 'DRUM-001', availableLength: 1000, material: 'copper' }
  ],
  constraints: {
    maxWastePercentage: 15,
    prioritizeHighPriority: true
  }
});

console.log(`Effizienz: ${cuttingPlan.efficiency * 100}%`);
console.log(`Verschnitt: ${cuttingPlan.totalWaste}m`);
```

### Multi-Objektive Optimierung

```typescript path=null start=null
// Pareto-Front Generierung
const moResult = await optimizer.optimizeMultiObjective(request);

moResult.paretoFront.forEach((solution, index) => {
  console.log(`Alternative ${index + 1}:`);
  console.log(`  Verschnitt: ${solution.objectives.waste}m`);
  console.log(`  Effizienz: ${solution.objectives.efficiency}%`);
  console.log(`  Zeit: ${solution.objectives.time}min`);
});

// Beste Alternative nach Gewichtung auswählen
const bestSolution = moResult.alternatives
  .sort((a, b) => b.score - a.score)[0];
```

### RAG-Enhanced Queries

```typescript path=null start=null
import { RAGService } from '@/modules/ai/services';

const ragService = new RAGService(vectorService, embeddingService);

const enhancedQuery = await ragService.enhanceQuery(
  "Wie optimiere ich Kabelverschnitt?",
  {
    department: 'production',
    material: 'copper',
    urgency: 'high'
  }
);

// Erweiterte Query mit Kontext
console.log('Original:', enhancedQuery.originalQuery);
console.log('Kontext:', enhancedQuery.context);
console.log('Relevante Daten:', enhancedQuery.relevantData);
```

## 7. Datenmodelle & Schnittstellen

### Core Data Models

#### Cutting Optimization
```typescript path=/src/modules/ai/types/cutting.ts start=5
export interface CuttingOrder {
  id: string;
  requiredLength: number;
  quantity: number;
  priority: 'high' | 'medium' | 'low';
  material?: string;
  tolerance?: number;
}

export interface CuttingPlan {
  drumAllocations: DrumAllocation[];
  cuttingSequence: CuttingStep[];
  totalWaste: number;
  efficiency: number;
  estimatedTime: number;
}
```

#### RAG Services
```typescript path=null start=null
export interface RAGResponse {
  answer: string;
  sources: Source[];
  confidence: number;
  processingTime: number;
  metadata: {
    model: string;
    temperature: number;
    context_length: number;
  };
}

export interface Source {
  id: string;
  title: string;
  content: string;
  url?: string;
  relevance: number;
}
```

#### Error Handling
```typescript path=/src/modules/ai/types/errors.ts start=60
export interface AIServiceError extends Error {
  code: AIServiceErrorCode;
  severity: AIServiceErrorSeverity;
  service: string;
  operation: string;
  timestamp: Date;
  context?: Record<string, any>;
  recoverable: boolean;
  userMessage: string;
  technicalMessage: string;
}
```

### API Schemas

#### Chat Request/Response
```json
{
  "request": {
    "message": "string",
    "context": {
      "module": "cutting|inventory|warehouse",
      "userId": "number",
      "sessionId": "string"
    },
    "options": {
      "enableRAG": "boolean",
      "temperature": "number",
      "maxTokens": "number"
    }
  },
  "response": {
    "message": "string",
    "sources": ["array"],
    "suggestions": ["array"],
    "tools_used": ["array"],
    "processing_time": "number",
    "metadata": "object"
  }
}
```

#### Optimization Request/Response
```json
{
  "optimization_request": {
    "algorithm": "first-fit|best-fit|genetic|multi-objective",
    "orders": [{
      "id": "string",
      "required_length": "number",
      "quantity": "number",
      "priority": "high|medium|low"
    }],
    "constraints": {
      "max_waste_percentage": "number",
      "time_limit": "number"
    }
  },
  "optimization_response": {
    "plan": "object",
    "alternatives": ["array"],
    "metrics": {
      "efficiency": "number",
      "total_waste": "number",
      "processing_time": "number"
    },
    "visualization_data": "object"
  }
}
```

### Database Schemas

#### Vector Database (SQLite)
```sql
CREATE TABLE documents (
    id TEXT PRIMARY KEY,
    content TEXT NOT NULL,
    title TEXT,
    source TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE embeddings (
    id TEXT PRIMARY KEY,
    document_id TEXT REFERENCES documents(id),
    embedding BLOB NOT NULL,
    dimensions INTEGER DEFAULT 1536
);

CREATE INDEX idx_embeddings_doc ON embeddings(document_id);
```

## 8. Testing

### Test-Struktur

```
__tests__/
├── unit/                   # Unit Tests (87% Coverage)
│   ├── services/
│   ├── algorithms/
│   └── components/
├── integration/            # Integration Tests (76% Coverage)  
│   ├── ai-service-flow/
│   ├── rag-pipeline/
│   └── cutting-optimization/
├── performance/            # Performance Tests
│   ├── genetic-algorithm/
│   ├── vector-search/
│   └── concurrent-users/
├── e2e/                   # End-to-End Tests
│   ├── chatbot-flow/
│   └── optimization-workflow/
└── load/                  # Load Tests
    ├── api-endpoints/
    └── vector-operations/
```

### Testing Scripts

```bash
# Vollständige Test-Suite
pnpm run test:ai:comprehensive

# Performance Tests  
pnpm run test:ai:performance

# Load Tests
pnpm run test:ai:load

# Coverage Report
pnpm run test:ai:coverage
```

### Test Coverage Highlights

| Komponente | Unit Tests | Integration | E2E | Coverage |
|------------|-------------|-------------|-----|----------|
| RAG Service | ✅ | ✅ | ✅ | 91% |
| Cutting Optimizer | ✅ | ✅ | ✅ | 88% |
| Genetic Algorithm | ✅ | ✅ | ❌ | 85% |
| Chat Components | ✅ | ✅ | ✅ | 92% |
| Error Handling | ✅ | ✅ | ❌ | 94% |

### Mock-Strategien

#### Service Mocks
```typescript path=null start=null
// Vector Database Mock
export const mockVectorService = {
  insertVector: vi.fn().mockResolvedValue(undefined),
  searchSimilar: vi.fn().mockResolvedValue([
    { id: '1', similarity: 0.95, metadata: { content: 'Test' } }
  ]),
  deleteVector: vi.fn().mockResolvedValue(true)
};

// OpenAI API Mock  
export const mockOpenAIClient = {
  embeddings: {
    create: vi.fn().mockResolvedValue({
      data: [{ embedding: new Array(1536).fill(0.1) }]
    })
  }
};
```

### Performance Benchmarks

```typescript path=null start=null
describe('Performance Tests', () => {
  test('Genetic Algorithm scales linearly', async () => {
    const smallProblem = generateProblem(5, 3);
    const largeProblem = generateProblem(50, 15);
    
    const smallTime = await timeExecution(() => 
      optimizer.optimize(smallProblem)
    );
    const largeTime = await timeExecution(() => 
      optimizer.optimize(largeProblem)
    );
    
    // Skalierung sollte < O(n²) sein
    expect(largeTime / smallTime).toBeLessThan(100);
  });
});
```

## 9. Entwicklungshinweise

### Best Practices

#### Code-Qualität & Stil
- **TypeScript Strict Mode** aktiviert für maximale Type Safety
- **ESLint + Prettier** Konfiguration mit projektspezifischen Regeln
- **Sprechende Bezeichner** aus dem Domänen-Vokabular verwenden

```typescript path=null start=null
// ✅ Gut - Sprechende Bezeichner
interface CuttingOrder {
  requiredLengthInMeters: number;
  priorityLevel: OrderPriority;
  materialSpecification: MaterialType;
}

// ❌ Schlecht - Abkürzungen
interface CO {
  len: number;
  prio: number;
  mat: string;
}
```

#### Architektur-Patterns
- **Clean Architecture**: Strikte Trennung der Schichten
- **Dependency Injection**: Services über Konstruktor injizieren
- **Single Responsibility**: Jede Klasse hat genau einen Zweck

```typescript path=null start=null
// Dependency Injection Pattern
export class CuttingOptimizerService {
  constructor(
    private readonly inventoryService: InventoryService,
    private readonly cacheService: CacheService,
    private readonly logger: Logger
  ) {}
}
```

### Häufige Fehlerquellen

#### 1. Memory Leaks in Genetic Algorithm
**Problem**: Große Populationen werden nicht garbage collected
**Lösung**: Explizite cleanup() Methode implementieren

```typescript path=null start=null
export class GeneticAlgorithm {
  private population: Individual[] = [];
  
  cleanup(): void {
    this.population.length = 0;
    this.fitnessCache.clear();
  }
}
```

#### 2. Vector Database Locking
**Problem**: Concurrent writes auf SQLite Database
**Lösung**: Write Queue mit Retry Logic

```typescript path=null start=null
class VectorDatabaseService {
  private writeQueue = new Queue({ concurrency: 1 });
  
  async insertVector(id: string, embedding: number[]): Promise<void> {
    return this.writeQueue.add(async () => {
      await this.db.insertVector(id, embedding);
    });
  }
}
```

#### 3. RAG Context Overflow
**Problem**: Zu viele Sources überschreiten Context Limit
**Lösung**: Intelligente Source-Priorisierung

```typescript path=null start=null
private selectBestSources(
  sources: Source[], 
  maxContextLength: number
): Source[] {
  let totalLength = 0;
  return sources
    .sort((a, b) => b.relevance - a.relevance)
    .filter(source => {
      totalLength += source.content.length;
      return totalLength <= maxContextLength;
    });
}
```

### Performance-Hotspots

#### 1. Embedding Generation
**Bottleneck**: API Calls zu OpenAI
**Optimierung**: Batch-Processing und Caching

```typescript path=null start=null
async createEmbeddingsBatch(
  texts: string[]
): Promise<number[][]> {
  const cacheKey = `embeddings:${hash(texts)}`;
  const cached = await this.cache.get(cacheKey);
  if (cached) return cached;
  
  const response = await this.openai.embeddings.create({
    input: texts, // Batch-Request
    model: 'text-embedding-3-small'
  });
  
  const embeddings = response.data.map(d => d.embedding);
  await this.cache.set(cacheKey, embeddings, { ttl: 3600 });
  return embeddings;
}
```

#### 2. UI Rendering bei großen Datasets
**Bottleneck**: Große Chat-Historien
**Optimierung**: Virtualization mit React-Window

```typescript path=null start=null
import { FixedSizeList as List } from 'react-window';

export const ConversationList = ({ messages }) => (
  <List
    height={600}
    itemCount={messages.length}
    itemSize={120}
    itemData={messages}
  >
    {({ index, style, data }) => (
      <div style={style}>
        <Message message={data[index]} />
      </div>
    )}
  </List>
);
```

### TODOs & Verbesserungspotentiale

#### Kurzfristig (Q1 2024)
- [ ] **Vector Database Migration**: SQLite → Pinecone für bessere Skalierung
- [ ] **Genetic Algorithm GPU**: CUDA-Beschleunigung für große Probleme
- [ ] **Real-time Optimization**: WebSocket-basierte Live-Updates

#### Mittelfristig (Q2 2024) 
- [ ] **Multi-Model Support**: Integration von Claude, Gemini zusätzlich zu GPT
- [ ] **Federated Learning**: Lokale Model-Anpassung auf Kundendaten
- [ ] **Advanced Analytics**: Predictive Quality Control

#### Langfristig (2024+)
- [ ] **Edge Deployment**: Lokale LLM-Inferenz ohne Internet
- [ ] **AutoML Pipeline**: Automatische Algorithm-Auswahl
- [ ] **Digital Twin Integration**: 3D-Fabrik-Simulation

### Coding Standards

#### TypeScript Configuration
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "exactOptionalPropertyTypes": true
  }
}
```

#### Import Organization
```typescript path=null start=null
// 1. Node modules
import React from 'react';
import { z } from 'zod';

// 2. Internal modules (absolute paths)
import { BaseService } from '@/services/base.service';
import { validateInput } from '@/utils/validation';

// 3. Relative imports
import { CuttingOrder } from './types/cutting';
import { GeneticAlgorithm } from './algorithms/GeneticAlgorithm';
```

#### Error Handling Pattern
```typescript path=null start=null
export class AIService {
  async processRequest(request: AIRequest): Promise<AIResponse> {
    try {
      // Validate input
      const validated = this.validateRequest(request);
      
      // Process with monitoring
      const startTime = Date.now();
      const result = await this.process(validated);
      
      // Log success metrics
      this.metricsCollector.recordSuccess(Date.now() - startTime);
      
      return result;
    } catch (error) {
      // Structured error handling
      const aiError = this.createAIError(error, 'processRequest', request);
      this.errorHandler.handle(aiError);
      
      // Attempt recovery
      return this.fallbackResponse(request);
    }
  }
}
```

### Deployment & CI/CD

#### GitHub Actions Workflow
```yaml
name: AI Module Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: pnpm install
      - name: Run AI tests
        run: pnpm run test:ai:comprehensive
      - name: Performance benchmarks
        run: pnpm run test:ai:performance
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

#### Docker Deployment
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

COPY src/modules/ai ./src/modules/ai
RUN pnpm run build:ai

EXPOSE 3000
CMD ["pnpm", "run", "start:ai"]
```

---

## Fazit

Das AI-Modul ist ein hochentwickeltes System, das modernste KI-Technologien für die Kabelindustrie implementiert. Mit seiner robusten Architektur, umfassenden Tests und klaren Entwicklungsrichtlinien bildet es das technische Rückgrat für intelligente Prozessoptimierung und Entscheidungsunterstützung.

**Letzte Aktualisierung**: 6. September 2025  
**Version**: 1.0.0  
**Maintainer**: Johann Zimmer

<citations>
<document>
    <document_type>RULE</document_type>
    <document_id>8lpf3ONx961vk9RVgOSPbD</document_id>
</document>
<document>
    <document_type>RULE</document_type>
    <document_id>hDYfzfD20DWAh2rhd8fC0P</document_id>
</document>
</citations>

