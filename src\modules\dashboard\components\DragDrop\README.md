# Drag-and-Drop Funktionalität für KPI-Karten

Diese Implementierung bietet eine vollständige Drag-and-Drop-Funktionalität für KPI-Karten im Dashboard unter Verwendung des dnd Kit Frameworks.

## 🚀 Features

- **Ziehbare KPI-Karten**: Alle vier Haupt-KPI-Karten (Wareneingang, Tonnage, Performance, Effizienz) können per Drag-and-Drop verschoben werden
- **Persistente Speicherung**: Kartenpositionen werden automatisch im localStorage gespeichert und beim nächsten Laden wiederhergestellt
- **Visuelle Rückmeldung**: Animationen und visuelle Hinweise während des Ziehens
- **Barrierefreie Bedienung**: Vollständige Keyboard-Navigation und Screen-Reader-Unterstützung
- **Responsive Design**: Funktioniert auf Desktop und mobilen Geräten
- **Performance-optimiert**: Minimale Re-Renders durch optimierte State-Verwaltung

## 📁 Dateistruktur

```
src/modules/dashboard/components/DragDrop/
├── DragDropProvider.tsx     # Haupt-Provider für Drag-and-Drop-Kontext
├── DraggableCard.tsx        # Wrapper für ziehbare Karten
├── DroppableContainer.tsx   # Container für Drop-Zonen
├── DragDrop.module.css      # Styling für alle Drag-and-Drop-Komponenten
└── README.md               # Diese Dokumentation

src/modules/dashboard/hooks/
└── usePersistentCardPositions.ts  # Hook für persistente Speicherung

src/modules/dashboard/components/Bento-Grid/
└── DraggableBentoGrid.tsx   # Erweiterte BentoGrid mit Drag-and-Drop
```

## 🔧 Komponenten-Übersicht

### DragDropProvider
Der Haupt-Provider, der das gesamte Dashboard mit Drag-and-Drop-Funktionalität umhüllt.

**Features:**
- Verwaltet den globalen Drag-and-Drop-Zustand
- Behandelt Drag-Start/End-Events
- Bietet Kontext für alle Kind-Komponenten
- Integriert Sensoren für Maus-, Touch- und Keyboard-Eingaben

### DraggableCard
Wrapper-Komponente, die bestehende Karten ziehbar macht.

**Features:**
- Drag-Handle für bessere UX
- Visuelle Rückmeldung während des Ziehens
- Accessibility-Unterstützung
- Anpassbare Styling-Optionen

### DroppableContainer
Container-Komponente für Drop-Zonen.

**Features:**
- Erkennt Drop-Events
- Visuelle Hervorhebung bei Hover
- Animierte Übergänge
- Accessibility-Labels

### usePersistentCardPositions
Custom Hook für die persistente Speicherung von Kartenpositionen.

**Features:**
- Automatisches Laden/Speichern im localStorage
- Fallback auf Standard-Positionen
- Reset-Funktionalität
- TypeScript-Typisierung

## 🎯 Verwendung

### Basis-Integration

```tsx
import { DragDropProvider } from './components/DragDrop/DragDropProvider';
import { DraggableBentoGrid } from './components/Bento-Grid/DraggableBentoGrid';

function Dashboard() {
  return (
    <DragDropProvider>
      <DraggableBentoGrid departmentKPIs={kpiData} />
    </DragDropProvider>
  );
}
```

### Erweiterte Konfiguration

```tsx
// Custom Positionen definieren
const customPositions = [
  { id: 'wareneingang', position: 0 },
  { id: 'tonnage', position: 1 },
  { id: 'performance', position: 2 },
  { id: 'efficiency', position: 3 }
];

// Hook mit custom Positionen verwenden
const { cardPositions, updatePositions, resetPositions } = usePersistentCardPositions(customPositions);
```

## 🧪 Testing

### Manuelle Tests

1. **Basis-Funktionalität testen:**
   ```bash
   npm run dev
   ```
   - Navigiere zum Dashboard
   - Versuche KPI-Karten zu ziehen
   - Überprüfe, ob Positionen gespeichert werden

2. **Drag-and-Drop testen:**
   - Klicke und ziehe eine KPI-Karte
   - Lass sie an einer neuen Position fallen
   - Lade die Seite neu → Position sollte gespeichert sein

3. **Keyboard-Navigation testen:**
   - Verwende Tab-Taste zur Navigation
   - Drücke Space/Enter auf Drag-Handle
   - Verwende Pfeiltasten zum Verschieben
   - Drücke Escape zum Abbrechen

4. **Mobile Geräte testen:**
   - Öffne Developer Tools
   - Aktiviere Touch-Simulation
   - Teste Touch-Drag-Funktionalität

### Automatisierte Tests

```bash
# Unit Tests
npm run test

# E2E Tests
npm run test:e2e
```

## 🎨 Styling-Anpassungen

Das Styling kann über die CSS-Module-Datei `DragDrop.module.css` angepasst werden:

```css
/* Custom Drag-Handle Farbe */
.dragHandle {
  background: rgba(your-color, 0.1);
}

/* Custom Drop-Zone Hervorhebung */
.droppableContainer--over {
  border-color: your-accent-color;
  background-color: rgba(your-color, 0.1);
}
```

## 🔧 Konfiguration

### localStorage Schlüssel
Standardmäßig wird der Schlüssel `dashboard-kpi-positions` verwendet. Dies kann im Hook geändert werden:

```tsx
const STORAGE_KEY = 'custom-kpi-positions';
```

### Standard-Positionen
Die Standard-Kartenpositionen können angepasst werden:

```tsx
const DEFAULT_POSITIONS: CardPosition[] = [
  { id: 'custom-card-1', position: 0 },
  { id: 'custom-card-2', position: 1 },
  // ...
];
```

## 🐛 Troubleshooting

### Häufige Probleme

1. **Karten lassen sich nicht ziehen:**
   - Überprüfe, ob DragDropProvider korrekt eingebunden ist
   - Stelle sicher, dass dnd Kit installiert ist
   - Prüfe Browser-Konsole auf Fehler

2. **Positionen werden nicht gespeichert:**
   - Überprüfe localStorage-Berechtigung
   - Teste in Inkognito-Modus
   - Prüfe usePersistentCardPositions Hook

3. **Styling-Probleme:**
   - Stelle sicher, dass CSS-Module korrekt importiert sind
   - Überprüfe CSS-Variablen (--accent-cyan, etc.)
   - Teste in verschiedenen Browsern

### Debug-Modus

Füge diese Zeile zum DragDropProvider hinzu für Debug-Ausgaben:

```tsx
console.log('Drag event:', { active, over, cardPositions });
```

## 🚀 Performance-Optimierungen

- **useMemo**: Kartenpositionen werden gecacht
- **useCallback**: Event-Handler werden optimiert
- **CSS-Transforms**: Hardware-beschleunigte Animationen
- **Lazy Loading**: Komponenten werden nur bei Bedarf geladen

## 📱 Browser-Unterstützung

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ✅ Mobile Safari (iOS 14+)
- ✅ Chrome Mobile (Android 10+)

## 🔄 Updates & Wartung

### dnd Kit Updates
```bash
npm update @dnd-kit/core @dnd-kit/sortable @dnd-kit/utilities @dnd-kit/modifiers
```

### Breaking Changes beachten
- Prüfe dnd Kit Changelog
- Teste alle Drag-and-Drop-Funktionen
- Aktualisiere TypeScript-Typen falls nötig

## 📄 Lizenz

Diese Implementierung folgt den Projektrichtlinien und verwendet die gleiche Lizenz wie das Hauptprojekt.