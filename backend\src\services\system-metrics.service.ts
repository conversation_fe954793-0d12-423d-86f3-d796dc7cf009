/**
 * System Metrics Service
 *
 * Erfasst echte System-Metriken wie CPU-Auslastung, Speicherverbrauch,
 * Event Loop Lag und andere wichtige System-Performance-Indikatoren.
 */

import { EventEmitter } from 'events';
import * as os from 'os';
import * as process from 'process';

export interface SystemMetrics {
  memoryUsage: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
    heapUsagePercent: number;
  };
  cpuUsage: number;
  uptime: number;
  eventLoopLag: number;
  systemLoad: {
    loadavg1: number;
    loadavg5: number;
    loadavg15: number;
  };
  networkInterfaces: {
    [key: string]: {
      address: string;
      netmask: string;
      family: string;
      mac: string;
      internal: boolean;
      cidr: string;
    };
  };
  diskUsage: {
    total: number;
    free: number;
    used: number;
    usagePercent: number;
  };
}

export interface SystemMetricsConfig {
  enableEventLoopMonitoring: boolean;
  eventLoopCheckInterval: number;
  enableDetailedNetworkInfo: boolean;
  enableDiskMonitoring: boolean;
}

const DEFAULT_CONFIG: SystemMetricsConfig = {
  enableEventLoopMonitoring: true,
  eventLoopCheckInterval: 1000, // 1 second
  enableDetailedNetworkInfo: false,
  enableDiskMonitoring: true
};

export class SystemMetricsService extends EventEmitter {
  private config: SystemMetricsConfig;
  private eventLoopTimer?: NodeJS.Timeout;
  private lastEventLoopCheck = Date.now();
  private eventLoopLag = 0;

  constructor(config: Partial<SystemMetricsConfig> = {}) {
    super();
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.startEventLoopMonitoring();
  }

  /**
   * Holt alle aktuellen System-Metriken
   */
  getCurrentMetrics(): SystemMetrics {
    const memUsage = process.memoryUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;

    // CPU Usage (approximated from load average)
    const cpuUsage = this.calculateCpuUsage();

    // Event Loop Lag
    const eventLoopLag = this.getEventLoopLag();

    // System Load
    const loadavg = os.loadavg();

    // Network Interfaces (simplified)
    const networkInterfaces = this.getNetworkInterfaces();

    // Disk Usage (approximated)
    const diskUsage = this.getDiskUsage();

    const metrics: SystemMetrics = {
      memoryUsage: {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external,
        rss: memUsage.rss,
        heapUsagePercent: (memUsage.heapUsed / memUsage.heapTotal) * 100
      },
      cpuUsage,
      uptime: process.uptime(),
      eventLoopLag,
      systemLoad: {
        loadavg1: loadavg[0],
        loadavg5: loadavg[1],
        loadavg15: loadavg[2]
      },
      networkInterfaces,
      diskUsage
    };

    this.emit('metrics', metrics);
    return metrics;
  }

  /**
   * Holt historische System-Metriken für einen Zeitraum
   */
  getHistoricalMetrics(timeRange: { start: Date; end: Date }): SystemMetrics[] {
    // In einer echten Implementierung würden hier historische Daten aus einer Datenbank geholt
    // Für jetzt geben wir nur die aktuellen Metriken zurück
    return [this.getCurrentMetrics()];
  }

  /**
   * Überprüft, ob System-Thresholds überschritten wurden
   */
  checkThresholds(metrics: SystemMetrics): Array<{
    type: 'warning' | 'error';
    metric: string;
    value: number;
    threshold: number;
    message: string;
  }> {
    const alerts: Array<{
      type: 'warning' | 'error';
      metric: string;
      value: number;
      threshold: number;
      message: string;
    }> = [];

    // Memory alerts
    if (metrics.memoryUsage.heapUsagePercent > 90) {
      alerts.push({
        type: 'error',
        metric: 'memoryUsage',
        value: metrics.memoryUsage.heapUsagePercent,
        threshold: 90,
        message: 'Heap memory usage exceeds 90%'
      });
    } else if (metrics.memoryUsage.heapUsagePercent > 80) {
      alerts.push({
        type: 'warning',
        metric: 'memoryUsage',
        value: metrics.memoryUsage.heapUsagePercent,
        threshold: 80,
        message: 'Heap memory usage exceeds 80%'
      });
    }

    // CPU alerts
    if (metrics.cpuUsage > 90) {
      alerts.push({
        type: 'error',
        metric: 'cpuUsage',
        value: metrics.cpuUsage,
        threshold: 90,
        message: 'CPU usage exceeds 90%'
      });
    } else if (metrics.cpuUsage > 70) {
      alerts.push({
        type: 'warning',
        metric: 'cpuUsage',
        value: metrics.cpuUsage,
        threshold: 70,
        message: 'CPU usage exceeds 70%'
      });
    }

    // Event Loop Lag alerts
    if (metrics.eventLoopLag > 100) {
      alerts.push({
        type: 'error',
        metric: 'eventLoopLag',
        value: metrics.eventLoopLag,
        threshold: 100,
        message: 'Event loop lag exceeds 100ms'
      });
    } else if (metrics.eventLoopLag > 50) {
      alerts.push({
        type: 'warning',
        metric: 'eventLoopLag',
        value: metrics.eventLoopLag,
        threshold: 50,
        message: 'Event loop lag exceeds 50ms'
      });
    }

    return alerts;
  }

  /**
   * Startet das Event Loop Monitoring
   */
  private startEventLoopMonitoring(): void {
    if (!this.config.enableEventLoopMonitoring) {
      return;
    }

    this.eventLoopTimer = setInterval(() => {
      this.measureEventLoopLag();
    }, this.config.eventLoopCheckInterval);
  }

  /**
   * Misst den Event Loop Lag
   */
  private measureEventLoopLag(): void {
    const start = Date.now();
    setImmediate(() => {
      const end = Date.now();
      this.eventLoopLag = end - start;
      this.lastEventLoopCheck = end;
    });
  }

  /**
   * Holt den aktuellen Event Loop Lag
   */
  private getEventLoopLag(): number {
    return this.eventLoopLag;
  }

  /**
   * Berechnet die CPU-Auslastung (approximiert)
   */
  private calculateCpuUsage(): number {
    const loadavg = os.loadavg();
    const cpus = os.cpus().length;

    // Load average gibt die Anzahl der Prozesse in der Run Queue über 1/5/15 Minuten an
    // Wir verwenden den 1-Minuten-Wert und konvertieren ihn zu einem Prozentwert
    const loadPercent = (loadavg[0] / cpus) * 100;

    // Begrenzen auf 0-100%
    return Math.min(Math.max(loadPercent, 0), 100);
  }

  /**
   * Holt vereinfachte Netzwerk-Interface-Informationen
   */
  private getNetworkInterfaces(): { [key: string]: any } {
    if (!this.config.enableDetailedNetworkInfo) {
      return {};
    }

    try {
      const interfaces = os.networkInterfaces();
      const result: { [key: string]: any } = {};

      for (const [name, nets] of Object.entries(interfaces)) {
        if (nets && nets.length > 0) {
          // Nehmen wir die erste nicht-interne IPv4-Adresse
          const ipv4 = nets.find(net => net.family === 'IPv4' && !net.internal);
          if (ipv4) {
            result[name] = {
              address: ipv4.address,
              netmask: ipv4.netmask,
              family: ipv4.family,
              mac: ipv4.mac,
              internal: ipv4.internal,
              cidr: `${ipv4.address}/${this.cidrFromNetmask(ipv4.netmask)}`
            };
          }
        }
      }

      return result;
    } catch (error) {
      console.warn('Error getting network interfaces:', error);
      return {};
    }
  }

  /**
   * Berechnet CIDR aus Netmask
   */
  private cidrFromNetmask(netmask: string): number {
    const octets = netmask.split('.').map(Number);
    let cidr = 0;
    for (const octet of octets) {
      cidr += this.bitCount(octet);
    }
    return cidr;
  }

  /**
   * Zählt die Anzahl der 1-Bits in einer Zahl
   */
  private bitCount(n: number): number {
    let count = 0;
    while (n) {
      count += n & 1;
      n >>= 1;
    }
    return count;
  }

  /**
   * Holt Disk-Nutzungsinformationen (approximiert)
   */
  private getDiskUsage(): { total: number; free: number; used: number; usagePercent: number } {
    if (!this.config.enableDiskMonitoring) {
      return { total: 0, free: 0, used: 0, usagePercent: 0 };
    }

    try {
      // Verwenden wir temporär die System-Memory-Informationen als Proxy
      // In einer echten Implementierung würden hier echte Disk-Informationen geholt
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const usedMemory = totalMemory - freeMemory;

      return {
        total: totalMemory,
        free: freeMemory,
        used: usedMemory,
        usagePercent: (usedMemory / totalMemory) * 100
      };
    } catch (error) {
      console.warn('Error getting disk usage:', error);
      return { total: 0, free: 0, used: 0, usagePercent: 0 };
    }
  }

  /**
   * Stoppt das Monitoring
   */
  destroy(): void {
    if (this.eventLoopTimer) {
      clearInterval(this.eventLoopTimer);
    }
    this.removeAllListeners();
  }
}

// Singleton instance
export const systemMetricsService = new SystemMetricsService();
export default systemMetricsService;